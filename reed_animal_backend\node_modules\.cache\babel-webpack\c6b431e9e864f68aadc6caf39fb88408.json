{"ast": null, "code": "import copyObject from './_copyObject.js';\nimport keys from './keys.js';\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\n\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\nexport default baseAssign;", "map": null, "metadata": {}, "sourceType": "module"}