{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n/** `Object#toString` result references. */\n\nvar dateTag = '[object Date]';\n/**\n * The base implementation of `_.isDate` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n */\n\nfunction baseIsDate(value) {\n  return isObjectLike(value) && baseGetTag(value) == dateTag;\n}\n\nexport default baseIsDate;", "map": null, "metadata": {}, "sourceType": "module"}