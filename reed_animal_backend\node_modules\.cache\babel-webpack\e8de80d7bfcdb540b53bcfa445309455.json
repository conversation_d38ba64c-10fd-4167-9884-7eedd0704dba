{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kurdish [ku]\n//! author : <PERSON><PERSON> : https://github.com/ShahramMebashar\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var symbolMap = {\n    1: '١',\n    2: '٢',\n    3: '٣',\n    4: '٤',\n    5: '٥',\n    6: '٦',\n    7: '٧',\n    8: '٨',\n    9: '٩',\n    0: '٠'\n  },\n      numberMap = {\n    '١': '1',\n    '٢': '2',\n    '٣': '3',\n    '٤': '4',\n    '٥': '5',\n    '٦': '6',\n    '٧': '7',\n    '٨': '8',\n    '٩': '9',\n    '٠': '0'\n  },\n      months = ['کانونی دووەم', 'شوبات', 'ئازار', 'نیسان', 'ئایار', 'حوزەیران', 'تەمموز', 'ئاب', 'ئەیلوول', 'تشرینی یەكەم', 'تشرینی دووەم', 'كانونی یەکەم'];\n  var ku = moment.defineLocale('ku', {\n    months: months,\n    monthsShort: months,\n    weekdays: 'یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌'.split('_'),\n    weekdaysShort: 'یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌'.split('_'),\n    weekdaysMin: 'ی_د_س_چ_پ_ه_ش'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ئێواره‌|به‌یانی/,\n    isPM: function (input) {\n      return /ئێواره‌/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'به‌یانی';\n      } else {\n        return 'ئێواره‌';\n      }\n    },\n    calendar: {\n      sameDay: '[ئه‌مرۆ كاتژمێر] LT',\n      nextDay: '[به‌یانی كاتژمێر] LT',\n      nextWeek: 'dddd [كاتژمێر] LT',\n      lastDay: '[دوێنێ كاتژمێر] LT',\n      lastWeek: 'dddd [كاتژمێر] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'له‌ %s',\n      past: '%s',\n      s: 'چه‌ند چركه‌یه‌ك',\n      ss: 'چركه‌ %d',\n      m: 'یه‌ك خوله‌ك',\n      mm: '%d خوله‌ك',\n      h: 'یه‌ك كاتژمێر',\n      hh: '%d كاتژمێر',\n      d: 'یه‌ك ڕۆژ',\n      dd: '%d ڕۆژ',\n      M: 'یه‌ك مانگ',\n      MM: '%d مانگ',\n      y: 'یه‌ك ساڵ',\n      yy: '%d ساڵ'\n    },\n    preparse: function (string) {\n      return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n\n    }\n  });\n  return ku;\n});", "map": null, "metadata": {}, "sourceType": "script"}