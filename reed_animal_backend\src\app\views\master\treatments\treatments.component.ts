import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Treatment } from '../../../views/models/treatment.models'
import { treatmentService } from '../../services/treatment.services'
import { TokenStorageService } from '../../services/token-storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';

@Component({
  selector: 'app-treatments',
  templateUrl: './treatments.component.html',
  styleUrls: ['./treatments.component.scss']
})
export class TreatmentsComponent implements OnInit {
  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('AddModal') public AddModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  loginForm: FormGroup;
  isFormReady = false;
  submitted = false;
  treatments = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  treatment: Treatment = {};
  nameFailed = false
  Add = true;
  Edit = true;
  Delete = true;

  constructor(private formBuilder: FormBuilder,private TreatmentService: treatmentService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    this.tokens();
    this.SignForm();
  }

  //clear modal window
  clear(): void {
    this.treatment = {};
    this.nameFailed = false
    this.isFormReady = false;
    this.submitted = false;
    this.loginForm.reset();
  }

  getfocus() {
    this.nameFailed = false
  }

  //token verified treatment
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // if (key != null) {
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Treatments") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.treatmentLists();
    // }
    // else {
    //   this.router.navigate(['/login']);
    // }
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //Get All treatment List
  treatmentLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.TreatmentService.GetTreatmentsList(skip, this.name)
      .subscribe((res: any) => {
        this.treatments = res.data;
        this.count = res.count;
        // console.log(this.treatments);
        // console.log(this.count);
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.treatmentLists();
  }

  //Edit or update treatment 
  GetTreatment(id): void {
    // console.log('id-->', id);
    this.TreatmentService.GetTreatmentDetail(id)
      .subscribe((res) => {
        this.treatment = res.data[0];
        this.f.name.setValue(res.data[0].name, {
          onlySelf: true
        })
        // console.log(res.data)
      })
  }

  EditTreatment(id): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {   
       const data = {
      name: this.loginForm.value.name
    }
    this.TreatmentService.UpdateTreatment(id, data)
      .subscribe((res) => {
        // console.log('res-->', res);
        this.primaryModal.hide()
        this.clear();
        this.treatmentLists();
      })
    }
  }

  //Status ON & OFF
  changed(active, id) {
    const data = {
      status: active
    }
    this.TreatmentService.UpdateTreatment(id, data)
      .subscribe((res: any) => {
        // console.log('res-->', res);
      })
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      name: ['', [Validators.required]],
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  //Add new treatment
  AddTreatment(): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {
      const data = {
        name: this.loginForm.value.name
      }
      this.AddModal.hide();
      this.TreatmentService.NewTreatment(data)
        .subscribe((res) => {
          this.clear();
          // console.log('new-->', res)
          this.treatmentLists();
        })
    }
  }

  //Delete treatment using id
  DeleteTreatment(id): void {
    // console.log('id-->', id)
    this.TreatmentService.DeleteTreatment(id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.removeModal.hide();
        this.treatmentLists();
      })
  }

}
