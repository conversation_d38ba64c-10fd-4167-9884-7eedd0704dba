"use strict";(self.webpackChunkDr_Reed_Admin_Panel=self.webpackChunkDr_Reed_Admin_Panel||[]).push([[354],{19148:function(e,i,t){t.d(i,{q:function(){return N}});var n=t(50022),o=t(6642),s=t(26415),d=t(74875),r=t(90658),a=t(99777),g=t(11192),l=t(63237),c=t(45055),u=t(30386);const Z=["primaryModal"],p=["AddModal"],m=["removeModal"];function f(e,i){if(1&e){const e=r.EpF();r.TgZ(0,"button",38),r.NdJ("click",function(){return r.CHM(e),r.oxw(),r.MAs(29).show()}),r._uU(1," Add Species "),r.qZA()}}function h(e,i){1&e&&(r.TgZ(0,"th"),r._uU(1,"Status"),r.qZA())}function q(e,i){if(1&e){const e=r.EpF();r.TgZ(0,"td"),r.TgZ(1,"label",40),r.TgZ(2,"input",41),r.NdJ("change",function(){r.CHM(e);const i=r.oxw().$implicit;return r.oxw().changed(i.status,i._id)})("ngModelChange",function(i){return r.CHM(e),r.oxw().$implicit.status=i}),r.qZA(),r._UZ(3,"span",42),r.qZA(),r.qZA()}if(2&e){const e=r.oxw().$implicit;r.xp6(2),r.Q6J("ngModel",e.status)}}function A(e,i){if(1&e){const e=r.EpF();r.TgZ(0,"a",43),r.NdJ("click",function(){r.CHM(e);const i=r.oxw().$implicit,t=r.oxw();return r.MAs(50).show(),t.GetType(i._id)}),r.TgZ(1,"span",44),r._UZ(2,"i",45),r._uU(3," Edit"),r.qZA(),r.qZA()}}function T(e,i){if(1&e){const e=r.EpF();r.TgZ(0,"a",43),r.NdJ("click",function(){r.CHM(e);const i=r.oxw().$implicit,t=r.oxw();return r.MAs(71).show(),t.GetType(i._id)}),r.TgZ(1,"span",46),r._UZ(2,"i",47),r._uU(3," Delete"),r.qZA(),r.qZA()}}function b(e,i){if(1&e&&(r.TgZ(0,"tr"),r.TgZ(1,"td"),r._uU(2),r.qZA(),r.YNc(3,q,4,1,"td",14),r.TgZ(4,"td"),r.YNc(5,A,4,0,"a",39),r.YNc(6,T,4,0,"a",39),r.qZA(),r.qZA()),2&e){const e=i.$implicit,t=r.oxw();r.xp6(2),r.Oqu(e.name),r.xp6(1),r.Q6J("ngIf",t.Edit),r.xp6(2),r.Q6J("ngIf",t.Edit),r.xp6(1),r.Q6J("ngIf",t.Delete)}}function v(e,i){1&e&&(r.TgZ(0,"div"),r._uU(1,"Name is mandatory"),r.qZA())}function y(e,i){if(1&e&&(r.TgZ(0,"div",48),r.YNc(1,v,2,0,"div",14),r.qZA()),2&e){const e=r.oxw();r.xp6(1),r.Q6J("ngIf",e.f.name.errors.required)}}function x(e,i){1&e&&(r.TgZ(0,"div"),r._uU(1,"Name is mandatory"),r.qZA())}function _(e,i){if(1&e&&(r.TgZ(0,"div",48),r.YNc(1,x,2,0,"div",14),r.qZA()),2&e){const e=r.oxw();r.xp6(1),r.Q6J("ngIf",e.f.name.errors.required)}}const J=function(e,i){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:i}},M=function(){return{backdrop:"static",keyboard:!1}},U=function(e){return{"is-invalid":e}};let N=(()=>{class e{constructor(e,i,t,n,o,s,d){this.animalservice=e,this.route=i,this.router=t,this.tokenStorage=n,this.formBuilder=o,this.Permission=s,this.EmployeeService=d,this.isFormReady=!1,this.submitted=!1,this.types=[],this.page=1,this.count=0,this.search="",this.name="",this.type={},this.nameFailed=!1,this.modal=!0,this.Add=!0,this.Edit=!0,this.Delete=!0}ngOnInit(){this.tokens(),this.SignForm()}clear(){this.type={},this.loginForm.reset(),this.nameFailed=!1,this.isFormReady=!1,this.submitted=!1}getfocus(){this.nameFailed=!1}tokens(){const e=this.tokenStorage.getUser();this.Permission.GetModule(e.role_id._id).subscribe(e=>{for(var i=0;i<e.data.length;i++)"Species"==e.data[i].module_name&&(this.Add=e.data[i].add,this.Edit=e.data[i].edit,this.Delete=e.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(e._id).subscribe(e=>{0==e.data.status?this.tokenStorage.signOut():this.typeLists()})}getrequestparams(e){let i={};return i.skip=10*(e-1),i}typeLists(){const e=this.getrequestparams(this.page);this.animalservice.GetTypesList(e,this.name).subscribe(e=>{this.types=e.data,this.count=e.count})}handlePageChange(e){this.page=e,this.typeLists()}GetType(e){this.animalservice.GetTypeDetail(e).subscribe(e=>{this.type=e.data[0],this.f.name.setValue(e.data[0].name,{onlySelf:!0})})}EditType(e){this.submitted=!0,this.loginForm.invalid||this.animalservice.UpdateType(e,{name:this.loginForm.value.name}).subscribe(e=>{this.primaryModal.hide(),this.type={},this.typeLists()})}changed(e,i){this.animalservice.UpdateType(i,{status:e}).subscribe(e=>{})}SignForm(){this.loginForm=this.formBuilder.group({name:["",[d.kI.required]]})}get f(){return this.loginForm.controls}AddType(){this.submitted=!0,this.loginForm.invalid||(this.animalservice.Newtype({name:this.loginForm.value.name}).subscribe(e=>{this.AddModal.hide(),this.clear(),this.typeLists()}),this.nameFailed=!0)}DeleteType(e){this.animalservice.Deletetype(e).subscribe(e=>{this.removeModal.hide(),this.type={},this.typeLists()})}}return e.\u0275fac=function(i){return new(i||e)(r.Y36(n.l),r.Y36(a.gz),r.Y36(a.F0),r.Y36(g.i),r.Y36(d.qu),r.Y36(o.$),r.Y36(s.d))},e.\u0275cmp=r.Xpm({type:e,selectors:[["app-animal-type"]],viewQuery:function(e,i){if(1&e&&(r.Gf(Z,1),r.Gf(p,1),r.Gf(m,1)),2&e){let e;r.iGM(e=r.CRH())&&(i.primaryModal=e.first),r.iGM(e=r.CRH())&&(i.AddModal=e.first),r.iGM(e=r.CRH())&&(i.removeModal=e.first)}},decls:87,vars:26,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","firstName"],["type","text","placeholder","e.g. Dog, Cat","formControlName","name",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"]],template:function(e,i){if(1&e){const e=r.EpF();r.TgZ(0,"div",0),r.TgZ(1,"div",1),r.TgZ(2,"div",2),r.TgZ(3,"div",3),r._uU(4," Species "),r.qZA(),r.TgZ(5,"div",4),r.TgZ(6,"div",0),r.TgZ(7,"div",5),r.YNc(8,f,2,0,"button",6),r.TgZ(9,"div",7),r.TgZ(10,"div",8),r.TgZ(11,"div",9),r.TgZ(12,"span",10),r.NdJ("click",function(){return i.typeLists()}),r._UZ(13,"i",11),r.qZA(),r.qZA(),r.TgZ(14,"input",12),r.NdJ("input",function(){return i.typeLists()})("ngModelChange",function(e){return i.name=e}),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(15,"table",13),r.TgZ(16,"thead"),r.TgZ(17,"tr"),r.TgZ(18,"th"),r._uU(19,"Species Name"),r.qZA(),r.YNc(20,h,2,0,"th",14),r.TgZ(21,"th"),r._uU(22,"Action"),r.qZA(),r.qZA(),r.qZA(),r.TgZ(23,"tbody"),r.YNc(24,b,7,4,"tr",15),r.ALo(25,"paginate"),r.qZA(),r.qZA(),r.TgZ(26,"div"),r.TgZ(27,"pagination-controls",16),r.NdJ("pageChange",function(e){return i.handlePageChange(e)}),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(28,"div",17,18),r.TgZ(30,"div",19),r.TgZ(31,"div",20),r.TgZ(32,"div",21),r.TgZ(33,"h4",22),r._uU(34,"Add Species"),r.qZA(),r.qZA(),r.TgZ(35,"div",23),r.TgZ(36,"div",0),r.TgZ(37,"div",24),r.TgZ(38,"form",25),r.TgZ(39,"div",26),r.TgZ(40,"label",27),r._uU(41,"Species Name*"),r.qZA(),r._UZ(42,"input",28),r.YNc(43,y,2,1,"div",29),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(44,"div",30),r.TgZ(45,"button",31),r.NdJ("click",function(){return r.CHM(e),r.MAs(29).hide(),i.clear()}),r._uU(46,"Cancel"),r.qZA(),r.TgZ(47,"button",32),r.NdJ("click",function(){return i.AddType()}),r._uU(48,"Save"),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(49,"div",17,33),r.TgZ(51,"div",19),r.TgZ(52,"div",20),r.TgZ(53,"div",21),r.TgZ(54,"h4",22),r._uU(55,"Edit Species"),r.qZA(),r.qZA(),r.TgZ(56,"div",23),r.TgZ(57,"div",0),r.TgZ(58,"div",24),r.TgZ(59,"form",25),r.TgZ(60,"div",26),r.TgZ(61,"label",27),r._uU(62,"Species Name*"),r.qZA(),r._UZ(63,"input",28),r.YNc(64,_,2,1,"div",29),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(65,"div",30),r.TgZ(66,"button",31),r.NdJ("click",function(){return r.CHM(e),r.MAs(50).hide(),i.clear()}),r._uU(67,"Cancel"),r.qZA(),r.TgZ(68,"button",32),r.NdJ("click",function(){return i.EditType(i.type._id)}),r._uU(69,"Save"),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(70,"div",34,35),r.TgZ(72,"div",36),r.TgZ(73,"div",20),r.TgZ(74,"div",21),r.TgZ(75,"h4",22),r._uU(76,"Are you sure ?"),r.qZA(),r.qZA(),r.TgZ(77,"div",23),r.TgZ(78,"div",0),r.TgZ(79,"div",24),r.TgZ(80,"p"),r._uU(81,"Do you want to delete this Species?"),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.TgZ(82,"div",30),r.TgZ(83,"button",31),r.NdJ("click",function(){return r.CHM(e),r.MAs(71).hide(),i.clear()}),r._uU(84,"Cancel"),r.qZA(),r.TgZ(85,"button",37),r.NdJ("click",function(){return i.DeleteType(i.type._id)}),r._uU(86,"Delete"),r.qZA(),r.qZA(),r.qZA(),r.qZA(),r.qZA()}2&e&&(r.xp6(8),r.Q6J("ngIf",i.Add),r.xp6(6),r.Q6J("ngModel",i.name),r.xp6(6),r.Q6J("ngIf",i.Edit),r.xp6(4),r.Q6J("ngForOf",r.xi3(25,13,i.types,r.WLB(16,J,i.page,i.count))),r.xp6(4),r.Q6J("config",r.DdM(19,M)),r.xp6(10),r.Q6J("formGroup",i.loginForm),r.xp6(4),r.Q6J("ngClass",r.VKq(20,U,i.submitted&&i.f.name.errors)),r.xp6(1),r.Q6J("ngIf",i.submitted&&i.f.name.errors),r.xp6(6),r.Q6J("config",r.DdM(22,M)),r.xp6(10),r.Q6J("formGroup",i.loginForm),r.xp6(4),r.Q6J("ngClass",r.VKq(23,U,i.submitted&&i.f.name.errors)),r.xp6(1),r.Q6J("ngIf",i.submitted&&i.f.name.errors),r.xp6(6),r.Q6J("config",r.DdM(25,M)))},directives:[l.O5,d.Fj,d.JJ,d.On,l.sg,c.LS,u.oB,d.vK,d.JL,d.sg,d.u,l.mk,d.Wl],pipes:[c._s],styles:[""]}),e})()},8816:function(e,i,t){t.d(i,{g:function(){return O}});var n=t(75874),o=t(50022),s=t(6642),d=t(26415),r=t(74875),a=t(90658),g=t(99777),l=t(11192),c=t(63237),u=t(45055),Z=t(30386);const p=["primaryModal"],m=["deleteModal"],f=["removeModal"];function h(e,i){if(1&e){const e=a.EpF();a.TgZ(0,"button",53),a.NdJ("click",function(){return a.CHM(e),a.oxw(),a.MAs(38).show()}),a._uU(1," Add Breed "),a.qZA()}}function q(e,i){if(1&e){const e=a.EpF();a.TgZ(0,"a",55),a.NdJ("click",function(){a.CHM(e);const i=a.oxw().index,t=a.oxw();return a.MAs(81).show(),t.GetBreeding(i)}),a.TgZ(1,"span",56),a._UZ(2,"i",57),a._uU(3," Edit"),a.qZA(),a.qZA()}}function A(e,i){if(1&e){const e=a.EpF();a.TgZ(0,"a",55),a.NdJ("click",function(){a.CHM(e);const i=a.oxw().$implicit,t=a.oxw();return a.MAs(123).show(),t.GetBreeding(i._id)}),a.TgZ(1,"span",58),a._UZ(2,"i",59),a._uU(3," Delete"),a.qZA(),a.qZA()}}function T(e,i){if(1&e&&(a.TgZ(0,"tr"),a.TgZ(1,"td"),a._uU(2),a.qZA(),a.TgZ(3,"td"),a._uU(4),a.qZA(),a.TgZ(5,"td"),a._uU(6),a.qZA(),a.TgZ(7,"td"),a._uU(8),a.qZA(),a.TgZ(9,"td"),a.YNc(10,q,4,0,"a",54),a.YNc(11,A,4,0,"a",54),a.qZA(),a.qZA()),2&e){const e=i.$implicit,t=a.oxw();a.xp6(2),a.Oqu(e.name),a.xp6(2),a.Oqu(e.code),a.xp6(2),a.Oqu(e.size),a.xp6(2),a.Oqu(e.type),a.xp6(2),a.Q6J("ngIf",t.Edit),a.xp6(1),a.Q6J("ngIf",t.Delete)}}function b(e,i){if(1&e&&(a.TgZ(0,"option",60),a._uU(1),a.qZA()),2&e){const e=i.$implicit;a.Q6J("value",e.name),a.xp6(1),a.Oqu(e.name)}}function v(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Species is mandatory"),a.qZA())}function y(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,v,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.species.errors.required)}}function x(e,i){if(1&e&&(a.TgZ(0,"option",60),a._uU(1),a.qZA()),2&e){const e=i.$implicit;a.Q6J("value",e),a.xp6(1),a.Oqu(e)}}function _(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Size is mandatory"),a.qZA())}function J(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,_,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.size.errors.required)}}function M(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Breed name is mandatory"),a.qZA())}function U(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,M,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.breed.errors.required)}}function N(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Breed code is mandatory"),a.qZA())}function k(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,N,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.code.errors.required)}}function C(e,i){if(1&e&&(a.TgZ(0,"option",60),a._uU(1),a.qZA()),2&e){const e=i.$implicit;a.Q6J("value",e.name),a.xp6(1),a.Oqu(e.name)}}function B(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Species is mandatory"),a.qZA())}function F(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,B,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.species.errors.required)}}function S(e,i){if(1&e&&(a.TgZ(0,"option",60),a._uU(1),a.qZA()),2&e){const e=i.$implicit;a.Q6J("value",e),a.xp6(1),a.Oqu(e)}}function Q(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Size is mandatory"),a.qZA())}function G(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,Q,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.size.errors.required)}}function Y(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Breed name is mandatory"),a.qZA())}function w(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,Y,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.breed.errors.required)}}function I(e,i){1&e&&(a.TgZ(0,"div"),a._uU(1,"Breed code is mandatory"),a.qZA())}function L(e,i){if(1&e&&(a.TgZ(0,"div",61),a.YNc(1,I,2,0,"div",62),a.qZA()),2&e){const e=a.oxw();a.xp6(1),a.Q6J("ngIf",e.f.code.errors.required)}}const E=function(e,i){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:i}},z=function(){return{backdrop:"static",keyboard:!1}},D=function(e){return{"is-invalid":e}};let O=(()=>{class e{constructor(e,i,t,n,o,s,d,r){this.BreedingService=e,this.AnimaltypeService=i,this.route=t,this.router=n,this.tokenStorage=o,this.Permission=s,this.EmployeeService=d,this.formBuilder=r,this.Types=[],this.Breedings=[],this.Size=["Toy","Small","Medium","Large","Giant"],this.page=1,this.count=0,this.search="",this.name="",this.ddlFileId="",this.value1="",this.breeding={_id:"",name:"",status:!1,type:"",size:"",code:""},this.isLoginFailed=!1,this.isFormReady=!1,this.submitted=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.sort=!1,this.value=1,this.field="name",this.typeFailed=!1}ngOnInit(){this.tokens(),this.SignForm()}clear(){this.loginForm.reset(),this.submitted=!1,this.Types=[],this.GetTypeLists(),this.breeding={_id:"",name:"",status:!1,type:"",size:"",code:""}}tokens(){this.tokenStorage.getToken();const e=this.tokenStorage.getUser();this.Permission.GetModule(e.role_id._id).subscribe(e=>{for(var i=0;i<e.data.length;i++)"Breed"==e.data[i].module_name&&(this.Add=e.data[i].add,this.Edit=e.data[i].edit,this.Delete=e.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(e._id).subscribe(e=>{0==e.data.status?this.tokenStorage.signOut():this.GetTypeLists()})}GetTypeLists(){this.BreedingService.GetTypesList().subscribe(e=>{this.Types=e.data,this.GetBreedingLists()})}getrequestparams(e){let i={};return i.skip=10*(e-1),i.value=this.value,i.field=this.field,i}GetBreedingLists(){const e=this.getrequestparams(this.page);this.BreedingService.GetBreedingsList(e,this.name).subscribe(e=>{this.Breedings=e.data;const i=e.data;return this.count=e.count,i})}handlePageChange(e){this.page=e,this.GetBreedingLists()}searched(e,i){this.breeding[i]=e}SignForm(){this.loginForm=this.formBuilder.group({species:["",[r.kI.required]],size:["",[r.kI.required]],breed:["",[r.kI.required]],code:["",[r.kI.required]]})}get f(){return this.loginForm.controls}AddBreeding(){this.submitted=!0,this.loginForm.invalid||this.BreedingService.NewBreeding({name:this.loginForm.value.breed,type:this.loginForm.value.species,code:this.loginForm.value.code,size:this.loginForm.value.size}).subscribe(e=>{this.submitted=!1,this.loginForm.reset(),this.primaryModal.hide(),this.GetTypeLists(),this.GetBreedingLists()})}GetBreeding(e){console.log("id",e),this.delete_id=e,this.breeding=this.Breedings[e],this.f.species.setValue(this.Breedings[e].type,{onlySelf:!0}),this.f.size.setValue(this.Breedings[e].size,{onlySelf:!0}),this.f.breed.setValue(this.Breedings[e].name,{onlySelf:!0}),this.f.code.setValue(this.Breedings[e].code,{onlySelf:!0})}EditBreeding(e){this.submitted=!0,this.loginForm.invalid||this.BreedingService.UpdateBreeding(e,{name:this.loginForm.value.breed,type:this.loginForm.value.species,code:this.loginForm.value.code,size:this.loginForm.value.size}).subscribe(e=>{this.clear(),this.deleteModal.hide(),this.GetBreedingLists()})}DeleteBreeding(){this.BreedingService.DeleteBreeding(this.delete_id).subscribe(e=>{this.removeModal.hide(),this.GetBreedingLists()})}changed(e,i){this.BreedingService.UpdateBreeding(i,{status:e}).subscribe(e=>{})}Field(e){1==this.sort?(this.sort=!1,this.field=e,this.value=-1,this.GetBreedingLists()):(this.sort=!0,this.field=e,this.value=1,this.GetBreedingLists())}}return e.\u0275fac=function(i){return new(i||e)(a.Y36(n.s),a.Y36(o.l),a.Y36(g.gz),a.Y36(g.F0),a.Y36(l.i),a.Y36(s.$),a.Y36(d.d),a.Y36(r.qu))},e.\u0275cmp=a.Xpm({type:e,selectors:[["app-breeding"]],viewQuery:function(e,i){if(1&e&&(a.Gf(p,1),a.Gf(m,1),a.Gf(f,1)),2&e){let e;a.iGM(e=a.CRH())&&(i.primaryModal=e.first),a.iGM(e=a.CRH())&&(i.deleteModal=e.first),a.iGM(e=a.CRH())&&(i.removeModal=e.first)}},decls:139,vars:53,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"Change"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","keydown.enter","input","ngModelChange"],[1,"table","table-striped"],["checked","sort",1,"fa","fa-sort",3,"click"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],["ngNativeValidate",""],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","species"],["id","species","name","species","formControlName","species",1,"form-control",3,"ngClass"],["value","","disabled","","selected","","hidden",""],[3,"value",4,"ngFor","ngForOf"],["class","invalid-feedback",4,"ngIf"],["for","size"],["id","size","name","size","formControlName","size",1,"form-control",3,"ngClass"],["for","breed"],["type","text","id","breed","placeholder","e.g. Beagle, Boxer","formControlName","breed",1,"form-control",3,"ngClass"],["for","code"],["type","text","id","code","placeholder","e.g. BEAGLE, BOXER","formControlName","code",1,"form-control",3,"ngClass"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","submit",1,"btn","btn-primary",3,"click"],["deleteModal","bs-modal"],["name","species","formControlName","species",1,"form-control",3,"ngClass"],["hidden",""],["name","size","formControlName","size",1,"form-control",3,"ngClass"],["type","text","placeholder","e.g. Beagle, Boxer","formControlName","breed",1,"form-control",3,"ngClass"],["type","text","placeholder","e.g. BEAGLE, BOXER","formControlName","code",1,"form-control",3,"ngClass"],["type","button",1,"btn","btn-primary",3,"click"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[3,"value"],[1,"invalid-feedback"],[4,"ngIf"]],template:function(e,i){if(1&e){const e=a.EpF();a.TgZ(0,"div",0),a.TgZ(1,"div",1),a.TgZ(2,"div",2),a.TgZ(3,"div",3),a._uU(4," Breed "),a.qZA(),a.TgZ(5,"div",4),a.TgZ(6,"div",0),a.TgZ(7,"div",5),a.YNc(8,h,2,0,"button",6),a.TgZ(9,"div",7),a.TgZ(10,"div",8),a.TgZ(11,"div",9),a.TgZ(12,"span",10),a.NdJ("Change",function(){return i.GetBreedingLists()}),a._UZ(13,"i",11),a.qZA(),a.qZA(),a.TgZ(14,"input",12),a.NdJ("keydown.enter",function(){return i.GetBreedingLists()})("input",function(){return i.GetBreedingLists()})("ngModelChange",function(e){return i.name=e}),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(15,"table",13),a.TgZ(16,"thead"),a.TgZ(17,"tr"),a.TgZ(18,"th"),a._uU(19,"Breed Name "),a.TgZ(20,"i",14),a.NdJ("click",function(){return i.Field("name")}),a.qZA(),a.qZA(),a.TgZ(21,"th"),a._uU(22,"Breed Code "),a.TgZ(23,"i",14),a.NdJ("click",function(){return i.Field("code")}),a.qZA(),a.qZA(),a.TgZ(24,"th"),a._uU(25,"Size "),a.TgZ(26,"i",14),a.NdJ("click",function(){return i.Field("size")}),a.qZA(),a.qZA(),a.TgZ(27,"th"),a._uU(28,"Species "),a.TgZ(29,"i",14),a.NdJ("click",function(){return i.Field("type")}),a.qZA(),a.qZA(),a.TgZ(30,"th"),a._uU(31,"Action"),a.qZA(),a.qZA(),a.qZA(),a.TgZ(32,"tbody"),a.YNc(33,T,12,6,"tr",15),a.ALo(34,"paginate"),a.qZA(),a.qZA(),a.TgZ(35,"div"),a.TgZ(36,"pagination-controls",16),a.NdJ("pageChange",function(e){return i.handlePageChange(e)}),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(37,"div",17,18),a.TgZ(39,"div",19),a.TgZ(40,"div",20),a.TgZ(41,"div",21),a.TgZ(42,"h4",22),a._uU(43,"Add Breed"),a.qZA(),a.qZA(),a.TgZ(44,"div",23),a.TgZ(45,"form",24),a.TgZ(46,"div",0),a.TgZ(47,"div",25),a.TgZ(48,"form",26),a.TgZ(49,"div",27),a.TgZ(50,"label",28),a._uU(51,"Species*"),a.qZA(),a.TgZ(52,"select",29),a.TgZ(53,"option",30),a._uU(54,"--Select--"),a.qZA(),a.YNc(55,b,2,2,"option",31),a.qZA(),a.YNc(56,y,2,1,"div",32),a.qZA(),a.TgZ(57,"div",27),a.TgZ(58,"label",33),a._uU(59,"Size*"),a.qZA(),a.TgZ(60,"select",34),a.TgZ(61,"option",30),a._uU(62,"--Select--"),a.qZA(),a.YNc(63,x,2,2,"option",31),a.qZA(),a.YNc(64,J,2,1,"div",32),a.qZA(),a.TgZ(65,"div",27),a.TgZ(66,"label",35),a._uU(67,"Breed Name*"),a.qZA(),a._UZ(68,"input",36),a.YNc(69,U,2,1,"div",32),a.qZA(),a.TgZ(70,"div",27),a.TgZ(71,"label",37),a._uU(72,"Breed Code*"),a.qZA(),a._UZ(73,"input",38),a.YNc(74,k,2,1,"div",32),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(75,"div",39),a.TgZ(76,"button",40),a.NdJ("click",function(){return a.CHM(e),a.MAs(38).hide(),i.clear()}),a._uU(77,"Cancel"),a.qZA(),a.TgZ(78,"button",41),a.NdJ("click",function(){return i.AddBreeding()}),a._uU(79,"Save"),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(80,"div",17,42),a.TgZ(82,"div",19),a.TgZ(83,"div",20),a.TgZ(84,"div",21),a.TgZ(85,"h4",22),a._uU(86,"Edit Breed"),a.qZA(),a.qZA(),a.TgZ(87,"div",23),a.TgZ(88,"div",0),a.TgZ(89,"div",25),a.TgZ(90,"form",26),a.TgZ(91,"div",27),a.TgZ(92,"label",28),a._uU(93,"Species*"),a.qZA(),a.TgZ(94,"select",43),a.TgZ(95,"option",44),a._uU(96,"--Select--"),a.qZA(),a.YNc(97,C,2,2,"option",31),a.qZA(),a.YNc(98,F,2,1,"div",32),a.qZA(),a.TgZ(99,"div",27),a.TgZ(100,"label",33),a._uU(101,"Size*"),a.qZA(),a.TgZ(102,"select",45),a.TgZ(103,"option",30),a._uU(104,"--Select--"),a.qZA(),a.YNc(105,S,2,2,"option",31),a.qZA(),a.YNc(106,G,2,1,"div",32),a.qZA(),a.TgZ(107,"div",27),a.TgZ(108,"label",35),a._uU(109,"Breed Name*"),a.qZA(),a._UZ(110,"input",46),a.YNc(111,w,2,1,"div",32),a.qZA(),a.TgZ(112,"div",27),a.TgZ(113,"label",37),a._uU(114,"Breed Code*"),a.qZA(),a._UZ(115,"input",47),a.YNc(116,L,2,1,"div",32),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(117,"div",39),a.TgZ(118,"button",40),a.NdJ("click",function(){return a.CHM(e),a.MAs(81).hide(),i.clear()}),a._uU(119,"Cancel"),a.qZA(),a.TgZ(120,"button",48),a.NdJ("click",function(){return i.EditBreeding(i.breeding._id)}),a._uU(121,"Save"),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(122,"div",49,50),a.TgZ(124,"div",51),a.TgZ(125,"div",20),a.TgZ(126,"div",21),a.TgZ(127,"h4",22),a._uU(128,"Are you sure ?"),a.qZA(),a.qZA(),a.TgZ(129,"div",23),a.TgZ(130,"div",0),a.TgZ(131,"div",25),a.TgZ(132,"p"),a._uU(133,"Do you want to delete this Breed?"),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.TgZ(134,"div",39),a.TgZ(135,"button",40),a.NdJ("click",function(){return a.CHM(e),a.MAs(123).hide(),i.clear()}),a._uU(136,"Cancel"),a.qZA(),a.TgZ(137,"button",52),a.NdJ("click",function(){return i.DeleteBreeding()}),a._uU(138,"Delete"),a.qZA(),a.qZA(),a.qZA(),a.qZA(),a.qZA()}2&e&&(a.xp6(8),a.Q6J("ngIf",i.Add),a.xp6(6),a.Q6J("ngModel",i.name),a.xp6(19),a.Q6J("ngForOf",a.xi3(34,28,i.Breedings,a.WLB(31,E,i.page,i.count))),a.xp6(4),a.Q6J("config",a.DdM(34,z)),a.xp6(11),a.Q6J("formGroup",i.loginForm),a.xp6(4),a.Q6J("ngClass",a.VKq(35,D,i.submitted&&i.f.species.errors)),a.xp6(3),a.Q6J("ngForOf",i.Types),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.species.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(37,D,i.submitted&&i.f.size.errors)),a.xp6(3),a.Q6J("ngForOf",i.Size),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.size.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(39,D,i.submitted&&i.f.breed.errors)),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.breed.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(41,D,i.submitted&&i.f.code.errors)),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.code.errors),a.xp6(6),a.Q6J("config",a.DdM(43,z)),a.xp6(10),a.Q6J("formGroup",i.loginForm),a.xp6(4),a.Q6J("ngClass",a.VKq(44,D,i.submitted&&i.f.species.errors)),a.xp6(3),a.Q6J("ngForOf",i.Types),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.species.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(46,D,i.submitted&&i.f.size.errors)),a.xp6(3),a.Q6J("ngForOf",i.Size),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.size.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(48,D,i.submitted&&i.f.breed.errors)),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.breed.errors),a.xp6(4),a.Q6J("ngClass",a.VKq(50,D,i.submitted&&i.f.code.errors)),a.xp6(1),a.Q6J("ngIf",i.submitted&&i.f.code.errors),a.xp6(6),a.Q6J("config",a.DdM(52,z)))},directives:[c.O5,r.Fj,r.JJ,r.On,c.sg,u.LS,Z.oB,r.JL,r.F,r.vK,r.sg,r.EJ,r.u,c.mk,r.YN,r.ks],pipes:[u._s],styles:["#select1[_ngcontent-%COMP%]{width:100%}"]}),e})()}}]);