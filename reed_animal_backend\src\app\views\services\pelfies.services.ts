import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { TokenStorageService } from '../services/token-storage.service';
import { Configuration } from '../../../configuration';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class PelfieService extends Api {

    //Get All Pelfies
    GetPelfiesList(data: any, params: any,): Observable<any> {
         return this.http.get(`${this.config.APIUrl}/pelfie?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Update or Edit Pelfie
    UpdatePelfie(id, data): Observable<any> {
         return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${id}?token=${localStorage.auth_token}`, data);
    }
}