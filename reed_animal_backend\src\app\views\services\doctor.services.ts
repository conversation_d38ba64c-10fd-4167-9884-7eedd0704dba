import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class DoctorService extends Api {

    //Add New Doctor 
    NewDoctor(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Doctor?token=${localStorage.auth_token}`, data);
    }

    //Get All Doctor Type 
    GetDoctorsList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Doctor?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular Doctor by using Doctor id 
    GetDoctorDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit Doctor details
    UpdateDoctor(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Doctor by using id
    DeleteDoctor(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`);
    }

}