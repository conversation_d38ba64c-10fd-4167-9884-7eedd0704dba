<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Resources
            </div>
            <div class="card-body">
                <!-- <div class="row">
            <div class="col-lg-12 my-3">
          
              <div class="form-group table-search">
                <div class="input-group" style="top: 3px;">
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i class="fa fa-search"></i></span>
                  </div>
                  <input type="text" id="Search" name="Search" placeholder="Search" class="form-control"
                    autocomplete="off" class="form-control" (input)="GetCustomerLists()" [(ngModel)]="name">
                </div>
              </div>
  
            </div>
          </div> -->

                <div class="row">
                    <div class="col-md-12" style="margin:auto; margin-bottom:50px;">

                        <!-- Nav tabs -->
                        <tabset>
                            <!-- Health Tips -->
                            <tab>
                                <ng-template tabHeading>Health Tips</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModaltips.show()" *ngIf="Add">
                  Add Health Tips
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Poster Image</th>
                                            <th>Title</th>
                                            <th style="text-align: center;">Description</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of TipsList| paginate: { id: 'listing_pagination',
                    itemsPerPage: 10,
                    currentPage: page,
                    totalItems: count };let i=index" width="100%">
                                            <td width="15%">
                                                <div class="img-responsive"><img [src]="user.poster_image"></div>
                                            </td>
                                            <td width="15%">{{user.title}}</td>
                                            <td style="text-align: center;">
                                                <p class="mb-0">{{user.description}}</p>
                                            </td>
                                            <td width="15%"><a data-toggle="modal" (click)="EditId=user._id;GetTipsBy(i);primaryModaltips.show();" *ngIf="Edit" style="cursor: pointer; margin-right: 10px;"><span class="badge badge-success"><i class="fa fa-edit"></i>
                            Edit</span></a>
                                                <a data-toggle="modal" style="cursor: pointer;" *ngIf="Delete" (click)="EditId=user._id;removeTips.show();"><span class="badge badge-danger"><i
                              class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="page=$event;GetTips();">
                                    </pagination-controls>
                                </div>

                            </tab>

                            <!-- Videos -->
                            <tab>
                                <ng-template tabHeading>Videos</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalvideo.show()" *ngIf="Add">
                  Add Videos
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Poster Image</th>
                                            <th>Title</th>
                                            <th>Video</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of Videos| paginate: { id: 'listing_video',
                    itemsPerPage: 10,
                    currentPage: pageV,
                    totalItems: countV };let i=index">
                                            <td>
                                                <div class="img-responsive"><img [src]='user.poster_image'></div>
                                            </td>
                                            <td>
                                                <h6>{{user.title}}</h6>
                                            </td>
                                            <td><video controls width="250">

                          <source [src]='user.video' type="video/webm">

                          <source [src]='user.video' type="video/mp4">

                          Sorry, your browser doesn't support embedded videos.
                        </video></td>
                                            <td><a data-toggle="modal" (click)="EditId=user._id;GetVideoBy(i);primaryModalvideo.show();" style="cursor: pointer;"><span class="badge badge-success"><i class="fa fa-edit"
                              *ngIf="Edit"></i> Edit</span></a>
                                                <a data-toggle="modal" (click)="EditId=user._id;removevideo.show();" *ngIf="Delete" style="cursor: pointer;"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_video" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="pageV=$event;Getvideos();">
                                    </pagination-controls>
                                </div>

                            </tab>

                            <!-- Audio -->
                            <tab>
                                <ng-template tabHeading>Audios</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalaudio.show()" *ngIf="Add">
                  Add Audio
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Poster Image</th>
                                            <th>Title</th>
                                            <th>Audio</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of Audios| paginate: { id: 'listing_audio',
                    itemsPerPage: 10,
                    currentPage: pageA,
                    totalItems: countA };let i=index">
                                            <td>
                                                <div class="img-responsive"><img [src]="user.poster_image"></div>
                                            </td>
                                            <td>
                                                <h6>{{user.title}}</h6>
                                            </td>
                                            <td><audio controls>
                          <source [src]="user.audio" type="audio/mp3">
                          <source [src]="user.audio" type="audio/mpeg">
                          Your browser does not support the audio element.
                        </audio></td>
                                            <td><a data-toggle="modal" *ngIf="Edit" (click)="EditId=user._id;GetaudioBy(i);primaryModalaudio.show();" style="cursor: pointer;"><span class="badge badge-success"><i class="fa fa-edit"></i>
                            Edit</span></a>
                                                <a data-toggle="modal" *ngIf="Delete" style="cursor: pointer;" (click)="EditId=user._id;removeaudio.show();"><span class="badge badge-danger"><i
                              class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_audio" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="pageA=$event;Getaudios();">
                                    </pagination-controls>
                                </div>
                            </tab>

                            <!-- FAQ -->
                            <tab>
                                <ng-template tabHeading>FAQ</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalfaq.show();" *ngIf="Add">
                  Add FAQ
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>

                                            <th>Question</th>
                                            <th style="text-align: center;">Answer</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of FAQs| paginate: { id: 'listing_faq',
                    itemsPerPage: 10,
                    currentPage: pageFAQ,
                    totalItems: countFAQ };let i=index" width="100%">

                                            <td width="20%">{{user.question}} </td>
                                            <td>{{user.answer}} </td>
                                            <td width="15%"><a data-toggle="modal" *ngIf="Edit" (click)="EditId=user._id;GetFAQBy(i);primaryModalfaq.show();" style="cursor: pointer;"><span
                            class="badge badge-success"><i class="fa fa-edit"></i>
                            Edit</span></a>
                                                <a data-toggle="modal" *ngIf="Delete" (click)="EditId=user._id;removefaq.show();" style="cursor: pointer;"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_faq" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="pageFAQ=$event;GetFAQs();">
                                    </pagination-controls>
                                </div>
                            </tab>
                        </tabset>
                    </div>
                    <!--/.col-->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add / Edit Modal Health tips -->
<div bsModal #primaryModaltips="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h4 class="modal-title">{{EditId==''?'Add':'Edit'}} Health Tips</h4>
            </div>
            <form class="form" [formGroup]="loginForm" autocomplete="off" (ngSubmit)="Addtips();">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">

                            <div class="form-group">
                                <label for="title">Title <span style="color: red;">*</span></label>
                                <input type="text" id="health-title" class="form-control" formControlName="title" placeholder="e.g. vaccine, hiccups" [ngClass]="{ 'is-invalid': submitted && f.title.errors }">
                                <div *ngIf="submitted && f.title.errors" class="invalid-feedback">
                                    <div *ngIf="f.title.errors.required">*Title is mandatory</div>
                                    <div *ngIf="f.title.errors.pattern">Alphabet characters only</div>
                                    <div *ngIf="f.title.errors.minlength">Name isn't long enough, minimum of 5 characters</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="description">Description <span style="color: red;">*</span></label>
                                <textarea class="form-control" placeholder="Description" formControlName="description" [ngClass]="{ 'is-invalid': submitted && f.description.errors }"></textarea>
                                <div *ngIf="submitted && f.description.errors" class="invalid-feedback">
                                    <div *ngIf="f.description.errors.required">*Description is mandatory</div>
                                    <div *ngIf="f.description.errors.minlength">Name isn't long enough, minimum of 20 characters</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="poster_image" class="form-label">Poster Image <span style="color: red;">*</span></label>
                                <div class="img-responsive">
                                    <input class="form-control  image" type="file" #file id="formFile" formControlName="poster_image" style="padding: 3px;" [ngClass]="{ 'is-invalid': submitted && f.poster_image.errors }" (change)="onUpload($event,'loginForm')" accept="image/*">
                                    <img *ngIf='!ImageUrl' src="../../../../assets/img/dummy_img.png">
                                    <img *ngIf='ImageUrl' [src]=ImageUrl class="brand-img" alt="">
                                    <div *ngIf="submitted && f.poster_image.errors" class="invalid-feedback">
                                        <div *ngIf="f.poster_image.errors.required" style="position: absolute;">*Image is mandatory</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="primaryModaltips.hide();clear();">Cancel</button>
                    <button class="btn btn-primary" type="submit">Save</button>
                </div>
            </form>
            <!-- 
      <div class="d-flex justify-content-center spinner-top" *ngIf="loading">
        <div class="spinner-border" role="status">
          <span class="sr-only" id="loading"></span>
        </div>
      </div> -->
        </div>
    </div>
</div>

<!-- Delete Health Tips -->
<div bsModal #removeTips="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Health Tips?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeTips.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteTips(EditId);">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Add Modal Video -->
<div bsModal #primaryModalvideo="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{EditId==''?'Add':'Edit'}} Video</h4>
            </div>
            <form class="form" [formGroup]="videoForm" autocomplete="off" (ngSubmit)="AddVideo();">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">


                            <div class="form-group">
                                <label for="title">Title <span style="color: red;">*</span></label>
                                <input type="text" id="title" class="form-control" formControlName="title" placeholder="e.g. vaccine, hiccups" [ngClass]="{ 'is-invalid': videosubmitted && v.title.errors }">
                                <div *ngIf="videosubmitted && v.title.errors" class="invalid-feedback">
                                    <div *ngIf="v.title.errors.required">Title is mandatory</div>
                                    <div *ngIf="v.title.errors.pattern">Alphabet characters only</div>
                                    <div *ngIf="v.title.errors.minlength">Name isn't long enough, minimum of 5 characters</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="poster_image" class="form-label">Poster Image <span style="color: red;">*</span></label>
                                <div class="img-responsive">
                                    <input class="form-control  image" type="file" #file id="formFile1" formControlName="poster_image" style="padding: 3px;" [ngClass]="{ 'is-invalid': videosubmitted && v.poster_image.errors }" (change)="onUpload($event,'videoForm')" accept="image/*">
                                    <img *ngIf='!ImageUrl' src="../../../../assets/img/dummy_img.png">
                                    <img *ngIf='ImageUrl' [src]=ImageUrl class="brand-img" alt="">
                                    <div *ngIf="videosubmitted && v.poster_image.errors" class="invalid-feedback">
                                        <div *ngIf="v.poster_image.errors.required" style="position: absolute;">*Image is mandatory</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="video" class="form-label">Video <span style="color: red;">*</span></label>
                                <div class="img-responsive">
                                    <input class="form-control image" type="file" #file id="formFile2" formControlName="video" style="padding: 3px;" [ngClass]="{ 'is-invalid': videosubmitted && v.video.errors }" accept="video/*" (change)="VideoUpload($event)">
                                    <img *ngIf="VideoUrl==''" src="../../../../assets/img/upload.png">
                                    <video *ngIf="VideoUrl!=''" controls width="220">

                    <source [src]='VideoUrl' type="video/webm">

                    <source [src]='VideoUrl' type="video/mp4">

                    Sorry, your browser doesn't support embedded videos.
                  </video>
                                    <div *ngIf="videosubmitted && v.video.errors" class="invalid-feedback">
                                        <div *ngIf="v.video.errors.required">Video is mandatory</div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="primaryModalvideo.hide();clear();">Cancel</button>
                    <button class="btn btn-primary" type="submit">Save</button>
                </div>
            </form>

        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Videos -->
<div bsModal #removevideo="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Video?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removevideo.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteVideo(EditId);">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Add Modal Audio -->
<div bsModal #primaryModalaudio="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{EditId==''?'Add':'Edit'}} Audio</h4>
            </div>
            <form class="form" [formGroup]="audioForm" autocomplete="off" (ngSubmit)="Addaudios();">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">


                            <div class="form-group">
                                <label for="title">Title <span style="color: red;">*</span></label>
                                <input type="text" id="titleA" class="form-control" formControlName="title" placeholder="e.g. vaccine, hiccups" [ngClass]="{ 'is-invalid': audiosubmitted && A.title.errors }">
                                <div *ngIf="audiosubmitted && A.title.errors" class="invalid-feedback">
                                    <div *ngIf="A.title.errors.required">*Title is mandatory</div>
                                    <div *ngIf="A.title.errors.minlength">Name isn't long enough, minimum of 5 characters</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="poster_image" class="form-label">Poster Image <span style="color: red;">*</span></label>
                                <div class="img-responsive">
                                    <input class="form-control  image" type="file" #file id="formFile3" formControlName="poster_image" style="padding: 3px;" [ngClass]="{ 'is-invalid': audiosubmitted && A.poster_image.errors }" (change)="onUpload($event,'audioForm')" accept="image/*">
                                    <img *ngIf="ImageUrl==''" src="../../../../assets/img/dummy_img.png">
                                    <img *ngIf="ImageUrl!=''" [src]=ImageUrl class="brand-img" alt="">
                                    <div *ngIf="audiosubmitted && A.poster_image.errors" class="invalid-feedback">
                                        <div *ngIf="A.poster_image.errors.required" style="position: absolute;">Image is mandatory</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="audio_url" class="form-label">Audio <span style="color: red;">*</span></label>
                                <div class="img-responsive">
                                    <input class="form-control image" type="file" #file id="fAudio" formControlName="audio" style="padding: 3px;" [ngClass]="{ 'is-invalid': audiosubmitted && A.audio.errors }" accept="audio/*" (change)="AudioUpload($event);">
                                    <img *ngIf="AudioUrl==''" src="../../../../assets/img/music-file.png">
                                    <audio controls *ngIf="AudioUrl!=''">
                    <source [src]="AudioUrl" type="audio/mp3">
                    <source [src]="AudioUrl" type="audio/mpeg">
                    Your browser does not support the audio element.
                  </audio>
                                    <div *ngIf="audiosubmitted && A.audio.errors" class="invalid-feedback">
                                        <div *ngIf="A.audio.errors.required">Audio is mandatory</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="primaryModalaudio.hide();clear();">Cancel</button>
                    <button class="btn btn-primary" type="submit">Save</button>
                </div>
            </form>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Delete Audio -->
<div bsModal #removeaudio="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Audio?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeaudio.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteAudio(EditId);">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Add Modal FAQ -->
<div bsModal #primaryModalfaq="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{EditId==''?'Add':'Edit'}} FAQ</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form class="form" [formGroup]="FAQForm" autocomplete="off">

                            <div class="form-group">
                                <label for="Question">Question <span style="color: red;">*</span></label>
                                <input type="text" id="Question" class="form-control" formControlName="question" placeholder="Question?" [ngClass]="{ 'is-invalid': FAQsubmitted && F.question.errors }">
                                <div *ngIf="FAQsubmitted && F.question.errors" class="invalid-feedback">
                                    <div *ngIf="F.question.errors.required">Question is mandatory</div>
                                    <div *ngIf="F.question.errors.minlength">Name isn't long enough, minimum of 5 characters</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="answer" class="form-label">Answer <span style="color: red;">*</span></label>
                                <textarea class="form-control" type="text" placeholder="Answer" id="answer" formControlName="answer" style="padding: 3px;" [ngClass]="{ 'is-invalid': FAQsubmitted && F.answer.errors }"></textarea>
                                <div *ngIf="FAQsubmitted && F.answer.errors" class="invalid-feedback">
                                    <div *ngIf="F.answer.errors.required">Answer is mandatory</div>
                                    <div *ngIf="F.answer.errors.minlength">Name isn't long enough, minimum of 5 characters</div>
                                </div>
                            </div>

                        </form>

                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModalfaq.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="submit" (click)="AddFAQs()">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete FAQ -->
<div bsModal #removefaq="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this FAQ?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removefaq.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteFAQ(EditId);">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->