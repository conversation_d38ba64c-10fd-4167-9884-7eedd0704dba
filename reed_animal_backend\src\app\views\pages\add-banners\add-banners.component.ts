import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductService } from '../../services/product.services'
import { Subscription } from 'rxjs';
import * as moment from 'moment';
import { FormsModule } from '@angular/forms';




@Component({
  selector: 'app-add-banners',
  templateUrl: './add-banners.component.html',
  styleUrls: ['./add-banners.component.scss']
})
export class AddBannersComponent implements OnInit {

  @ViewChild('removeModal') public removeModal: ModalDirective;

  Add = true;
  Edit = true;
  Delete = true;
  Products = [];
  page = 1;
  count = 0;
  id = ''
  name = ''
  add : boolean = true;
  edit :boolean =false
  poster_image: any;
  Bannerform : FormGroup;
  submitted : boolean = false
  url: any;
  getallproducts:any;
  poster_images:any;
  splice_list=[];
  image: any;
  Edit_id:any;
  product_data_Id:any;
  Edit_binding_data = []
  Add_button : any
  Edit_button:any
  searchdata=''
  validation : boolean = false;

  
  constructor(private productService: ProductService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { 
    
  }
  
  ngOnInit(): void {
    this.tokens();
  this. getallproduct()
 this.ListProduct();
 this.Product();
 


  this.route.queryParams
  .subscribe((params: any) => {
    this.Edit_id = params['search'];
    console.log("%%%%%",this.Edit_button)
    this.Edit_button = params['Edit_button']
    this.Add_button = params['Add_button']
   

 
   

  } );

}

 

  //page handle request
  getrequestparams(page: number): any {
    console.log("page")
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    return skip;
    
  }

    //List all Product
    Product() {
      const skip = this.getrequestparams(this.page);
      console.log("name",this.name)
      this.productService.GetProduct(this.name, skip)
        .subscribe((res: any) => {
          console.log("valueeee",res.data)
          this.Products = res.data;
          this.count = res.count;
        })
    }
  
  //List all Product
  ListProduct() {
    const skip = this.getrequestparams(this.page);
    this.productService.GetBanners(skip)
      .subscribe((res: any) => {
        this.Products = res.data;
        console.log("new product ",this.Products)
        this.count = res.count;
        for( let data of this.Products){
          if(data._id == this.Edit_id){
            console.log("edit data", data)
            this.url = data.image_url
           this.product_data_Id= data.products
           console.log("dataaaaaaa",this.product_data_Id)
          //  for(let product of this.getallproducts){
          //   if (product._id == this.product_data_Id){

          //   }
          //  }
          for(let product of this.getallproducts){
            for( let banner of this.product_data_Id){
              if(banner == product._id){
                console.log("final data ",product)
                console.log("final banner ",banner)
               this.Edit_binding_data.push(product)
              

              }
            }
            this.splice_list =this.Edit_binding_data
          }
         
      
          
            console.log("product list", this.splice_list) 
            


          }
        }
      })
  }




  SignForm() {
    this.Bannerform = this.formBuilder.group({
      banner: ['', [Validators.required]],
    
    });
  }

  get f() {
    return this.Bannerform.controls;
  }

    Addbanner(): void {
    this.submitted = true;
    // console.log(this.loginForm.value)
    if (this.Bannerform.invalid) {
      return
    } 
    else {
      const data = {
        banner_image: this.Bannerform.value.title,
       
      }
      console.log("banner image ", data)
      
  }
    }

    











  //token verified type
  tokens(): void {
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Shopping") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        } else {
          this.ListProduct();
        }
      })
  }
  
  newbanner(){
    this.router.navigate(['../../pages/add-banners'])}
  
  
  
 

  addproduct(data:any,i :any){
    this.splice_list.push(data)

    console.log("data",data,"index",i)
    this.getallproducts.splice(i,1)

    console.log("splice_data", this.splice_list)

  }

  revocket(data:any,i:any){
    this.getallproducts.push(data)

    console.log("revocket test",data,"index id",i)
    this.splice_list.splice(i,1)
  }




  onsubmit(value :any){
    console.log("@@@@",value)

    console.log("spllice list",this.splice_list)
    var products_id =[]
   products_id = this.splice_list.map((item)=>item._id)
   var image_url_value = 'https://reedapp.net:3000/uploads/'+this.image
   var data = {

    image_url : this.poster_images || this.url ,
    products : products_id

   }
   if(value == 'edit' ){
    console.log("edit working")
    this.productService.EditBanners(data,this.Edit_id).subscribe((res: any) => {
      console.log("final data", this.productService)
      this.router.navigate(['/pages/banners'])
    })
  }


  if(value == 'add'){
    console.log("add working")
    console.log("validation", data.image_url)
    if(data.image_url== undefined ){
      this.validation = true
      console.log("validation working")
      }
      else{
          this.productService.AddBanners(data) .subscribe((res: any) => {
        console.log("final datassss=",res)
       })
       this.router.navigate(['/pages/banners'])

      }

    

    }
    
    
    
    

   } 



   




   
   searchChangeEvent(event:any){
  this.page = 1;
    this.name = event.target.value;
    this.getallproduct()
  }
  

  getallproduct(){

    const skip = this.getrequestparams(this.page);
    this.productService.getallproduct(this.name,skip).subscribe((res:any) =>{
      console.log("get all proudctssss ", res)
      this.getallproducts = res.data
      console.log("all product@@@@@@@@@ ", this.getallproducts)
    })


  }
  
  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    this.ListProduct();
  }
  
  //Get Product
  GetProductById(index, param) {
    if (param == "Delete") {
      this.id = this.Products[index]._id
      this.removeModal.show();
    }
  }
  
  //Delete single product by using id
  DeleteProduct(id) {
    this.productService.DeleteProduct(id)
      .subscribe((res: any) => {
        this.removeModal.hide();
        this.ListProduct();
      })
  }
  
  //Status changed by using id
  changed(status, id) {
    // console.log(status)
    const data = {
      status: status
    }
    this.productService.UpdateProduct(id, data)
      .subscribe((res: any) => {
        // this.ListProduct();
      })
  }
  
  //Edit product and navigation to add product
  EditProduct(Id) {
    this.router.navigate(['/pages/add-banners'], { queryParams: { 'search': Id } });
  }


  onSelectFile(event) {
    
    console.log("eventttttttttt",event)
    this.poster_image = event.target.value
    this.image = event.target.files[0].name;

    console.log("poster image", this.poster_image)
    console.log("poster image2222222222222222", event.target.files[0].name)
  //  this. edit = true
  if (event.target.files && event.target.files[0]) {
    var reader = new FileReader();

    reader.readAsDataURL(event.target.files[0]); // read file as data url

    reader.onload = (event: any) => { // called once readAsDataURL is completed
      console.log("image valuesssss",event);
      this.url = event.target.result;
      // this.imageupload()
    
    }
  }

  this.productService.uploadFile(event.target.files[0])
  .subscribe((res: any) => {
    this.poster_images = res.data;
    console.log("product image",this.poster_images)
  })

  }

 
 



  assignFile(event){

    if (this.poster_image != null)
    console.log("Target Vf.target", event.target)

  this.Bannerform.get('banner').setValue(this.poster_image)

  }
  

}
