<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        Role
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-12 my-3">
            <button *ngIf="Add" type="button" class="btn btn-primary mr-1" data-toggle="modal"
              (click)="AddModal.show()">
              Add Role
            </button>
            <div class="form-group table-search">
              <div class="input-group" style="top: 3px;">
                <div class="input-group-prepend">
                  <span class="input-group-text"><i class="fa fa-search" (click)="GetRoleLists()"></i></span>
                </div>
                <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off"
                  class="form-control" (input)="GetRoleLists()" [(ngModel)]="name">
              </div>
            </div>

          </div>
        </div>

        <table class="table table-striped">
          <thead>
            <tr>
              <th>Role Name</th>
              <!-- <th *ngIf="Edit">Status</th> -->
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of roles| paginate: { id: 'listing_pagination',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };">
              <td>{{user.name}}</td>
              <!-- <td *ngIf="Edit"><label class="switch">
                  <input type="checkbox" checked="user.acc_activation" (change)="changing(user.acc_activation,user._id)"
                    [(ngModel)]="user.acc_activation">
                  <span class="slider round"></span>
                </label></td> -->
              <td>
                <a data-toggle="modal" *ngIf="Edit" (click)="primaryModal.show();GetRole(user._id);onChange(user._id)"
                  style="cursor: pointer;"><span class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                <a data-toggle="modal" *ngIf="Delete" (click)="deleteModal.show();GetRolee(user._id);"
                  style="cursor: pointer;"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                    Delete</span></a>
              </td>
            </tr>
          </tbody>
        </table>
        <div>
          <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true"
            (pageChange)="handlePageChange($event)">
          </pagination-controls>
        </div>
      </div>

    </div>
  </div>
  <!--/.col-->
</div>

<!-- Add Modal -->
<div bsModal #AddModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Add Role</h4>
        <!-- <button type="button" class="close" (click)="primaryModal.hide()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="name">Role Name*</label>
              <input type="text" id="role-name" placeholder="e.g. Manager, Doctor" class="form-control"
                autocomplete="off" required [(ngModel)]="role.name" [ngModelOptions]="{standalone: true}"
                (keydown.enter)="AddRole()">
              <div *ngIf="rolefailed" style="font-size: smaller;color: red;">*Role is mandatory</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="AddModal.hide();clear();">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="AddRole();">Save</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Edit Modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Edit Role</h4>
        <!-- <button type="button" class="close" (click)="primaryModal.hide()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
      </div>

      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="name">Role Name*</label>
              <input type="text" id="edit-name" placeholder="e.g. Manager, Doctor" class="form-control"
                autocomplete="off" required [(ngModel)]="role.name" [ngModelOptions]="{standalone: true}"
                (keydown.enter)="EditRole(role._id);primaryModal.hide();" readonly>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Module Name</th>
              <th>Add</th>
              <th>Edit</th>
              <!-- <th>View</th> -->
              <th>Delete</th>
              <th>View</th>

            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of permissions | paginate: { id: 'listing_paginations',
               itemsPerPage: 6,
               currentPage: page1,
               totalItems: count1 };let i = index">
              <td>{{user.module_name}}</td>

              <td><label class="switch">
                  <input type="checkbox" (change)="changed('add',user.add,user._id,i)" [(ngModel)]="user.add">
                  <span class="slider round"></span>
                </label></td>

              <td><label class="switch">
                  <input type="checkbox" (change)="changed('edit',user.edit,user._id,i)" [(ngModel)]="user.edit">
                  <span class="slider round"></span>
                </label></td>

              <!-- <td><label class="switch view-slider">
                  <input type="checkbox" checked="user.view" (change)="changed('view',user.view,user._id)"
                    [(ngModel)]="user.view">
                  <span class="slider round"></span>
                </label></td> -->
              <td><label class="switch">
                  <input type="checkbox" (change)="changed('delete',user.delete,user._id,i)" [(ngModel)]="user.delete">
                  <span class="slider round"></span>
                </label></td>

              <td><label class="switch">
                  <input type="checkbox" (change)="changed('all',user.status,user._id,i)" [(ngModel)]="user.status">
                  <span class="slider round"></span>
                </label>
              </td>

            </tr>
          </tbody>
        </table>
        <div>
          <pagination-controls id="listing_paginations" style="text-align:right;" maxSize="5" directionLinks="true"
            (pageChange)="handlePageChange1($event)">
          </pagination-controls>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="EditRole(role._id);primaryModal.hide();">Save</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Delete Modal -->
<div bsModal #deleteModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-danger modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Are you sure ?</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <p>Do you want to delete this Role?</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="deleteModal.hide()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="Deleterole();deleteModal.hide();">Delete</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->