{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Sindhi [sd]\n//! author : <PERSON><PERSON> : https://github.com/narainsagar\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var months = ['جنوري', 'فيبروري', 'مارچ', 'اپريل', 'مئي', 'جون', 'جولاءِ', 'آگسٽ', 'سيپٽمبر', 'آڪٽوبر', 'نومبر', 'ڊسمبر'],\n      days = ['آچر', 'سومر', 'اڱارو', 'اربع', 'خميس', 'جمع', 'ڇنڇر'];\n  var sd = moment.defineLocale('sd', {\n    months: months,\n    monthsShort: months,\n    weekdays: days,\n    weekdaysShort: days,\n    weekdaysMin: days,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd، D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /صبح|شام/,\n    isPM: function (input) {\n      return 'شام' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'صبح';\n      }\n\n      return 'شام';\n    },\n    calendar: {\n      sameDay: '[اڄ] LT',\n      nextDay: '[سڀاڻي] LT',\n      nextWeek: 'dddd [اڳين هفتي تي] LT',\n      lastDay: '[ڪالهه] LT',\n      lastWeek: '[گزريل هفتي] dddd [تي] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s پوء',\n      past: '%s اڳ',\n      s: 'چند سيڪنڊ',\n      ss: '%d سيڪنڊ',\n      m: 'هڪ منٽ',\n      mm: '%d منٽ',\n      h: 'هڪ ڪلاڪ',\n      hh: '%d ڪلاڪ',\n      d: 'هڪ ڏينهن',\n      dd: '%d ڏينهن',\n      M: 'هڪ مهينو',\n      MM: '%d مهينا',\n      y: 'هڪ سال',\n      yy: '%d سال'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return sd;\n});", "map": null, "metadata": {}, "sourceType": "script"}