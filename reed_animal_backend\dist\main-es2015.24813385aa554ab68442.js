(self.webpackChunkDr_Reed_Admin_Panel=self.webpackChunkDr_Reed_Admin_Panel||[]).push([[179],{98255:function(t){function e(t){return Promise.resolve().then(function(){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e})}e.keys=function(){return[]},e.resolve=e,e.id=98255,t.exports=e},57481:function(t,e,r){"use strict";r.d(e,{P:function(){return a}});var o=r(26415),n=r(6642),s=r(90658),i=r(99777);let a=(()=>{class t{constructor(t,e,r){this.router=t,this.EmployeeService=e,this.Permission=r}canActivate(t){const e=JSON.parse(localStorage.getItem("Verify"));return this.verifyCustomer(),!!e.includes(t.data.path)||(this.router.navigate(["/login"]),!1)}verifyCustomer(){const t=JSON.parse(localStorage.getItem("auth-user"));this.EmployeeService.GetEmployeeDetail(t._id).subscribe(t=>{!1===t.data.status&&(localStorage.clear(),this.router.navigate(["/login"]))})}}return t.\u0275fac=function(e){return new(e||t)(s.LFG(i.F0),s.LFG(o.d),s.LFG(n.$))},t.\u0275prov=s.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},11160:function(t,e,r){"use strict";r.d(e,{V:function(){return a}});var o=r(24242),n=r(267);var s=r(49731),i=r(90658);new o.WM({"Content-Type":"application/json"});let a=(()=>{class t{constructor(t,e){this.http=t,this.config=e}getBaseUrl(){return"https://reedapp.net:3000"}handleError(t="operation",e){return t=>(0,n.of)(e)}}return t.\u0275fac=function(e){return new(e||t)(i.LFG(o.eN),i.LFG(s.V))},t.\u0275prov=i.Yz7({token:t,factory:t.\u0275fac}),t})()},50022:function(t,e,r){"use strict";r.d(e,{l:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{Newtype(t){return this.http.post(`${this.config.APIUrl}/animal?token=${localStorage.auth_token}`,t)}GetTypesList(t,e){return this.http.get(`${this.config.APIUrl}/animal?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetTypeDetail(t){return this.http.get(`${this.config.APIUrl}/animal/${t}?token=${localStorage.auth_token}`)}UpdateType(t,e){return this.http.put(`${this.config.APIUrl}/animal/${t}?token=${localStorage.auth_token}`,e)}Deletetype(t){return this.http.delete(`${this.config.APIUrl}/animal/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},79306:function(t,e,r){"use strict";r.d(e,{H:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetDoctorlist(t){return this.http.get(`${this.config.APIUrl}/Employee`,{params:t})}GetDoctorDetails(t,e,r,o,n){return this.http.get(`${this.config.APIUrl}/bookedAppointment/${t}?search=${e}&start=${r}&end=${o}&token=${localStorage.auth_token}`,{params:n})}DeleteBooked(t){return this.http.delete(`${this.config.APIUrl}/DeleteBookedAppointment/${t}?token=${localStorage.auth_token}`)}GetDoctorsList(t){return this.http.get(`${this.config.APIUrl}/Employee`,{params:t})}GetDoctor(t){return this.http.get(`${this.config.APIUrl}/app_schedule/${t}?token=${localStorage.auth_token}`)}UpdateDoctor(t,e){return this.http.put(`${this.config.APIUrl}/app_schedule/${t}?token=${localStorage.auth_token}`,e)}GetAllappointment(t){return this.http.get(`${this.config.APIUrl}/allappointment?token=${localStorage.auth_token}`,{params:t})}GetPastVisit(t,e){return this.http.get(`${this.config.APIUrl3}/v1/PastVisit/${t}?token=${localStorage.auth_token}`,{params:e})}appointmentDetail(t){return this.http.get(`${this.config.APIUrl}/appointmentDetail/${t}?token=${localStorage.auth_token}`)}getappointment(t,e){return this.http.get(`${this.config.APIUrl4}/getAppointment/${t}?token=${localStorage.auth_token}`,{params:e})}update_appointment(t,e){return this.http.put(`${this.config.APIUrl4}/appointment/${t}?token=${localStorage.auth_token}`,e)}update_appointment_Reson(t,e,r){return this.http.put(`${this.config.APIUrl4}/appointment/${t}?token=${localStorage.auth_token}`,e,r)}getReson(t){return this.http.get(`${this.config.APIUrl}/treatment/?token=${localStorage.auth_token}`,{params:t})}getbreed(t){return this.http.get(`${this.config.APIUrl}/breeding/?token=${localStorage.auth_token}`,{params:t})}getDoctor(t){return this.http.get(`${this.config.APIUrl}/Doctor?search=${t}`)}uploadFile(t){const e=new FormData;return e.append("file",t),this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`,e,{reportProgress:!0,responseType:"json"})}backendappointment(t){return this.http.post(`${this.config.APIUrl4}/addCustomAppointment`,t)}GetUserSearch(t){return this.http.post(`${this.config.APIUrl4}/user/getUsersByEmail?token=${localStorage.auth_token}`,t)}emailSearchData(t){return this.http.post(`${this.config.APIUrl4}/user/findUserByEmailId`,t)}Cancleappointment(t,e){return this.http.put(`${this.config.APIUrl4}/cancelAppointment/${t}`,e)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},75874:function(t,e,r){"use strict";r.d(e,{s:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{NewBreeding(t){return this.http.post(`${this.config.APIUrl}/Breeding?token=${localStorage.auth_token}`,t)}GetBreedingsList(t,e){return this.http.get(`${this.config.APIUrl}/Breeding?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetBreedingDetail(t){return this.http.get(`${this.config.APIUrl}/Breeding/${t}?token=${localStorage.auth_token}`)}UpdateBreeding(t,e){return this.http.put(`${this.config.APIUrl}/Breeding/${t}?token=${localStorage.auth_token}`,e)}DeleteBreeding(t){return this.http.delete(`${this.config.APIUrl}/Breeding/${t}?token=${localStorage.auth_token}`)}GetTypesList(){return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},72945:function(t,e,r){"use strict";r.d(e,{x:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetCovertusList(t){return this.http.get(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`,{params:t})}UpdateCovertus(t){return this.http.post(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`,t)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},59815:function(t,e,r){"use strict";r.d(e,{v:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetCustomerList(t,e){return this.http.get(`${this.config.APIUrl}/user?search=${e}&token=${localStorage.auth_token}`,{params:t})}UpdateUser(t,e){return this.http.put(`${this.config.APIUrl}/user/${t}?token=${localStorage.auth_token}`,e)}FindById(t){return this.http.get(`${this.config.APIUrl}/pet/${t}?token=${localStorage.auth_token}`)}GetPetDetails(t){return this.http.get(`${this.config.APIUrl}/petDetails/${t}?token=${localStorage.auth_token}`)}GetUpcomingAppoint(t,e){return this.http.get(`${this.config.APIUrl}/pet/upcomingAppointment/${t}?token=${localStorage.auth_token}&apt_date_time=${e}`)}GetPastVisit(t,e){return this.http.get(`${this.config.APIUrl}/pet/PastVisit/${t}?token=${localStorage.auth_token}&apt_date_time=${e}`)}AddCustomer(t){return this.http.post(`${this.config.APIUrl3}/signUp/`,t)}FindByUserId(t){return this.http.get(`${this.config.APIUrl}/user/${t}?token=${localStorage.auth_token}`)}DeleteCustomer(t){return this.http.delete(`${this.config.APIUrl}/user/${t}?token=${localStorage.auth_token}`)}GetBreedingsList(t){return this.http.get(`${this.config.APIUrl}/Breeding?search=${t}&token=${localStorage.auth_token}`)}GetTypesList(){return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`)}uploadFile(t){const e=new FormData;return e.append("file",t),this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`,e,{reportProgress:!0,responseType:"json"})}AddPet(t){return this.http.post(`${this.config.APIUrl3}/v1/pet?token=${localStorage.auth_token}`,t)}Deletepet(t){return this.http.delete(`${this.config.APIUrl3}/v1/pet/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},26415:function(t,e,r){"use strict";r.d(e,{d:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetEmployeeList(t){return this.http.get(`${this.config.APIUrl}/employee`,{params:t})}GetViewLog(t){return this.http.get(`${this.config.APIUrl5}/${t}`)}GetRoleList(){return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`)}NewEmployee(t){return this.http.post(`${this.config.APIUrl}/employee?token=${localStorage.auth_token}`,t)}GetEmployeeDetail(t){return this.http.get(`${this.config.APIUrl}/employee/${t}?token=${localStorage.auth_token}`)}EditEmployeeDetail(t,e){return this.http.put(`${this.config.APIUrl}/employee/${t}?token=${localStorage.auth_token}`,e)}DeleteEmployee(t){return this.http.delete(`${this.config.APIUrl}/employee/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},87188:function(t,e,r){"use strict";r.d(e,{a:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{NewLocation(t){return this.http.post(`${this.config.APIUrl}/Location?token=${localStorage.auth_token}`,t)}GetLocationsList(t,e){return this.http.get(`${this.config.APIUrl}/Location?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetLocationDetail(t){return this.http.get(`${this.config.APIUrl}/Location/${t}?token=${localStorage.auth_token}`)}UpdateLocation(t,e){return this.http.put(`${this.config.APIUrl}/Location/${t}?token=${localStorage.auth_token}`,e)}DeleteLocation(t){return this.http.delete(`${this.config.APIUrl}/Location/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},49533:function(t,e,r){"use strict";r.d(e,{C:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{NewModule(t){return this.http.post(`${this.config.APIUrl}/module?token=${localStorage.auth_token}`,t)}GetModuleList(t,e){return this.http.get(`${this.config.APIUrl}/module?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetModuleDetail(t){return this.http.get(`${this.config.APIUrl}/module/${t}?token=${localStorage.auth_token}`)}UpdateModule(t,e){return this.http.put(`${this.config.APIUrl}/module/${t}?token=${localStorage.auth_token}`,e)}DeleteModule(t){return this.http.delete(`${this.config.APIUrl}/module/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},5929:function(t,e,r){"use strict";r.d(e,{p:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetOrderList(t){return this.http.get(`${this.config.APIUrl}/allorder?token=${localStorage.auth_token}`,{params:t})}UpdatePelfie(t,e){return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${t}?token=${localStorage.auth_token}`,e)}OrderDetail(t){return this.http.get(`${this.config.APIUrl}/order/${t}?token=${localStorage.auth_token}`)}UpdateOrderDetail(t,e){return this.http.put(`${this.config.APIUrl}/order/${t}?token=${localStorage.auth_token}`,e)}DeleteAllOrders(){return this.http.delete(`${this.config.APIUrl}/delete_all_orders?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},52831:function(t,e,r){"use strict";r.d(e,{D:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetPelfiesList(t,e){return this.http.get(`${this.config.APIUrl}/pelfie?search=${t}&token=${localStorage.auth_token}`,{params:e})}UpdatePelfie(t,e){return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${t}?token=${localStorage.auth_token}`,e)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},6642:function(t,e,r){"use strict";r.d(e,{$:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetRolelist(){return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`)}GetRoleDetails(t,e){return this.http.get(`${this.config.APIUrl}/permission?search=${t}&token=${localStorage.auth_token}`,{params:e})}UpdatePermission(t,e){return this.http.put(`${this.config.APIUrl}/permission/${t}?token=${localStorage.auth_token}`,e)}GetModule(t){return this.http.get(`${this.config.APIUrl}/permission/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},9499:function(t,e,r){"use strict";r.d(e,{M:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{ImageUpload(t){return this.http.post(`${this.config.APIUrl4}/pet/petimage?token=${localStorage.auth_token}`,t)}AddCategory(t){return this.http.post(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`,t)}DeleteCategory(t){return this.http.delete(`${this.config.APIUrl}/category/${t}?token=${localStorage.auth_token}`)}UpdateCategory(t,e){return this.http.put(`${this.config.APIUrl}/category/${t}?token=${localStorage.auth_token}`,e)}GetCategory(t){return this.http.get(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`,{params:t})}AddBrand(t){return this.http.post(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`,t)}GetBrand(t){return this.http.get(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`,{params:t})}UpdateBrand(t,e){return this.http.put(`${this.config.APIUrl}/brand/${t}?token=${localStorage.auth_token}`,e)}DeleteBrand(t){return this.http.delete(`${this.config.APIUrl}/brand/${t}?token=${localStorage.auth_token}`)}AddVariant(t){return this.http.post(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`,t)}GetVariant(t){return this.http.get(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`,{params:t})}UpdateVariant(t,e){return this.http.put(`${this.config.APIUrl}/Variant/${t}?token=${localStorage.auth_token}`,e)}DeleteVariant(t){return this.http.delete(`${this.config.APIUrl}/Variant/${t}?token=${localStorage.auth_token}`)}uploadFile(t){const e=new FormData;return e.append("file",t),this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`,e,{reportProgress:!0,responseType:"json"})}AddProduct(t){return this.http.post(`${this.config.APIUrl}/Product?token=${localStorage.auth_token}`,t)}AddBanners(t){return this.http.post(`${this.config.APIUrl}/Banner?token=${localStorage.auth_token}`,t)}EditBanners(t,e){return this.http.put(`${this.config.APIUrl}/Banner/${e}`,t)}GetProduct(t,e){return this.http.get(`${this.config.APIUrl}/Product?search=${t}&token=${localStorage.auth_token}`,{params:e})}GetallProduct(t,e){return this.http.get(`${this.config.APIUrl}/Product?skip=0&limit=0&search=${t}&token=${localStorage.auth_token}`,{params:e})}GetBanners(t){return this.http.get(`${this.config.APIUrl}/Banner?&token=${localStorage.auth_token}`,{params:t})}getallproduct(t,e){return this.http.get(`${this.config.APIUrl}/products?skip=0&limit=0&search=${t}&token=${localStorage.auth_token}`,{params:e})}DeleteProduct(t){return this.http.delete(`${this.config.APIUrl}/Product/${t}?token=${localStorage.auth_token}`)}DeleteBanner(t){return this.http.delete(`${this.config.APIUrl}/Banner/${t}?token=${localStorage.auth_token}`)}UpdateProduct(t,e){return this.http.put(`${this.config.APIUrl}/Product/${t}?token=${localStorage.auth_token}`,e)}GetProductById(t){return this.http.get(`${this.config.APIUrl}/Product/${t}?token=${localStorage.auth_token}`)}UpdateBanner(t,e){return this.http.put(`${this.config.APIUrl}/Banner/${t}?token=${localStorage.auth_token}`,e)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},86207:function(t,e,r){"use strict";r.d(e,{z:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{GetTips(t){return this.http.get(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`,{params:t})}AddTips(t){return this.http.post(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`,t)}Updatetips(t,e){return this.http.put(`${this.config.APIUrl}/health_tip/${t}?token=${localStorage.auth_token}`,e)}uploadFile(t){const e=new FormData;return e.append("file",t),this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`,e,{reportProgress:!0,responseType:"json"})}DeleteTips(t){return this.http.delete(`${this.config.APIUrl}/health_tip/${t}?token=${localStorage.auth_token}`)}AddVideo(t){return this.http.post(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`,t)}GetVideos(t){return this.http.get(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`,{params:t})}UpdateVideo(t,e){return this.http.put(`${this.config.APIUrl}/video/${t}?token=${localStorage.auth_token}`,e)}DeleteVideo(t){return this.http.delete(`${this.config.APIUrl}/video/${t}?token=${localStorage.auth_token}`)}AddAudio(t){return this.http.post(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`,t)}GetAudios(t){return this.http.get(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`,{params:t})}UpdateAudio(t,e){return this.http.put(`${this.config.APIUrl}/audio/${t}?token=${localStorage.auth_token}`,e)}DeleteAudio(t){return this.http.delete(`${this.config.APIUrl}/audio/${t}?token=${localStorage.auth_token}`)}AddFAQ(t){return this.http.post(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`,t)}GetFAQs(t){return this.http.get(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`,{params:t})}UpdateFAQ(t,e){return this.http.put(`${this.config.APIUrl}/FAQ/${t}?token=${localStorage.auth_token}`,e)}DeleteFAQ(t){return this.http.delete(`${this.config.APIUrl}/FAQ/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},83711:function(t,e,r){"use strict";r.d(e,{N:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{NewRole(t){return this.http.post(`${this.config.APIUrl}/role?token=${localStorage.auth_token}`,t)}GetRoleList(t,e){return this.http.get(`${this.config.APIUrl}/role?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetRoleDetail(t){return this.http.get(`${this.config.APIUrl}/role/${t}?token=${localStorage.auth_token}`)}UpdateRole(t,e){return this.http.put(`${this.config.APIUrl}/role/${t}?token=${localStorage.auth_token}`,e)}DeleteRole(t){return this.http.delete(`${this.config.APIUrl}/role/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},11192:function(t,e,r){"use strict";r.d(e,{i:function(){return l}});var o=r(90658),n=r(99777);const s="auth_token",i="auth-user",a="auth-module";let l=(()=>{class t{constructor(t,e){this._compiler=t,this.router=e}signOut(){window.localStorage.clear(),this._compiler.clearCache(),this.router.navigate(["./login"])}saveToken(t){window.localStorage.removeItem(s),window.localStorage.setItem(s,t)}getToken(){return localStorage.getItem(s)}saveUser(t){window.localStorage.removeItem(i),window.localStorage.setItem(i,JSON.stringify(t))}getUser(){return JSON.parse(localStorage.getItem(i))}saveModule(t){window.localStorage.removeItem(a),window.localStorage.setItem(a,JSON.stringify(t))}getModule(){return JSON.parse(localStorage.getItem(a))}}return t.\u0275fac=function(e){return new(e||t)(o.LFG(o.Sil),o.LFG(n.F0))},t.\u0275prov=o.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},21771:function(t,e,r){"use strict";r.d(e,{J:function(){return s}});var o=r(11160),n=r(90658);let s=(()=>{class t extends o.V{NewTreatment(t){return this.http.post(`${this.config.APIUrl}/treatment?token=${localStorage.auth_token}`,t)}GetTreatmentsList(t,e){return this.http.get(`${this.config.APIUrl}/treatment?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetTreatmentDetail(t){return this.http.get(`${this.config.APIUrl}/treatment/${t}?token=${localStorage.auth_token}`)}UpdateTreatment(t,e){return this.http.put(`${this.config.APIUrl}/treatment/${t}?token=${localStorage.auth_token}`,e)}DeleteTreatment(t){return this.http.delete(`${this.config.APIUrl}/treatment/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return i(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const i=n.n5z(s)},49731:function(t,e,r){"use strict";r.d(e,{V:function(){return n}});var o=r(24766);class n{constructor(){this.APIUrl=o.N.serverUrl+"/api/v1/cms",this.APIUrl1=o.N.serverUrl+"/api/admin",this.APIUrl2=o.N.serverUrl+"",this.APIUrl3=o.N.serverUrl+"/api",this.APIUrl4=o.N.serverUrl+"/api/v1",this.APIUrl5=o.N.serverUrl+"/api/v1/log"}}},24766:function(t,e,r){"use strict";r.d(e,{N:function(){return o}});const o={production:!0,serverUrl:"https://reedapp.net:3000"}},82399:function(t,e,r){"use strict";var o=r(54464),n=r(90658),s=r(63237),i=r(12980),a=r(24242),l=r(74526),c=r(74875),u=r(62209),d=r(29923),p=r(7535),g=r(88561),h=r(72072),f=r(99777),m=r(72318);let Z=(()=>{class t{constructor(){this.requestInFlight$=new d.X(!1)}setHttpStatus(t){this.requestInFlight$.next(t)}getHttpStatus(){return this.requestInFlight$.asObservable()}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac}),t})(),v=(()=>{class t{constructor(t,e,r,o){this.router=t,this.platformId=e,this.toastr=r,this.status=o,this.userTokenDetail={}}intercept(t,e){return this.status.setHttpStatus(!0),this.userTokenDetail&&(t=t.clone({setHeaders:{Authorization:`Bearer ${localStorage.auth_token}`}})),e.handle(t).pipe((0,g.U)(e=>{if(e instanceof a.Zn){this.status.setHttpStatus(!1);const r=e.body;r.message&&""!==r.message&&"GET"!==t.method&&this.showSuccess(e.body.message)}return e}),(0,h.K)(t=>{if(this.status.setHttpStatus(!1),console.log("response==--\x3e",t),200===t.status||201===t.status)return t;switch(t.status){case 400:this.handleBadRequest(t);break;case 422:this.handleUnProcessableEntry(t.error);break;case 401:this.handleUnAuthorized();break;case 500:this.handleServerError()}return(0,p._)(t)}))}handleBadRequest(t){if("http://api.spurtcommerce.com/api/product/product-excel-list/?productId="===t.url&&this.showNotificationError("Please Choose a Valid Data"),"http://api.spurtcommerce.com/api/order/order-excel-list/?orderId="===t.url&&this.showNotificationError("Please Choose a Valid Data"),"http://api.spurtcommerce.com/api/customer/customer-excel-list/?customerId="===t.url&&this.showNotificationError("Please Choose a Valid Data"),t.error)try{this.handleErrorMessages(t.error)}catch(e){}}handleErrorMessages(t){!t||!t.message||this.showNotificationError(t.message)}handleUnProcessableEntry(t){t&&t.data&&t.data.message&&this.showNotificationError(Array.isArray(t.data.message)?t.data.message[0]:t.data.message)}handleUnAuthorized(){localStorage.clear(),this.router.navigate(["/auth/login"])}handleServerError(){this.showNotificationError("Server Error")}showNotificationError(t){console.log("error"),this.toastr.error(t)}showSuccess(t){console.log("login"),this.toastr.success(t)}}return t.\u0275fac=function(e){return new(e||t)(n.LFG(f.F0),n.LFG(n.Lbi),n.LFG(m._W),n.LFG(Z))},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac}),t})();var k=r(60956);const A=[{name:"Customers",url:"/pages/customers",icon:"fa fa-group"},{name:"Appointments",url:"/pages/appointments",icon:"cil-calendar-check"},{name:"Availability",url:"/pages/availability",icon:"fa fa-calendar"},{name:"Resources",url:"/pages/resources",icon:"cil-puzzle"},{name:"Pelfies",url:"/pages/pelfies",icon:"fa fa-picture-o"},{name:"Shopping",url:"/pages/shopping",icon:"fa fa-shopping-cart",children:[{name:"Products",url:"/pages/shopping",icon:"fa fa-id-card"},{name:"Banners",url:"/pages/banners",icon:"fa fa-id-card"},{name:"Shop Setting",url:"/pages/shop-setting",icon:"fa fa-id-card"}]},{name:"Orders",url:"/pages/orders",icon:"cil-task"},{name:"Change Password",url:"/pages/change-password",icon:"fa fa-key"},{name:"Settings",url:"/settings",icon:"fa fa-mortar-board",children:[{name:"Admin Users",url:"/settings/employee",icon:"fa fa-id-card"},{name:"Role",url:"/settings/role",icon:"fa fa-cogs"},{name:"Species",url:"/settings/animal-type",icon:"fa fa-paw"},{name:"Appointment Types",url:"/settings/appointment-types",icon:"fa fa-user-md"},{name:"Location",url:"/settings/location",icon:"fa fa-location-arrow"},{name:"Breed",url:"/settings/breed",icon:"fa fa-paw"},{name:"Covetrus",url:"/settings/covetrus",icon:"fa fa-unlock-alt"}]}];let $=(()=>{class t{constructor(t,e,r){this.router=t,this.iconSet=e,this.httpStatus=r,this.loader=!1,this.navItems=A,this.navItem=[],e.icons=Object.assign({},k.z),this.getHttpResponse()}ngOnInit(){this.router.events.subscribe(t=>{t instanceof f.m2&&window.scrollTo(0,0)})}getHttpResponse(){this.httpStatus.getHttpStatus().subscribe(t=>{this.loader=t})}}return t.\u0275fac=function(e){return new(e||t)(n.Y36(f.F0),n.Y36(u.uk),n.Y36(Z))},t.\u0275cmp=n.Xpm({type:t,selectors:[["body"]],features:[n._Bn([u.uk])],decls:1,vars:0,template:function(t,e){1&t&&n._UZ(0,"router-outlet")},directives:[f.lC],encapsulation:2}),t})();var T=r(45055),w=r(6642);function U(t,e){return r=>{const o=r.controls[e];o.errors&&!o.errors.mustMatch||o.setErrors(r.controls[t].value!==o.value?{mustMatch:!0}:null)}}var _=r(11160);let q=(()=>{class t extends _.V{constructor(){super(...arguments),this.basUrl=this.getBaseUrl()}AdminLogin(t){return this.http.post(this.config.APIUrl1+"/signin",t)}ForgotPassword(t){return this.http.post(`${this.config.APIUrl1}/forgotpassword`,t)}PasswordToken(t){return this.http.get(`${this.config.APIUrl2}/emp/reset_password?token=${t.token}`)}ChangePassword(t){return this.http.post(`${this.config.APIUrl2}/emp/reset_password`,t)}userPasswordToken(t){return console.log("params--\x3e",t),this.http.get(`${this.config.APIUrl2}/reset_password?token=${t.token}`)}userChangePassword(t){return this.http.post(`${this.config.APIUrl2}/reset_password`,t)}GetUser(t,e){return this.http.post(`${this.config.APIUrl}/check_password/${t}`,e)}EditEmployeeDetail(t,e){return this.http.put(`${this.config.APIUrl}/employee/${t}`,e)}}return t.\u0275fac=function(e){return I(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const I=n.n5z(q);var b=r(11192),P=r(7763),y=r(64505),S=r(30386);const x=["primaryModal"];function j(t,e){if(1&t){const t=n.EpF();n.TgZ(0,"div",39),n.TgZ(1,"a",40),n.NdJ("click",function(){return n.CHM(t),n.oxw(),n.MAs(30).show()}),n._UZ(2,"i",41),n._uU(3,"Change password"),n.qZA(),n.TgZ(4,"a",40),n.NdJ("click",function(){return n.CHM(t),n.oxw().ClearStorage()}),n._UZ(5,"i",42),n._uU(6," Logout"),n.qZA(),n.qZA()}}function N(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*old password is mandatory"),n.qZA())}function F(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password must be at least 8 characters"),n.qZA())}function J(t,e){if(1&t&&(n.TgZ(0,"div",43),n.YNc(1,N,2,0,"div",44),n.YNc(2,F,2,0,"div",44),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.Q6J("ngIf",t.f.old_password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.old_password.errors.minlength)}}function z(t,e){1&t&&(n.TgZ(0,"div",45),n._uU(1,"*your old password is incorrect"),n.qZA())}function C(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*new password is mandatory"),n.qZA())}function Y(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password must be at least 8 characters"),n.qZA())}function D(t,e){if(1&t&&(n.TgZ(0,"div",43),n.YNc(1,C,2,0,"div",44),n.YNc(2,Y,2,0,"div",44),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.Q6J("ngIf",t.f.new_password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.new_password.errors.minlength)}}function L(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password is mandatory"),n.qZA())}function Q(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password must be at least 8 characters"),n.qZA())}function G(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Passwords must match"),n.qZA())}function M(t,e){if(1&t&&(n.TgZ(0,"div",43),n.YNc(1,L,2,0,"div",44),n.YNc(2,Q,2,0,"div",44),n.YNc(3,G,2,0,"div",44),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.Q6J("ngIf",t.f.confirm_password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.confirm_password.errors.minlength),n.xp6(1),n.Q6J("ngIf",t.f.confirm_password.errors.mustMatch)}}const B=function(){return{src:"assets/img/brand/logo.png",height:50,alt:"Dr Reed Logo"}},R=function(){return{src:"assets/img/brand/sygnet.png",height:30,alt:"Dr Reed Logo"}},V=function(){return{backdrop:"static",keyboard:!1}},E=function(t,e){return{"fa-eye-slash":t,"fa-eye":e}},O=function(t){return{"is-invalid":t}};let H=(()=>{class t{constructor(t,e,r,o,n,s){this.appComponet=t,this.tokenStorage=e,this.Permissionservice=r,this.router=o,this.formBuilder=n,this.service=s,this.sidebarMinimized=!1,this.navItems=[],this.isFormReady=!1,this.submitted=!1,this.old=!1}ngOnInit(){this.navItems=this.tokenStorage.getModule();const t=this.tokenStorage.getUser();this.Name=t.name,this.Role=t.role_id.name,this.Id=t._id,this.SignForm()}toggleMinimize(t){this.sidebarMinimized=t}ClearStorage(){this.tokenStorage.signOut()}clear(){this.AddForm.reset(),this.isFormReady=!1,this.submitted=!1,this.old=!1}SignForm(){this.AddForm=this.formBuilder.group({old_password:["",[c.kI.required,c.kI.minLength(8)]],new_password:["",[c.kI.required,c.kI.minLength(8)]],confirm_password:["",[c.kI.required,c.kI.minLength(8)]]},{validator:U("new_password","confirm_password")})}get f(){return this.AddForm.controls}AddPassword(){if(this.submitted=!0,!this.AddForm.invalid){const t={old_password:this.AddForm.value.old_password,new_password:this.AddForm.value.new_password,password:this.AddForm.value.confirm_password};this.service.GetUser(this.Id,t).subscribe(e=>{200==e.code?(this.old=!1,this.service.EditEmployeeDetail(this.Id,t).subscribe(t=>{this.primaryModal.hide(),this.clear()})):this.old=!0})}}toggleFieldTextType(){this.fieldTextType=!this.fieldTextType}toggleFieldTextType1(){this.fieldTextType1=!this.fieldTextType1}toggleFieldTextType2(){this.fieldTextType2=!this.fieldTextType2}}return t.\u0275fac=function(e){return new(e||t)(n.Y36($),n.Y36(b.i),n.Y36(w.$),n.Y36(f.F0),n.Y36(c.qu),n.Y36(q))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-dashboard"]],viewQuery:function(t,e){if(1&t&&n.Gf(x,1),2&t){let t;n.iGM(t=n.CRH())&&(e.primaryModal=t.first)}},decls:70,vars:47,consts:[[3,"fixed","navbarBrandFull","navbarBrandMinimized","sidebarToggler","asideMenuToggler","mobileAsideMenuToggler","mobileSidebarToggler"],[1,"nav","navbar-nav","ml-auto"],["dropdown","","placement","bottom right",1,"nav-item","dropdown"],["data-toggle","dropdown","href","#","role","button","aria-haspopup","true","aria-expanded","false","dropdownToggle","",1,"nav-link",3,"click"],["src","assets/img/avatars/6.jpg","alt","<EMAIL>",1,"img-avatar"],["class","dropdown-menu dropdown-menu-right","aria-labelledby","simple-dropdown",4,"dropdownMenu"],[1,"app-body"],[3,"fixed","display","minimized","minimizedChange"],["appSidebar",""],[3,"navItems","perfectScrollbar","disabled"],[1,"main"],[1,"container-fluid"],["href","#"],[1,"ml-auto"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"row"],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],["for","old_password"],[1,"input-group","mb-3"],[1,"input-group-append"],[1,"input-group-text"],[1,"fa",3,"ngClass","click"],["type","password","placeholder","Enter old password","formControlName","old_password","autocomplete","off",1,"form-control",3,"type","ngClass"],["class","invalid-feedback",4,"ngIf"],["style","font-size:smaller;color:#f86c6b;margin-top: -15px;",4,"ngIf"],["for","new_password"],["type","password","placeholder","Enter new password","formControlName","new_password","autocomplete","off",1,"form-control",3,"type","ngClass"],["for","confirm_password"],["type","password","placeholder","re-enter new password","formControlName","confirm_password","autocomplete","off",1,"form-control",3,"type","ngClass"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","submit",1,"btn","btn-primary",3,"click"],["aria-labelledby","simple-dropdown",1,"dropdown-menu","dropdown-menu-right"],[1,"dropdown-item",3,"click"],[1,"fa","fa-key"],[1,"fa","fa-sign-out"],[1,"invalid-feedback"],[4,"ngIf"],[2,"font-size","smaller","color","#f86c6b","margin-top","-15px"]],template:function(t,e){if(1&t){const t=n.EpF();n.TgZ(0,"app-header",0),n.TgZ(1,"ul",1),n.TgZ(2,"span"),n.TgZ(3,"strong"),n._uU(4),n.qZA(),n._UZ(5,"br"),n._uU(6),n.qZA(),n.TgZ(7,"li",2),n.TgZ(8,"a",3),n.NdJ("click",function(){return!1}),n._UZ(9,"img",4),n.qZA(),n.YNc(10,j,7,0,"div",5),n.qZA(),n.qZA(),n.qZA(),n.TgZ(11,"div",6),n.TgZ(12,"app-sidebar",7,8),n.NdJ("minimizedChange",function(t){return e.toggleMinimize(t)}),n._UZ(14,"app-sidebar-nav",9),n._UZ(15,"app-sidebar-minimizer"),n.qZA(),n.TgZ(16,"main",10),n._UZ(17,"cui-breadcrumb"),n.TgZ(18,"div",11),n._UZ(19,"router-outlet"),n.qZA(),n.qZA(),n.qZA(),n.TgZ(20,"app-footer"),n.TgZ(21,"span"),n.TgZ(22,"a",12),n._uU(23,"All Rights Reserved - "),n.qZA(),n._uU(24," \xa9 2021 Dr Reed."),n.qZA(),n.TgZ(25,"span",13),n._uU(26,"Powered by "),n.TgZ(27,"a",12),n._uU(28,"Dr Reed."),n.qZA(),n.qZA(),n.qZA(),n.TgZ(29,"div",14,15),n.TgZ(31,"div",16),n.TgZ(32,"div",17),n.TgZ(33,"div",18),n.TgZ(34,"h4",19),n._uU(35,"Change Password"),n.qZA(),n.qZA(),n.TgZ(36,"div",20),n.TgZ(37,"div",21),n.TgZ(38,"div",22),n.TgZ(39,"form",23),n.TgZ(40,"label",24),n._uU(41,"Old Password"),n.qZA(),n.TgZ(42,"div",25),n.TgZ(43,"div",26),n.TgZ(44,"span",27),n.TgZ(45,"i",28),n.NdJ("click",function(){return e.toggleFieldTextType1()}),n.qZA(),n.qZA(),n.qZA(),n._UZ(46,"input",29),n.YNc(47,J,3,2,"div",30),n.qZA(),n.YNc(48,z,2,0,"div",31),n.TgZ(49,"label",32),n._uU(50,"New Password"),n.qZA(),n.TgZ(51,"div",25),n.TgZ(52,"div",26),n.TgZ(53,"span",27),n.TgZ(54,"i",28),n.NdJ("click",function(){return e.toggleFieldTextType()}),n.qZA(),n.qZA(),n.qZA(),n._UZ(55,"input",33),n.YNc(56,D,3,2,"div",30),n.qZA(),n.TgZ(57,"label",34),n._uU(58,"Confirm Password"),n.qZA(),n.TgZ(59,"div",25),n.TgZ(60,"div",26),n.TgZ(61,"span",27),n.TgZ(62,"i",28),n.NdJ("click",function(){return e.toggleFieldTextType2()}),n.qZA(),n.qZA(),n.qZA(),n._UZ(63,"input",35),n.YNc(64,M,4,3,"div",30),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.TgZ(65,"div",36),n.TgZ(66,"button",37),n.NdJ("click",function(){return n.CHM(t),n.MAs(30).hide(),e.clear()}),n._uU(67,"Cancel"),n.qZA(),n.TgZ(68,"button",38),n.NdJ("click",function(){return e.AddPassword()}),n._uU(69,"Save"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()}if(2&t){const t=n.MAs(13);n.Q6J("fixed",!0)("navbarBrandFull",n.DdM(29,B))("navbarBrandMinimized",n.DdM(30,R))("sidebarToggler","lg")("asideMenuToggler",!1)("mobileAsideMenuToggler",!1)("mobileSidebarToggler","lg"),n.xp6(4),n.Oqu(e.Name),n.xp6(2),n.Oqu(e.Role),n.xp6(6),n.Q6J("fixed",!0)("display","lg")("minimized",e.sidebarMinimized),n.xp6(2),n.Q6J("navItems",e.navItems)("disabled",t.minimized),n.xp6(15),n.Q6J("config",n.DdM(31,V)),n.xp6(10),n.Q6J("formGroup",e.AddForm),n.xp6(6),n.Q6J("ngClass",n.WLB(32,E,!e.fieldTextType1,e.fieldTextType1)),n.xp6(1),n.Q6J("type",e.fieldTextType1?"text":"password")("ngClass",n.VKq(35,O,e.submitted&&e.f.old_password.errors)),n.xp6(1),n.Q6J("ngIf",e.submitted&&e.f.old_password.errors),n.xp6(1),n.Q6J("ngIf",e.old),n.xp6(6),n.Q6J("ngClass",n.WLB(37,E,!e.fieldTextType,e.fieldTextType)),n.xp6(1),n.Q6J("type",e.fieldTextType?"text":"password")("ngClass",n.VKq(40,O,e.submitted&&e.f.new_password.errors)),n.xp6(1),n.Q6J("ngIf",e.submitted&&e.f.new_password.errors),n.xp6(6),n.Q6J("ngClass",n.WLB(42,E,!e.fieldTextType2,e.fieldTextType2)),n.xp6(1),n.Q6J("type",e.fieldTextType2?"text":"password")("ngClass",n.VKq(45,O,e.submitted&&e.f.confirm_password.errors)),n.xp6(1),n.Q6J("ngIf",e.submitted&&e.f.confirm_password.errors)}},directives:[P.Pj,y.TO,y.Mq,y.Hz,P.ys,P.zH,l.$V,P.hq,P.dL,f.lC,P.qB,S.oB,c.vK,c.JL,c.sg,s.mk,c.Fj,c.JJ,c.u,s.O5],encapsulation:2}),t})();function K(t,e){if(1&t&&(n.TgZ(0,"div",20),n._uU(1),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.hij(" ",t.data," ")}}const X=function(){return{standalone:!0}},W=function(){return["/login"]};let tt=(()=>{class t{constructor(t,e,r,o){this.userservice=t,this.route=e,this.router=r,this.tokenStorage=o,this.forgot={email:""},this.isFailed=!0,this.isLoginFailed=!1}ngOnInit(){}getfocus(){this.isLoginFailed=!1}ForgotPassword(){if(""!=this.forgot.email){const t={email:this.forgot.email};console.log("data",t),this.userservice.ForgotPassword(t).subscribe(t=>{console.log("testtttt"),this.data=t.message,this.isLoginFailed=!0})}}}return t.\u0275fac=function(e){return new(e||t)(n.Y36(q),n.Y36(f.gz),n.Y36(f.F0),n.Y36(b.i))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-forgot-password"]],decls:26,vars:6,consts:[[1,"app-body","auth-login-sign"],[1,"main","d-flex","align-items-center"],[1,"container"],[1,"row"],[1,"col-md-5","mx-auto"],[1,"card-group"],[1,"card","p-4"],[1,"card-body"],[2,"margin-bottom","20px"],[1,"input-group","mb-3"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"icon-envelope-letter","icons"],["type","email","placeholder","Email","autocomplete","Email","required","","id","email",1,"form-control","form-control-lg",3,"ngModel","ngModelOptions","ngModelChange","click"],[1,"col-12"],["type","button",1,"btn","btn-primary","px-4",3,"click"],[1,"col-12","text-right"],["type","submit",1,"btn","btn-link","px-0",3,"routerLink"],["href","javascript:;"],["class","alert alert-danger","role","alert","style","margin-top: 10px;",4,"ngIf"],["role","alert",1,"alert","alert-danger",2,"margin-top","10px"]],template:function(t,e){1&t&&(n.TgZ(0,"div",0),n.TgZ(1,"main",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"div",5),n.TgZ(6,"div",6),n.TgZ(7,"div",7),n.TgZ(8,"form"),n.TgZ(9,"h1",8),n._uU(10,"Forgot Password"),n.qZA(),n.TgZ(11,"div",9),n.TgZ(12,"div",10),n.TgZ(13,"span",11),n._UZ(14,"i",12),n.qZA(),n.qZA(),n.TgZ(15,"input",13),n.NdJ("ngModelChange",function(t){return e.forgot.email=t})("click",function(){return e.getfocus()}),n.qZA(),n.qZA(),n.TgZ(16,"div",3),n.TgZ(17,"div",14),n.TgZ(18,"button",15),n.NdJ("click",function(){return e.ForgotPassword()}),n._uU(19,"Submit"),n.qZA(),n.qZA(),n.qZA(),n.TgZ(20,"div",3),n.TgZ(21,"div",16),n.TgZ(22,"button",17),n.TgZ(23,"a",18),n._uU(24,"Login"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.YNc(25,K,2,1,"div",19),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()),2&t&&(n.xp6(15),n.Q6J("ngModel",e.forgot.email)("ngModelOptions",n.DdM(4,X)),n.xp6(7),n.Q6J("routerLink",n.DdM(5,W)),n.xp6(3),n.Q6J("ngIf",e.isLoginFailed))},directives:[c.vK,c.JL,c.F,c.Fj,c.Q7,c.JJ,c.On,f.rH,s.O5],styles:[""]}),t})(),et=(()=>{class t{constructor(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=n.Xpm({type:t,selectors:[["ng-component"]],decls:19,vars:0,consts:[[1,"app","flex-row","align-items-center"],[1,"container"],[1,"row","justify-content-center"],[1,"col-md-6"],[1,"clearfix"],[1,"float-left","display-3","mr-4"],[1,"pt-3"],[1,"text-muted"],[1,"input-prepend","input-group"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search"],["id","prependedInput","size","16","type","text","placeholder","What are you looking for?",1,"form-control"],[1,"input-group-append"],["type","button",1,"btn","btn-info"]],template:function(t,e){1&t&&(n.TgZ(0,"div",0),n.TgZ(1,"div",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"h1",5),n._uU(6,"404"),n.qZA(),n.TgZ(7,"h4",6),n._uU(8,"Oops! You're lost."),n.qZA(),n.TgZ(9,"p",7),n._uU(10,"The page you are looking for was not found."),n.qZA(),n.qZA(),n.TgZ(11,"div",8),n.TgZ(12,"div",9),n.TgZ(13,"span",10),n._UZ(14,"i",11),n.qZA(),n.qZA(),n._UZ(15,"input",12),n.TgZ(16,"span",13),n.TgZ(17,"button",14),n._uU(18,"Search"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA())},encapsulation:2}),t})();function rt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Email cannot be empty"),n.qZA())}function ot(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Please enter a valid Email address"),n.qZA())}function nt(t,e){if(1&t&&(n.TgZ(0,"div",24),n.YNc(1,rt,2,0,"div",25),n.YNc(2,ot,2,0,"div",25),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.Q6J("ngIf",t.f.email.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.email.errors.email||t.f.email.errors.pattern)}}function st(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password cannot be empty"),n.qZA())}function it(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password must be at least 8 characters"),n.qZA())}function at(t,e){if(1&t&&(n.TgZ(0,"div",24),n.YNc(1,st,2,0,"div",25),n.YNc(2,it,2,0,"div",25),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.Q6J("ngIf",t.f.password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.password.errors.minlength)}}function lt(t,e){if(1&t&&(n.TgZ(0,"div",26),n._uU(1),n.qZA()),2&t){const t=n.oxw();n.xp6(1),n.hij(" ",t.errormessage," ")}}const ct=function(t){return{"is-invalid":t}},ut=function(){return["/forgot-password"]};function dt(t,e){1&t&&(n.TgZ(0,"div"),n.TgZ(1,"h3",2),n._uU(2,"Invalid Request!."),n.qZA(),n.qZA())}function pt(t,e){1&t&&(n.TgZ(0,"div"),n.TgZ(1,"h3",3),n._uU(2,"Password changed successfully."),n.qZA(),n.qZA())}function gt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password cannot be empty"),n.qZA())}function ht(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must be 8 digits"),n.qZA())}function ft(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must contain uppercase, lowercase, special character and numbers"),n.qZA())}function mt(t,e){if(1&t&&(n.TgZ(0,"div",25),n.YNc(1,gt,2,0,"div",0),n.YNc(2,ht,2,0,"div",0),n.YNc(3,ft,2,0,"div",0),n.qZA()),2&t){const t=n.oxw(2);n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.minlength),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.pattern)}}function Zt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password cannot be empty"),n.qZA())}function vt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must be a 8 digits"),n.qZA())}function kt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must contain uppercase, lowercase, special character and numbers"),n.qZA())}function At(t,e){if(1&t&&(n.TgZ(0,"div",25),n.YNc(1,Zt,2,0,"div",0),n.YNc(2,vt,2,0,"div",0),n.YNc(3,kt,2,0,"div",0),n.qZA()),2&t){const t=n.oxw(2);n.xp6(1),n.Q6J("ngIf",t.f.password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.password.errors.minlength),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.pattern)}}function $t(t,e){1&t&&(n.TgZ(0,"div",26),n._uU(1,"Please make sure your passwords match "),n.qZA())}const Tt=function(t){return{"is-invalid":t}};function wt(t,e){if(1&t){const t=n.EpF();n.TgZ(0,"div",4),n.TgZ(1,"main",5),n.TgZ(2,"div",6),n.TgZ(3,"div",7),n.TgZ(4,"div",8),n.TgZ(5,"div",9),n.TgZ(6,"div",10),n.TgZ(7,"div",11),n.TgZ(8,"form",12),n.NdJ("ngSubmit",function(){return n.CHM(t),n.oxw().onSubmit()}),n.TgZ(9,"h1",13),n._uU(10,"Reset Password"),n.qZA(),n.TgZ(11,"div",14),n.TgZ(12,"div",15),n.TgZ(13,"span",16),n._UZ(14,"i",17),n.qZA(),n.qZA(),n.TgZ(15,"input",18),n.NdJ("click",function(){return n.CHM(t),n.oxw().getfocus()}),n.qZA(),n.YNc(16,mt,4,3,"div",19),n.qZA(),n.TgZ(17,"div",14),n.TgZ(18,"div",15),n.TgZ(19,"span",16),n._UZ(20,"i",17),n.qZA(),n.qZA(),n.TgZ(21,"input",20),n.NdJ("click",function(){return n.CHM(t),n.oxw().getfocus()}),n.qZA(),n.YNc(22,At,4,3,"div",19),n.qZA(),n.TgZ(23,"div",21),n.YNc(24,$t,2,0,"div",22),n.qZA(),n.TgZ(25,"div",7),n.TgZ(26,"div",23),n.TgZ(27,"button",24),n._uU(28,"Submit"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()}if(2&t){const t=n.oxw();n.xp6(8),n.Q6J("formGroup",t.validForm),n.xp6(7),n.Q6J("ngClass",n.VKq(6,Tt,t.submitted&&t.f.firstName.errors)),n.xp6(1),n.Q6J("ngIf",t.submitted&&t.f.firstName.errors),n.xp6(5),n.Q6J("ngClass",n.VKq(8,Tt,t.submitted&&t.f.password.errors)),n.xp6(1),n.Q6J("ngIf",t.submitted&&t.f.password.errors),n.xp6(2),n.Q6J("ngIf",t.failed)}}function Ut(t,e){1&t&&(n.TgZ(0,"div"),n.TgZ(1,"h3",2),n._uU(2,"Invalid Request!."),n.qZA(),n.qZA())}function _t(t,e){1&t&&(n.TgZ(0,"div"),n.TgZ(1,"h3",3),n._uU(2,"Password changed successfully."),n.qZA(),n.qZA())}function qt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password cannot be empty"),n.qZA())}function It(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must be 8 digits"),n.qZA())}function bt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must contain uppercase, lowercase, special character and numbers"),n.qZA())}function Pt(t,e){if(1&t&&(n.TgZ(0,"div",25),n.YNc(1,qt,2,0,"div",0),n.YNc(2,It,2,0,"div",0),n.YNc(3,bt,2,0,"div",0),n.qZA()),2&t){const t=n.oxw(2);n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.minlength),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.pattern)}}function yt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"*Password cannot be empty"),n.qZA())}function St(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must be a 8 digits"),n.qZA())}function xt(t,e){1&t&&(n.TgZ(0,"div"),n._uU(1,"Password must contain uppercase, lowercase, special character and numbers"),n.qZA())}function jt(t,e){if(1&t&&(n.TgZ(0,"div",25),n.YNc(1,yt,2,0,"div",0),n.YNc(2,St,2,0,"div",0),n.YNc(3,xt,2,0,"div",0),n.qZA()),2&t){const t=n.oxw(2);n.xp6(1),n.Q6J("ngIf",t.f.password.errors.required),n.xp6(1),n.Q6J("ngIf",t.f.password.errors.minlength),n.xp6(1),n.Q6J("ngIf",t.f.firstName.errors.pattern)}}function Nt(t,e){1&t&&(n.TgZ(0,"div",26),n._uU(1,"Please make sure your passwords match "),n.qZA())}const Ft=function(t){return{"is-invalid":t}};function Jt(t,e){if(1&t){const t=n.EpF();n.TgZ(0,"div",4),n.TgZ(1,"main",5),n.TgZ(2,"div",6),n.TgZ(3,"div",7),n.TgZ(4,"div",8),n.TgZ(5,"div",9),n.TgZ(6,"div",10),n.TgZ(7,"div",11),n.TgZ(8,"form",12),n.NdJ("ngSubmit",function(){return n.CHM(t),n.oxw().onSubmit()}),n.TgZ(9,"h1",13),n._uU(10,"Reset Password"),n.qZA(),n.TgZ(11,"div",14),n.TgZ(12,"div",15),n.TgZ(13,"span",16),n._UZ(14,"i",17),n.qZA(),n.qZA(),n.TgZ(15,"input",18),n.NdJ("click",function(){return n.CHM(t),n.oxw().getfocus()}),n.qZA(),n.YNc(16,Pt,4,3,"div",19),n.qZA(),n.TgZ(17,"div",14),n.TgZ(18,"div",15),n.TgZ(19,"span",16),n._UZ(20,"i",17),n.qZA(),n.qZA(),n.TgZ(21,"input",20),n.NdJ("click",function(){return n.CHM(t),n.oxw().getfocus()}),n.qZA(),n.YNc(22,jt,4,3,"div",19),n.qZA(),n.TgZ(23,"div",21),n.YNc(24,Nt,2,0,"div",22),n.qZA(),n.TgZ(25,"div",7),n.TgZ(26,"div",23),n.TgZ(27,"button",24),n._uU(28,"Submit"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()}if(2&t){const t=n.oxw();n.xp6(8),n.Q6J("formGroup",t.validForm),n.xp6(7),n.Q6J("ngClass",n.VKq(6,Ft,t.submitted&&t.f.firstName.errors)),n.xp6(1),n.Q6J("ngIf",t.submitted&&t.f.firstName.errors),n.xp6(5),n.Q6J("ngClass",n.VKq(8,Ft,t.submitted&&t.f.password.errors)),n.xp6(1),n.Q6J("ngIf",t.submitted&&t.f.password.errors),n.xp6(2),n.Q6J("ngIf",t.failed)}}const zt=[{path:"",redirectTo:"/login",pathMatch:"full"},{path:"404",component:et,data:{title:"Page 404"}},{path:"500",component:(()=>{class t{constructor(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=n.Xpm({type:t,selectors:[["ng-component"]],decls:19,vars:0,consts:[[1,"app","flex-row","align-items-center"],[1,"container"],[1,"row","justify-content-center"],[1,"col-md-6"],[1,"clearfix"],[1,"float-left","display-3","mr-4"],[1,"pt-3"],[1,"text-muted"],[1,"input-prepend","input-group"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search"],["id","prependedInput","size","16","type","text","placeholder","What are you looking for?",1,"form-control"],[1,"input-group-append"],["type","button",1,"btn","btn-info"]],template:function(t,e){1&t&&(n.TgZ(0,"div",0),n.TgZ(1,"div",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"h1",5),n._uU(6,"500"),n.qZA(),n.TgZ(7,"h4",6),n._uU(8,"Houston, we have a problem!"),n.qZA(),n.TgZ(9,"p",7),n._uU(10,"The page you are looking for is temporarily unavailable."),n.qZA(),n.qZA(),n.TgZ(11,"div",8),n.TgZ(12,"div",9),n.TgZ(13,"span",10),n._UZ(14,"i",11),n.qZA(),n.qZA(),n._UZ(15,"input",12),n.TgZ(16,"span",13),n.TgZ(17,"button",14),n._uU(18,"Search"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA())},encapsulation:2}),t})(),data:{title:"Page 500"}},{path:"login",component:(()=>{class t{constructor(t,e,r,o,n,s,i,a){this.userservice=t,this.permissionService=e,this.route=r,this.router=o,this.tokenStorage=n,this.appComponent=s,this.formBuilder=i,this._compiler=a,this.login={email:"",password:""},this.errormessage="",this.required=!1,this.isLoginFailed=!1,this.isFormReady=!1,this.submitted=!1,this.loader=!1}ngOnInit(){const t=this.tokenStorage.getModule();null!=t?this.router.navigate([t[0].url]):this.SignForm(),this.SignForm()}getfocus(){this.isLoginFailed=!1}SignForm(){this.loginForm=this.formBuilder.group({email:["",[c.kI.required,c.kI.email,c.kI.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]],password:["",[c.kI.required,c.kI.minLength(8)]]})}get f(){return this.loginForm.controls}onSubmit(){this.submitted=!0,this.loginForm.invalid||this.userservice.AdminLogin({email:this.loginForm.value.email,password:this.loginForm.value.password}).subscribe(t=>{200===t.code?(this._compiler.clearCache(),this.resultName=t,this.tokenStorage.saveToken(t.data.tokens),this.tokenStorage.saveUser(t.data),this.permissionService.GetModule(t.data.role_id._id).subscribe(t=>{let e=[],r=[];for(var o=0;o<t.data.length;o++)for(var n=0;n<A.length;n++)if(t.data[o].module_name===A[n].name&&(e.push(A[n]),r.push(A[n]),A[n].children)){const e=[];for(var s=0;s<A[n].children.length;s++)for(var i=0;i<t.data.length;i++)t.data[i].module_name===A[n].children[s].name&&(e.push(A[n].children[s]),r.push(A[n].children[s]));A[n].children=e}const a=e.reverse();this.loginForm.reset();const l=r.map(t=>t.url);localStorage.setItem("Verify",JSON.stringify(l)),0!=a.length&&(this.tokenStorage.saveModule(a),this.submitted=!1,this.loginForm.reset(),this.isFormReady=!0,this._compiler.clearCache(),this.router.navigate([a[0].url]))})):(this.errormessage=t.message,this.isLoginFailed=!0)})}}return t.\u0275fac=function(e){return new(e||t)(n.Y36(q),n.Y36(w.$),n.Y36(f.gz),n.Y36(f.F0),n.Y36(b.i),n.Y36($),n.Y36(c.qu),n.Y36(n.Sil))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-dashboard"]],decls:35,vars:12,consts:[[1,"app-body","auth-login-sign"],[1,"main","d-flex","align-items-center"],[1,"container"],[1,"row"],[1,"col-md-5","mx-auto"],[1,"card-group"],["visible","true",1,"card","p-4"],[1,"card-body"],["autocomplete","off",1,"form",3,"formGroup","ngSubmit"],[1,"text-muted"],[1,"input-group","mb-4"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"icon-user"],["type","email","placeholder","Email","formControlName","email",1,"form-control",3,"ngClass","click"],["class","invalid-feedback",4,"ngIf"],[1,"icon-lock"],["type","password","placeholder","Password","formControlName","password",1,"form-control",3,"ngClass","click"],[1,"form-group"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"col-12","text-right"],["type","button",1,"btn","btn-link","px-0",3,"routerLink"],[1,"col-12"],[1,"btn","btn-primary","px-4"],[1,"invalid-feedback"],[4,"ngIf"],["role","alert",1,"alert","alert-danger"]],template:function(t,e){1&t&&(n.TgZ(0,"div",0),n.TgZ(1,"main",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"div",5),n.TgZ(6,"div",6),n.TgZ(7,"div",7),n.TgZ(8,"form",8),n.NdJ("ngSubmit",function(){return e.onSubmit()}),n.TgZ(9,"h1"),n._uU(10,"Login"),n.qZA(),n.TgZ(11,"p",9),n._uU(12,"Sign In to your account"),n.qZA(),n.TgZ(13,"div",10),n.TgZ(14,"div",11),n.TgZ(15,"span",12),n._UZ(16,"i",13),n.qZA(),n.qZA(),n.TgZ(17,"input",14),n.NdJ("click",function(){return e.getfocus()}),n.qZA(),n.YNc(18,nt,3,2,"div",15),n.qZA(),n.TgZ(19,"div",10),n.TgZ(20,"div",11),n.TgZ(21,"span",12),n._UZ(22,"i",16),n.qZA(),n.qZA(),n.TgZ(23,"input",17),n.NdJ("click",function(){return e.getfocus()}),n.qZA(),n.YNc(24,at,3,2,"div",15),n.qZA(),n.TgZ(25,"div",18),n.YNc(26,lt,2,1,"div",19),n.qZA(),n.TgZ(27,"div",3),n.TgZ(28,"div",20),n.TgZ(29,"button",21),n._uU(30,"Forgot password?"),n.qZA(),n.qZA(),n.qZA(),n.TgZ(31,"div",3),n.TgZ(32,"div",22),n.TgZ(33,"button",23),n._uU(34,"Login"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA()),2&t&&(n.xp6(8),n.Q6J("formGroup",e.loginForm),n.xp6(9),n.Q6J("ngClass",n.VKq(7,ct,e.submitted&&e.f.email.errors)),n.xp6(1),n.Q6J("ngIf",e.submitted&&e.f.email.errors),n.xp6(5),n.Q6J("ngClass",n.VKq(9,ct,e.submitted&&e.f.password.errors)),n.xp6(1),n.Q6J("ngIf",e.submitted&&e.f.password.errors),n.xp6(2),n.Q6J("ngIf",e.isLoginFailed),n.xp6(3),n.Q6J("routerLink",n.DdM(11,ut)))},directives:[c.vK,c.JL,c.sg,c.Fj,c.JJ,c.u,s.mk,s.O5,f.rH],encapsulation:2}),t})(),data:{title:"Login Page"}},{path:"register",component:(()=>{class t{constructor(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-dashboard"]],decls:34,vars:0,consts:[[1,"app-body","auth-login-sign"],[1,"main","d-flex","align-items-center"],[1,"container"],[1,"row"],[1,"col-md-6","mx-auto"],[1,"card","mx-4"],[1,"card-body","p-4"],[1,"text-muted"],[1,"input-group","mb-3"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"icon-user"],["type","text","placeholder","Username","autocomplete","username","required","",1,"form-control"],["type","text","placeholder","Email","autocomplete","email","required","",1,"form-control"],[1,"icon-lock"],["type","password","placeholder","Password","autocomplete","new-password","required","",1,"form-control"],[1,"input-group","mb-4"],["type","password","placeholder","Repeat password","autocomplete","new-password","required","",1,"form-control"],["type","button",1,"btn","btn-block","btn-success"]],template:function(t,e){1&t&&(n.TgZ(0,"div",0),n.TgZ(1,"main",1),n.TgZ(2,"div",2),n.TgZ(3,"div",3),n.TgZ(4,"div",4),n.TgZ(5,"div",5),n.TgZ(6,"div",6),n.TgZ(7,"form"),n.TgZ(8,"h1"),n._uU(9,"Register"),n.qZA(),n.TgZ(10,"p",7),n._uU(11,"Create your account"),n.qZA(),n.TgZ(12,"div",8),n.TgZ(13,"div",9),n.TgZ(14,"span",10),n._UZ(15,"i",11),n.qZA(),n.qZA(),n._UZ(16,"input",12),n.qZA(),n.TgZ(17,"div",8),n.TgZ(18,"div",9),n.TgZ(19,"span",10),n._uU(20,"@"),n.qZA(),n.qZA(),n._UZ(21,"input",13),n.qZA(),n.TgZ(22,"div",8),n.TgZ(23,"div",9),n.TgZ(24,"span",10),n._UZ(25,"i",14),n.qZA(),n.qZA(),n._UZ(26,"input",15),n.qZA(),n.TgZ(27,"div",16),n.TgZ(28,"div",9),n.TgZ(29,"span",10),n._UZ(30,"i",14),n.qZA(),n.qZA(),n._UZ(31,"input",17),n.qZA(),n.TgZ(32,"button",18),n._uU(33,"Create Account"),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA(),n.qZA())},directives:[c.vK,c.JL,c.F],encapsulation:2}),t})(),data:{title:"Register Page"}},{path:"forgot-password",component:tt,data:{title:"Forgot Password"}},{path:"reset-password",component:(()=>{class t{constructor(t,e,r,o){this.formBuilder=t,this.userservice=e,this.route=r,this.router=o,this.token="",this.TokenFailed=!1,this.TokenPass=!1,this.password={firstName:"",password:""},this.errormessage="",this.required=!1,this.isLoginFailed=!1,this.response=[],this.isFormReady=!1,this.submitted=!1,this.show=!1,this.failed=!1,this.success=!1}ngOnInit(){this.route.queryParams.subscribe(t=>{this.userservice.PasswordToken({token:t.token}).subscribe(e=>{100===e.code?this.TokenFailed=!0:(this.TokenPass=!0,this.token=t.token,this.valid())})}),this.valid()}valid(){this.validForm=this.formBuilder.group({firstName:["",[c.kI.required,c.kI.minLength(8)]],password:["",[c.kI.required,c.kI.minLength(8)]]})}getfocus(){this.failed=!1}get f(){return this.validForm.controls}onSubmit(){this.submitted=!0,""!=this.validForm.value.firstName&&""!=this.validForm.value.password&&this.validForm.value.firstName.length>=8&&this.validForm.value.password.length>=8&&(this.validForm.value.firstName===this.validForm.value.password?(this.isFormReady=!0,this.userservice.ChangePassword({newPassword:this.validForm.value.firstName,confirmPassword:this.validForm.value.password,token:this.token}).subscribe(t=>{200===t.code?(this.success=!0,this.TokenPass=!1):this.errormessage=t.data})):this.failed=!0)}}return t.\u0275fac=function(e){return new(e||t)(n.Y36(c.qu),n.Y36(q),n.Y36(f.gz),n.Y36(f.F0))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-reset-password"]],decls:3,vars:3,consts:[[4,"ngIf"],["class","app-body auth-login-sign",4,"ngIf"],[2,"color","red","font-weight","600","text-align","center","padding","200px"],[2,"color","green","font-weight","600","text-align","center","padding","250px 50px 0px"],[1,"app-body","auth-login-sign"],[1,"main","d-flex","align-items-center"],[1,"container"],[1,"row"],[1,"col-md-5","mx-auto"],[1,"card-group"],[1,"card","p-4"],[1,"card-body"],["autocomplete","off",1,"form",3,"formGroup","ngSubmit"],[2,"margin-bottom","20px"],[1,"input-group","mb-3"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"icon-lock","icons"],["type","password","placeholder","New Password","required","","formControlName","firstName",1,"form-control",3,"ngClass","click"],["class","invalid-feedback",4,"ngIf"],["type","password","placeholder","Confirm New Password","required","","formControlName","password",1,"form-control",3,"ngClass","click"],[1,"form-group"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"col-12"],[1,"btn","btn-primary","px-4"],[1,"invalid-feedback"],["role","alert",1,"alert","alert-danger"]],template:function(t,e){1&t&&(n.YNc(0,dt,3,0,"div",0),n.YNc(1,pt,3,0,"div",0),n.YNc(2,wt,29,10,"div",1)),2&t&&(n.Q6J("ngIf",e.TokenFailed),n.xp6(1),n.Q6J("ngIf",e.success),n.xp6(1),n.Q6J("ngIf",e.TokenPass))},directives:[s.O5,c.vK,c.JL,c.sg,c.Fj,c.Q7,c.JJ,c.u,s.mk],styles:[""]}),t})(),data:{title:"Reset Password"}},{path:"app-reset-password",component:(()=>{class t{constructor(t,e,r,o){this.formBuilder=t,this.userservice=e,this.route=r,this.router=o,this.token="",this.TokenFailed=!1,this.TokenPass=!1,this.password={firstName:"",password:""},this.errormessage="",this.required=!1,this.isLoginFailed=!1,this.response=[],this.isFormReady=!1,this.submitted=!1,this.show=!1,this.failed=!1,this.success=!1}ngOnInit(){this.route.queryParams.subscribe(t=>{this.userservice.userPasswordToken({token:t.token}).subscribe(e=>{100===e.code?this.TokenFailed=!0:(this.TokenPass=!0,this.token=t.token,this.valid())})}),this.valid()}valid(){this.validForm=this.formBuilder.group({firstName:["",[c.kI.required,c.kI.minLength(8)]],password:["",[c.kI.required,c.kI.minLength(8)]]})}getfocus(){this.failed=!1}get f(){return this.validForm.controls}onSubmit(){this.submitted=!0,!this.validForm.invalid&&""!=this.validForm.value.firstName&&""!=this.validForm.value.password&&this.validForm.value.firstName.length>=8&&this.validForm.value.password.length>=8&&(this.validForm.value.firstName===this.validForm.value.password?(this.submitted=!0,this.isFormReady=!0,this.userservice.userChangePassword({newPassword:this.validForm.value.firstName,confirmPassword:this.validForm.value.password,token:this.token}).subscribe(t=>{200===t.code?(this.success=!0,this.TokenPass=!1):this.errormessage=t.data})):this.failed=!0)}}return t.\u0275fac=function(e){return new(e||t)(n.Y36(c.qu),n.Y36(q),n.Y36(f.gz),n.Y36(f.F0))},t.\u0275cmp=n.Xpm({type:t,selectors:[["app-app-forgot-password"]],decls:3,vars:3,consts:[[4,"ngIf"],["class","app-body auth-login-sign","style","margin-top: 172px",4,"ngIf"],[2,"color","red","font-weight","600","text-align","center","padding","200px"],[2,"color","green","font-weight","600","text-align","center","padding","250px 50px 0px"],[1,"app-body","auth-login-sign",2,"margin-top","172px"],[1,"main","d-flex","align-items-center"],[1,"container"],[1,"row"],[1,"col-md-5","mx-auto"],[1,"card-group"],[1,"card","p-4"],[1,"card-body"],["autocomplete","off",1,"form",3,"formGroup","ngSubmit"],[2,"margin-bottom","20px"],[1,"input-group","mb-3"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"icon-lock","icons"],["type","password","placeholder","New Password","required","","formControlName","firstName",1,"form-control",3,"ngClass","click"],["class","invalid-feedback",4,"ngIf"],["type","password","placeholder","Confirm New Password","required","","formControlName","password",1,"form-control",3,"ngClass","click"],[1,"form-group"],["class","alert alert-danger","role","alert",4,"ngIf"],[1,"col-12"],[1,"btn","btn-primary","px-4"],[1,"invalid-feedback"],["role","alert",1,"alert","alert-danger"]],template:function(t,e){1&t&&(n.YNc(0,Ut,3,0,"div",0),n.YNc(1,_t,3,0,"div",0),n.YNc(2,Jt,29,10,"div",1)),2&t&&(n.Q6J("ngIf",e.TokenFailed),n.xp6(1),n.Q6J("ngIf",e.success),n.xp6(1),n.Q6J("ngIf",e.TokenPass))},directives:[s.O5,c.vK,c.JL,c.sg,c.Fj,c.Q7,c.JJ,c.u,s.mk],styles:[""]}),t})(),data:{title:"Reset Password"}},{path:"",component:H,data:{title:"Home"},children:[{path:"settings",loadChildren:()=>Promise.all([r.e(354),r.e(959)]).then(r.bind(r,28959)).then(t=>t.MasterModule)},{path:"pages",loadChildren:()=>Promise.all([r.e(354),r.e(592),r.e(962)]).then(r.bind(r,61962)).then(t=>t.PagesModule)},{path:"notifications",loadChildren:()=>Promise.all([r.e(592),r.e(50)]).then(r.bind(r,26050)).then(t=>t.NotificationsModule)}]},{path:"**",component:et}];let Ct=(()=>{class t{}return t.\u0275mod=n.oAB({type:t}),t.\u0275inj=n.cJS({factory:function(e){return new(e||t)},imports:[[f.Bz.forRoot(zt,{relativeLinkResolution:"legacy"}),a.JF],f.Bz]}),t})();var Yt=r(24706),Dt=r(58862),Lt=r(62897),Qt=r(49731),Gt=r(83711),Mt=r(49533),Bt=r(50022),Rt=r(21771),Vt=r(87188),Et=r(75874),Ot=r(59815);let Ht=(()=>{class t extends _.V{NewDoctor(t){return this.http.post(`${this.config.APIUrl}/Doctor?token=${localStorage.auth_token}`,t)}GetDoctorsList(t,e){return this.http.get(`${this.config.APIUrl}/Doctor?search=${e}&token=${localStorage.auth_token}`,{params:t})}GetDoctorDetail(t){return this.http.get(`${this.config.APIUrl}/Doctor/${t}?token=${localStorage.auth_token}`)}UpdateDoctor(t,e){return this.http.put(`${this.config.APIUrl}/Doctor/${t}?token=${localStorage.auth_token}`,e)}DeleteDoctor(t){return this.http.delete(`${this.config.APIUrl}/Doctor/${t}?token=${localStorage.auth_token}`)}}return t.\u0275fac=function(e){return Kt(e||t)},t.\u0275prov=n.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const Kt=n.n5z(Ht);var Xt=r(86207),Wt=r(79306),te=r(9499),ee=r(52831),re=r(65805),oe=r(57481),ne=r(5929),se=r(72945);let ie=(()=>{class t{}return t.\u0275mod=n.oAB({type:t,bootstrap:[$]}),t.\u0275inj=n.cJS({factory:function(e){return new(e||t)},providers:[{provide:s.S$,useClass:s.Do},{provide:a.TP,useClass:v,multi:!0},Z,u.uk,q,Xt.z,Bt.l,Wt.H,Ot.v,Et.s,Vt.a,Rt.J,b.i,Qt.V,Gt.N,Mt.C,H,Ht,oe.P,te.M,ee.D,ne.p,se.x],imports:[[re.kn.forRoot(),o.b2,i.PW,Ct,P.kI,P.so.forRoot(),P.JM,P.k3,P.el,l.Xd,y.mr.forRoot(),Dt.P4.forRoot(),Lt.m9,s.ez,m.Rh.forRoot(),u.QX,u.Eb.forRoot(),a.JF,c.u5,T.JX,c.UX,Yt._G,S.zk]]}),t})();r(24766).N.production&&(0,n.G48)(),o.q6().bootstrapModule(ie,{useJit:!0,preserveWhitespaces:!0}).catch(t=>console.log(t))},46700:function(t,e,r){var o={"./af":74511,"./af.js":74511,"./ar":46371,"./ar-dz":1586,"./ar-dz.js":1586,"./ar-kw":36674,"./ar-kw.js":36674,"./ar-ly":40234,"./ar-ly.js":40234,"./ar-ma":28280,"./ar-ma.js":28280,"./ar-ps":94784,"./ar-ps.js":94784,"./ar-sa":58961,"./ar-sa.js":58961,"./ar-tn":3579,"./ar-tn.js":3579,"./ar.js":46371,"./az":46308,"./az.js":46308,"./be":28212,"./be.js":28212,"./bg":36850,"./bg.js":36850,"./bm":47231,"./bm.js":47231,"./bn":40968,"./bn-bd":31848,"./bn-bd.js":31848,"./bn.js":40968,"./bo":20977,"./bo.js":20977,"./br":65466,"./br.js":65466,"./bs":38481,"./bs.js":38481,"./ca":81598,"./ca.js":81598,"./cs":69192,"./cs.js":69192,"./cv":2744,"./cv.js":2744,"./cy":52387,"./cy.js":52387,"./da":56056,"./da.js":56056,"./de":97809,"./de-at":15455,"./de-at.js":15455,"./de-ch":9938,"./de-ch.js":9938,"./de.js":97809,"./dv":16389,"./dv.js":16389,"./el":69062,"./el.js":69062,"./en-au":52038,"./en-au.js":52038,"./en-ca":25418,"./en-ca.js":25418,"./en-gb":74204,"./en-gb.js":74204,"./en-ie":99882,"./en-ie.js":99882,"./en-il":20385,"./en-il.js":20385,"./en-in":46715,"./en-in.js":46715,"./en-nz":31629,"./en-nz.js":31629,"./en-sg":79478,"./en-sg.js":79478,"./eo":26069,"./eo.js":26069,"./es":41550,"./es-do":1049,"./es-do.js":1049,"./es-mx":66750,"./es-mx.js":66750,"./es-us":57634,"./es-us.js":57634,"./es.js":41550,"./et":87469,"./et.js":87469,"./eu":52481,"./eu.js":52481,"./fa":75539,"./fa.js":75539,"./fi":84220,"./fi.js":84220,"./fil":25743,"./fil.js":25743,"./fo":83610,"./fo.js":83610,"./fr":16981,"./fr-ca":19572,"./fr-ca.js":19572,"./fr-ch":91067,"./fr-ch.js":91067,"./fr.js":16981,"./fy":9556,"./fy.js":9556,"./ga":94798,"./ga.js":94798,"./gd":89058,"./gd.js":89058,"./gl":22457,"./gl.js":22457,"./gom-deva":39161,"./gom-deva.js":39161,"./gom-latn":22332,"./gom-latn.js":22332,"./gu":85223,"./gu.js":85223,"./he":85940,"./he.js":85940,"./hi":73902,"./hi.js":73902,"./hr":23801,"./hr.js":23801,"./hu":3521,"./hu.js":3521,"./hy-am":58614,"./hy-am.js":58614,"./id":32087,"./id.js":32087,"./is":77382,"./is.js":77382,"./it":68176,"./it-ch":46489,"./it-ch.js":46489,"./it.js":68176,"./ja":86925,"./ja.js":86925,"./jv":55237,"./jv.js":55237,"./ka":82353,"./ka.js":82353,"./kk":97336,"./kk.js":97336,"./km":71526,"./km.js":71526,"./kn":25523,"./kn.js":25523,"./ko":35731,"./ko.js":35731,"./ku":10604,"./ku-kmr":62387,"./ku-kmr.js":62387,"./ku.js":10604,"./ky":67227,"./ky.js":67227,"./lb":28976,"./lb.js":28976,"./lo":7629,"./lo.js":7629,"./lt":77082,"./lt.js":77082,"./lv":89795,"./lv.js":89795,"./me":74718,"./me.js":74718,"./mi":84604,"./mi.js":84604,"./mk":692,"./mk.js":692,"./ml":54971,"./ml.js":54971,"./mn":65202,"./mn.js":65202,"./mr":94512,"./mr.js":94512,"./ms":94471,"./ms-my":72840,"./ms-my.js":72840,"./ms.js":94471,"./mt":97438,"./mt.js":97438,"./my":37323,"./my.js":37323,"./nb":98782,"./nb.js":98782,"./ne":52842,"./ne.js":52842,"./nl":23419,"./nl-be":83245,"./nl-be.js":83245,"./nl.js":23419,"./nn":60219,"./nn.js":60219,"./oc-lnc":92717,"./oc-lnc.js":92717,"./pa-in":8301,"./pa-in.js":8301,"./pl":12568,"./pl.js":12568,"./pt":91318,"./pt-br":96858,"./pt-br.js":96858,"./pt.js":91318,"./ro":30490,"./ro.js":30490,"./ru":38520,"./ru.js":38520,"./sd":84467,"./sd.js":84467,"./se":75774,"./se.js":75774,"./si":29352,"./si.js":29352,"./sk":9639,"./sk.js":9639,"./sl":69767,"./sl.js":69767,"./sq":83421,"./sq.js":83421,"./sr":55443,"./sr-cyrl":18228,"./sr-cyrl.js":18228,"./sr.js":55443,"./ss":46985,"./ss.js":46985,"./sv":3060,"./sv.js":3060,"./sw":45093,"./sw.js":45093,"./ta":66988,"./ta.js":66988,"./te":8146,"./te.js":8146,"./tet":40839,"./tet.js":40839,"./tg":47386,"./tg.js":47386,"./th":93378,"./th.js":93378,"./tk":61924,"./tk.js":61924,"./tl-ph":75053,"./tl-ph.js":75053,"./tlh":29913,"./tlh.js":29913,"./tr":56594,"./tr.js":56594,"./tzl":59439,"./tzl.js":59439,"./tzm":89711,"./tzm-latn":94166,"./tzm-latn.js":94166,"./tzm.js":89711,"./ug-cn":81509,"./ug-cn.js":81509,"./uk":1485,"./uk.js":1485,"./ur":98955,"./ur.js":98955,"./uz":36067,"./uz-latn":51527,"./uz-latn.js":51527,"./uz.js":36067,"./vi":98501,"./vi.js":98501,"./x-pseudo":67666,"./x-pseudo.js":67666,"./yo":94031,"./yo.js":94031,"./zh-cn":74660,"./zh-cn.js":74660,"./zh-hk":46383,"./zh-hk.js":46383,"./zh-mo":86961,"./zh-mo.js":86961,"./zh-tw":58771,"./zh-tw.js":58771};function n(t){var e=s(t);return r(e)}function s(t){if(!r.o(o,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return o[t]}n.keys=function(){return Object.keys(o)},n.resolve=s,t.exports=n,n.id=46700}},function(t){t.O(0,[736],function(){return t(t.s=82399)}),t.O()}]);