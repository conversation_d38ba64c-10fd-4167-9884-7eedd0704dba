<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        Availability </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-12 my-3">
            <div class="row">
              <div class="col-md-3" *ngIf="DoctorDrop">
                <label>Doctor : </label>
                <select id="select1" name="select1" class="form-control" style="width: 150px;"
                  (change)="Doctor($event.target.value)">
                  <option *ngFor="let role of Doctors" value="{{role._id}}">{{role.name}}</option>
                </select>
              </div>
              <div class="col-md-4">
                <label>Appointment Type</label>
                <select id="select1" name="select1" class="form-control" [(ngModel)]="appointmentType" (change)="onAppointmentTypeChange($event.target.value)">
                    <option value="IN-PERSON">In-Person</option>
                    <option value="VIDEO">Video</option>
                </select>
            </div>
              <div class="col-md-9">
              </div>
            </div>
            <div class="row set-weekly">
              <div class="col-lg-8" style="border-right: 1px solid #e5e5e5;">
                <h5 style="padding: 25px 0px;">Set your weekly hours</h5>
                <div class="row weekDays-selector">
                  <div class="col-lg-12 py-2">

                    <!-- Sunday -->
                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-sun" class="weekday" [(ngModel)]="containers.sun.flag"
                          (change)="point(containers.sun.flag,'sun')" />
                        <label for="weekday-sun">SUN</label>
                      </div>

                      <!-- <div > -->
                      <div class="col-lg-8" *ngIf="containers.sun.flag || (containers.sun && containers.sun.time && containers.sun.time.length > 0)">
                  
                        
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.sun.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'sun','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'sun','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('sun',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>
                      <!-- </div> -->

                      <div class="col-lg-8" *ngIf="!containers.sun.flag && (!containers.sun || !containers.sun.time || containers.sun.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('sun')"><i
                            class="nav-icon cil-plus"></i></a>

                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" checked=true
                                    readonly id="checkbox1" class="form-check-input"
                                    (click)="Selectall('sun')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday<input type="checkbox"
                                    [(ngModel)]='option1' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday<input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('sun');dropdown.hide();">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>
                    <hr>
                  </div>

                  <!-- Monday -->
                  <div class="col-lg-12 py-2">
                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-mon" class="weekday"
                          (change)="point(containers.mon.flag,'mon')" [(ngModel)]="containers.mon.flag" />
                        <label for="weekday-mon">MON</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.mon.flag || (containers.mon && containers.mon.time && containers.mon.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.mon.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'mon','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'mon','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('mon',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.mon.flag && (!containers.mon || !containers.mon.time || containers.mon.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('mon')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input" (click)="Selectall('sun')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday<input type="checkbox" id="checkbox2"
                                    class="form-check-input" checked=true readonly></label> </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday<input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label> </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('mon');dropdown.hide()">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>
                    <hr>
                  </div>

                  <!-- Tuesday -->
                  <div class="col-lg-12 py-2">

                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-tue" class="weekday"
                          (change)="point(containers.tue.flag,'tue')" [(ngModel)]="containers.tue.flag" />
                        <label for="weekday-tue">TUE</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.tue.flag || (containers.tue && containers.tue.time && containers.tue.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.tue.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'tue','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'tue','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('tue',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.tue.flag && (!containers.tue || !containers.tue.time || containers.tue.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('tue')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input" (click)="Selectall('sun')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday<input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label> </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday<input type="checkbox" id="checkbox3"
                                    class="form-check-input" checked=true readonly (click)="Selectall('tue')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('tue');dropdown.hide();">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>

                    <hr>
                  </div>

                  <!-- Wednesday -->
                  <div class="col-lg-12 py-2">

                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-wed" class="weekday" [(ngModel)]="containers.wed.flag"
                          (change)="point(containers.wed.flag,'wed')" />
                        <label for="weekday-wed">WED</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.wed.flag || (containers.wed && containers.wed.time && containers.wed.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.wed.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'wed','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'wed','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('wed',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.wed.flag && (!containers.wed || !containers.wed.time || containers.wed.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('wed')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input" (click)="Selectall('sun')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday<input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label> </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday<input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label> </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    id="checkbox4" class="form-check-input" checked=true readonly
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('wed');dropdown.hide()">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>

                    <hr>
                  </div>

                  <!-- Thursday -->
                  <div class="col-lg-12 py-2">

                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-thu" class="weekday"
                          (change)="point(containers.thu.flag,'thu')" [(ngModel)]="containers.thu.flag" />
                        <label for="weekday-thu">THU</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.thu.flag || (containers.thu && containers.thu.time && containers.thu.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.thu.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'thu','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'thu','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('thu',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.thu.flag && (!containers.thu || !containers.thu.time || containers.thu.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('thu')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input" (click)="Selectall('sun')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday <input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday <input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox" id="checkbox5"
                                    class="form-check-input" checked=true readonly
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('thu');dropdown.hide()">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>

                    <hr>
                  </div>

                  <!-- Friday -->
                  <div class="col-lg-12 py-2">

                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-fri" class="weekday"
                          (change)="point(containers.fri.flag,'fri')" [(ngModel)]="containers.fri.flag" />
                        <label for="weekday-fri">FRI</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.fri.flag || (containers.fri && containers.fri.time && containers.fri.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.fri.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'fri','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'fri','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('fri',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.fri.flag && (!containers.fri || !containers.fri.time || containers.fri.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('fri')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday<input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input" (click)="Selectall('sun')"></label>
                              </a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday <input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday <input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox" id="checkbox6"
                                    class="form-check-input" checked=true readonly
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox7" class="form-check-input"
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('fri');dropdown.hide()">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>

                    <hr>
                  </div>

                  <!-- Saturday -->
                  <div class="col-lg-12 py-2">

                    <div class="row">
                      <div class="col-lg-2 text-center">
                        <input type="checkbox" id="weekday-sat" class="weekday"
                          (change)="point(containers.sat.flag,'sat')" [(ngModel)]="containers.sat.flag" />
                        <label for="weekday-sat">SAT</label>
                      </div>

                      <div class="col-lg-8" *ngIf="containers.sat.flag || (containers.sat && containers.sat.time && containers.sat.time.length > 0)">
                        <!-- Add and Delete Time -->
                        <div class="time-slots-container">
                          <div class="time-slot-item" *ngFor="let container of containers.sat.time;let i = index" 
                               [style.display]="(!container.mode || container.mode === appointmentType) ? 'block' : 'none'"
                               style="display: flex;">
                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'sat','from')">
                              <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                                value="{{role.value}}">{{role.value}}</option>
                            </select>

                            <span style="font-weight: bold; font-size: 18px;">-</span>

                            <select id="select1" name="select1" class="form-control"
                              style="width: 120px;-webkit-appearance: none;"
                              (change)="searched($event.target.value,i,'sat','to')">
                              <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                                value="{{role.value}}">
                                {{role.value}}</option>
                            </select>

                            <a style="cursor: pointer;" class="Delete-selection" (click)="delete('sat',i)">
                              <i class="nav-icon cil-trash"></i>
                            </a>
                            
                            <div style="color: red; font-size: 12px;" *ngIf="container.error">{{container.message}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-8" *ngIf="!containers.sat.flag && (!containers.sat || !containers.sat.time || containers.sat.time.length === 0)">
                        <div class="row py-1">
                          <div class="col-lg-12">
                            <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                          </div>
                        </div>
                      </div>

                      <div class="col-lg-2">
                        <a style="cursor: pointer;" class="add-time pr-2" (click)="add('sat')"><i
                            class="nav-icon cil-plus"></i></a>
                        <span class="all-day-week" dropdown #dropdown="bs-dropdown" [insideClick]="true">
                          <a style="cursor: pointer;" class="weekly-days pr-2  dropdown-toggle" dropdownToggle><i
                              class="fa cil-clone"></i></a>
                          <ul *dropdownMenu class="dropdown-menu" role="menu">
                            <span>COPY Times To...</span>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox1" class="form-check-label">Sunday <input type="checkbox" id="checkbox1"
                                    [(ngModel)]='option1' class="form-check-input"
                                    (click)="Selectall('sun')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox2" class="form-check-label">Monday <input type="checkbox"
                                    [(ngModel)]='option2' id="checkbox2" class="form-check-input"
                                    (click)="Selectall('mon')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox3" class="form-check-label">Tuesday <input type="checkbox"
                                    [(ngModel)]='option3' id="checkbox3" class="form-check-input"
                                    (click)="Selectall('tue')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox4" class="form-check-label">Wednesday<input type="checkbox"
                                    [(ngModel)]='option4' id="checkbox4" class="form-check-input"
                                    (click)="Selectall('wed')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox5" class="form-check-label">Thursday<input type="checkbox"
                                    [(ngModel)]='option5' id="checkbox5" class="form-check-input"
                                    (click)="Selectall('thu')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox6" class="form-check-label">Friday<input type="checkbox"
                                    [(ngModel)]='option6' id="checkbox6" class="form-check-input"
                                    (click)="Selectall('fri')"></label></a></li>
                            <li role="menuitem"><a class="dropdown-item form-check" href="javascript:;"><label
                                  for="checkbox7" class="form-check-label">Saturday<input type="checkbox" id="checkbox7"
                                    class="form-check-input" checked=true readonly
                                    (click)="Selectall('sat')"></label></a></li>
                            <li><a class="btn btn-primary apply-btn" (click)="PushAll('sat');dropdown.hide()">Apply</a>
                            </li>
                          </ul>
                        </span>
                      </div>
                    </div>
                  </div>


                </div>
              </div>

              <!-- Date OverRide -->
              <div class="col-lg-4 p-0">
                <div class="date-overrides px-3">
                  <h5 style="padding: 25px 0px;">Add date overrides</h5>
                  <button type="button" class="btn btn-secondary" data-toggle="modal" (click)="myModal.show()" style="width: 90%;
                              margin: auto; display: block;">
                    Add date overrides
                  </button>
                </div>
                <div class="add-overrides" *ngFor="let container of containers.date.time | keyvalue; let i = index">
                  <div class="row m-0" (click)="myModal.show();modalClick(i)">

                    <span class="mb-0 col-lg-5 p-0"
                      *ngFor="let contval1 of container.value | keyvalue">{{contval1.key|date:'dd MMM yyyy'}}
                    </span>

                    <span class="mb-0 col-lg-5 p-0" *ngFor="let contval of container.value | keyvalue">
                      <span>
                        <span class="col-lg-6 p-0" style="text-align: right;"
                          *ngFor="let times of (contval.value) ">{{times.from}} - {{times.to}} ({{times.mode || 'IN-PERSON'}})
                        </span>
                      </span>

                      <span class="mb-0 col-lg-5 p-0" *ngIf="contval.value.length == 0">
                        <span class="col-lg-6 p-0" style="text-align: right;">Unavailable</span>
                      </span>
                    </span>

                    <span class="col-lg-1 p-0"><a style="cursor: pointer; padding-left: 10px;" class="Delete-selection"
                        (click)="DeleteDate(i)"><i class="nav-icon cil-trash" style="top:4px;"></i></a> </span>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
      <!--/.col-->
    </div>


    <!-- Exculding Date Model -->
    <div bsModal #myModal="bs-modal" class="modal fade override-popup" tabindex="-1" role="dialog"
      aria-labelledby="myModalLabel" aria-hidden="true" [config]="{'backdrop':'static', 'keyboard': false}">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Select the date(s) you want to assign specific hours</h5>

          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-lg-12" style="text-align: center;">
                <bs-datepicker-inline [bsValue]="dateSelected" (bsValueChange)="onValueChange($event)"
                  [dateCustomClasses]="selectedClass" [bsConfig]="{ showWeekNumbers:false }" [minDate]="minDate" [datesDisabled]="disabledDates">
                </bs-datepicker-inline>
              </div>

              <div class="col-lg-12" *ngIf="showBottom">
                <div class="Available-hours p-4">
                  <h6>What hours are you available?</h6>



                  <div class="row py-1">
                    <div class="col-lg-10" *ngIf="Field">
                      <div class="row py-1" *ngFor="let container of Selectedtime;let i = index">
                        <div class="col-lg-4 text-center">
                          <select id="select1" name="select1" class="form-control"
                            style="width: 100px;-webkit-appearance: none;"
                            (change)="Datesearched($event.target.value,i,'from')">
                            <option *ngFor="let role of times" [selected]="role.value === container.from? true: false"
                              value="{{role.value}}">{{role.value}}</option>
                          </select>
                        </div>

                        <div class="col-lg-1 text-center center-icon">
                          -
                        </div>

                        <div class="col-lg-4 text-center">
                          <select id="select1" name="select1" class="form-control"
                            style="width: 100px;-webkit-appearance: none;"
                            (change)="Datesearched($event.target.value,i,'to')">
                            <option *ngFor="let role of times" [selected]="role.value === container.to? true: false"
                              value="{{role.value}}">
                              {{role.value}}</option>
                          </select>
                        </div>

                        <div class="col-lg-3">
                          <a style="cursor: pointer;" class="Delete-selection"><i class="nav-icon cil-trash"
                              (click)="DeleteTime(i)"></i></a>
                        </div>

                        <div style="padding-left: 16px;color: red;" *ngIf="container.error">{{container.message1}}</div>
                      </div>
                    </div>

                    <div class="col-lg-10" *ngIf="Selectedtime.length==0">
                      <div class="row py-1">
                        <div class="col-lg-12">
                          <p style="color: rgba(77, 80, 85, 0.6);font-size: 16px;">Unavailable</p>
                        </div>
                      </div>
                    </div>

                    <div class="col-lg-2 text-center">
                      <a style="cursor: pointer;" class="add-time pr-2" (click)="AddOver()"><i
                          class="nav-icon cil-plus"></i></a>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="myModal.hide();clear();">Close</button>
            <button type="button" class="btn btn-primary" (click)="myModal.hide();OnSubmit()">Save</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->