import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
// import { TokenStorageService } from '../services/token-storage.service';
import { Configuration } from '../../../configuration';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class Loginservice extends Api {

    public basUrl = this.getBaseUrl();

    // constructor(private http: HttpClient, private tokenStorage: TokenStorageService, private config: Configuration) { }

    //Admin login 
    AdminLogin(data): Observable<any> {
        // const token_key = this.tokenStorage.getToken();
        return this.http.post(this.config.APIUrl1 + '/signin', data);
    }

    //forgotpassword 
    ForgotPassword(data): Observable<any> {
        // const token_key = this.tokenStorage.getToken();
        return this.http.post(`${this.config.APIUrl1}/forgotpassword`, data);
    }

    //Token Checking in Backend at admin 
    PasswordToken(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl2}/emp/reset_password?token=${params.token}`);
    }

    //Change password by using new password and conirm password
    ChangePassword(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl2}/emp/reset_password`, data);
    }

    //Token Checking in Backend at user 
    userPasswordToken(params: any): Observable<any> {
        console.log("params-->", params)
        return this.http.get(`${this.config.APIUrl2}/reset_password?token=${params.token}`);
    }

    //Change password by using new password and conirm password at user
    userChangePassword(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl2}/reset_password`, data);
    }

    //Get User Details by using user id
    GetUser(Id: any, data): Observable<any> {
        // const token_key = this.tokenStorage.getToken();
        return this.http.post(`${this.config.APIUrl}/check_password/${Id}`, data);
    }

    //Edit or Update Employee Details
    EditEmployeeDetail(id: any, params: any): Observable<any> {
        // const token_key = this.tokenStorage.getToken();
        return this.http.put(`${this.config.APIUrl}/employee/${id}`, params);
    }
}