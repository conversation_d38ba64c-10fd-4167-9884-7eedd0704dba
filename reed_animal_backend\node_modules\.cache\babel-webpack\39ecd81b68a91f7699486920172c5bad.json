{"ast": null, "code": "/**\n * A specialized version of `_.lastIndexOf` which performs strict equality\n * comparisons of values, i.e. `===`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction strictLastIndexOf(array, value, fromIndex) {\n  var index = fromIndex + 1;\n\n  while (index--) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n\n  return index;\n}\n\nexport default strictLastIndexOf;", "map": null, "metadata": {}, "sourceType": "module"}