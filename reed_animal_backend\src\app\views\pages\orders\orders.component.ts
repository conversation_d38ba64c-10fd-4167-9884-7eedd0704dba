import { Component, OnInit, HostListener, ViewChild } from '@angular/core';
import { OrderService } from '../../services/order.service'
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import * as moment from 'moment';

@Component({
  selector: 'app-orders',
  templateUrl: './orders.component.html',
  styleUrls: ['./orders.component.scss']
})
export class OrdersComponent implements OnInit {

  @ViewChild('removeModal') public removeModal: ModalDirective;

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    // console.log('handleKeyboardEvent', event.altKey, event.shiftKey, event.key)
    if (event.altKey && event.shiftKey && event.key === 'D')
      this.removeModal.show();
  }
  public orders = [];
  public TotalAmount = 0;
  public TotalOrder = 0;
  public TodayAmount = 0;
  public TodayOrder = 0;
  public page = 1;
  public count = 0;
  public search = '';
  public name = '';
  public type = '';
  public Add = true;
  public Edit = true;
  public Delete = true;
  public from_date: Date = new Date('2021-09-01T00:00:00z');
  public to_date: Date = new Date();
  constructor(private orderService: OrderService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {

    const module = this.tokenStorage.getModule();
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // console.log(module)
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Orders") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.GetOrderLists();
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    // skip['value'] = this.value
    // skip['field'] = this.field
    return skip;
  }

  //Get All Order List
  GetOrderLists(): void {
    let params = {
      skip: (this.page - 1) * 10,
      search: this.name,
      to_date: moment(this.to_date).utc().toISOString(),
      from_date: moment(this.from_date).utc().toISOString()
    };
    this.orderService.GetOrderList(params)
      .subscribe((res: any) => {
        this.orders = res.data;
        this.count = res.count;
        this.TotalAmount = res.totalAmount.length>0?res.totalAmount[0].totalAmount:0;
        this.TodayOrder = res.todayOrder;
        this.TotalOrder = res.totalOrder;
        this.TodayAmount = res.todayAmount.length>0?res.todayAmount[0].totalAmount:0;
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    console.log(this.page);
    this.GetOrderLists();
  }

  //Delete All Order list Pop up
  DeleteAll() {
    this.orderService.DeleteAllOrders()
      .subscribe((res: any) => {
        this.page = 1;
        this.GetOrderLists();
        this.removeModal.hide();
      })
  }
}
