{"ast": null, "code": "import baseRandom from './_baseRandom.js';\n/**\n * A specialized version of `_.shuffle` which mutates and sets the size of `array`.\n *\n * @private\n * @param {Array} array The array to shuffle.\n * @param {number} [size=array.length] The size of `array`.\n * @returns {Array} Returns `array`.\n */\n\nfunction shuffleSelf(array, size) {\n  var index = -1,\n      length = array.length,\n      lastIndex = length - 1;\n  size = size === undefined ? length : size;\n\n  while (++index < size) {\n    var rand = baseRandom(index, lastIndex),\n        value = array[rand];\n    array[rand] = array[index];\n    array[index] = value;\n  }\n\n  array.length = size;\n  return array;\n}\n\nexport default shuffleSelf;", "map": null, "metadata": {}, "sourceType": "module"}