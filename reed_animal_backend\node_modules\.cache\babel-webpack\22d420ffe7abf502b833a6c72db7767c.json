{"ast": null, "code": "import baseWrapperValue from './_baseWrapperValue.js';\nimport getView from './_getView.js';\nimport isArray from './isArray.js';\n/** Used to indicate the type of lazy iteratees. */\n\nvar LAZY_FILTER_FLAG = 1,\n    LAZY_MAP_FLAG = 2;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMin = Math.min;\n/**\n * Extracts the unwrapped value from its lazy wrapper.\n *\n * @private\n * @name value\n * @memberOf LazyWrapper\n * @returns {*} Returns the unwrapped value.\n */\n\nfunction lazyValue() {\n  var array = this.__wrapped__.value(),\n      dir = this.__dir__,\n      isArr = isArray(array),\n      isRight = dir < 0,\n      arrLength = isArr ? array.length : 0,\n      view = getView(0, arrLength, this.__views__),\n      start = view.start,\n      end = view.end,\n      length = end - start,\n      index = isRight ? end : start - 1,\n      iteratees = this.__iteratees__,\n      iterLength = iteratees.length,\n      resIndex = 0,\n      takeCount = nativeMin(length, this.__takeCount__);\n\n  if (!isArr || !isRight && arrLength == length && takeCount == length) {\n    return baseWrapperValue(array, this.__actions__);\n  }\n\n  var result = [];\n\n  outer: while (length-- && resIndex < takeCount) {\n    index += dir;\n    var iterIndex = -1,\n        value = array[index];\n\n    while (++iterIndex < iterLength) {\n      var data = iteratees[iterIndex],\n          iteratee = data.iteratee,\n          type = data.type,\n          computed = iteratee(value);\n\n      if (type == LAZY_MAP_FLAG) {\n        value = computed;\n      } else if (!computed) {\n        if (type == LAZY_FILTER_FLAG) {\n          continue outer;\n        } else {\n          break outer;\n        }\n      }\n    }\n\n    result[resIndex++] = value;\n  }\n\n  return result;\n}\n\nexport default lazyValue;", "map": null, "metadata": {}, "sourceType": "module"}