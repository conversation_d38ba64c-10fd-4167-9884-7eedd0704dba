(function () {
  "use strict";

  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

  function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

  function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

  (self["webpackChunkDr_Reed_Admin_Panel"] = self["webpackChunkDr_Reed_Admin_Panel"] || []).push([[246], {
    /***/
    19148: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "AnimalTypeComponent": function AnimalTypeComponent() {
          return (
            /* binding */
            _AnimalTypeComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_animal_type_services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/animal_type.services */
      50022);
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["primaryModal"];
      var _c1 = ["AddModal"];
      var _c2 = ["removeModal"];

      function AnimalTypeComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 38);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r9);

            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

            return _r3.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Species ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_th_20_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Status");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_tr_24_td_3_Template(rf, ctx) {
        if (rf & 1) {
          var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "label", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "input", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function AnimalTypeComponent_tr_24_td_3_Template_input_change_2_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r16);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r14.changed(user_r10.status, user_r10._id);
          })("ngModelChange", function AnimalTypeComponent_tr_24_td_3_Template_input_ngModelChange_2_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r16);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            return user_r10.status = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "span", 42);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r10.status);
        }
      }

      function AnimalTypeComponent_tr_24_a_5_Template(rf, ctx) {
        if (rf & 1) {
          var _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_tr_24_a_5_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](50);

            _r5.show();

            return ctx_r20.GetType(user_r10._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 44);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_tr_24_a_6_Template(rf, ctx) {
        if (rf & 1) {
          var _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_tr_24_a_6_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r24);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](71);

            _r7.show();

            return ctx_r23.GetType(user_r10._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_tr_24_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, AnimalTypeComponent_tr_24_td_3_Template, 4, 1, "td", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, AnimalTypeComponent_tr_24_a_5_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](6, AnimalTypeComponent_tr_24_a_6_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r10 = ctx.$implicit;

          var ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r10.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Delete);
        }
      }

      function AnimalTypeComponent_div_43_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Name is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_div_43_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, AnimalTypeComponent_div_43_div_1_Template, 2, 0, "div", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r4.f.name.errors.required);
        }
      }

      function AnimalTypeComponent_div_64_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Name is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function AnimalTypeComponent_div_64_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, AnimalTypeComponent_div_64_div_1_Template, 2, 0, "div", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.name.errors.required);
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5(a0) {
        return {
          "is-invalid": a0
        };
      };

      var _AnimalTypeComponent = /*#__PURE__*/function () {
        var AnimalTypeComponent = /*#__PURE__*/function () {
          function AnimalTypeComponent(animalservice, route, router, tokenStorage, formBuilder, Permission, EmployeeService) {
            _classCallCheck(this, AnimalTypeComponent);

            this.animalservice = animalservice;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.formBuilder = formBuilder;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.isFormReady = false;
            this.submitted = false;
            this.types = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.type = {};
            this.nameFailed = false;
            this.modal = true;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
          }

          _createClass(AnimalTypeComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
              this.SignForm();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.type = {};
              this.loginForm.reset();
              this.nameFailed = false;
              this.isFormReady = false;
              this.submitted = false;
            }
          }, {
            key: "getfocus",
            value: function getfocus() {
              this.nameFailed = false;
            } //token verified type

          }, {
            key: "tokens",
            value: function tokens() {
              var _this = this;

              var Role = this.tokenStorage.getUser();
              this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                // console.log(res)
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].module_name == "Species") {
                    _this.Add = res.data[i].add;
                    _this.Edit = res.data[i].edit;
                    _this.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                  }
                }
              });
              this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                // console.log(res.data[0].status)
                if (res.data.status == false) {
                  _this.tokenStorage.signOut();
                } else {
                  _this.typeLists();
                }
              });
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              return skip;
            } //Get All type List

          }, {
            key: "typeLists",
            value: function typeLists() {
              var _this2 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.animalservice.GetTypesList(skip, this.name).subscribe(function (res) {
                _this2.types = res.data;
                _this2.count = res.count; // console.log(this.types);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.typeLists();
            } //Edit or update type 

          }, {
            key: "GetType",
            value: function GetType(id) {
              var _this3 = this;

              // console.log('id-->', id);
              this.animalservice.GetTypeDetail(id).subscribe(function (res) {
                _this3.type = res.data[0];

                _this3.f.name.setValue(res.data[0].name, {
                  onlySelf: true
                }); // console.log(res.data)

              });
            }
          }, {
            key: "EditType",
            value: function EditType(id) {
              var _this4 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.name
                };
                this.animalservice.UpdateType(id, data).subscribe(function (res) {
                  // console.log('res-->', res);
                  _this4.primaryModal.hide();

                  _this4.type = {};

                  _this4.typeLists();
                });
              }
            } //Status ON & OFF

          }, {
            key: "changed",
            value: function changed(active, id) {
              var data = {
                status: active
              };
              this.animalservice.UpdateType(id, data).subscribe(function (res) {// console.log('res-->', res);
              });
            }
          }, {
            key: "SignForm",
            value: function SignForm() {
              this.loginForm = this.formBuilder.group({
                name: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required]]
              });
            }
          }, {
            key: "f",
            get: function get() {
              return this.loginForm.controls;
            } //Add new type

          }, {
            key: "AddType",
            value: function AddType() {
              var _this5 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.name
                };
                this.animalservice.Newtype(data).subscribe(function (res) {
                  // this.type = {};
                  _this5.AddModal.hide();

                  _this5.clear(); // this.nameFailed = false;
                  // console.log('new-->')


                  _this5.typeLists();
                });
                this.nameFailed = true;
              }
            } //Delete type using id

          }, {
            key: "DeleteType",
            value: function DeleteType(id) {
              var _this6 = this;

              // console.log('id-->', id)
              this.animalservice.Deletetype(id).subscribe(function (res) {
                // console.log('res-->', res)
                _this6.removeModal.hide();

                _this6.type = {};

                _this6.typeLists();
              });
            }
          }]);

          return AnimalTypeComponent;
        }();

        AnimalTypeComponent.ɵfac = function AnimalTypeComponent_Factory(t) {
          return new (t || AnimalTypeComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_animal_type_services__WEBPACK_IMPORTED_MODULE_0__.AnimalTypeService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_3__.Employeeservice));
        };

        AnimalTypeComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: AnimalTypeComponent,
          selectors: [["app-animal-type"]],
          viewQuery: function AnimalTypeComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.AddModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.removeModal = _t.first);
            }
          },
          decls: 87,
          vars: 26,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text", 3, "click"], [1, "fa", "fa-search"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngIf"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "Module", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["AddModal", "bs-modal"], ["Module", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "col-sm-12"], ["autocomplete", "off", 1, "form", 3, "formGroup"], [1, "form-group"], ["for", "firstName"], ["type", "text", "placeholder", "e.g. Dog, Cat", "formControlName", "name", 1, "form-control", 3, "ngClass"], ["class", "invalid-feedback", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["primaryModal", "bs-modal"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["removeModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], [1, "switch"], ["type", "checkbox", "checked", "user.status", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [1, "invalid-feedback"]],
          template: function AnimalTypeComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Species ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, AnimalTypeComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_span_click_12_listener() {
                return ctx.typeLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function AnimalTypeComponent_Template_input_input_14_listener() {
                return ctx.typeLists();
              })("ngModelChange", function AnimalTypeComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Species Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](20, AnimalTypeComponent_th_20_Template, 2, 0, "th", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](22, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](24, AnimalTypeComponent_tr_24_Template, 7, 4, "tr", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](25, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "pagination-controls", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function AnimalTypeComponent_Template_pagination_controls_pageChange_27_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "div", 17, 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, "Add Species");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "form", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "div", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](40, "label", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](41, "Species Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](42, "input", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](43, AnimalTypeComponent_div_43_Template, 2, 1, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_45_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

                _r3.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](46, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](47, "button", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_47_listener() {
                return ctx.AddType();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](48, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "div", 17, 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](54, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](55, "Edit Species");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](56, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](59, "form", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](60, "div", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "label", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](62, "Species Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](63, "input", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](64, AnimalTypeComponent_div_64_Template, 2, 1, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](66, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_66_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](50);

                _r5.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](67, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](68, "button", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_68_listener() {
                return ctx.EditType(ctx.type._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](69, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](70, "div", 34, 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](72, "div", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](73, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](74, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](76, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](77, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](78, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](79, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](81, "Do you want to delete this Species?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](82, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](83, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_83_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](71);

                _r7.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](84, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](85, "button", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function AnimalTypeComponent_Template_button_click_85_listener() {
                return ctx.DeleteType(ctx.type._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](86, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Edit);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](25, 13, ctx.types, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](16, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](19, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](20, _c5, ctx.submitted && ctx.f.name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](22, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](23, _c5, ctx.submitted && ctx.f.name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](25, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe],
          styles: [""]
        });
        return AnimalTypeComponent;
      }();
      /***/

    },

    /***/
    8816: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "BreedingComponent": function BreedingComponent() {
          return (
            /* binding */
            _BreedingComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_breeding_services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/breeding.services */
      74143);
      /* harmony import */


      var _services_animal_type_services__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/animal_type.services */
      50022);
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["primaryModal"];
      var _c1 = ["deleteModal"];
      var _c2 = ["removeModal"];

      function BreedingComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 53);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r18);

            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

            var _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](38);

            return _r2.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Add Breed ");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_tr_33_a_10_Template(rf, ctx) {
        if (rf & 1) {
          var _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 55);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_tr_33_a_10_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r24);

            var i_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().index;

            var ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

            var _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](81);

            _r9.show();

            return ctx_r23.GetBreeding(i_r20);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "span", 56);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 57);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_tr_33_a_11_Template(rf, ctx) {
        if (rf & 1) {
          var _r27 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 55);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_tr_33_a_11_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r27);

            var user_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;

            var ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

            var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](123);

            _r16.show();

            return ctx_r26.GetBreeding(user_r19._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "span", 58);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 59);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_tr_33_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, BreedingComponent_tr_33_a_10_Template, 4, 0, "a", 54);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](11, BreedingComponent_tr_33_a_11_Template, 4, 0, "a", 54);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r19 = ctx.$implicit;

          var ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r19.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r19.code);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r19.size);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r19.type);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r1.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r1.Delete);
        }
      }

      function BreedingComponent_option_55_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 60);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var role_r29 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", role_r29.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](role_r29.name);
        }
      }

      function BreedingComponent_div_56_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Species is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_56_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_56_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r4.f.species.errors.required);
        }
      }

      function BreedingComponent_option_63_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 60);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var role_r31 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", role_r31);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](role_r31);
        }
      }

      function BreedingComponent_div_64_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Size is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_64_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_64_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r6.f.size.errors.required);
        }
      }

      function BreedingComponent_div_69_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Breed name is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_69_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_69_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r7.f.breed.errors.required);
        }
      }

      function BreedingComponent_div_74_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Breed code is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_74_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_74_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r8.f.code.errors.required);
        }
      }

      function BreedingComponent_option_97_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 60);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var role_r35 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", role_r35.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](role_r35.name);
        }
      }

      function BreedingComponent_div_98_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Species is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_98_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_98_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r11.f.species.errors.required);
        }
      }

      function BreedingComponent_option_105_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 60);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var role_r37 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", role_r37);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](role_r37);
        }
      }

      function BreedingComponent_div_106_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Size is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_106_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_106_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r13.f.size.errors.required);
        }
      }

      function BreedingComponent_div_111_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Breed name is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_111_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_111_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r14.f.breed.errors.required);
        }
      }

      function BreedingComponent_div_116_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Breed code is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
      }

      function BreedingComponent_div_116_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, BreedingComponent_div_116_div_1_Template, 2, 0, "div", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r15.f.code.errors.required);
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5(a0) {
        return {
          "is-invalid": a0
        };
      };

      var _BreedingComponent = /*#__PURE__*/function () {
        var BreedingComponent = /*#__PURE__*/function () {
          function BreedingComponent(BreedingService, AnimaltypeService, route, router, tokenStorage, Permission, EmployeeService, formBuilder) {
            _classCallCheck(this, BreedingComponent);

            this.BreedingService = BreedingService;
            this.AnimaltypeService = AnimaltypeService;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.formBuilder = formBuilder;
            this.Types = [];
            this.Breedings = [];
            this.Size = ['Toy', 'Small', 'Medium', 'Large', 'Giant'];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.ddlFileId = '';
            this.value1 = '';
            this.breeding = {
              _id: "",
              name: "",
              status: false,
              type: "",
              size: '',
              code: ''
            };
            this.isLoginFailed = false;
            this.isFormReady = false;
            this.submitted = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
            this.sort = false;
            this.value = 1;
            this.field = "name";
            this.typeFailed = false;
          }

          _createClass(BreedingComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
              this.SignForm();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.loginForm.reset();
              this.submitted = false;
              this.Types = [];
              this.GetTypeLists();
              this.breeding = {
                _id: "",
                name: "",
                status: false,
                type: "",
                size: '',
                code: ''
              };
            } //token verified module

          }, {
            key: "tokens",
            value: function tokens() {
              var _this7 = this;

              var key = this.tokenStorage.getToken();
              var Role = this.tokenStorage.getUser();
              this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                // console.log(res)
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].module_name == "Breed") {
                    _this7.Add = res.data[i].add;
                    _this7.Edit = res.data[i].edit;
                    _this7.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                  }
                }
              });
              this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                // console.log(res.data[0].status)
                if (res.data.status == false) {
                  _this7.tokenStorage.signOut();
                } else {
                  _this7.GetTypeLists();
                }
              });
            } //Get All Type List

          }, {
            key: "GetTypeLists",
            value: function GetTypeLists() {
              var _this8 = this;

              this.BreedingService.GetTypesList().subscribe(function (res) {
                _this8.Types = res.data; // console.log(this.Types);

                _this8.GetBreedingLists();
              });
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              skip['value'] = this.value;
              skip['field'] = this.field;
              return skip;
            } //Get All Type List

          }, {
            key: "GetBreedingLists",
            value: function GetBreedingLists() {
              var _this9 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.BreedingService.GetBreedingsList(skip, this.name).subscribe(function (res) {
                _this9.Breedings = res.data;
                var bre = res.data;
                _this9.count = res.count;
                return bre; // console.log(this.Breedings);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.GetBreedingLists();
            } //

          }, {
            key: "searched",
            value: function searched(id, param) {
              this.breeding[param] = id; // console.log('id-->', param, id)
            }
          }, {
            key: "SignForm",
            value: function SignForm() {
              this.loginForm = this.formBuilder.group({
                species: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required]],
                size: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required]],
                breed: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required]],
                code: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required]]
              });
            }
          }, {
            key: "f",
            get: function get() {
              return this.loginForm.controls;
            } //Add New Breeding

          }, {
            key: "AddBreeding",
            value: function AddBreeding() {
              var _this10 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.breed,
                  type: this.loginForm.value.species,
                  code: this.loginForm.value.code,
                  size: this.loginForm.value.size
                }; // console.log('new-->', data)

                this.BreedingService.NewBreeding(data).subscribe(function (res) {
                  _this10.submitted = false;

                  _this10.loginForm.reset();

                  _this10.primaryModal.hide(); // console.log('new-->', res)


                  _this10.GetTypeLists();

                  _this10.GetBreedingLists();
                });
              }
            } //Update or Edit Breeding

          }, {
            key: "GetBreeding",
            value: function GetBreeding(id) {
              console.log("id", id);
              this.delete_id = id;
              this.breeding = this.Breedings[id]; // console.log(this.Breedings[id])

              this.f.species.setValue(this.Breedings[id].type, {
                onlySelf: true
              });
              this.f.size.setValue(this.Breedings[id].size, {
                onlySelf: true
              });
              this.f.breed.setValue(this.Breedings[id].name, {
                onlySelf: true
              });
              this.f.code.setValue(this.Breedings[id].code, {
                onlySelf: true
              });
            }
          }, {
            key: "EditBreeding",
            value: function EditBreeding(id) {
              var _this11 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.breed,
                  type: this.loginForm.value.species,
                  code: this.loginForm.value.code,
                  size: this.loginForm.value.size
                };
                this.BreedingService.UpdateBreeding(id, data).subscribe(function (res) {
                  // console.log('res-->', res);
                  _this11.clear();

                  _this11.deleteModal.hide();

                  _this11.GetBreedingLists();
                });
              }
            } //Delete Breeding

          }, {
            key: "DeleteBreeding",
            value: function DeleteBreeding() {
              var _this12 = this;

              // console.log('id-->', id)
              this.BreedingService.DeleteBreeding(this.delete_id).subscribe(function (res) {
                // console.log('res-->', res)
                _this12.removeModal.hide();

                _this12.GetBreedingLists();
              });
            } //ON or OFF value in table

          }, {
            key: "changed",
            value: function changed(param, id) {
              // console.log(param, id)
              var data = {
                status: param
              };
              this.BreedingService.UpdateBreeding(id, data).subscribe(function (res) {// console.log(res)
              });
            } //Field name sorted

          }, {
            key: "Field",
            value: function Field(param) {
              // console.log(this.Id)
              if (this.sort == true) {
                // console.log('hi', param)
                this.sort = false, this.field = param;
                this.value = -1;
                this.GetBreedingLists();
              } else {
                // console.log('hi1', param)
                this.sort = true;
                this.field = param;
                this.value = 1;
                this.GetBreedingLists();
              }
            }
          }]);

          return BreedingComponent;
        }();

        BreedingComponent.ɵfac = function BreedingComponent_Factory(t) {
          return new (t || BreedingComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_breeding_services__WEBPACK_IMPORTED_MODULE_0__.BreedingService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_animal_type_services__WEBPACK_IMPORTED_MODULE_1__.AnimalTypeService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_2__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_3__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_4__.Employeeservice), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormBuilder));
        };

        BreedingComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
          type: BreedingComponent,
          selectors: [["app-breeding"]],
          viewQuery: function BreedingComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.deleteModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.removeModal = _t.first);
            }
          },
          decls: 139,
          vars: 53,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text", 3, "Change"], [1, "fa", "fa-search"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "keydown.enter", "input", "ngModelChange"], [1, "table", "table-striped"], ["checked", "sort", 1, "fa", "fa-sort", 3, "click"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["primaryModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], ["ngNativeValidate", ""], [1, "col-sm-12"], ["autocomplete", "off", 1, "form", 3, "formGroup"], [1, "form-group"], ["for", "species"], ["id", "species", "name", "species", "formControlName", "species", 1, "form-control", 3, "ngClass"], ["value", "", "disabled", "", "selected", "", "hidden", ""], [3, "value", 4, "ngFor", "ngForOf"], ["class", "invalid-feedback", 4, "ngIf"], ["for", "size"], ["id", "size", "name", "size", "formControlName", "size", 1, "form-control", 3, "ngClass"], ["for", "breed"], ["type", "text", "id", "breed", "placeholder", "e.g. Beagle, Boxer", "formControlName", "breed", 1, "form-control", 3, "ngClass"], ["for", "code"], ["type", "text", "id", "code", "placeholder", "e.g. BEAGLE, BOXER", "formControlName", "code", 1, "form-control", 3, "ngClass"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "click"], ["deleteModal", "bs-modal"], ["name", "species", "formControlName", "species", 1, "form-control", 3, "ngClass"], ["hidden", ""], ["name", "size", "formControlName", "size", 1, "form-control", 3, "ngClass"], ["type", "text", "placeholder", "e.g. Beagle, Boxer", "formControlName", "breed", 1, "form-control", 3, "ngClass"], ["type", "text", "placeholder", "e.g. BEAGLE, BOXER", "formControlName", "code", 1, "form-control", 3, "ngClass"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["removeModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [3, "value"], [1, "invalid-feedback"], [4, "ngIf"]],
          template: function BreedingComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r41 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " Breed ");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, BreedingComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("Change", function BreedingComponent_Template_span_Change_12_listener() {
                return ctx.GetBreedingLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("keydown.enter", function BreedingComponent_Template_input_keydown_enter_14_listener() {
                return ctx.GetBreedingLists();
              })("input", function BreedingComponent_Template_input_input_14_listener() {
                return ctx.GetBreedingLists();
              })("ngModelChange", function BreedingComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](19, "Breed Name ");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](20, "i", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_i_click_20_listener() {
                return ctx.Field("name");
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](22, "Breed Code ");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "i", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_i_click_23_listener() {
                return ctx.Field("code");
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](24, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](25, "Size ");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](26, "i", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_i_click_26_listener() {
                return ctx.Field("size");
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](27, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](28, "Species ");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](29, "i", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_i_click_29_listener() {
                return ctx.Field("type");
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](30, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](31, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](32, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](33, BreedingComponent_tr_33_Template, 12, 6, "tr", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](34, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](35, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](36, "pagination-controls", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("pageChange", function BreedingComponent_Template_pagination_controls_pageChange_36_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](37, "div", 17, 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](39, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](40, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](41, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](42, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](43, "Add Breed");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](44, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](45, "form", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](46, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](47, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](48, "form", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](49, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](50, "label", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](51, "Species*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](52, "select", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](53, "option", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](54, "--Select--");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](55, BreedingComponent_option_55_Template, 2, 2, "option", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](56, BreedingComponent_div_56_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](57, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](58, "label", 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](59, "Size*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](60, "select", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](61, "option", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](62, "--Select--");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](63, BreedingComponent_option_63_Template, 2, 2, "option", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](64, BreedingComponent_div_64_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](65, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](66, "label", 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](67, "Breed Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](68, "input", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](69, BreedingComponent_div_69_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](70, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](71, "label", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](72, "Breed Code*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](73, "input", 38);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](74, BreedingComponent_div_74_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](75, "div", 39);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](76, "button", 40);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_76_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r41);

                var _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](38);

                _r2.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](77, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](78, "button", 41);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_78_listener() {
                return ctx.AddBreeding();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](79, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](80, "div", 17, 42);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](82, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](83, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](84, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](85, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](86, "Edit Breed");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](87, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](88, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](89, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](90, "form", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](91, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](92, "label", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](93, "Species*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](94, "select", 43);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](95, "option", 44);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](96, "--Select--");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](97, BreedingComponent_option_97_Template, 2, 2, "option", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](98, BreedingComponent_div_98_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](99, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](100, "label", 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](101, "Size*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](102, "select", 45);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](103, "option", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](104, "--Select--");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](105, BreedingComponent_option_105_Template, 2, 2, "option", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](106, BreedingComponent_div_106_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](107, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](108, "label", 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](109, "Breed Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](110, "input", 46);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](111, BreedingComponent_div_111_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](112, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](113, "label", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](114, "Breed Code*");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](115, "input", 47);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](116, BreedingComponent_div_116_Template, 2, 1, "div", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](117, "div", 39);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](118, "button", 40);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_118_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r41);

                var _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](81);

                _r9.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](119, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](120, "button", 48);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_120_listener() {
                return ctx.EditBreeding(ctx.breeding._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](121, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](122, "div", 49, 50);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](124, "div", 51);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](125, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](126, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](127, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](128, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](129, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](130, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](131, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](132, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](133, "Do you want to delete this Breed?");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](134, "div", 39);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](135, "button", 40);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_135_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r41);

                var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](123);

                _r16.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](136, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](137, "button", 52);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function BreedingComponent_Template_button_click_137_listener() {
                return ctx.DeleteBreeding();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](138, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](19);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind2"](34, 28, ctx.Breedings, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction2"](31, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](34, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](11);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](35, _c5, ctx.submitted && ctx.f.species.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.Types);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.species.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](37, _c5, ctx.submitted && ctx.f.size.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.Size);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.size.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](39, _c5, ctx.submitted && ctx.f.breed.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.breed.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](41, _c5, ctx.submitted && ctx.f.code.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.code.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](43, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](10);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](44, _c5, ctx.submitted && ctx.f.species.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.Types);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.species.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](46, _c5, ctx.submitted && ctx.f.size.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.Size);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.size.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](48, _c5, ctx.submitted && ctx.f.breed.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.breed.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](50, _c5, ctx.submitted && ctx.f.code.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.code.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](52, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_5__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_10__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgForm, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgClass, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵangular_packages_forms_forms_x"]],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_5__.PaginatePipe],
          styles: ["#select1[_ngcontent-%COMP%] {\n  width: 100%;\n}"]
        });
        return BreedingComponent;
      }();
      /***/

    },

    /***/
    81467: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "ChangePasswordComponent": function ChangePasswordComponent() {
          return (
            /* binding */
            _ChangePasswordComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! @angular/forms */
      33865);

      var _ChangePasswordComponent = /*#__PURE__*/function () {
        var ChangePasswordComponent = /*#__PURE__*/function () {
          function ChangePasswordComponent() {
            _classCallCheck(this, ChangePasswordComponent);
          }

          _createClass(ChangePasswordComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {}
          }]);

          return ChangePasswordComponent;
        }();

        ChangePasswordComponent.ɵfac = function ChangePasswordComponent_Factory(t) {
          return new (t || ChangePasswordComponent)();
        };

        ChangePasswordComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
          type: ChangePasswordComponent,
          selectors: [["app-change-password"]],
          decls: 20,
          vars: 0,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-sm-4"], [1, "input-group", "mb-4"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-lock"], ["type", "password", "placeholder", "Old Password", "formControlName", "password", 1, "form-control"], ["type", "password", "placeholder", "New Password", "formControlName", "password", 1, "form-control"]],
          template: function ChangePasswordComponent_Template(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4, " Change Password ");

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "div", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "span", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](12, "i", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](13, "input", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](14, "div", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](15, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "span", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](17, "i", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](18, "input", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](19, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
            }
          },
          directives: [_angular_forms__WEBPACK_IMPORTED_MODULE_1__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_1__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_1__.FormControlName],
          styles: [""]
        });
        return ChangePasswordComponent;
      }();
      /***/

    }
  }]);
})();