{"ast": null, "code": "import identity from './identity.js';\n/**\n * Casts `value` to `identity` if it's not a function.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Function} Returns cast function.\n */\n\nfunction castFunction(value) {\n  return typeof value == 'function' ? value : identity;\n}\n\nexport default castFunction;", "map": null, "metadata": {}, "sourceType": "module"}