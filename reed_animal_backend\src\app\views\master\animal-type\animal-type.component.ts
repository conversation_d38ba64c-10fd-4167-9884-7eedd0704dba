import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Types } from '../../../views/models/animal_type'
import { AnimalTypeService } from '../../services/animal_type.services'
import { TokenStorageService } from '../../services/token-storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';

@Component({
  selector: 'app-animal-type',
  templateUrl: './animal-type.component.html',
  styleUrls: ['./animal-type.component.scss']
})

export class AnimalTypeComponent implements OnInit {
  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('AddModal') public AddModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  loginForm: FormGroup;
  isFormReady = false;
  submitted = false;
  types = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  type: Types = {};
  nameFailed = false;
  modal = true;
  Add = true;
  Edit = true;
  Delete = true;
  constructor(private animalservice: AnimalTypeService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    this.tokens();
    this.SignForm();
  }

  //clear modal window
  clear(): void {
    this.type = {};
    this.loginForm.reset();
    this.nameFailed = false;
    this.isFormReady = false;
    this.submitted = false;
  }

  getfocus() {
    this.nameFailed = false;
  }

  //token verified type
  tokens(): void {
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Species") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        } else {
          this.typeLists();
        }
      })
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //Get All type List
  typeLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.animalservice.GetTypesList(skip, this.name)
      .subscribe((res: any) => {
        this.types = res.data;
        this.count = res.count;
        // console.log(this.types);
        // console.log(this.count);
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.typeLists();
  }

  //Edit or update type 
  GetType(id): void {
    // console.log('id-->', id);
    this.animalservice.GetTypeDetail(id)
      .subscribe((res) => {
        this.type = res.data[0];
        this.f.name.setValue(res.data[0].name, {
          onlySelf: true
        })
        // console.log(res.data)
      })
  }

  EditType(id): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {
          const data = {
      name: this.loginForm.value.name
    }
    this.animalservice.UpdateType(id, data)
      .subscribe((res) => {
        // console.log('res-->', res);
        this.primaryModal.hide();
        this.type = {};
        this.typeLists();
      })
    }
  }

  //Status ON & OFF
  changed(active, id) {
    const data = {
      status: active
    }
    this.animalservice.UpdateType(id, data)
      .subscribe((res: any) => {
        // console.log('res-->', res);
      })
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      name: ['', [Validators.required]],
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  //Add new type
  AddType(): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {
      const data = {
        name: this.loginForm.value.name
      }
      this.animalservice.Newtype(data)
        .subscribe((res) => {
          // this.type = {};
          this.AddModal.hide();
          this.clear()
          // this.nameFailed = false;
          // console.log('new-->')
          this.typeLists();
        })
      this.nameFailed = true;
    }
  }

  //Delete type using id
  DeleteType(id): void {
    // console.log('id-->', id)
    this.animalservice.Deletetype(id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.removeModal.hide();
        this.type = {};
        this.typeLists();
      })
  }
}