import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class ModuleService extends Api {

    //Add New Module 
    NewModule(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/module?token=${localStorage.auth_token}`, data);
    }

    //Get All Module 
    GetModuleList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/module?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular Module by using module id 
    GetModuleDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit Module details
    UpdateModule(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Module by using id
    DeleteModule(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`);
    }
}