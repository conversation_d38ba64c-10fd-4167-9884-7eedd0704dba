{"ast": null, "code": "import assocIndexOf from './_assocIndexOf.js';\n/** Used for built-in method references. */\n\nvar arrayProto = Array.prototype;\n/** Built-in value references. */\n\nvar splice = arrayProto.splice;\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\n\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n\n  var lastIndex = data.length - 1;\n\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n\n  --this.size;\n  return true;\n}\n\nexport default listCacheDelete;", "map": null, "metadata": {}, "sourceType": "module"}