import { Component, OnInit, ViewChild } from '@angular/core';
import { CustomerService } from '../../services/customer.services'
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { User } from '../../models/user.models';
@Component({
  selector: 'app-customers',
  templateUrl: './customers.component.html',
  styleUrls: ['./customers.component.scss']
})

export class CustomersComponent implements OnInit {

  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('EditModal') public EditModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;
  @ViewChild('okayModal') public okayModal: ModalDirective;

  customers = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  type = ''
  user: User = {
  }
  AddForm: FormGroup;
  isFormReady = false;
  submitted = false;
  Add = true;
  Edit = true;
  Delete = true;
  sort = false
  value = -1
  field = "_id"
  constructor(private customerService: CustomerService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    const module = this.tokenStorage.getModule();
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Customers") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.GetCustomerLists();
    this.SignForm();
  }

  clear(): void {
    this.isFormReady = true;
    this.submitted = false;
    this.AddForm.reset();
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    skip['value'] = this.value;
    skip['field'] = this.field;
    return skip;
  }

  //Get All Type List
  GetCustomerLists(): void {
    const skip = this.getrequestparams(this.page);
    this.customerService.GetCustomerList(skip, this.name)
      .subscribe((res: any) => {
        this.customers = res.data;
        this.count = res.count;
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    this.GetCustomerLists();
  }

  //Status update
  changed(actived, id): void {
    const data = {
      active: actived,
    };
    this.customerService.UpdateUser(id, data).subscribe((res: any) => {
      
    })
  } 

  //search by procedure code
  SearchbyuserId(search): void {
    this.router.navigate(['/pages/pet-detail'], { queryParams: { 'search': search } });

  }

  SignForm() {
    this.AddForm = this.formBuilder.group({
      firstName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      // phone_no: ['', [Validators.required, Validators.minLength(10)]],
    });
  }

  get f() {
    return this.AddForm.controls;
  }

  //Add New Customer
  AddCustomer() {
    this.submitted = true;
    if (this.AddForm.invalid) {
      return
    } else {
      const data = {
        email: this.AddForm.value.email,
        first_name: this.AddForm.value.firstName,
        password: this.AddForm.value.password,
      }
      this.customerService.AddCustomer(data)
        .subscribe((res) => {
          this.primaryModal.hide();
          this.okayModal.show();
          this.isFormReady = true;
          this.submitted = false;
          this.AddForm.reset();
          this.GetCustomerLists();
        })
    }
  }

  //Update or Edit Employee
  GetCustomer(id): void {
    this.customerService.FindByUserId(id)
      .subscribe((res) => {
        this.user = res.user;
      })
  }

  EditCustomer(id): void {
    const data = {
      first_name: this.user.first_name,
      phone_number: this.user.phone_number,
    }
    this.customerService.UpdateUser(id, data)
      .subscribe((res) => {
        this.removeModal.hide();
        this.GetCustomerLists();
      })
  }

  //Delete Employee
  Deletecustomer(id): void {
    this.customerService.DeleteCustomer(id)
      .subscribe((res) => {
        this.removeModal.hide();
        this.GetCustomerLists();
      })
  }

  //Field name sorted
  Field(param) {
    if (this.sort == true) {
      this.sort = false,
        this.field = param
      this.value = -1
      this.GetCustomerLists();
    } else {
      this.sort = true
      this.field = param
      this.value = 1
      this.GetCustomerLists();
    }
  }


}