{"ast": null, "code": "/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */\n\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED);\n\n  return this;\n}\n\nexport default setCacheAdd;", "map": null, "metadata": {}, "sourceType": "module"}