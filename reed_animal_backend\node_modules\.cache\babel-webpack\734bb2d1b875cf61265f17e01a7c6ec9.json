{"ast": null, "code": "import { Subscriber } from './Subscriber';\nexport class InnerSubscriber extends Subscriber {\n  constructor(parent, outerValue, outerIndex) {\n    super();\n    this.parent = parent;\n    this.outerValue = outerValue;\n    this.outerIndex = outerIndex;\n    this.index = 0;\n  }\n\n  _next(value) {\n    this.parent.notifyNext(this.outerValue, value, this.outerIndex, this.index++, this);\n  }\n\n  _error(error) {\n    this.parent.notifyError(error, this);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.parent.notifyComplete(this);\n    this.unsubscribe();\n  }\n\n} //# sourceMappingURL=InnerSubscriber.js.map", "map": null, "metadata": {}, "sourceType": "module"}