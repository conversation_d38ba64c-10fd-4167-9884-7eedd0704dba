<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Appointments
                <span style="position: absolute;right: 30px;margin-top: -6px;"><button (click)="AddAppointment()"
                        class="btn btn-primary">Add Appointment</button></span>
            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-lg-12 my-3">
                        <!-- <button type="button" class="btn btn-primary mr-1" data-toggle="modal" (click)="primaryModal.show()">
                        Add Customer
                        </button> -->
                        <div class="row">
                            <div class="col-md-2" *ngIf="Doctorfailed">
                                <label>Doctor</label>
                                <br>
                                <select id="select1" name="select1" class="form-control" (change)="page=1;searched($event.target.value);">
                                    <option value='all'>All</option>
                                    <option *ngFor="let role of doctors" [value]="role._id">{{role.name}}</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Appointment Type</label>
                                <br>
                                <select id="select1" name="select1" class="form-control" (change)="page=1;perfer($event.target.value);">
                                    <option value="">All</option>
                                    <option value="In-Person">In-Person</option>
                                    <option value="Video">Video</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label>From Date</label>
                                <input type="text" placeholder="From Date" class="form-control" bsDatepicker [(ngModel)]="fromdate" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}" (ngModelChange)="page=1;Fromchanged($event,'from');">
                            </div>

                            <div class="col-md-2">
                                <label>To Date</label>
                                <input type="text" placeholder="To Date" class="form-control" bsDatepicker [(ngModel)]="todate" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}" (ngModelChange)="page=1;Fromchanged($event,'to');">
                            </div>

                            <div class="col-md-2">
                                <label>Location</label>
                                <br>
                                <select id="select1" name="select1" class="form-control" (change)="page=1;location($event.target.value);">
                                    <option value="">All</option>
                                    <option value="Campbell">Campbell</option>
                                    <option value="Saratoga">Saratoga</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label style="width: 160px;">Confirmation</label>
                                <br>
                                <select id="confSelect" name="confSelect" class="form-control" (change)="page=1;confirmation($event.target.value);">
                                    <option value="">All</option>
                                    <option value="true">Confirmed</option>
                                    <option value="false">Unconfirmed</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 form-group table-search"></div>
                            <div class="col-md-4 form-group table-search" style="margin-top: 16px;">
                                <!-- <label style="visibility: hidden;margin: 0;">Select From and To Date</label> -->
                                <div class="input-group" style="top: 3px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" (click)="page=1;onChanging();"><i
                                                class="fa fa-search"></i></span>
                                    </div>
                                    <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" (input)="page=1;onChanging();" [(ngModel)]="name" />
                                </div>
                            </div>

                        </div>
                        <!-- <button style="height: 100px;width: 100px;background-color: #cdf1b1;"></button> -->
                    </div>
                </div>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Customer Name <i class="fa fa-sort" checked="sort" (click)="Field('name')"></i></th>
                            <th>Pet Name <i class="fa fa-sort" checked="sort" (click)="Field('pet_name')"></i></th>
                            <th>Species <i class="fa fa-sort" checked="sort" (click)="Field('species')"></i></th>
                            <th>Reason <i class="fa fa-sort" checked="sort" (click)="Field('kind_appointment')"></i>
                            </th>
                            <th>Appointment <br>Type</th>
                            <th>Doctor Name <i class="fa fa-sort" checked="sort" (click)="Field('doctor_name')"></i>
                            </th>
                            <th>Location </th>
                            <th>Date <i class="fa fa-sort" checked="sort" (click)="Field('apt_date_time')"></i></th>
                            <th>Time</th>
                            <th style="text-align: center;">Phone Number</th>
                            <th style="text-align: center;">Confirmation</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of Appointments| paginate: { id: 'listing_pagination', itemsPerPage: 10, currentPage: page, totalItems: count };let i=index" [style.background-color]="user.status === 'Cancelled' ?'#ff7c6d':user.confirmed ? '#cdf1b1' : '#d5d1d1'">
                            <!-- <td>{{user.doctor_name}}</td> -->
                            <td (click)="Router(i,user.status,user._id,user);">{{user.user_id[0].first_name | titlecase }} {{user.user_id[0].last_name | titlecase }} </td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.pet_name||user.pet_id[0].pet_name}}
                            </td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.species ||user.pet_id[0]. animal_type}} </td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.kind_appointment}}</td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.prefer}}</td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.doctor_name}}</td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.location}}</td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.date|date:'dd MMM yyyy'}}</td>
                            <td (click)="Router(i,user.status,user._id,user)">{{user.time}}</td>
                            <td style="text-align: center;" (click)="Router(i,user.status,user._id,user)">
                                {{user.user_id[0]?user.user_id[0].phone_number:"-" }}
                            </td>
                            <td style="text-align: center;" (click)="Router(i,user.status,user._id,user)">
                                {{user.confirmed?"Confirmed":"Unconfirmed"}}</td>
                            <td (click)="edit(user)">{{user.status}}</td>
                        </tr>

                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>

<!-- Delete Modal -->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Appointment?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteAppointment(deleteId);">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- show appointments modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Appointment detailss</h4>
                <div type="button" style="color:white;" class="close" (click)="primaryModal.hide();close()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <div class="card">

                    <div class="card-body">
                        <!-- top save function -->

                        <!-- <label for="" style="position: absolute;
                        left: 63%;top: 28px;">Confirm <input style="position: relative;top: 3px; height: 13px;left: 10px;" type="checkbox"> </label> -->

                        <!-- <label for="confirm-checkbox" style="position: absolute; left: 60%; top: 28px;">
                            Confirm :
                            <input
                              type="checkbox"
                              id="confirm-checkbox"
                              class="custom-checkbox"
                              style="position: relative; top: 3px; left: 10px;"
                              [(ngModel)]="this.isChecked"
                              (change)="onCheckboxChange($event)"
                            />
                          </label> -->

                        <button class="focus" *ngIf="confirmbtn" style="position: absolute;left: 62%;top: 21px;height: 32px;width: 85px;border-radius: 4px;" [disabled]="App_Details.confirmed" [ngStyle]="App_Details.confirmed?{color:'#568d2c',border:'0px',backgroundColor:'white','font-weight': 'bolder'}:isChecked?{backgroundColor:'#568d2c',color:'white',border:'1px solid #568d2c'}:{backgroundColor:'white',color:'black',border:'1px solid #568d2c'}"
                            (click)="onCheckboxChange()">{{this.isChecked?"Confirmed":"Confirm"}}</button>


                        <button class="btn btn-danger" *ngIf="Save" style="position: relative;left: 77%;" (click)="cancleAppointment()">Cancel</button>


                        <button class="btn btn-primary" *ngIf="savebtn" style="position: relative;left: 80%;" (click)="appointment_update()">save</button>
                        <div class="row">


                            <div class="col-sm-6">

                                <div class="form-group">
                                    <label for="select1">Location:</label>

                                    <select id="select1" name="select1" class="form-control" (change)="locationchange()" [(ngModel)]="App_Details.location" (click)="page=1">


                                        <option *ngFor="let location of final_location" [value]="location.name">
                                            {{location.name}}</option>
                                    </select>


                                    <!-- <input type="text" id="role-name11" class="form-control" [(ngModel)]="App_Details.location" [ngModelOptions]="{standalone: true}"> -->
                                </div>



                                <div class="form-group">
                                    <label for="select1">Doctor name:</label>
                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="App_Details.doctor_name" (change)="editdorct($event.target.value)">
                                        <option *ngFor="let role of locDoctor" [value]="role.name">
                                            {{role.name}}
                                        </option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="select1">Appointment type:</label>
                                    <input type="text" id="role-name211" class="form-control" [(ngModel)]="App_Details.prefer" [ngModelOptions]="{standalone: true}" readonly>
                                </div>


                                <div class="form-group" *ngIf="messageText">
                                    <label for="select1">Message:</label>
                                    <textarea type="text" id="msg" [(ngModel)]="note" class="form-control" style="height: 70px !important;"></textarea>
                                </div>

                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="select1">Date:</label>

                                    <!-- <input type="text" id="role-name221" class="form-control" [(ngModel)]="App_Details.date" [ngModelOptions]="{standalone: true}" readonly> -->

                                    <input type="text" id="role-name221" class="form-control" placeholder="--Select Date--" [minDate]="todayDate" bsDatepicker [(ngModel)]="App_Details.date" (bsValueChange)="getDetailsdate($event)" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}"
                                        autocomplete="off" [ngModelOptions]="{standalone: true}">

                                    <label *ngIf="App_Details.date == '' && validation" style="color: rgb(248, 27, 27);">Please Select Date</label>

                                </div>

                                <div class="form-group">
                                    <label for="select1">Time:</label>


                                    <select id="select1" name="select1" class="form-control" (change)="onTimeChange($event)">
                                        <!-- <option value='all'>{{App_Details?App_Details.time:"--Select Time--"}}</option> -->
                                        <option value=''>{{App_Details?App_Details.time:"--Select Time--"}}</option>
                                        <option *ngFor="let time of this.Appointments_time" [value]="time">{{time}}
                                        </option>
                                    </select>

                                    <label *ngIf="this.time =='' && validation " style="color: rgb(248, 27, 27);">Please
                                        Select Time</label>

                                </div>

                                <div class="form-group">
                                    <label for="select1">Reason:</label>

                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="App_Details.kind_appointment" (click)="page=1;Editreason($event.target.value);">
                                        <!-- <option value=''>--Select Reason--</option> -->
                                        <option *ngFor="let reason of ReasonData" [value]="reason.name">{{reason.name}}
                                        </option>
                                    </select>


                                    <!-- <input type="text" id="role-name2211" class="form-control" [(ngModel)]="App_Details.kind_appointment" [ngModelOptions]="{standalone: true}"> -->
                                </div>


                            </div>
                        </div>
                        <hr>
                        <div class="row">

                            <div class="col-sm-6">
                                <h5>Owner Detail</h5>
                                <div class="form-group">
                                    <label for="select1">Customer name:</label>
                                    <input type="text" id="role-name12" class="form-control" autocomplete="off" [(ngModel)]="current_owner.first_name" [ngModelOptions]="{standalone: true}" (change)="editcustomername()" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="select1">Phone number:</label>
                                    <input type="text" id="role-name12" class="form-control" autocomplete="off" [maxlength]="10" (change)="editphoneNumber()" [(ngModel)]="current_owner.phone_number" [ngModelOptions]="{standalone: true}" readonly>
                                </div>
                                <!-- <div class="form-group">
                  <label for="select1">Phone Number:</label>
                  <input type="text" id="Phone-Number" class="form-control"
                    [ngModelOptions]="{standalone: true}" readonly value="+919555522525">
                </div> -->
                            </div>
                            <div class="col-sm-6">
                                <h5 style="visibility:hidden;">Owner Detail</h5>
                                <div class="form-group">
                                    <label for="select1">Email:</label>
                                    <input type="email" id="email" class="form-control" autocomplete="off" [ngModelOptions]="{standalone: true}" readonly [(ngModel)]="current_owner.email" readonly>
                                </div>
                                <!-- <div class="form-group">
                  <label for="select1">Reminders:</label>
                  <input type="text" id="Reminders" class="form-control"
                    [ngModelOptions]="{standalone: true}" readonly value="March 3,2021: Wellness Exam">
                </div> -->
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-6">
                                <h5>Pet Detail</h5>
                                <div class="form-group">
                                    <label for="select1">Pet Name:</label>
                                    <input type="text" id="role-name1" (change)="editpetName()" class="form-control" [(ngModel)]="current_pet.pet_name" [ngModelOptions]="{standalone: true}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="select1">Pet medical ID:</label>
                                    <input type="text" id="petmed" class="form-control" [ngModelOptions]="{standalone: true}" readonly [(ngModel)]="current_pet.pet_mid" readonly>
                                </div>



                                <!-- <div class="form-group">
                                    <label for="select1">Species:</label>
                                    <input type="text" id="role-nam2" class="form-control" (change)="editspecies()" [(ngModel)]="current_pet.animal_type" [ngModelOptions]="{standalone: true}">
                                </div> -->




                                <div class="form-group">
                                    <label for="select1">Species:</label>

                                    <input type="text" id="role-name1" class="form-control" [(ngModel)]="current_pet.animal_type" [ngModelOptions]="{standalone: true}" readonly>
                                    <!-- <select id="select1" name="select1" class="form-control" readonly [(ngModel)]="current_pet.animal_type" (change)="page=1;editspecies();">
                                        <option value=''>--select Species--</option>
                                        <option value="Dog">Dog</option>
                                        <option value="Cat">Cat</option>
                                      </select> -->
                                </div>

                                <div class="form-group">
                                    <label for="select1">Breed:</label>

                                    <input type="text" id="role-name1" class="form-control" [(ngModel)]="App_Details.breed_name" [ngModelOptions]="{standalone: true}" readonly>

                                    <!-- <select id="select1" readonly name="select1" class="form-control" [(ngModel)]="App_Details.breed_name" (change)="selectedbreed($event.target.value)">
                                        <option value=''>--Select breed--</option>
                                        <option *ngFor="let brd of  breed " [value]="brd.name">{{brd.name}}</option>
                                      </select> -->
                                </div>




                                <!-- <div class="form-group">
                                    <label for="select1">Breed:</label>
                                    <input type="text" id="role-nam2" class="form-control" [(ngModel)]="current_pet.animal_type" [ngModelOptions]="{standalone: true}" readonly>
                                </div> -->

                            </div>
                            <div class="col-sm-6">
                                <h5 style="visibility:hidden;">Pet Detail</h5>
                                <!-- <div class="form-group">
                                    <label for="select1">Sex:</label>
                                    <input type="text" id="sex" class="form-control" [ngModelOptions]="{standalone: true}" readonly [(ngModel)]="current_pet.gender">
                                </div> -->

                                <div class="form-group">
                                    <label for="select1">Sex:</label>
                                    <input type="text" id="role-name1" class="form-control" [(ngModel)]="current_pet.gender" [ngModelOptions]="{standalone: true}" readonly>


                                    <!-- <select id="select1" name="select1" class="form-control" [(ngModel)]="current_pet.gender" (change)="page=1;editsex()">
                                        <option value="">--select--</option>
                                        <option value="Male">Male</option>
                                        <option value="female">Female</option>
                                      </select> -->
                                </div>

                                <div class="form-group">
                                    <label for="select1">Color:</label>
                                    <input type="text" id="Color" class="form-control" readonly (change)="editcolor()" [ngModelOptions]="{standalone: true}" [(ngModel)]="current_pet.color">
                                </div>


                                <div class="form-group">
                                    <label for="select1">Age:</label>

                                    <input type="text" id="role-name1" class="form-control" [(ngModel)]="current_pet.dob" [ngModelOptions]="{standalone: true}" readonly>

                                    <!-- <input type="text" id="Age" readonly id="role-name221" autocomplete="off" [(ngModel)]="current_pet.dob" placeholder="--Select Date--" class="form-control" bsDatepicker (bsValueChange)="editdob($event)" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}"> -->

                                </div>
                                <!-- <div class="form-group">
                                    <label for="select1">Age:</label>
                                    <input type="text" id="Age" class="form-control" [ngModelOptions]="{standalone: true}" readonly [(ngModel)]="current_pet.dob">
                                </div> -->
                                <!-- <div class="form-group">
                                    <label for="select1">Weight:</label>

                                    <input type="text" id="Weightin" class="form-control" readonly value="75lbs">
                                </div>
                                <div class="form-group">
                                    <label for="select1">Allergies:</label>
                                    <input type="text" id="Allergies" class="form-control" readonly value="None">
                                </div>
                                <div class="form-group">
                                    <label for="select1">Medical Alerts:</label>
                                    <input type="text" id="Medical" class="form-control" readonly value="None">
                                </div> -->
                            </div>

                        </div>
                    </div>
                </div>




                <div class="card">
                    <div class="card-header">
                        History
                    </div>
                    <h5 style="margin: 1rem ;margin-bottom: -1rem; font-weight: bold; color: #568d2c;">Created</h5>
                    <div class="container mt-4" style="margin-left: 2rem;">
                        <div class="row align-items-center mb-3">
                            <div class="col-md-2 fw-bold">Name:</div>
                            <div class="col-md-4">{{ Name }}</div>
                            <div class="col-md-2 fw-bold">Date:</div>
                            <div class="col-md-4">{{ this.App_Details.createdAt | date:'MM/dd/yyyy hh:mm a' }}</div>
                        </div>
                    </div>




                    <div *ngIf="reschedule.length>0" style="margin-top: -17px; ">
                        <h5 style="margin: 1rem ;margin-bottom: -1rem; font-weight: bold; color: #568d2c;">Reschedule</h5>

                        <div class="container-fluid mt-4" style=" overflow-y: scroll;max-height: 152px;">
                            <div class="container">
                                <div class="row">
                                    <div *ngFor="let user of reschedule; let i = index" class="col-md-12 mb-3">

                                        <!-- First Row: Name & Reason -->
                                        <div class="row mb-2 d-flex align-items-center">
                                            <div class="col-md-2 col-4 fw-bold text-end">Name:</div>
                                            <div class="col-md-4 col-8">{{ Name }}</div>
                                            <div class="col-md-2 col-4 fw-bold text-end">Reason:</div>
                                            <div class="col-md-4 col-8 ">{{ user?.reason }}</div>
                                        </div>

                                        <!-- Second Row: Old Date & New Date -->
                                        <div class="row mb-2 d-flex align-items-center">
                                            <div class="col-md-2 col-4 fw-bold text-end">Old Date:</div>
                                            <div class="col-md-4 col-8">{{ user?.oldDateTime }}</div>
                                            <div class="col-md-2 col-4 fw-bold text-end">New Date:</div>
                                            <div class="col-md-4 col-8">{{ user?.newDateTime }}</div>
                                        </div>

                                        <!-- Row Separator -->
                                        <!-- <hr class="my-2"> -->

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div *ngIf="cancelled.length>0" style="margin-top: -17px;">
                        <h5 style="margin-left: 1rem ;margin-bottom: 0.5rem; font-weight: bold;color: #568d2c;">Cancelled</h5>
                        <div style="margin-top: -13px;">

                            <div class="container-fluid mt-4">
                                <div class="container">
                                    <div *ngFor="let user of cancelled; let i = index" class="mb-3">

                                        <!-- First Row: Name & Date -->
                                        <div class="row mb-2 align-items-center">
                                            <div class="col-md-2 col-4 fw-bold text-end">Name:</div>
                                            <div class="col-md-4 col-8">{{ Name }}</div>
                                            <div class="col-md-2 col-4 fw-bold text-end">Date:</div>
                                            <div class="col-md-4 col-8">{{ user?.datatime }}</div>
                                        </div>

                                        <!-- Second Row: Reason -->
                                        <div class="row mb-2 align-items-center">
                                            <div class="col-md-2 col-4 fw-bold text-end">Reason:</div>
                                            <div class="col-md-10 col-8 ">{{ user?.reason }}</div>
                                        </div>

                                        <!-- Separator -->
                                        <!-- <hr class="my-2"> -->

                                    </div>
                                </div>
                            </div>




                            <!-- <div *ngFor="let user of cancelled let i=index">

                                <h6>Name :<a>{{Name}}</a></h6>
                                <h6>Date :<a>{{user?.datatime}}</a></h6>
                                <h6>Reason : <a>{{user?.reason}}</a></h6>
                                <br>


                            </div> -->
                        </div>
                    </div>

                </div>












                <div class="card">
                    <div class="card-header">
                        Past History
                    </div>

                    <div class="card-body" *ngFor="let user of PastVisit| paginate: { id: 'past_pagination',
          itemsPerPage: 1,
          currentPage: past_page,
          totalItems: past_count };let i=index">
                        <div class="row">
                            <div class="col-sm-12">
                                <pagination-controls id="past_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="PasthandlePageChange($event)">
                                </pagination-controls>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12 name-pastvisit">
                                <!-- <h5>{{user.doctor_name}}<span>{{user.apt_date_time | date:'MMM dd YYYY'}} at {{user.time }}</span></h5> -->
                            </div>
                        </div>
                        <!-- <pre>{{user|json}}</pre> -->
                        <div class="row">
                            <div class="col-sm-12 name-pastvisit">
                                <h5>{{user.doctor_name}}<span>{{user.apt_date_time | date:'MMM dd YYYY'}} at {{user.time
                                        }}</span></h5>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label for="select1">Reason For Visit:</label>
                                            <input type="text" id="reason-visit" class="form-control" readonly [(ngModel)]="user.kind_appointment" [ngModelOptions]="{standalone: true}">
                                        </div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Weight:</label>
                                            <input type="text" id="Weight" class="form-control" value="{{user.treatment?user.treatment.weight:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Temp:</label>
                                            <input type="text" id="Temp" class="form-control" readonly value="{{user.treatment?user.treatment.temp:''}}">
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Pulse:</label>
                                            <input type="text" id="Pulse" class="form-control" value="{{user.treatment?user.treatment.pulse:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Resp:</label>
                                            <input type="text" id="Resp" class="form-control" value="{{user.treatment?user.treatment.resp:''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <h5>Vaccines - <small>date last given or due date</small></h5>
                                <div class="row mt-4">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">DHP:</label>
                                            <input type="text" id="DHP" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.DHP.date:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">BORD:</label>
                                            <input type="text" id="BORD" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.BORD.date:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">LEPTO:</label>
                                            <input type="text" id="LEPTO" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.LEPTO.date:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Rabies:</label>
                                            <input type="text" id="Rabies" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.Rabies.date:''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">HWT:</label>
                                            <input type="text" id="HWT" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.HWT.date:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Fecal:</label>
                                            <input type="text" id="Fecal" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.Fecal.date:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Bloodwork:</label>
                                            <input type="text" id="Bloodwork" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.Bloodwork.date:''}}" readonly>
                                        </div>
                                    </div>

                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1">Influenza:</label>
                                            <input type="text" id="Influenza" class="form-control" value="{{user.treatment?user.treatment.vaccinationdata.Influenza.date:''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="select1">Indoor/Outdoor:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.placePet:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Activity/Mobility:</label>
                                            <input type="text" id="act-mob" class="form-control" value="{{user.treatment?user.treatment.activityPet:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Weight Change:</label>
                                            <input type="text" id="Weightc" class="form-control" value="{{user.treatment?user.treatment.weightchange:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">E/D/U/D</label>
                                            <input type="text" id="E/D/U/D" class="form-control" value="{{user.treatment?user.treatment.EDUD:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">C/S/V/D</label>
                                            <input type="text" id="C/S/V/D" class="form-control" value="{{user.treatment?user.treatment.CSVD:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Stool</label>
                                            <input type="text" id="Stool" class="form-control" value="{{user.treatment?user.treatment.Stool:''}}" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Urinary Habits</label>
                                            <input type="text" id="UrinaryHabits" class="form-control" value="{{user.treatment?user.treatment.UrinaryHabits:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="select1">Diet (including Treats)</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.diet:''}}</p>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Prescriptions/Supplements</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.suppliment:''}}</p>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Flea/Heartworm Prevention</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.flea:''}}</p>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">drinking Habits</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.drinkingHabits:''}}
                                            </p>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Appetite</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.Appetite:''}}</p>
                                        </div>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <label for="select1">Any RX refills needed</label>
                                                    <input type="text" id="RX-refills" class="form-control" readonly value="{{user.treatment!=null && user.treatment.RxRefill!=null ?user.treatment.RxRefill.Value:''}}">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="select1">Dental <br>Care</label>
                                                    <input type="text" id="Dental-Care" class="form-control" readonly value="{{user.treatment !=null && user.treatment.Dentalcare!=null ?user.treatment.Dentalcare.Value:''}}">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="select1">Nail<br> Trim</label>
                                                    <input type="text" id="Nail-Trim" class="form-control" readonly value="{{user.treatment != null && user.treatment.Nailtrim !=null ?user.treatment.Nailtrim.Value:''}}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label for="select1">Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.notes:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" *ngIf="user.treatment && user.treatment.RxRefill && user.treatment.RxRefill.Value!=''&& user.treatment.RxRefill.Value!='No'">
                                        <div class="form-group">
                                            <label for="select1">Refill Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.RefillNotes:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" *ngIf="user.treatment &&  user.treatment.Dentalcare &&user.treatment.Dentalcare.Value!=''&& user.treatment.Dentalcare.Value!='No'">
                                        <div class="form-group">
                                            <label for="select1">Dental Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.DentalNotes:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group" *ngIf="user.treatment && user.treatment.Nailtrim &&user.treatment.Nailtrim.Value!='' && user.treatment.Nailtrim.Value!='No'">
                                            <label for="select1">Nail Trim Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.NailTrimNotes:''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-12" style="text-align: center;">
                                        <div class="form-group" style=" display: inline-block;">
                                            <label for="select1">BCS
                                                <input type="text" id="BCS" class="form-control" readonly style=" display: inline-block;
                   width: auto;" value="{{user.treatment?user.treatment.bcs:''}}"> /9</label>
                                        </div>
                                        <div class="form-group" style="display: inline-block; padding-left: 10px;">
                                            <label for="select1">CRT
                                                <input type="text" id="CRT" class="form-control" readonly style=" display: inline-block;
                   width: auto;align-content: center;" value="{{user.treatment?user.treatment.crt:''}}"> /S</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">General:</label>
                                            <input type="text" id="General" class="form-control " readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment && user.treatment.diseaselist && user.treatment.diseaselist.General?!user.treatment.diseaselist.General:''}">
                                            <input type="text" id="General1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment && user.treatment.diseaselist && user.treatment.diseaselist.General?!user.treatment.diseaselist.General:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">EENT:</label>
                                            <input type="text" id="EENT" class="form-control " readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.EENT?!user.treatment.diseaselist.EENT:''}">
                                            <input type="text" id="EENT1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.EENT?user.treatment.diseaselist.EENT:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Oral:</label>
                                            <input type="text" id="Oral" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Oral?!user.treatment.diseaselist.Oral:''}">
                                            <input type="text" id="Oral1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Oral?user.treatment.diseaselist.Oral:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Respiritory:</label>
                                            <input type="text" id="Respiritory" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Respiritory?user.treatment.diseaselist.Respiritory:''}">
                                            <input type="text" id="Respiritory1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Respiritory?user.treatment.diseaselist.Respiritory:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Cardiovascular:</label>
                                            <input type="text" id="Cardiovascular" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment && user.treatment.diseaselist && user.treatment.diseaselist.Cardiovascular ?! user.treatment.diseaselist.Cardiovascular:''}">
                                            <input type="text" id="Cardiovascular1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment && user.treatment.diseaselist && user.treatment.diseaselist.Cardiovascular ?! user.treatment.diseaselist.Cardiovascular:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">GI/Abdomen:</label>
                                            <input type="text" id="GI/Abdomen" class="form-control " readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist['GI/Abdomen']?!user.treatment.diseaselist['GI/Abdomen']:''}">
                                            <input type="text" id="GI/Abdomen1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist['GI/Abdomen']?!user.treatment.diseaselist['GI/Abdomen']:''}">
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Musculoskel:</label>
                                            <input type="text" id="Musculoskel" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Musculoskel?!user.treatment.diseaselist.Musculoskel:''}">
                                            <input type="text" id="Musculoskel1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Musculoskel?!user.treatment.diseaselist.Musculoskel:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Integument:</label>
                                            <input type="text" id="Integument" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment && user.treatment.diseaselist&&user.treatment.diseaselist.Integument?!user.treatment.diseaselist.Integument:''}">
                                            <input type="text" id="Integument1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Integument?!user.treatment.diseaselist.Integument:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Uro-Genital:</label>
                                            <input type="text" id="Uro-Genital" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist['Uro-Genital']?!user.treatment.diseaselist['Uro-Genital']:''}">
                                            <input type="text" id="Uro-Genital1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' :user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist['Uro-Genital']?!user.treatment.diseaselist['Uro-Genital']:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Lymphatic:</label>
                                            <input type="text" id="Lymphatic" class="form-control " readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Lymphatic?!user.treatment.diseaselist.Lymphatic:''}">
                                            <input type="text" id="Lymphatic1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Lymphatic?user.treatment.diseaselist.Lymphatic:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Neurologic:</label>
                                            <input type="text" id="Neurologic" class="form-control" readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment &&user.treatment.diseaselist&&user.treatment.diseaselist.Neurologic?!user.treatment.diseaselist.Neurologic:''}">
                                            <input type="text" id="Neurologic1" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment&&user.treatment.diseaselist&&user.treatment.diseaselist.Neurologic?user.treatment.diseaselist.Neurologic:''}">
                                        </div>
                                        <div class="form-group">
                                            <label for="select1" style="width:50%;">Endocrine:</label>
                                            <input type="text" id="Endocrine1" class="form-control " readonly value="Norm" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment  && user.treatment.diseaselist&& user.treatment.diseaselist.Endocrine?!user.treatment.diseaselist.Endocrine:''}">
                                            <input type="text" id="Endocrine" class="form-control" readonly value="Abn" style=" display: inline-block;
                     width: 20%; text-align: center;" [ngClass]="{'normal-case' :  user.treatment  && user.treatment.diseaselist&& user.treatment.diseaselist.Endocrine?!user.treatment.diseaselist.Endocrine:''}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label for="select1">Assessment:</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.commonAsse:''}}</p>
                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Plan:</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.plan:''}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div *ngIf="user.treatment &&user.treatment.prescription_data&& user.treatment.prescription_data.dataArray.length>0">
                            <!-- <pre>{{status|json}}</pre> -->

                            <h4 style="color: #568d2c;">Prescription & Services</h4>
                            <table class="table table-bordered">
                                <thead>
                                    <tr style="background-color: #568d2c;">
                                        <th scope="col" class="text">No</th>
                                        <th scope="col" class="text">Description</th>
                                        <th scope="col" class="text">Qty</th>
                                        <th scope="col" class="text">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of user.treatment.prescription_data.dataArray; let i = index">
                                        <th scope="row">{{i+1}}</th>
                                        <td width="450px">{{item.CodeDescription}}</td>
                                        <td>{{item.medicine_qty}}</td>
                                        <td>$ {{item.Decline?"0.00":item.BasePrice
                                            <=21.85?(21.85*item.medicine_qty).toFixed(2):(item.BasePrice*item.medicine_qty).toFixed(2)}}< /td>

                                    </tr>
                                    <!-- <tr>
                                        <th scope="row">2</th>
                                        <td>{{status.treatment.prescription_data.dataArray[1].CodeDescription}}</td>
                                        <td>{{status.treatment.prescription_data.dataArray[1].medicine_qty}}</td>
                                        <td>{{status.treatment.prescription_data.dataArray[1].Decline?"0":status.treatment.prescription_data.dataArray[1].BasePrice}}</td>
                                    </tr> -->

                                </tbody>
                                <td colspan="3" style="text-align: right;color: #568d2c; font-weight: bold;">Grand Total
                                </td>

                                <td> $ {{totalAmount}}</td>
                            </table>
                        </div>



                        <div class="row">
                            <div class="col-sm-12">
                                <pagination-controls id="past_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="PasthandlePageChange($event)">
                                </pagination-controls>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: none !important;">
                    <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();">Close</button>
                    <!-- <button type="submit" class="btn btn-primary" (click)="AddBreeding()">Save</button> -->
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- /.modal -->
</div>




<!-- copeing detailsssss -->

<div bsModal #secondaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Appointment details </h4>
                <div type="button" style="color:white;" class="close" (click)="secondaryModal.hide()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </div>
            </div>
            <div class="modal-body" *ngFor="let status of appointment_status ">
                <div class="card">
                    <div class="card-body">
                        <div class="row">

                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="select1"><span class="details">Doctor name:&nbsp;</span>
                                        {{status.doctor_name}}</label>

                                </div>
                                <div class="form-group">
                                    <label for="select1"> <span class="details">Doctor
                                            name:&nbsp;</span>{{status.prefer}}</label>

                                </div>
                                <div class="form-group">
                                    <label for="select1"> <span
                                            class="details">Reason:&nbsp;</span>{{status.kind_appointment}}</label>

                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="select1"> <span
                                            class="details">Date:&nbsp;</span>{{status.date}}</label>

                                </div>
                                <div class="form-group">
                                    <label for="select1"> <span
                                            class="details">Time:&nbsp;</span>{{status.time}}</label>

                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span
                                            class="details">Location:&nbsp;</span>{{status.location}}</label>

                                </div>
                            </div>
                        </div>
                        <br>
                        <!-- <hr> -->
                        <div class="row" *ngFor="let detail of status.user_id">

                            <div class="col-sm-6">
                                <h5><span class="details">Owner Detail</span></h5><br>
                                <div class="form-group">
                                    <!-- <label for="select1">Customer name:</label> -->
                                    <label for="select1"> <span class="details">Customer
                                            name:&nbsp;</span>{{detail.first_name}}</label>



                                    <!-- <input type="text" id="role-name12" class="form-control" [(ngModel)]="current_owner.first_name" [ngModelOptions]="{standalone: true}" readonly> -->
                                </div>

                                <!-- <div class="form-group">
                  <label for="select1">Phone Number:</label>
                  <input type="text" id="Phone-Number" class="form-control"
                    [ngModelOptions]="{standalone: true}" readonly value="+919555522525">

                </div> -->


                                <!-- <h5 style="visibility:hidden;">Owner Detail</h5> -->
                                <div class="form-group">

                                    <label for="select1"> <span
                                            class="details">Email:&nbsp;</span>{{detail.email}}</label>


                                    <!-- <div class="form-group">
      <label for="select1">Reminders:</label>
      <input type="text" id="Reminders" class="form-control"
        [ngModelOptions]="{standalone: true}" readonly value="March 3,2021: Wellness Exam">
    </div> -->
                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span class="details">Phone
                                            number:&nbsp;</span>{{detail.phone_number}}</label>



                                </div>
                            </div>

                        </div>
                        <!-- <hr> -->
                        <br>
                        <div class="row" *ngFor="let item of status.pet_id">
                            <div class="col-sm-6">

                                <h5><span class="details">Pet Detail</span></h5><br>
                                <div class="form-group">


                                    <label for="select1"> <span class="details">Pet
                                            Name:&nbsp;</span>{{item.pet_name}}</label>

                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span class="details">Pet medical
                                            ID:&nbsp;</span>{{item.pet_mid}}</label>

                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span
                                            class="details">Species,Breed:&nbsp;</span>{{item.animal_type}}</label>

                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span
                                            class="details">Color:&nbsp;</span>{{item.color}}</label>

                                </div>
                                <div class="form-group">

                                    <label for="select1"> <span class="details">Age:&nbsp;</span>{{item.dob}}</label>

                                </div>
                            </div>
                            <div class="col-sm-6" style="margin-top: 20px;">
                                <!-- <h5 style="visibility:hidden;">Pet Detail</h5> -->
                                <div class="form-group">

                                    <label for="select1"> <span class="details">Sex:&nbsp;</span>{{item.gender}}</label>

                                </div>
                                <div class="form-group">


                                    <label for="select1"> <span
                                            class="details">Weight:&nbsp;</span>{{status.treatment.weight}}</label>
                                    <!-- <input type="text" id="Weightin" class="form-control" readonly value="75lbs"> -->
                                </div>
                                <div class="form-group">
                                    <!-- <label for="select1">Allergies:</label> -->
                                    <label for="select1"> <span class="details">Allergies:&nbsp;</span>None</label>
                                    <!-- <input type="text" id="Allergies" class="form-control" readonly value="None"> -->
                                </div>
                                <div class="form-group">
                                    <!-- <label for="select1">Medical Alerts:</label> -->
                                    <label for="select1"> <span class="details">Medical Alerts:&nbsp;</span>None</label>
                                    <!-- <input type="text" id="Medical" class="form-control" readonly value="None"> -->
                                </div>
                            </div>

                        </div>
                    </div>

                    <br>

                    <h5><span class="col-sm-6" style="font-weight: bold;">Past History</span></h5><br>

                    <div class="card-body">
                        <!-- {{status.treatment.commonAsse}} -->
                        <div class="row">
                            <div class="col-sm-12">
                                <!-- <pagination-controls id="past_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="PasthandlePageChange($event)">
                                    </pagination-controls> -->
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 name-pastvisit">
                                <h5>{{status.doctor_name }}
                                    <!-- <span>{{status.apt_date_time | date:'MMM dd YYYY'}} at {{status.time}}</span> -->
                                </h5>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label for="select1"> <span class="details">Reason For
                                                    Visit:&nbsp;</span>{{status.kind_appointment}}</label>
                                            <!-- <label for="select1">Reason For Visit:</label> -->
                                            <!-- status.treatment. -->
                                            <!-- <input type="text" id="reason-visit" class="form-control" readonly [(ngModel)]="user.kind_appointment" [ngModelOptions]="{standalone: true}"> -->
                                        </div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1"> <span
                                                    class="details">Weight:&nbsp;</span>{{status.treatment.weight?status.treatment.weight:"---"}}</label>
                                            <!-- <label for="select1">Weight:</label> -->
                                            <!-- <input type="text" id="Weight" class="form-control" value="{{status.treatment.weight}}" readonly> -->
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1"> <span
                                                    class="details">Temp:&nbsp;</span>{{status.treatment.temp?status.treatment.temp:"---"}}</label>

                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1"> <span
                                                    class="details">Pulse:&nbsp;</span>{{status.treatment.pulse?status.treatment.pulse:"---"}}</label>

                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1"> <span
                                                    class="details">Resp:&nbsp;</span>{{status.treatment.resp?status.treatment.resp:"---"}}</label>

                                        </div>
                                    </div>
                                </div>
                                <!-- <hr> -->
                                <h5>Vaccines - <small>date last given or due date</small></h5>
                                <div class="row mt-4">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label for="select1"> <span
                                                    class="details">DHP:&nbsp;</span>{{status.treatment.vaccinationdata.DHP.date?status.treatment.vaccinationdata.DHP.date:"---"}}</label>

                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">BORD:&nbsp;</span>{{status.treatment.vaccinationdata.BORD.date?status.treatment.vaccinationdata.BORD.date:"---"}}</label>

                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">LEPTO:&nbsp;</span>{{status.treatment.vaccinationdata.LEPTO.date?status.treatment.vaccinationdata.LEPTO.date:"---"}}</label>


                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Rabies:&nbsp;</span>{{status.treatment.vaccinationdata.Rabies.date?status.treatment.vaccinationdata.Rabies.date:"---"}}</label>



                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">HWT:&nbsp;</span>{{status.treatment.vaccinationdata.HWT.date?status.treatment.vaccinationdata.HWT.date:"---"}}</label>


                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Fecal:&nbsp;</span>{{status.treatment.vaccinationdata.Fecal.date?status.treatment.vaccinationdata.Fecal.date:"---"}}</label>


                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Bloodwork:&nbsp;</span>{{status.treatment.vaccinationdata.Bloodwork.date?status.treatment.vaccinationdata.Bloodwork.date:"---"}}</label>


                                        </div>
                                    </div>

                                    <div class="col-sm-3">
                                        <div class="form-group">



                                            <label for="select1"> <span
                                                    class="details">Influenza:&nbsp;</span>{{status.treatment.vaccinationdata.Influenza.date?status.treatment.vaccinationdata.Influenza.date:"---"}}</label>



                                        </div>
                                    </div>
                                </div>
                                <!-- <hr> -->
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Indoor/Outdoor:&nbsp;</span>{{status.treatment.placePet?status.treatment.placePet:"---"}}</label>



                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Activity/Mobility:&nbsp;</span>{{status.treatment.activityPet?status.treatment.activityPet:"---"}}</label>



                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span class="details">Weight
                                                    Change:&nbsp;</span>{{status.treatment.weightchange?status.treatment.weightchange:"---"}}</label>




                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">E/D/U/D:&nbsp;</span>{{status.treatment.EDUD?status.treatment.EDUD:"---"}}</label>



                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">C/S/V/D:&nbsp;</span>{{status.treatment.CSVD?status.treatment.CSVD:"---"}}</label>

                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Stool:&nbsp;</span>{{status.treatment.Stool?status.treatment.Stool:"---"}}</label>



                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span class="details">Urinary
                                                    Habits:&nbsp;</span>{{status.treatment.UrinaryHabits?status.treatment.UrinaryHabits:"---"}}</label>


                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">


                                            <label for="select1"> <span class="details">Diet (including
                                                    Treats):&nbsp;</span>{{status.treatment.diet?status.treatment.diet:"---"}}</label>



                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Prescriptions/Supplements:&nbsp;</span>{{status.treatment.suppliment?status.treatment.suppliment:"---"}}</label>


                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span class="details">Flea/Heartworm
                                                    Prevention:&nbsp;</span>{{status.treatment.flea?status.treatment.flea:"---"}}</label>


                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span class="details">drinking
                                                    Habits:&nbsp;</span>{{status.treatment.drinkingHabits?status.treatment.drinkingHabits:"---"}}</label>


                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Appetite:&nbsp;</span>{{status.treatment.Appetite?status.treatment.Appetite:"---"}}</label>

                                        </div>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-md-4">


                                                    <label for="select1"> <span class="details">Any RX refills
                                                            needed:&nbsp;</span>{{status.treatment.RxRefill.Value?status.treatment.RxRefill.Value:"---"}}</label>



                                                </div>
                                                <div class="col-md-4">


                                                    <label for="select1"> <span class="details">Dental
                                                            <br>Care:&nbsp;</span>{{status.treatment.Dentalcare.Value?status.treatment.Dentalcare.Value:"---"}}</label>



                                                </div>
                                                <div class="col-md-4">


                                                    <label for="select1"> <span class="details">Nail<br>
                                                            Trim:&nbsp;</span>{{status.treatment.Nailtrim.Value?status.treatment.Nailtrim.Value:"---"}}</label>



                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">



                                            <label for="select1"> <span
                                                    class="details">Notes:&nbsp;</span>{{status.treatment.notes?status.treatment.notes:"---"}}</label>



                                        </div>
                                    </div>
                                    <!-- <div class="col-sm-12" *ngIf="user.treatment && user.treatment.RxRefill.Value!=''&& user.treatment.RxRefill.Value!='No'">
                                        <div class="form-group">
                                            <label for="select1">Refill Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.RefillNotes:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-12" *ngIf="user.treatment && user.treatment.Dentalcare.Value!=''&& user.treatment.Dentalcare.Value!='No'">
                                        <div class="form-group">
                                            <label for="select1">Dental Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.DentalNotes:''}}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group" *ngIf="user.treatment && user.treatment.Nailtrim.Value!='' && user.treatment.Nailtrim.Value!='No'">
                                            <label for="select1">Nail Trim Notes:</label>
                                            <input type="text" id="in-outdoor" class="form-control" value="{{user.treatment?user.treatment.NailTrimNotes:''}}" readonly>
                                        </div>
                                    </div> -->
                                </div>
                                <!-- <hr> -->
                                <div class="row">
                                    <div class="col-sm-12" style="text-align: center;">
                                        <div class="form-group" style=" display: inline-block;">
                                            <!-- <label for="select1">BCS -->

                                            <label for="select1"> <span
                                                    class="details">BCS:&nbsp;</span>{{status.treatment.bcs?status.treatment.bcs:"---"}}/9</label>

                                            <!-- <input type="text" id="BCS" class="form-control" readonly style=" display: inline-block;
                       width: auto;" value="{{user.treatment?user.treatment.bcs:''}}"> /9</label> -->
                                        </div>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <div class="form-group" style="display: inline-block; padding-left: 10px;">
                                            <!-- <label for="select1">CRT -->

                                            <label for="select1"> <span
                                                    class="details">CRT:&nbsp;</span>{{status.treatment.crt?status.treatment.crt:"---"}}/S</label>

                                            <!-- <input type="text" id="CRT" class="form-control" readonly style=" display: inline-block;
                       width: auto;align-content: center;" value="{{user.treatment?user.treatment.crt:''}}"> /S</label> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <!-- <label for="select1" style="width:50%;">General:</label> -->

                                            <label for="select1"> <span
                                                    class="details">General:&nbsp;</span>{{status.treatment.diseaselist.General
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.General != '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.General}}</label>

                                            <!-- <input type="text" id="General" class="form-control " readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.General:''}">
                                            <input type="text" id="General1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.General:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">EENT:&nbsp;</span>{{status.treatment.diseaselist.EENT
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.EENT != '' "> <span
                                                    class="details">Command:&nbsp;</span>
                                                {{status.treatment.diseaselist.EENT}} </label>


                                            <!-- <label for="select1" style="width:50%;">EENT:</label>
                                            <input type="text" id="EENT" class="form-control " readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.EENT:''}">
                                            <input type="text" id="EENT1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.EENT:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Oral:&nbsp;</span>{{status.treatment.diseaselist.Oral
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Oral != '' "> <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Oral}}</label>



                                            <!-- 
                                            <label for="select1" style="width:50%;">Oral:</label>
                                            <input type="text" id="Oral" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Oral:''}">
                                            <input type="text" id="Oral1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Oral:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Respiritory:&nbsp;</span>{{status.treatment.diseaselist.Respiritory
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Respiritory != '' "> <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Respiritory}}</label>



                                            <!-- <label for="select1" style="width:50%;">Respiritory:</label>
                                            <input type="text" id="Respiritory" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Respiritory:''}">
                                            <input type="text" id="Respiritory1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Respiritory:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Cardiovascular:&nbsp;</span>{{status.treatment.diseaselist.Cardiovascular
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Cardiovascular != '' "> <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Cardiovascular}}</label>


                                            <!-- <label for="select1" style="width:50%;">Cardiovascular:</label>
                                            <input type="text" id="Cardiovascular" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Cardiovascular:''}">
                                            <input type="text" id="Cardiovascular1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Cardiovascular:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">GI/Abdomen:&nbsp;</span>{{status.treatment.diseaselist['GI/Abdomen']
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist['GI/Abdomen']  != '' "> <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist['GI/Abdomen']
                                                }}</label>



                                            <!-- <label for="select1" style="width:50%;">GI/Abdomen:</label>
                                            <input type="text" id="GI/Abdomen" class="form-control " readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist['GI/Abdomen']:''}">
                                            <input type="text" id="GI/Abdomen1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist['GI/Abdomen']:''}"> -->


                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Musculoskel:&nbsp;</span>{{status.treatment.diseaselist.Musculoskel
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Musculoskel!= '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Musculoskel
                                                }}</label>


                                            <!-- <label for="select1" style="width:50%;">Musculoskel:</label>
                                            <input type="text" id="Musculoskel" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Musculoskel:''}">
                                            <input type="text" id="Musculoskel1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Musculoskel:''}"> -->

                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Integument:&nbsp;</span>{{status.treatment.diseaselist.Integument
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Integument!= '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Integument
                                                }}</label>


                                            <!-- <label for="select1" style="width:50%;">Integument:</label>
                                            <input type="text" id="Integument" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Integument:''}">
                                            <input type="text" id="Integument1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Integument:''}"> -->
                                        </div>
                                        <div class="form-group">
                                            <!-- <label for="select1" style="width:50%;">Uro-Genital:</label> -->

                                            <label for="select1"> <span
                                                    class="details">Uro-Genital:&nbsp;</span>{{status.treatment.diseaselist['Uro-Genital']
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist['Uro-Genital']!= '' "> <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist['Uro-Genital']
                                                }}</label>


                                            <!-- <input type="text" id="Uro-Genital" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist['Uro-Genital']:''}">
                                            <input type="text" id="Uro-Genital1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist['Uro-Genital']:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Lymphatic:&nbsp;</span>{{status.treatment.diseaselist.Lymphatic
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Lymphatic!= '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Lymphatic}}</label>

                                            <!-- 
                                            <label for="select1" style="width:50%;">Lymphatic:</label>
                                            <input type="text" id="Lymphatic" class="form-control " readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Lymphatic:''}">
                                            <input type="text" id="Lymphatic1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Lymphatic:''}"> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Neurologic:&nbsp;</span>{{status.treatment.diseaselist.Neurologic
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Neurologic!= '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Neurologic}}</label>

                                            <!-- 
                                            <label for="select1" style="width:50%;">Neurologic:</label>
                                            <input type="text" id="Neurologic" class="form-control" readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Neurologic:''}">
                                            <input type="text" id="Neurologic1" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Neurologic:''}"> -->
                                        </div>
                                        <div class="form-group">


                                            <label for="select1"> <span
                                                    class="details">Endocrine:&nbsp;</span>{{status.treatment.diseaselist.Endocrine
                                                === ""?"Norm":"Abn"}}</label><br>
                                            <label for="select1" *ngIf="status.treatment.diseaselist.Endocrine!= '' ">
                                                <span
                                                    class="details">Command:&nbsp;</span>{{status.treatment.diseaselist.Endocrine}}</label>

                                            <!-- 
                                            <label for="select1" style="width:50%;">Endocrine:</label>
                                            <input type="text" id="Endocrine1" class="form-control " readonly value="Norm" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?!user.treatment.diseaselist.Endocrine:''}">
                                            <input type="text" id="Endocrine" class="form-control" readonly value="Abn" style=" display: inline-block;
                         width: 20%; text-align: center;" [ngClass]="{'normal-case' : user.treatment?user.treatment.diseaselist.Endocrine:''}"> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Assessment:&nbsp;</span>{{status.treatment.commonAsse?status.treatment.commonAsse:"---"}}</label>





                                            <!-- <label for="select1">Assessment:</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.commonAsse:''}}</p> -->
                                        </div>
                                        <div class="form-group">

                                            <label for="select1"> <span
                                                    class="details">Plan:&nbsp;</span>{{status.treatment.plan?status.treatment.plan:"---"}}</label>


                                            <!-- <label for="select1">Plan:</label>
                                            <p class="para-textarea">{{user.treatment?user.treatment.plan:''}}</p> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="status.treatment">
                            <!-- <pre>{{status|json}}</pre> -->

                            <h4 style="color: #568d2c;">Prescription & Services</h4>
                            <table class="table table-bordered">
                                <thead>
                                    <tr style="background-color: #568d2c;">
                                        <th scope="col" class="text">No</th>
                                        <th scope="col" class="text">Description</th>
                                        <th scope="col" class="text">Qty</th>
                                        <th scope="col" class="text">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of status.treatment.prescription_data.dataArray; let i = index">
                                        <th scope="row">{{i+1}}</th>
                                        <td width="450px">{{item.CodeDescription}}</td>
                                        <td>{{item.medicine_qty}}</td>
                                        <td>$ {{item.Decline?"0.00":item.BasePrice
                                            <=21.85?(21.85*item.medicine_qty).toFixed(2):(item.BasePrice*item.medicine_qty).toFixed(2)}}< /td>

                                    </tr>
                                    <!-- <tr>
                                        <th scope="row">2</th>
                                        <td>{{status.treatment.prescription_data.dataArray[1].CodeDescription}}</td>
                                        <td>{{status.treatment.prescription_data.dataArray[1].medicine_qty}}</td>
                                        <td>{{status.treatment.prescription_data.dataArray[1].Decline?"0":status.treatment.prescription_data.dataArray[1].BasePrice}}</td>
                                    </tr> -->

                                </tbody>
                                <td colspan="3" style="text-align: right;color: #568d2c; font-weight: bold;">Grand Total
                                </td>

                                <td> $ {{totalAmount}}</td>
                            </table>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <!-- <pagination-controls id="past_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="PasthandlePageChange($event)">
                                </pagination-controls> -->
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="card"> -->
                <!-- <div class="card-header"> -->

                <!-- </div> -->


                <!-- </div> -->
                <div class="modal-footer" style="border-top: none !important;">
                    <button type="button" class="btn btn-secondary" (click)="secondaryModal.hide();">Close</button>
                    <!-- <button type="submit" class="btn btn-primary" (click)="AddBreeding()">Save</button> -->
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- /.modal -->
</div>



<div bsModal #AddAppointmentModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary modal-lg" role="document">

        <div class="modal-content">

            <div class="modal-header">

                <h4 class="modal-title" style="margin-left: 10px;">Add Appointment</h4>
                <div type="button" style="color:white;margin-top: -12px;margin-right: -11px;" class="close" (click)="AddAppointmentModal.hide(); closepopup()" aria-label="Close">

                    <span aria-hidden="true">&times;</span>
                </div>

            </div>

            <div class="modal-body">
                <div class="card">
                    <!-- for loop to be fix below the class card-body -->

                    <div class="card-body">

                        <button class="btn btn-primary" *ngIf="Save" style="position: relative;left: 90%;" (click)="AddBackendAppointment()">save</button>
                        <button class="btn btn-primary" *ngIf="emailSave" style="position: relative;left: 90%;" (click)="AddBackendPet()">save</button>
                        <div class="row">


                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="select1">Location:</label>

                                    <!-- <input type="text" id="role-name11" class="form-control" [(ngModel)]="App_Details.location" [ngModelOptions]="{standalone: true}" readonly> -->

                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="selectedLocation" (change)="page=1;selectLocation($event.target.value);">
                                        <option value=''>--Select Location--</option>
                                        <option *ngFor="let location of final_location" [value]="location.name">
                                            {{location.name}}</option>
                                    </select>

                                </div>

                                <div class="form-group">
                                    <label for="select1">Doctor name:</label>
                                    <!-- <input type="text" id="role-name21" class="form-control" value="{{App_Details.doctor_name}}" > -->
                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="selectDoctorName" (change)="page=1;selectDr($event.target.value);">
                                        <option value=''>--Select Doctor--</option>
                                        <option *ngFor="let role of locDoctor" [value]="role._id">{{role.name}}</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="select1">Reason:</label>
                                    <!-- <input type="text" id="role-name2211" class="form-control" [(ngModel)]="App_Details.kind_appointment" [ngModelOptions]="{standalone: true}"> -->

                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="selectReason" (change)="page=1;SelectReason($event.target.value);">
                                        <option value=''>--Select Reason--</option>
                                        <option *ngFor="let reason of ReasonData" [value]="reason.name">{{reason.name}}
                                        </option>
                                    </select>

                                </div>
                            </div>
                            <div class="col-sm-6">


                                <!-- <div class="form-group">
                                    <label for="select1">Date:</label>
                                    <input type="text" id="role-name221" placeholder="--Select Date--" autocomplete="off" [(ngModel)]="selectedDate" class="form-control" bsDatepicker (bsValueChange)="selectDate($event)" [minDate]="todayDate" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}"
                                        [ngModelOptions]="{standalone: true}">

                                </div> -->

                                <div class="form-group sticky-datepicker">
                                    <label for="select1">Date:</label>
                                    <input type="text" id="role-name221" placeholder="--Select Date--" autocomplete="off" [(ngModel)]="selectedDate" class="form-control" bsDatepicker (bsValueChange)="selectDate($event)" [minDate]="todayDate" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY', showWeekNumbers: false }"
                                        [ngModelOptions]="{ standalone: true }">
                                </div>




                                <div class="form-group">
                                    <label for="select1">Time:</label>


                                    <!-- <input type="" id="role-name121" class="form-control" [(ngModel)]="App_Details.time" [ngModelOptions]="{standalone: true}"> -->

                                    <!-- <input type="time" id="role-name121" class="form-control" [(ngModel)]="App_Details.time" [ngModelOptions]="{standalone: true}">  (change)="page=1;searched($event.target.value);-->

                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="selectTime" (change)="onTimeChange($event)">
                                        <option value=''>--Select Time--</option>
                                        <option *ngFor="let apptime of New_Appointments_time " [value]="apptime">
                                            {{apptime?apptime:'--Select Time--'}}</option>
                                    </select>

                                </div>

                                <div class="form-group">
                                    <label for="select1">Appointment type:</label>
                                    <!-- <input type="text" id="role-name211" class="form-control" [(ngModel)]="App_Details.prefer" [ngModelOptions]="{standalone: true}" > -->

                                    <select id="select1" name="select1" class="form-control" [(ngModel)]="selectAppointment" (change)="page=1;NewAppType($event.target.value);">
                                        <option value="">--select Appointment Type--</option>
                                        <option value="In-Person">In-Person</option>
                                        <!-- <option value="Video">Video</option> -->
                                    </select>
                                </div>


                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12" style="margin-left: -20px;
                            ">
                                <div class="form-group" style="margin-left: 20px;">
                                    <h5>Search Owner:</h5>
                                    <input type="text" class="focus form-control" #toggleButton (click)="this.showSuggestions = true; " [(ngModel)]="searchValue " (input)="search()" style="height: 40px;width: 100%;border-radius: 5px;border: 1px solid rgb(200, 200, 200);font-size: medium;padding-left: 10px;"
                                        placeholder="Search">

                                    <ul class="suggestions-list" *ngIf="showSuggestions" style="border: none; width: 93%; background-color:whitesmoke;border-radius: 5px;">
                                        <li *ngFor="let suggestion of Search_Data" (click)="searchMail(suggestion)" #menu>
                                            {{ suggestion.first_name+ " "+ (suggestion.last_name) +" (" + suggestion.email+")" }}
                                        </li>
                                    </ul>
                                </div>
                            </div>


                            <div class="col-sm-6">

                                <!-- <div class="form-group" style="margin-left: 20px;margin-top: 35px;">
                                    <h5>E-mail <span><input  style="    height: 40px;
                                        width: 89%;
                                        margin-left: 10px;
                                        border-radius: 5px;
                                        border: 1px solid rgb(200, 200, 200);font-size: medium;padding-left: 10px;" [(ngModel)]="searchValue" (input)="page=1;search()"  type="search" placeholder="Search email"></span></h5>
                                </div>
                                <div *ngFor="let item of Search_Data " style="    height: 20%;
                                width: 86%;
                                background-color: lightgrey;
                                margin-left: 11%;
                                margin-top: -3%;">
                                    <pre style=" font-weight: 100;color: blue;">{{item.email}}</pre>


                                </div> -->


                                <h5>Owner Detail</h5>

                                <div class="form-group">
                                    <label for="select1">Customer name:</label>
                                    <input type="text" id="role-name12" class="form-control" autocomplete="off" placeholder="Enter customer name" (change)="customer($event.target.value)" [(ngModel)]=" this.user_Details.first_name" [ngModelOptions]="{standalone: true}">
                                </div>

                                <div class="form-group">
                                    <label for="select1">Phone Number:</label>
                                    <input type="text" id="role-name12" maxlength="10" class="form-control" autocomplete="off" placeholder="Enter phone number" (change)="phone($event.target.value)" [(ngModel)]=" this.user_Details.phone_number" [ngModelOptions]="{standalone: true}">
                                </div>
                                <!-- <div class="form-group">
                  <label for="select1">Phone Number:</label>
                  <input type="text" id="Phone-Number" class="form-control"
                    [ngModelOptions]="{standalone: true}" readonly value="+919555522525">
                </div> -->
                            </div>
                            <div class="col-sm-6">
                                <h5 style="visibility:hidden;">Owner Detail</h5>

                                <div class="form-group">
                                    <label for="select1">Email:</label>
                                    <input type="email" id="email" class="form-control" autocomplete="off" placeholder="Enter email" (change)="Selectemail($event.target.value)" [ngModelOptions]="{standalone: true}" [(ngModel)]=" this.user_Details.email">
                                </div>
                                <!-- <div class="form-group">
                  <label for="select1">Reminders:</label>
                  <input type="text" id="Reminders" class="form-control"
                    [ngModelOptions]="{standalone: true}" readonly value="March 3,2021: Wellness Exam">
                </div> -->
                            </div>


                        </div>
                        <hr>

                        <div>

                            <!-- <pre>{{pet|json}}</pre> -->

                            <div class="col-sm-12">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <h5>Pet Detail</h5>
                                        <!-- <div style="position: relative; left: 77%;height: 200px;width: 200px; border-radius: 50%; border: 1px solid green;"><input type="file"></div> searched($event.target.value) -->
                                        <div style="margin-top: 20px;" *ngIf="this.pet_Details.length>1">
                                            <h6 for="">Select Pet:</h6>

                                            <select id="select1" style="width: 250px;" (change)="petselect($event.target.value)" name="select1" class="form-control">
                                                <!-- <option disabled selected ></option> -->
                                                <option *ngFor="let pet of searchdata;" [value]="pet._id"
                                                    [selected]="pet._id === selectedPetId">{{pet.pet_name}}</option>
                                            </select>


                                            <!-- <div>
                                                <ul class="list-group my-3">
                                                    <li class="list-group-item" *ngFor="let pet of pet_Details " (click)="petselect(pet)">
                                                        {{pet.pet_name}}
                                                    </li>
                                                </ul>
                                            </div> -->


                                        </div>
                                    </div>
                                    <h5 *ngIf="newpetLable" style="position: absolute;left: 45%;top:55%">New Pet</h5>
                                    <div class="col-sm-6">
                                        <button class="btn btn-primary" *ngIf="this.pet_Details.length>1 " (click)="clearInput()" style="float: right; margin-right: 10px;">Add New
                                            Pet</button>
                                    </div>
                                </div>

                            </div>

                            <div *ngIf="oldpetdetails">
                                <div class="row" *ngFor="let pet of pet_Details ; let i = index;">


                                    <div class="col-sm-6" *ngIf="i == pdindex">


                                        <div class="image-upload-container">
                                            <div class="image-placeholder" style="position: relative;bottom: 20px;" (click)="fileInput.click()">
                                                <img [src]="image_url||pet.image_url">
                                                <span *ngIf="!image_url && !pet.image_url" style="position: relative;right: 45%;">+</span>
                                            </div>
                                            <input #fileInput type="file" (change)="onFileSelected($event)" accept="image/*" hidden>
                                        </div>


                                        <div class="form-group">
                                            <label for="select1">Pet Name <span style="color: red;">*</span></label>
                                            <!-- <input type="text" id="petid" class="form-control" [(ngModel)]="pet._id" [ngModelOptions]="{standalone: true}" style="visibility:hidden"> -->
                                            <input type="text" id="role-name1" #petNameInput (blur)="petname()" (focus)="focuspet()" autocomplete="off" class="form-control" (change)="backpetName($event.target.value)" placeholder="Enter pet name" [(ngModel)]="pet.pet_name" [ngModelOptions]="{standalone: true}">


                                            <!-- <div *ngIf="petvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Pet name is required</label>
                                            </div> -->
                                        </div>


                                        <!-- <div class="form-group" >
                                    <label for="select1">Pet medical ID:</label>
                                    <input type="text" id="petmed" class="form-control" [ngModelOptions]="{standalone: true}" [(ngModel)]="current_pet.pet_mid">
                                </div> -->
                                        <div class="form-group">
                                            <label for="select1">Species <span style="color: red;"> *</span></label>
                                            <!-- <input type="text" id="role-nam2" class="form-control" [(ngModel)]="current_pet.animal_type" [ngModelOptions]="{standalone: true}"> -->

                                            <select id="select1" name="select1" #speciesInput (blur)="animaltype()" (focus)="focusanimaltype()" class="form-control" [(ngModel)]="pet.animal_type" (change)="page=1;SelectSpecies($event.target.value);">
                                                <option value=''>--select Species--</option>
                                                <option value="Dog">Dog</option>
                                                <option value="Cat">Cat</option>
                                            </select>

                                            <!-- <div *ngIf="animaltypevaldation">
                                                <label for="" style="color: red; margin-top: 2px;">Species is required</label>
                                            </div> -->
                                        </div>

                                        <div class="form-group">
                                            <label for="select1">Breed <span style="color: red;"> *</span></label>
                                            <!-- <input type="text" id="role-nam2" class="form-control" [(ngModel)]="current_pet.animal_type" [ngModelOptions]="{standalone: true}"> -->

                                            <select id="select1" #breedInput (blur)="breedtype()" (focus)="focusbreed()" name="select1" class="form-control" [(ngModel)]="pet.breed" (change)="selectedbreed($event.target.value)">
                                                <option value=''>{{pet?pet.breed:'--Select breed--'}}</option>
                                                <option *ngFor="let brd of  breed " [value]="brd.name">{{brd.name}}
                                                </option>
                                            </select>

                                            <!-- <div *ngIf="breedvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Breed is required</label>
                                            </div> -->


                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Age:</label>
                                            <input type="text" id="Age" #ageInput class="form-control" [(ngModel)]="this.petage" placeholder="Age" [ngModelOptions]="{standalone: true}" readonly>
                                        </div>


                                    </div>

                                    <div class="col-sm-6" style="position: relative;top: 142px;" *ngIf="i == pdindex">
                                        <h5 style="visibility: hidden;">Pet Detail</h5>
                                        <span style="position: absolute;right: 10px;margin-top: -30px;"></span>
                                        <div class="form-group">
                                            <label for="select1">Sex <span style="color: red;"> *</span></label>
                                            <!-- <input type="text" id="sex" class="form-control" [ngModelOptions]="{standalone: true}" [(ngModel)]="current_pet.gender"> -->


                                            <select id="select1" name="select1" #genderInput (blur)="sextype()" (focus)="focussex()" class="form-control" [(ngModel)]="pet.gender" (change)="page=1;petGender($event.target.value);">
                                                <option value="">--select--</option>
                                                <option value="Male">Male</option>
                                                <option value="female">Female</option>
                                            </select>

                                            <!-- <div *ngIf="sexvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Sex is required</label>
                                            </div> -->
                                        </div>
                                        <!-- <div class="form-group" >
                                    <label for="select1">Weight:</label>

                                    <input type="text" id="Weightin" class="form-control" [(ngModel)]="petweight" placeholder="Enter Weight" (change)="page=1;petWeight($event.target.value);" (blur)="ensurelbs()">
                                </div>
                                <div class="form-group" >
                                    <label for="select1">Allergies:</label>
                                    <input type="text" id="Allergies" class="form-control" value="None">
                                </div>
                                <div class="form-group" >
                                    <label for="select1">Medical Alerts:</label>
                                    <input type="text" id="Medical" class="form-control" value="None">
                                </div> -->

                                        <div class="form-group">
                                            <label for="select1">Date of Birth <span style="color: red;">
                                                    *</span></label>

                                            <!-- <input type="text" id="role-name221" class="form-control" [(ngModel)]="App_Details.date" [ngModelOptions]="{standalone: true}" readonly> [(ngModel)]="App_Details.date"  [ngModelOptions]="{standalone: true}"  -->

                                            <input type="text" id="role-name221" #dobInput (blur)="dobtype()" (focus)="focusdob()" autocomplete="off" [(ngModel)]="pet.dob" placeholder="--Select Date--" class="form-control" bsDatepicker (bsValueChange)="dod($event)" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}">

                                            <!-- <div *ngIf="dobvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Date is required</label>
                                            </div> -->

                                        </div>


                                        <div class="form-group">
                                            <label for="select1">Color <span style="color: red;"> *</span></label>
                                            <input type="text" #colorInput (blur)="colorname()" (focus)="focuscolor()" id="Color" class="form-control" placeholder="Enter color" [(ngModel)]="pet.color" autocomplete="off" (change)="color($event.target.value)" [ngModelOptions]="{standalone: true}">

                                            <!-- <div *ngIf="colorvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Color is required</label>
                                            </div> -->
                                        </div>

                                        <div class="form-group">
                                            <label for="select1">Spayed or Neutered<span style="color: red;"> *</span>
                                            </label>
                                            <!-- <input type="text" id="sex" class="form-control" [ngModelOptions]="{standalone: true}" [(ngModel)]="current_pet.gender"> -->


                                            <select id="select1" #spayInput name="select1" (blur)="spayedname()" (focus)="focusspayed()" class="form-control" [(ngModel)]="pet.spay" (change)="page=1;spayed($event.target.value);">
                                                <option value=''>--select--</option>
                                                <option value="Yes">Yes</option>
                                                <option value="No">No</option>
                                            </select>
                                            <!-- <div *ngIf="spayedvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Spayed or Neutered is required</label>
                                            </div> -->
                                        </div>








                                    </div>
                                </div>

                            </div>


                            <div *ngIf="newpetdetails">
                                <div class="row">


                                    <div class="col-sm-6">

                                        <div class="image-upload-container">
                                            <div class="image-placeholder" style="position: relative;bottom: 20px;" (click)="fileInput.click()">
                                                <img [src]="newpet_image_url">
                                                <span *ngIf="!newpet_image_url " style="position: relative;right: 45%;">+</span>
                                            </div>
                                            <input #fileInput type="file" (change)="newpetimage($event)" accept="image/*" hidden>
                                        </div>


                                        <div class="form-group">
                                            <label for="select1">Pet Name <span style="color: red;">*</span></label>

                                            <input type="text" id="role-name1" autocomplete="off" class="form-control" placeholder="Enter pet name" [(ngModel)]="addpetName" [ngModelOptions]="{standalone: true}">

                                            <!-- <div *ngIf="petvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Pet name is required</label>
                                            </div> -->

                                        </div>

                                        <!-- <button (click)="addpet()">click</button> -->

                                        <div class="form-group">
                                            <label for="select1">Species <span style="color: red;"> *</span></label>


                                            <select id="select1" name="select1" class="form-control" [(ngModel)]="newpet_animal_type" (change)="page=1;SelectNewSpecies($event.target.value);">
                                                <option value=''>--select Species--</option>
                                                <option value="Dog">Dog</option>
                                                <option value="Cat">Cat</option>
                                            </select>

                                            <!-- <div *ngIf="animaltypevaldation">
                                                <label for="" style="color: red; margin-top: 2px;">Species is required</label>
                                            </div> -->
                                        </div>

                                        <div class="form-group">
                                            <label for="select1">Breed <span style="color: red;"> *</span></label>


                                            <select id="select1" (blur)="breedtype()" (focus)="focusbreed()" name="select1" class="form-control" [(ngModel)]="addnewbreed" (change)="selectedNewbreed($event.target.value)">
                                                <option value=''>{{brd?pet.breed:'--Select breed--'}}</option>
                                                <option *ngFor="let brd of  Newbreed " [value]="brd.name">{{brd.name}}
                                                </option>
                                            </select>

                                            <!-- <div *ngIf="breedvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Breed is required</label>
                                            </div> -->


                                        </div>
                                        <div class="form-group">
                                            <label for="select1">Age:</label>
                                            <input type="text" id="Age" #ageInput class="form-control" [(ngModel)]="this.Newpetage" placeholder="Age" [ngModelOptions]="{standalone: true}" readonly>
                                        </div>


                                    </div>








                                    <div class="col-sm-6" style="position: relative;top: 142px;">
                                        <h5 style="visibility: hidden;">Pet Detail</h5>
                                        <span style="position: absolute;right: 10px;margin-top: -30px;"></span>
                                        <div class="form-group">
                                            <label for="select1">Sex <span style="color: red;"> *</span></label>



                                            <select id="select1" name="select1" (blur)="sextype()" (focus)="focussex()" class="form-control" [(ngModel)]="newpetgender" (change)="page=1;petGender($event.target.value);">
                                                <option value="" selected>--select--</option>
                                                <option value="Male">Male</option>
                                                <option value="female">Female</option>
                                            </select>

                                            <!-- <div *ngIf="sexvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Sex is required</label>
                                            </div> -->
                                        </div>


                                        <div class="form-group">
                                            <label for="select1">Date of Birth <span style="color: red;">
                                                    *</span></label>

                                            <input type="text" id="role-name221" (blur)="dobtype()" (focus)="focusdob()" autocomplete="off" [(ngModel)]="addnewdob" placeholder="--Select Date--" class="form-control" bsDatepicker (bsValueChange)="Newdod($event)" [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY',showWeekNumbers:false}">

                                            <!-- <div *ngIf="dobvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Date is required</label>
                                            </div> -->

                                        </div>


                                        <div class="form-group">
                                            <label for="select1">Color <span style="color: red;"> *</span></label>
                                            <input type="text" (blur)="colorname()" (focus)="focuscolor()" id="Color" class="form-control" placeholder="Enter color" [(ngModel)]="NewpetColor" autocomplete="off" (change)="NewPetcolor($event.target.value)" [ngModelOptions]="{standalone: true}">

                                            <!-- <div *ngIf="colorvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Color is required</label>
                                            </div> -->
                                        </div>

                                        <div class="form-group">
                                            <label for="select1">Spayed or Neutered<span style="color: red;"> *</span>
                                            </label>


                                            <select id="select1" name="select1" (blur)="spayedname()" (focus)="focusspayed()" class="form-control" [(ngModel)]="newpetspay" (change)="page=1;Newspayed($event.target.value);">
                                                <option value=''>--select--</option>
                                                <option value="Yes">Yes</option>
                                                <option value="No">No</option>
                                            </select>
                                            <!-- <div *ngIf="spayedvalidation">
                                                <label for="" style="color: red; margin-top: 2px;">Spayed or Neutered is required</label>
                                            </div> -->
                                        </div>








                                    </div>
                                </div>

                            </div>



                        </div>
                    </div>
                </div>

                <!-- <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();">Close</button>
                 
                </div> -->
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- /.modal -->
</div>