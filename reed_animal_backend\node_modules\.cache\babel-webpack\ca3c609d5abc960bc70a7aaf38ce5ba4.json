{"ast": null, "code": "import toInteger from './toInteger.js';\nimport toLength from './toLength.js';\n/**\n * The base implementation of `_.fill` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n */\n\nfunction baseFill(array, value, start, end) {\n  var length = array.length;\n  start = toInteger(start);\n\n  if (start < 0) {\n    start = -start > length ? 0 : length + start;\n  }\n\n  end = end === undefined || end > length ? length : toInteger(end);\n\n  if (end < 0) {\n    end += length;\n  }\n\n  end = start > end ? 0 : toLength(end);\n\n  while (start < end) {\n    array[start++] = value;\n  }\n\n  return array;\n}\n\nexport default baseFill;", "map": null, "metadata": {}, "sourceType": "module"}