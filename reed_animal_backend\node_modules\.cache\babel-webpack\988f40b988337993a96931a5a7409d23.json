{"ast": null, "code": "import copyArray from './_copyArray.js';\nimport shuffleSelf from './_shuffleSelf.js';\n/**\n * A specialized version of `_.shuffle` for arrays.\n *\n * @private\n * @param {Array} array The array to shuffle.\n * @returns {Array} Returns the new shuffled array.\n */\n\nfunction arrayShuffle(array) {\n  return shuffleSelf(copyArray(array));\n}\n\nexport default arrayShuffle;", "map": null, "metadata": {}, "sourceType": "module"}