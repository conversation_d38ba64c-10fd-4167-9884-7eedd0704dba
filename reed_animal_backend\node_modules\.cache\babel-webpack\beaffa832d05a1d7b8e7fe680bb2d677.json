{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor,\n    nativeRandom = Math.random;\n/**\n * The base implementation of `_.random` without support for returning\n * floating-point numbers.\n *\n * @private\n * @param {number} lower The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the random number.\n */\n\nfunction baseRandom(lower, upper) {\n  return lower + nativeFloor(nativeRandom() * (upper - lower + 1));\n}\n\nexport default baseRandom;", "map": null, "metadata": {}, "sourceType": "module"}