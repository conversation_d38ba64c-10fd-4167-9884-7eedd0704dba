{"ast": null, "code": "import createBaseFor from './_createBaseFor.js';\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\n\nvar baseFor = createBaseFor();\nexport default baseFor;", "map": null, "metadata": {}, "sourceType": "module"}