{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n/**\n * The base implementation of `_.inRange` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to check.\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\n\nfunction baseInRange(number, start, end) {\n  return number >= nativeMin(start, end) && number < nativeMax(start, end);\n}\n\nexport default baseInRange;", "map": null, "metadata": {}, "sourceType": "module"}