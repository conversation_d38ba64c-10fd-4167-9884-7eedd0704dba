{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\n\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = new array.constructor(length); // Add properties assigned by `RegExp#exec`.\n\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n\n  return result;\n}\n\nexport default initCloneArray;", "map": null, "metadata": {}, "sourceType": "module"}