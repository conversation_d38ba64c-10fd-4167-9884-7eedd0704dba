import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalModule } from 'ngx-bootstrap/modal';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormsModule } from '@angular/forms';

import { MasterRoutingModule } from './master-routing.module';
import { RoleComponent } from './role/role.component';
import { ModuleComponent } from './module/module.component';
// import { DoctorComponent } from './doctor/doctor.component';
import { AnimalTypeComponent } from './animal-type/animal-type.component';
import { TreatmentsComponent } from './treatments/treatments.component';
import { LocationComponent } from './location/location.component';
import { CovertusComponent } from './covertus/covertus.component';
import { EmployeeComponent } from './employee/employee.component';
import { ChangePasswordComponent } from '../pages/change-password/change-password.component';
import { BreedingComponent } from './breeding/breeding.component';
import { ReactiveFormsModule } from '@angular/forms';


@NgModule({
  declarations: [RoleComponent, ModuleComponent, BreedingComponent, AnimalTypeComponent, TreatmentsComponent, LocationComponent, CovertusComponent, EmployeeComponent, ChangePasswordComponent],
  imports: [
    CommonModule,
    MasterRoutingModule,
    ModalModule.forRoot(),
    NgxPaginationModule,
    FormsModule,ReactiveFormsModule
  ]
})
export class MasterModule { }
