import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class RoleService extends Api {

    //Add New Role 
    NewRole(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/role?token=${localStorage.auth_token}`, data);
    }

    //Get All Role 
    GetRoleList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/role?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular Role by using role id 
    GetRoleDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit role details
    UpdateRole(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Role by using id
    DeleteRole(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`);
    }
}