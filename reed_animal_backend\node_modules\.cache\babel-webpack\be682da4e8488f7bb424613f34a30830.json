{"ast": null, "code": "/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n  array || (array = Array(length));\n\n  while (++index < length) {\n    array[index] = source[index];\n  }\n\n  return array;\n}\n\nexport default copyArray;", "map": null, "metadata": {}, "sourceType": "module"}