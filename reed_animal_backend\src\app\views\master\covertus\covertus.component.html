<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Covertus
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col" style="margin-bottom: 12px;">
                        <button type="button" class="btn btn-primary" data-toggle="modal" (click)="UpdateCovertus();">
                            Update List </button>
                    </div>
                    <div class="col">

                    </div>
                    <div class="col">
                        <select id="select1" name="select1" style="width:100%" class="form-control" (change)="status=$event.target.value;GetCovertus();">
                            <option value=''>--Status--</option>
                            <option value='false'>Active</option>
                            <option value='true'>Inactive</option>
                        </select>
                    </div>
                    <div class="col">
                        <select id="select1" name="select1" style="width:100%" class="form-control" (change)="filter=$event.target.value;GetCovertus();">
                            <option value="">--Code Type--</option>
                            <option value="Diagnostic">Diagnostic</option>
                            <option value="Inventory">Inventory</option>
                            <option value="Service">Service</option>
                            <option value="Payment">Payment</option>
                            <option value="Problem">Problem</option>
                        </select>
                    </div>
                    <div class="col input-group">

                        <div class="input-group-prepend">
                            <span class="input-group-text" style="height: 35px;"><i class="fa fa-search"
                                    (click)="page=1;GetCovertus()"></i></span>
                        </div>
                        <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off"
                            class="form-control" (input)="page=1;GetCovertus()" [(ngModel)]="search">
                    </div>

                </div>
                <!-- Table Grid -->
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>DBID</th>
                            <th>ID</th>
                            <th>Code</th>
                            <th>Code <br>Category</th>
                            <th>Code Category <br>Description</th>
                            <th>Code Description</th>
                            <th>Code Type</th>
                            <th>Minimum <br>Price</th>
                            <th>Maximum <br>Price</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            *ngFor="let user of CovertusList | paginate: { id: 'listing_pagination',itemsPerPage: 10,   currentPage: page,totalItems: count };let i = index">
                            <td>{{user.DBID}}</td>
                            <td>{{user.Id}}</td>
                            <td>{{user.Code}}</td>
                            <td>{{user.CodeCategory}}</td>
                            <td>{{user.CodeCategoryDescription}}</td>
                            <td>{{user.CodeDescription}}</td>
                            <td>{{user.CodeType}}</td>
                            <td>{{user.MinimumPrice}}</td>
                            <td>{{user.MaximumPrice}}</td>
                            <td>{{user.Inactive==='false'?'Active':'Inactive'}}</td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5"
                        directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>