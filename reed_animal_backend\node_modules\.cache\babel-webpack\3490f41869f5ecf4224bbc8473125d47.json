{"ast": null, "code": "import isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n/** Used to match property names within property paths. */\n\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/;\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\n\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n\n  var type = typeof value;\n\n  if (type == 'number' || type == 'symbol' || type == 'boolean' || value == null || isSymbol(value)) {\n    return true;\n  }\n\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);\n}\n\nexport default isKey;", "map": null, "metadata": {}, "sourceType": "module"}