{"ast": null, "code": "import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n/**\n * The base implementation of `_.conforms` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property predicates to conform to.\n * @returns {Function} Returns the new spec function.\n */\n\nfunction baseConforms(source) {\n  var props = keys(source);\n  return function (object) {\n    return baseConformsTo(object, source, props);\n  };\n}\n\nexport default baseConforms;", "map": null, "metadata": {}, "sourceType": "module"}