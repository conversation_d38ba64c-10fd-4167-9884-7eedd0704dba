{"ast": null, "code": "import isObject from './isObject.js';\n/**\n * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` if suitable for strict\n *  equality comparisons, else `false`.\n */\n\nfunction isStrictComparable(value) {\n  return value === value && !isObject(value);\n}\n\nexport default isStrictComparable;", "map": null, "metadata": {}, "sourceType": "module"}