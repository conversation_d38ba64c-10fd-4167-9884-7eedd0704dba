{"ast": null, "code": "import baseForOwn from './_baseForOwn.js';\nimport createBaseEach from './_createBaseEach.js';\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\n\nvar baseEach = createBaseEach(baseForOwn);\nexport default baseEach;", "map": null, "metadata": {}, "sourceType": "module"}