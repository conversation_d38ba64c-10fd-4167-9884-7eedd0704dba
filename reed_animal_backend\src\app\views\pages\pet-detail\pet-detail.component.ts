import { Component, OnInit, ViewChild } from '@angular/core';
import { CustomerService } from '../../services/customer.services'
import { TokenStorageService } from '../../services/token-storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { customer, User } from '../../../views/models/pet-detail.models';
import { BreedingComponent } from '../../master/breeding/breeding.component';
import { AnimalTypeComponent } from '../../master/animal-type/animal-type.component';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { Employeeservice } from '../../services/employee.services';
import { PermissionService } from '../../services/permission.service';
import * as moment from "moment";
@Component({
  selector: 'app-pet-detail',
  templateUrl: './pet-detail.component.html',
  styleUrls: ['./pet-detail.component.scss']
})
export class PetDetailComponent implements OnInit {

  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('EditModal') public EditModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  upcomings: any;
  pastvisits: any;
  breedings: [];
  user: User = {}
  types: [];
  id = '';
  Pets = '';
  pet_id: any;
  pet_name = '';
  customers: customer = {};
  valid = false
  myDateValue: Date;
  AddForm: FormGroup;
  isFormReady = false;
  submitted = false;
  details = {
    spice: '',
    breed: '',
    dob: '',
    gender: '',
    spay: "",
    image_url: "",
  }
  selectedFiles?: FileList;
  currentFile?: File;
  picfailed = false;
  spayfailed = false;
  genderfailed = false;
  breedfailed = false;
  speciefailed = false;
  dobfailed = false;
  Add = true;
  Edit = true;
  Delete = true;
  date = ''
  constructor(
    private customerService: CustomerService, private route: ActivatedRoute, private router: Router,
    private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Breeding: BreedingComponent,
    private AnimalType: AnimalTypeComponent, private EmployeeService: Employeeservice,
    private Permission: PermissionService) {
    this.afterverified();
  }

  ngOnInit(): void {
    this.tokens();
    // this.SignForm();
  }

  //token verified module
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    if (key != null) {
      this.Permission.GetModule(Role.role_id._id)
        .subscribe((res: any) => {
          // console.log(res)
          for (var i = 0; i < res.data.length; i++) {
            if (res.data[i].module_name == "Pet-Detail") {
              this.Add = res.data[i].add
              this.Edit = res.data[i].edit
              this.Delete = res.data[i].delete
            }
          }
        })
      // this.EmployeeService.GetEmployeeDetail(Role._id)
      //   .subscribe((res) => {
      //     // console.log(res.data[0].status)
      //     if (res.data.status == false) {
      //       this.tokenStorage.signOut()
      //     }
      //   })
    }
    else {
      this.router.navigate(['/login']);
    }
  }

  afterverified(): void {
    this.route.queryParams
      .subscribe((params: any) => {
        this.id = params['search'];
        this.searchById();
      }
      );
  }

  //clear modal window
  clear(): void {
    this.submitted = false;
    this.isFormReady = true;
    this.picfailed = false;
    this.AddForm.reset();
    this.breedings = [];
    this.types = [];
    this.searchById()
  }

  //Search Name
  searchById(): void {
    const code = this.id;
    this.customerService.FindByUserId(code)
      .subscribe((res) => {
        this.user = res.user;
        // this.role_name = res.data[0].role_id.name
        // console.log('user-->', res)
        const dat = moment(res.user.createdAt)
        this.date = moment(dat).format('DD MMM YYYY')
        // console.log(this.date,dat)
        this.customerService.FindById(code)
          .subscribe((res: any) => {
            this.Pets = res.data;
            // this.pet_id = res.data[0]._id;
            if (res.data.length != 0) {
              this.customers = res.data[0];
              this.pet_id = res.data[0]._id;
              this.valid = true;
              // console.log('pet-->', res.data[0]._id);
              // console.log('pet',this.pet_id)
              this.onChange(res.data[0]._id)
            }
          })
      })

  }

  onChange(id): void {
    this.customerService.GetPetDetails(id)
      .subscribe((res: any) => {
        this.customers = res.data[0];
        // this.pet_name = res.data[0].pet_name
        const dat = moment(res.data[0].dob)
        this.pet_name = moment(dat).format('DD MMM YYYY')
        const data = moment().utc().format()
        // console.log(res.data[0].dob,data)
        this.customerService.GetPastVisit(id, data)
          .subscribe((res: any) => {
            this.pastvisits = res.data;
            // console.log('past-->', res);
            this.customerService.GetUpcomingAppoint(id, data)
              .subscribe((res: any) => {
                this.upcomings = res.data;
                // console.log('upcome-->', res);
              })
          })
      })



  }

  //DatePicker
  // onDateChange(newDate: Date) {
  //   // console.log(newDate);
  // }

  // SignForm() {
  //   this.AddForm = this.formBuilder.group({
  //     pet_name: ['', [Validators.required]],
  //     color: ['', [Validators.required]],
  //   });
  // }

  // get f() {
  //   return this.AddForm.controls;
  // }

  // Species(e) {
  //   this.details.spice = e.target.value
  //   this.customerService.GetBreedingsList(e.target.value)
  //     .subscribe((res: any) => {
  //       this.breedings = res.data;
  //     });
  // }

  // Breed(e) {
  //   this.details.breed = e.target.value
  // }

  // Gender(e) {
  //   this.details.gender = e.target.value
  // }

  // Spay(e) {
  //   this.details.spay = e.target.value
  // }

  // //File import 
  // onChanged(event: any): void {
  //   this.selectedFiles = event.target.files;
  //   console.log('filename-->', this.selectedFiles)
  //   const file: File | null = this.selectedFiles.item(0);
  //   this.customerService.uploadFile(file)
  //     .subscribe((res: any) => {
  //       this.details.image_url = res.data
  //       // console.log(res)
  //     })

  // }
  //Add New Customer
  // AddCustomer() {
  //   this.submitted = true;
  //   if (this.AddForm.invalid) {
  //     return
  //   }
  //   if (this.details.spay != '' && this.details.gender != '' && this.details.spice != '' && this.details.breed != '' && this.details.image_url != '' && this.id != '') {
  //     var date = new Date(this.myDateValue)
  //     var timeDiff = Math.abs(Date.now() - date.getTime());
  //     var age = Math.floor((timeDiff / (1000 * 3600 * 24)) / 365);
  //     var Dates = date.toLocaleDateString()
  //     const data = {
  //       pet_name: this.AddForm.value.pet_name,
  //       color: this.AddForm.value.color,
  //       spay: this.details.spay,
  //       gender: this.details.gender,
  //       animal_type: this.details.spice,
  //       breed: this.details.breed,
  //       image_url: this.details.image_url,
  //       user_id: this.id,
  //       dob: Dates,
  //       age: age
  //     }
  //     // console.log('new-->', data);
  //     this.customerService.AddPet(data)
  //       .subscribe((res) => {
  //         this.primaryModal.hide();
  //         window.location.reload();
  //       })
  //   }
  //   this.picfailed = true
  // }

  // //Delete Pet
  // DeletePet(id) {
  //   // console.log('id-->', id)
  //   this.customerService.Deletepet(id)
  //     .subscribe((res) => {
  //       // console.log('res-->', res)
  //       this.removeModal.hide();
  //       this.searchById()
  //     })
  // }

}