import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class treatmentService extends Api {

    //Add New treatment 
    NewTreatment(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/treatment?token=${localStorage.auth_token}`, data);
    }

    //Get All treatment Type 
    GetTreatmentsList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/treatment?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular treatment by using treatment id 
    GetTreatmentDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit treatment details
    UpdateTreatment(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete treatment by using id
    DeleteTreatment(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`);
    }
}