{"ast": null, "code": "import isSymbol from './isSymbol.js';\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\n\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var value = array[index],\n        current = iteratee(value);\n\n    if (current != null && (computed === undefined ? current === current && !isSymbol(current) : comparator(current, computed))) {\n      var computed = current,\n          result = value;\n    }\n  }\n\n  return result;\n}\n\nexport default baseExtremum;", "map": null, "metadata": {}, "sourceType": "module"}