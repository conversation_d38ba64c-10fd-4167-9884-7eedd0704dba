(self["webpackChunkDr_Reed_Admin_Panel"] = self["webpackChunkDr_Reed_Admin_Panel"] || []).push([[179],{

/***/ 98255:
/*!*******************************************************!*\
  !*** ./$_lazy_route_resources/ lazy namespace object ***!
  \*******************************************************/
/***/ (function(module) {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(function() {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = function() { return []; };
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 98255;
module.exports = webpackEmptyAsyncContext;

/***/ }),

/***/ 75874:
/*!*************************!*\
  !*** ./src/app/_nav.ts ***!
  \*************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "navItems": function() { return /* binding */ navItems; }
/* harmony export */ });
const navItems = [// {
//   name: 'Dashboard',
//   url: '/dashboard',
//   icon: 'icon-speedometer',
//   // badge: {
//   //   variant: 'info',
//   //   text: 'NEW'
//   // }
// },
{
  name: 'Customers',
  url: '/pages/customers',
  icon: 'fa fa-group'
}, {
  name: 'Appointments',
  url: '/pages/appointments',
  icon: 'cil-calendar-check'
}, {
  name: 'Availability',
  url: '/pages/availability',
  icon: 'fa fa-calendar'
}, {
  name: 'Resources',
  url: '/pages/resources',
  icon: 'cil-puzzle'
}, {
  name: 'Pelfies',
  url: '/pages/pelfies',
  icon: 'fa fa-picture-o'
}, {
  name: 'Shopping',
  url: '/pages/shopping',
  icon: 'fa fa-shopping-cart',
  children: [{
    name: 'Products',
    url: '/pages/shopping',
    icon: 'fa fa-id-card'
  }, {
    name: 'Banners',
    url: '/pages/banners',
    icon: 'fa fa-id-card'
  }, {
    name: 'Shop Setting',
    url: '/pages/shop-setting',
    icon: 'fa fa-id-card'
  }]
}, {
  name: 'Orders',
  url: '/pages/orders',
  icon: 'cil-task'
}, // {
//   name: 'Report',
//   url: '/pages/report',
//   icon: 'fa fa-file-text-o'
// },
{
  name: 'Change Password',
  url: '/pages/change-password',
  icon: 'fa fa-key'
}, {
  name: 'Settings',
  url: '/settings',
  icon: 'fa fa-mortar-board',
  children: [{
    name: 'Admin Users',
    url: '/settings/employee',
    icon: 'fa fa-id-card'
  }, {
    name: 'Role',
    url: '/settings/role',
    icon: 'fa fa-cogs'
  }, // {
  //   name: 'Module',
  //   url: '/settings/module',
  //   icon: 'fa fa-cubes'
  // },
  // {
  //   name: 'Permission',
  //   url: '/settings/permission',
  //   icon: 'fa fa-unlock-alt'
  // },
  // {
  //   name: 'Doctor',
  //   url: '/settings/doctor',
  //   icon: 'fa fa-hospital-o'
  // },
  {
    name: 'Species',
    url: '/settings/animal-type',
    icon: 'fa fa-paw'
  }, {
    name: 'Appointment Types',
    url: '/settings/appointment-types',
    icon: 'fa fa-user-md'
  }, {
    name: 'Location',
    url: '/settings/location',
    icon: 'fa fa-location-arrow'
  }, {
    name: 'Breed',
    url: '/settings/breed',
    icon: 'fa fa-paw'
  }, {
    name: 'Covetrus',
    url: '/settings/covetrus',
    icon: 'fa fa-unlock-alt'
  }]
}];

/***/ }),

/***/ 20721:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AppComponent": function() { return /* binding */ AppComponent; }
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @coreui/icons-angular */ 291);
/* harmony import */ var _coreui_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @coreui/icons */ 60956);
/* harmony import */ var _views_CommonInterceptor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./views/CommonInterceptor */ 60414);
/* harmony import */ var _nav__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_nav */ 75874);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 3048);









let AppComponent = /*#__PURE__*/(() => {
  class AppComponent {
    constructor(router, iconSet, httpStatus) {
      this.router = router;
      this.iconSet = iconSet;
      this.httpStatus = httpStatus;
      this.loader = false;
      this.navItems = _nav__WEBPACK_IMPORTED_MODULE_1__.navItems;
      this.navItem = [];
      iconSet.icons = Object.assign({}, _coreui_icons__WEBPACK_IMPORTED_MODULE_2__.freeSet);
      this.getHttpResponse();
    }

    ngOnInit() {
      this.router.events.subscribe(evt => {
        if (!(evt instanceof _angular_router__WEBPACK_IMPORTED_MODULE_3__.NavigationEnd)) {
          return;
        }

        window.scrollTo(0, 0);
      });
    }

    getHttpResponse() {
      this.httpStatus.getHttpStatus().subscribe(status => {
        this.loader = status;
      });
    }

  }

  AppComponent.ɵfac = function AppComponent_Factory(t) {
    return new (t || AppComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_coreui_icons_angular__WEBPACK_IMPORTED_MODULE_5__.IconSetService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_views_CommonInterceptor__WEBPACK_IMPORTED_MODULE_0__.HTTPStatus));
  };

  AppComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
    type: AppComponent,
    selectors: [["body"]],
    features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵProvidersFeature"]([_coreui_icons_angular__WEBPACK_IMPORTED_MODULE_5__.IconSetService])],
    decls: 1,
    vars: 0,
    template: function AppComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "router-outlet");
      }
    },
    directives: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterOutlet],
    encapsulation: 2
  });
  return AppComponent;
})();

/***/ }),

/***/ 50023:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AppModule": function() { return /* binding */ AppModule; }
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @angular/platform-browser */ 54464);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @angular/common */ 63237);
/* harmony import */ var _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @angular/platform-browser/animations */ 30519);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @angular/common/http */ 24242);
/* harmony import */ var ngx_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ngx-perfect-scrollbar */ 9010);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @coreui/icons-angular */ 291);
/* harmony import */ var _src_app_views_CommonInterceptor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../src/app/views/CommonInterceptor */ 60414);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 20721);
/* harmony import */ var ngx_pagination__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ngx-pagination */ 45055);
/* harmony import */ var _containers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./containers */ 59667);
/* harmony import */ var _views_error_404_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./views/error/404.component */ 46659);
/* harmony import */ var _views_error_500_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./views/error/500.component */ 14885);
/* harmony import */ var _views_login_login_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./views/login/login.component */ 77123);
/* harmony import */ var _views_register_register_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./views/register/register.component */ 4140);
/* harmony import */ var _views_forgot_password_forgot_password_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./views/forgot-password/forgot-password.component */ 19992);
/* harmony import */ var _views_reset_password_reset_password_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./views/reset-password/reset-password.component */ 95540);
/* harmony import */ var _coreui_angular__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @coreui/angular */ 7763);
/* harmony import */ var _app_routing__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./app.routing */ 59668);
/* harmony import */ var ngx_moment__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ngx-moment */ 24706);
/* harmony import */ var ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ngx-bootstrap/dropdown */ 64505);
/* harmony import */ var ngx_bootstrap_tabs__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ngx-bootstrap/tabs */ 58862);
/* harmony import */ var ng2_charts__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ng2-charts */ 85251);
/* harmony import */ var _configuration__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../configuration */ 49731);
/* harmony import */ var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ngx-bootstrap/modal */ 30386);
/* harmony import */ var _app_views_services_token_storage_service__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../app/views/services/token-storage.service */ 11192);
/* harmony import */ var _app_views_services_login_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../app/views/services/login.service */ 39582);
/* harmony import */ var _app_views_services_role_service__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../app/views/services/role.service */ 83711);
/* harmony import */ var _app_views_services_module_service__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../app/views/services/module.service */ 49533);
/* harmony import */ var _app_views_services_animal_type_services__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../app/views/services/animal_type.services */ 50022);
/* harmony import */ var _app_views_services_treatment_services__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../app/views/services/treatment.services */ 21771);
/* harmony import */ var _app_views_services_location_sevices__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../app/views/services/location.sevices */ 87188);
/* harmony import */ var _app_views_services_breeding_services__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../app/views/services/breeding.services */ 74143);
/* harmony import */ var _app_views_services_customer_services__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../app/views/services/customer.services */ 59815);
/* harmony import */ var _app_views_services_doctor_services__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ../app/views/services/doctor.services */ 43092);
/* harmony import */ var _app_views_services_resources_services__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../app/views/services/resources.services */ 86207);
/* harmony import */ var _views_app_forgot_password_app_forgot_password_component__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./views/app-forgot-password/app-forgot-password.component */ 65818);
/* harmony import */ var _app_views_services_appointments_services__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../app/views/services/appointments.services */ 79306);
/* harmony import */ var _app_views_services_product_services__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ../app/views/services/product.services */ 9499);
/* harmony import */ var _app_views_services_pelfies_services__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../app/views/services/pelfies.services */ 52831);
/* harmony import */ var ngx_bootstrap_datepicker__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ngx-bootstrap/datepicker */ 81372);
/* harmony import */ var _app_authguard__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../app/authguard */ 57481);
/* harmony import */ var _app_views_services_order_service__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ../app/views/services/order.service */ 5929);
/* harmony import */ var _app_views_services_covertus_services__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ../app/views/services/covertus.services */ 72945);
/* harmony import */ var ngx_toastr__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ngx-toastr */ 72318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @angular/core */ 3048);










 // Import containers








 // Import routing module


 // Import 3rd party components





 //Service





























const DEFAULT_PERFECT_SCROLLBAR_CONFIG = {
  suppressScrollX: true
};
const APP_CONTAINERS = [_containers__WEBPACK_IMPORTED_MODULE_3__.DefaultLayoutComponent];
let AppModule = /*#__PURE__*/(() => {
  class AppModule {}

  AppModule.ɵmod = _angular_core__WEBPACK_IMPORTED_MODULE_30__["ɵɵdefineNgModule"]({
    type: AppModule,
    bootstrap: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent]
  });
  AppModule.ɵinj = _angular_core__WEBPACK_IMPORTED_MODULE_30__["ɵɵdefineInjector"]({
    factory: function AppModule_Factory(t) {
      return new (t || AppModule)();
    },
    providers: [{
      provide: _angular_common__WEBPACK_IMPORTED_MODULE_31__.LocationStrategy,
      useClass: _angular_common__WEBPACK_IMPORTED_MODULE_31__.HashLocationStrategy
    }, {
      provide: _angular_common_http__WEBPACK_IMPORTED_MODULE_32__.HTTP_INTERCEPTORS,
      useClass: _src_app_views_CommonInterceptor__WEBPACK_IMPORTED_MODULE_0__.RequestInterceptor,
      multi: true
    }, _src_app_views_CommonInterceptor__WEBPACK_IMPORTED_MODULE_0__.HTTPStatus, _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__.IconSetService, _app_views_services_login_service__WEBPACK_IMPORTED_MODULE_13__.Loginservice, _app_views_services_resources_services__WEBPACK_IMPORTED_MODULE_22__.ResourcesService, _app_views_services_animal_type_services__WEBPACK_IMPORTED_MODULE_16__.AnimalTypeService, _app_views_services_appointments_services__WEBPACK_IMPORTED_MODULE_24__.AppointmentService, _app_views_services_customer_services__WEBPACK_IMPORTED_MODULE_20__.CustomerService, _app_views_services_breeding_services__WEBPACK_IMPORTED_MODULE_19__.BreedingService, _app_views_services_location_sevices__WEBPACK_IMPORTED_MODULE_18__.LocationService, _app_views_services_treatment_services__WEBPACK_IMPORTED_MODULE_17__.treatmentService, _app_views_services_token_storage_service__WEBPACK_IMPORTED_MODULE_12__.TokenStorageService, _configuration__WEBPACK_IMPORTED_MODULE_11__.Configuration, _app_views_services_role_service__WEBPACK_IMPORTED_MODULE_14__.RoleService, _app_views_services_module_service__WEBPACK_IMPORTED_MODULE_15__.ModuleService, _containers__WEBPACK_IMPORTED_MODULE_3__.DefaultLayoutComponent, _app_views_services_doctor_services__WEBPACK_IMPORTED_MODULE_21__.DoctorService, _app_authguard__WEBPACK_IMPORTED_MODULE_27__.AuthGuardService, _app_views_services_product_services__WEBPACK_IMPORTED_MODULE_25__.ProductService, _app_views_services_pelfies_services__WEBPACK_IMPORTED_MODULE_26__.PelfieService, _app_views_services_order_service__WEBPACK_IMPORTED_MODULE_28__.OrderService, _app_views_services_covertus_services__WEBPACK_IMPORTED_MODULE_29__.CovertusService],
    imports: [[ngx_bootstrap_datepicker__WEBPACK_IMPORTED_MODULE_34__.BsDatepickerModule.forRoot(), _angular_platform_browser__WEBPACK_IMPORTED_MODULE_35__.BrowserModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_36__.BrowserAnimationsModule, _app_routing__WEBPACK_IMPORTED_MODULE_10__.AppRoutingModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppAsideModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppBreadcrumbModule.forRoot(), _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppFooterModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppHeaderModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppSidebarModule, ngx_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_38__.PerfectScrollbarModule, ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_39__.BsDropdownModule.forRoot(), ngx_bootstrap_tabs__WEBPACK_IMPORTED_MODULE_40__.TabsModule.forRoot(), ng2_charts__WEBPACK_IMPORTED_MODULE_41__.ChartsModule, _angular_common__WEBPACK_IMPORTED_MODULE_31__.CommonModule, ngx_toastr__WEBPACK_IMPORTED_MODULE_42__.ToastrModule.forRoot(), _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__.IconModule, _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__.IconSetModule.forRoot(), _angular_common_http__WEBPACK_IMPORTED_MODULE_32__.HttpClientModule, _angular_forms__WEBPACK_IMPORTED_MODULE_43__.FormsModule, ngx_pagination__WEBPACK_IMPORTED_MODULE_2__.NgxPaginationModule, _angular_forms__WEBPACK_IMPORTED_MODULE_43__.ReactiveFormsModule, ngx_moment__WEBPACK_IMPORTED_MODULE_44__.MomentModule, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_45__.ModalModule]]
  });
  return AppModule;
})();

(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_30__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent, _containers__WEBPACK_IMPORTED_MODULE_3__.DefaultLayoutComponent, _views_error_404_component__WEBPACK_IMPORTED_MODULE_4__.P404Component, _views_error_500_component__WEBPACK_IMPORTED_MODULE_5__.P500Component, _views_login_login_component__WEBPACK_IMPORTED_MODULE_6__.LoginComponent, _views_register_register_component__WEBPACK_IMPORTED_MODULE_7__.RegisterComponent, _views_forgot_password_forgot_password_component__WEBPACK_IMPORTED_MODULE_8__.ForgotPasswordComponent, _views_reset_password_reset_password_component__WEBPACK_IMPORTED_MODULE_9__.ResetPasswordComponent, _views_app_forgot_password_app_forgot_password_component__WEBPACK_IMPORTED_MODULE_23__.AppForgotPasswordComponent],
    imports: [ngx_bootstrap_datepicker__WEBPACK_IMPORTED_MODULE_34__.BsDatepickerModule, _angular_platform_browser__WEBPACK_IMPORTED_MODULE_35__.BrowserModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_36__.BrowserAnimationsModule, _app_routing__WEBPACK_IMPORTED_MODULE_10__.AppRoutingModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppAsideModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppBreadcrumbModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppFooterModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppHeaderModule, _coreui_angular__WEBPACK_IMPORTED_MODULE_37__.AppSidebarModule, ngx_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_38__.PerfectScrollbarModule, ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_39__.BsDropdownModule, ngx_bootstrap_tabs__WEBPACK_IMPORTED_MODULE_40__.TabsModule, ng2_charts__WEBPACK_IMPORTED_MODULE_41__.ChartsModule, _angular_common__WEBPACK_IMPORTED_MODULE_31__.CommonModule, ngx_toastr__WEBPACK_IMPORTED_MODULE_42__.ToastrModule, // ToastrModule added
    _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__.IconModule, _coreui_icons_angular__WEBPACK_IMPORTED_MODULE_33__.IconSetModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_32__.HttpClientModule, _angular_forms__WEBPACK_IMPORTED_MODULE_43__.FormsModule, ngx_pagination__WEBPACK_IMPORTED_MODULE_2__.NgxPaginationModule, _angular_forms__WEBPACK_IMPORTED_MODULE_43__.ReactiveFormsModule, ngx_moment__WEBPACK_IMPORTED_MODULE_44__.MomentModule, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_45__.ModalModule]
  });
})();

/***/ }),

/***/ 59668:
/*!********************************!*\
  !*** ./src/app/app.routing.ts ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "routes": function() { return /* binding */ routes; },
/* harmony export */   "AppRoutingModule": function() { return /* binding */ AppRoutingModule; }
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _containers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./containers */ 59667);
/* harmony import */ var _views_forgot_password_forgot_password_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./views/forgot-password/forgot-password.component */ 19992);
/* harmony import */ var _views_error_404_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./views/error/404.component */ 46659);
/* harmony import */ var _views_error_500_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./views/error/500.component */ 14885);
/* harmony import */ var _views_login_login_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./views/login/login.component */ 77123);
/* harmony import */ var _views_register_register_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./views/register/register.component */ 4140);
/* harmony import */ var _views_reset_password_reset_password_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./views/reset-password/reset-password.component */ 95540);
/* harmony import */ var _views_app_forgot_password_app_forgot_password_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./views/app-forgot-password/app-forgot-password.component */ 65818);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common/http */ 24242);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 3048);
 // Import Containers












const routes = [{
  path: '',
  redirectTo: '/login',
  pathMatch: 'full'
}, {
  path: '404',
  component: _views_error_404_component__WEBPACK_IMPORTED_MODULE_2__.P404Component,
  data: {
    title: 'Page 404'
  }
}, {
  path: '500',
  component: _views_error_500_component__WEBPACK_IMPORTED_MODULE_3__.P500Component,
  data: {
    title: 'Page 500'
  }
}, {
  path: 'login',
  component: _views_login_login_component__WEBPACK_IMPORTED_MODULE_4__.LoginComponent,
  data: {
    title: 'Login Page'
  }
}, {
  path: 'register',
  component: _views_register_register_component__WEBPACK_IMPORTED_MODULE_5__.RegisterComponent,
  data: {
    title: 'Register Page'
  }
}, {
  path: 'forgot-password',
  component: _views_forgot_password_forgot_password_component__WEBPACK_IMPORTED_MODULE_1__.ForgotPasswordComponent,
  data: {
    title: 'Forgot Password'
  }
}, {
  path: 'reset-password',
  component: _views_reset_password_reset_password_component__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordComponent,
  data: {
    title: 'Reset Password'
  }
}, {
  path: 'app-reset-password',
  component: _views_app_forgot_password_app_forgot_password_component__WEBPACK_IMPORTED_MODULE_7__.AppForgotPasswordComponent,
  data: {
    title: 'Reset Password'
  }
}, {
  path: '',
  component: _containers__WEBPACK_IMPORTED_MODULE_0__.DefaultLayoutComponent,
  data: {
    title: 'Home'
  },
  children: [{
    path: 'settings',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e(246), __webpack_require__.e(891)]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/master/master.module */ 94891)).then(m => m.MasterModule)
  }, {
    path: 'pages',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e(246), __webpack_require__.e(582), __webpack_require__.e(938)]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/pages/pages.module */ 21938)).then(m => m.PagesModule)
  }, // {
  //   path: 'buttons',
  //   loadChildren: () => import('./views/buttons/buttons.module').then(m => m.ButtonsModule)
  // },
  // {
  //   path: 'charts',
  //   loadChildren: () => import('./views/chartjs/chartjs.module').then(m => m.ChartJSModule)
  // },
  // {
  //   path: 'dashboard',
  //   loadChildren: () => import('./views/dashboard/dashboard.module').then(m => m.DashboardModule)
  // },
  // {
  //   path: 'icons',
  //   loadChildren: () => import('./views/icons/icons.module').then(m => m.IconsModule)
  // },
  {
    path: 'notifications',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e(582), __webpack_require__.e(519)]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/notifications/notifications.module */ 59519)).then(m => m.NotificationsModule)
  }]
}, {
  path: '**',
  component: _views_error_404_component__WEBPACK_IMPORTED_MODULE_2__.P404Component
}];
let AppRoutingModule = /*#__PURE__*/(() => {
  class AppRoutingModule {}

  AppRoutingModule.ɵmod = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
    type: AppRoutingModule
  });
  AppRoutingModule.ɵinj = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
    factory: function AppRoutingModule_Factory(t) {
      return new (t || AppRoutingModule)();
    },
    imports: [[_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule.forRoot(routes, {
      relativeLinkResolution: 'legacy'
    }), _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule], _angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
  });
  return AppRoutingModule;
})();

(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterModule]
  });
})();

/***/ }),

/***/ 57481:
/*!******************************!*\
  !*** ./src/app/authguard.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AuthGuardService": function() { return /* binding */ AuthGuardService; }
/* harmony export */ });
/* harmony import */ var _views_services_employee_services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./views/services/employee.services */ 26415);
/* harmony import */ var _views_services_permission_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./views/services/permission.service */ 6642);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 3079);
 // import { AuthService } from './auth.service';







let AuthGuardService = /*#__PURE__*/(() => {
  class AuthGuardService {
    constructor(router, EmployeeService, Permission) {
      this.router = router;
      this.EmployeeService = EmployeeService;
      this.Permission = Permission;
    }

    canActivate(arr) {
      const Item = JSON.parse(localStorage.getItem('Verify'));
      this.verifyCustomer();
      const Verify = Item.includes(arr.data.path);

      if (Verify) {
        // logged in so return true
        return true;
      } // not logged in so redirect to login page


      this.router.navigate(['/login']);
      return false;
    }

    verifyCustomer() {
      const Item = JSON.parse(localStorage.getItem('auth-user'));
      this.EmployeeService.GetEmployeeDetail(Item._id).subscribe(res => {
        if (res.data.status === false) {
          localStorage.clear();
          this.router.navigate(['/login']);
        }
      });
    }

  }

  AuthGuardService.ɵfac = function AuthGuardService_Factory(t) {
    return new (t || AuthGuardService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_views_services_employee_services__WEBPACK_IMPORTED_MODULE_0__.Employeeservice), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_views_services_permission_service__WEBPACK_IMPORTED_MODULE_1__.PermissionService));
  };

  AuthGuardService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
    token: AuthGuardService,
    factory: AuthGuardService.ɵfac,
    providedIn: 'root'
  });
  return AuthGuardService;
})();

/***/ }),

/***/ 32457:
/*!***********************************************************************!*\
  !*** ./src/app/containers/default-layout/default-layout.component.ts ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DefaultLayoutComponent": function() { return /* binding */ DefaultLayoutComponent; }
/* harmony export */ });
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../app.component */ 20721);
/* harmony import */ var _app_views_services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../app/views/services/token-storage.service */ 11192);
/* harmony import */ var _app_views_services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../app/views/services/permission.service */ 6642);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helper */ 36779);
/* harmony import */ var _app_views_services_login_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../app/views/services/login.service */ 39582);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _coreui_angular__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @coreui/angular */ 7763);
/* harmony import */ var ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ngx-bootstrap/dropdown */ 64505);
/* harmony import */ var ngx_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ngx-perfect-scrollbar */ 9010);
/* harmony import */ var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ngx-bootstrap/modal */ 30386);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 63237);




















const _c0 = ["primaryModal"];

function DefaultLayoutComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "a", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_div_10_Template_a_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

      const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](30);

      return _r2.show();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Change password");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "a", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_div_10_Template_a_click_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return ctx_r9.ClearStorage();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](5, "i", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " Logout");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_47_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*old password is mandatory");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_47_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password must be at least 8 characters");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, DefaultLayoutComponent_div_47_div_1_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, DefaultLayoutComponent_div_47_div_2_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.f.old_password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.f.old_password.errors.minlength);
  }
}

function DefaultLayoutComponent_div_48_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*your old password is incorrect");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_56_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*new password is mandatory");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_56_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password must be at least 8 characters");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_56_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, DefaultLayoutComponent_div_56_div_1_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, DefaultLayoutComponent_div_56_div_2_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.f.new_password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.f.new_password.errors.minlength);
  }
}

function DefaultLayoutComponent_div_64_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password is mandatory");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_64_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password must be at least 8 characters");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_64_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Passwords must match");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function DefaultLayoutComponent_div_64_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, DefaultLayoutComponent_div_64_div_1_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, DefaultLayoutComponent_div_64_div_2_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, DefaultLayoutComponent_div_64_div_3_Template, 2, 0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.confirm_password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.confirm_password.errors.minlength);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.confirm_password.errors.mustMatch);
  }
}

const _c1 = function () {
  return {
    src: "assets/img/brand/logo.png",
    height: 50,
    alt: "Dr Reed Logo"
  };
};

const _c2 = function () {
  return {
    src: "assets/img/brand/sygnet.png",
    height: 30,
    alt: "Dr Reed Logo"
  };
};

const _c3 = function () {
  return {
    "backdrop": "static",
    "keyboard": false
  };
};

const _c4 = function (a0, a1) {
  return {
    "fa-eye-slash": a0,
    "fa-eye": a1
  };
};

const _c5 = function (a0) {
  return {
    "is-invalid": a0
  };
};

let DefaultLayoutComponent = /*#__PURE__*/(() => {
  class DefaultLayoutComponent {
    constructor(appComponet, tokenStorage, Permissionservice, router, formBuilder, service) {
      this.appComponet = appComponet;
      this.tokenStorage = tokenStorage;
      this.Permissionservice = Permissionservice;
      this.router = router;
      this.formBuilder = formBuilder;
      this.service = service;
      this.sidebarMinimized = false;
      this.navItems = [];
      this.isFormReady = false;
      this.submitted = false;
      this.old = false;
    }

    ngOnInit() {
      this.navItems = this.tokenStorage.getModule();
      const name = this.tokenStorage.getUser();
      this.Name = name.name;
      this.Role = name.role_id.name;
      this.Id = name._id;
      this.SignForm(); // console.log(this.Name)
      // console.log('here');
    }

    toggleMinimize(e) {
      // console.log(this.navItems)
      this.sidebarMinimized = e;
    } //Logout


    ClearStorage() {
      this.tokenStorage.signOut();
    } //Clear modal data


    clear() {
      this.AddForm.reset();
      this.isFormReady = false;
      this.submitted = false;
      this.old = false;
    }

    SignForm() {
      this.AddForm = this.formBuilder.group({
        old_password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(8)]],
        new_password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(8)]],
        confirm_password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(8)]]
      }, {
        validator: (0,_helper__WEBPACK_IMPORTED_MODULE_3__.MustMatch)('new_password', 'confirm_password')
      });
    }

    get f() {
      return this.AddForm.controls;
    }

    AddPassword() {
      this.submitted = true;

      if (this.AddForm.invalid) {
        return;
      } else {
        const data = {
          old_password: this.AddForm.value.old_password,
          new_password: this.AddForm.value.new_password,
          password: this.AddForm.value.confirm_password
        };
        this.service.GetUser(this.Id, data).subscribe(res => {
          if (res.code == 200) {
            this.old = false;
            this.service.EditEmployeeDetail(this.Id, data).subscribe(res1 => {
              this.primaryModal.hide();
              this.clear();
            });
          } else {
            this.old = true;
          }
        });
      }
    }

    toggleFieldTextType() {
      this.fieldTextType = !this.fieldTextType;
    }

    toggleFieldTextType1() {
      this.fieldTextType1 = !this.fieldTextType1;
    }

    toggleFieldTextType2() {
      this.fieldTextType2 = !this.fieldTextType2;
    }

  }

  DefaultLayoutComponent.ɵfac = function DefaultLayoutComponent_Factory(t) {
    return new (t || DefaultLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_views_services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_views_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_views_services_login_service__WEBPACK_IMPORTED_MODULE_4__.Loginservice));
  };

  DefaultLayoutComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
    type: DefaultLayoutComponent,
    selectors: [["app-dashboard"]],
    viewQuery: function DefaultLayoutComponent_Query(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);
      }

      if (rf & 2) {
        let _t;

        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
      }
    },
    decls: 70,
    vars: 47,
    consts: [[3, "fixed", "navbarBrandFull", "navbarBrandMinimized", "sidebarToggler", "asideMenuToggler", "mobileAsideMenuToggler", "mobileSidebarToggler"], [1, "nav", "navbar-nav", "ml-auto"], ["dropdown", "", "placement", "bottom right", 1, "nav-item", "dropdown"], ["data-toggle", "dropdown", "href", "#", "role", "button", "aria-haspopup", "true", "aria-expanded", "false", "dropdownToggle", "", 1, "nav-link", 3, "click"], ["src", "assets/img/avatars/6.jpg", "alt", "<EMAIL>", 1, "img-avatar"], ["class", "dropdown-menu dropdown-menu-right", "aria-labelledby", "simple-dropdown", 4, "dropdownMenu"], [1, "app-body"], [3, "fixed", "display", "minimized", "minimizedChange"], ["appSidebar", ""], [3, "navItems", "perfectScrollbar", "disabled"], [1, "main"], [1, "container-fluid"], ["href", "#"], [1, "ml-auto"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["primaryModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "row"], [1, "col-sm-12"], ["autocomplete", "off", 1, "form", 3, "formGroup"], ["for", "old_password"], [1, "input-group", "mb-3"], [1, "input-group-append"], [1, "input-group-text"], [1, "fa", 3, "ngClass", "click"], ["type", "password", "placeholder", "Enter old password", "formControlName", "old_password", "autocomplete", "off", 1, "form-control", 3, "type", "ngClass"], ["class", "invalid-feedback", 4, "ngIf"], ["style", "font-size:smaller;color:#f86c6b;margin-top: -15px;", 4, "ngIf"], ["for", "new_password"], ["type", "password", "placeholder", "Enter new password", "formControlName", "new_password", "autocomplete", "off", 1, "form-control", 3, "type", "ngClass"], ["for", "confirm_password"], ["type", "password", "placeholder", "re-enter new password", "formControlName", "confirm_password", "autocomplete", "off", 1, "form-control", 3, "type", "ngClass"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "click"], ["aria-labelledby", "simple-dropdown", 1, "dropdown-menu", "dropdown-menu-right"], [1, "dropdown-item", 3, "click"], [1, "fa", "fa-key"], [1, "fa", "fa-sign-out"], [1, "invalid-feedback"], [4, "ngIf"], [2, "font-size", "smaller", "color", "#f86c6b", "margin-top", "-15px"]],
    template: function DefaultLayoutComponent_Template(rf, ctx) {
      if (rf & 1) {
        const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "app-header", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "ul", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "span");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "strong");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](5, "br");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "li", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "a", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_a_click_8_listener() {
          return false;
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](9, "img", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](10, DefaultLayoutComponent_div_10_Template, 7, 0, "div", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "app-sidebar", 7, 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("minimizedChange", function DefaultLayoutComponent_Template_app_sidebar_minimizedChange_12_listener($event) {
          return ctx.toggleMinimize($event);
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](14, "app-sidebar-nav", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](15, "app-sidebar-minimizer");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "main", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](17, "cui-breadcrumb");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "div", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](19, "router-outlet");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "app-footer");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "span");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "a", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](23, "All Rights Reserved - ");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](24, " \u00A9 2021 Dr Reed.");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](25, "span", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](26, "Powered by ");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "a", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](28, "Dr Reed.");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "div", 14, 15);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 16);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 17);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "div", 18);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](34, "h4", 19);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](35, "Change Password");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 20);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 21);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "div", 22);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "form", 23);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](40, "label", 24);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](41, "Old Password");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](42, "div", 25);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "div", 26);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "span", 27);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "i", 28);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_i_click_45_listener() {
          return ctx.toggleFieldTextType1();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](46, "input", 29);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](47, DefaultLayoutComponent_div_47_Template, 3, 2, "div", 30);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](48, DefaultLayoutComponent_div_48_Template, 2, 0, "div", 31);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "label", 32);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](50, "New Password");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 25);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "div", 26);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "span", 27);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](54, "i", 28);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_i_click_54_listener() {
          return ctx.toggleFieldTextType();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](55, "input", 33);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](56, DefaultLayoutComponent_div_56_Template, 3, 2, "div", 30);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "label", 34);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](58, "Confirm Password");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](59, "div", 25);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](60, "div", 26);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "span", 27);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](62, "i", 28);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_i_click_62_listener() {
          return ctx.toggleFieldTextType2();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](63, "input", 35);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](64, DefaultLayoutComponent_div_64_Template, 4, 3, "div", 30);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "div", 36);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](66, "button", 37);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_button_click_66_listener() {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r17);

          const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](30);

          _r2.hide();

          return ctx.clear();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](67, "Cancel");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](68, "button", 38);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DefaultLayoutComponent_Template_button_click_68_listener() {
          return ctx.AddPassword();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](69, "Save");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      }

      if (rf & 2) {
        const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](13);

        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("fixed", true)("navbarBrandFull", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](29, _c1))("navbarBrandMinimized", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](30, _c2))("sidebarToggler", "lg")("asideMenuToggler", false)("mobileAsideMenuToggler", false)("mobileSidebarToggler", "lg");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx.Name);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx.Role);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("fixed", true)("display", "lg")("minimized", ctx.sidebarMinimized);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("navItems", ctx.navItems)("disabled", _r1.minimized);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](15);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](31, _c3));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.AddForm);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](32, _c4, !ctx.fieldTextType1, ctx.fieldTextType1));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("type", ctx.fieldTextType1 ? "text" : "password")("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](35, _c5, ctx.submitted && ctx.f.old_password.errors));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.old_password.errors);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.old);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](37, _c4, !ctx.fieldTextType, ctx.fieldTextType));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("type", ctx.fieldTextType ? "text" : "password")("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](40, _c5, ctx.submitted && ctx.f.new_password.errors));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.new_password.errors);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](42, _c4, !ctx.fieldTextType2, ctx.fieldTextType2));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("type", ctx.fieldTextType2 ? "text" : "password")("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](45, _c5, ctx.submitted && ctx.f.confirm_password.errors));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.confirm_password.errors);
      }
    },
    directives: [_coreui_angular__WEBPACK_IMPORTED_MODULE_8__.AppHeaderComponent, ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_9__.BsDropdownDirective, ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_9__.BsDropdownToggleDirective, ngx_bootstrap_dropdown__WEBPACK_IMPORTED_MODULE_9__.BsDropdownMenuDirective, _coreui_angular__WEBPACK_IMPORTED_MODULE_8__.AppSidebarComponent, _coreui_angular__WEBPACK_IMPORTED_MODULE_8__.AppSidebarNavComponent, ngx_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_10__.PerfectScrollbarDirective, _coreui_angular__WEBPACK_IMPORTED_MODULE_8__.AppSidebarMinimizerComponent, _coreui_angular__WEBPACK_IMPORTED_MODULE_8__.CuiBreadcrumbComponent, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterOutlet, _coreui_angular__WEBPACK_IMPORTED_MODULE_8__.AppFooterComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_11__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgClass, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgIf],
    encapsulation: 2
  });
  return DefaultLayoutComponent;
})();

/***/ }),

/***/ 36779:
/*!*****************************************************!*\
  !*** ./src/app/containers/default-layout/helper.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "MustMatch": function() { return /* binding */ MustMatch; }
/* harmony export */ });
// custom validator to check that two fields match
function MustMatch(controlName, matchingControlName) {
  return formGroup => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];

    if (matchingControl.errors && !matchingControl.errors.mustMatch) {
      // return if another validator has already found an error on the matchingControl
      return;
    } // set error on matchingControl if validation fails


    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({
        mustMatch: true
      });
    } else {
      matchingControl.setErrors(null);
    }
  };
}

/***/ }),

/***/ 2674:
/*!****************************************************!*\
  !*** ./src/app/containers/default-layout/index.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DefaultLayoutComponent": function() { return /* reexport safe */ _default_layout_component__WEBPACK_IMPORTED_MODULE_0__.DefaultLayoutComponent; }
/* harmony export */ });
/* harmony import */ var _default_layout_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./default-layout.component */ 32457);


/***/ }),

/***/ 59667:
/*!*************************************!*\
  !*** ./src/app/containers/index.ts ***!
  \*************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DefaultLayoutComponent": function() { return /* reexport safe */ _default_layout__WEBPACK_IMPORTED_MODULE_0__.DefaultLayoutComponent; }
/* harmony export */ });
/* harmony import */ var _default_layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./default-layout */ 2674);


/***/ }),

/***/ 31681:
/*!******************************!*\
  !*** ./src/app/views/Api.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "httpOptions": function() { return /* binding */ httpOptions; },
/* harmony export */   "Api": function() { return /* binding */ Api; }
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 24242);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 267);
/* harmony import */ var _environments_environment_prod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment.prod */ 93963);
/* harmony import */ var _configuration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../configuration */ 49731);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 3048);
/*
 * spurtcommerce
 * version 4.3
 * http://www.spurtcommerce.com
 *
 * Copyright (c) 2020 piccosoft ltd
 * Author piccosoft ltd <<EMAIL>>
 * Licensed under the MIT license.
 */







const httpOptions = {
  headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpHeaders({
    'Content-Type': 'application/json'
  }),
  withCredentials: false
};
let Api = /*#__PURE__*/(() => {
  class Api {
    constructor(http, config) {
      this.http = http;
      this.config = config;
    }

    getBaseUrl() {
      return _environments_environment_prod__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl;
    }

    handleError(operation = 'operation', result) {
      return error => {
        // TODO: send the error to remote logging infrastructure
        // TODO: better job of transforming error for user consumption
        // Let the app keep running by returning an empty result.
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.of)(result);
      };
    }

  }

  Api.ɵfac = function Api_Factory(t) {
    return new (t || Api)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_configuration__WEBPACK_IMPORTED_MODULE_1__.Configuration));
  };

  Api.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjectable"]({
    token: Api,
    factory: Api.ɵfac
  });
  return Api;
})();

/***/ }),

/***/ 60414:
/*!********************************************!*\
  !*** ./src/app/views/CommonInterceptor.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "HTTPStatus": function() { return /* binding */ HTTPStatus; },
/* harmony export */   "RequestInterceptor": function() { return /* binding */ RequestInterceptor; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common/http */ 24242);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 29923);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 7535);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 88561);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 72072);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var ngx_toastr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ngx-toastr */ 72318);





 // import { ToastrManager } from 'ng6-toastr-notifications';





let HTTPStatus = /*#__PURE__*/(() => {
  class HTTPStatus {
    constructor() {
      this.requestInFlight$ = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(false);
    }

    setHttpStatus(inFlight) {
      this.requestInFlight$.next(inFlight);
    }

    getHttpStatus() {
      return this.requestInFlight$.asObservable();
    }

  }

  HTTPStatus.ɵfac = function HTTPStatus_Factory(t) {
    return new (t || HTTPStatus)();
  };

  HTTPStatus.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: HTTPStatus,
    factory: HTTPStatus.ɵfac
  });
  return HTTPStatus;
})();
let RequestInterceptor = /*#__PURE__*/(() => {
  class RequestInterceptor {
    constructor(router, platformId, toastr, status) {
      this.router = router;
      this.platformId = platformId;
      this.toastr = toastr;
      this.status = status;
      this.userTokenDetail = {};
    }

    intercept(req, next) {
      // if (isPlatformBrowser(this.platformId)) {
      //     this.userTokenDetail = localStorage.getItem('adminUserdetail') ? JSON.parse(localStorage.getItem('adminUserdetail')) : {};
      // }
      this.status.setHttpStatus(true);

      if (this.userTokenDetail) {
        req = req.clone({
          setHeaders: {
            Authorization: `Bearer ${localStorage.auth_token}`
          }
        });
      }

      return next.handle(req).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(user => {
        if (user instanceof _angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpResponse) {
          this.status.setHttpStatus(false);
          const response = user.body;

          if (response.message && response.message !== '' && req.method !== 'GET') {
            this.showSuccess(user.body.message);
          }
        }

        return user;
      }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.catchError)(response => {
        this.status.setHttpStatus(false);
        console.log('response==-->', response);

        if (response.status === 200 || response.status === 201) {
          return response;
        }

        switch (response.status) {
          case 400:
            this.handleBadRequest(response);
            break;

          case 422:
            this.handleUnProcessableEntry(response.error);
            break;

          case 401:
            this.handleUnAuthorized();
            break;

          case 500:
            this.handleServerError();
            break;

          default:
            break;
        }

        return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(response);
      }));
    }
    /**
     * Shows notification errors when server response status is 401
     *
     * @params error
     */


    handleBadRequest(responseBody) {
      if (responseBody.url === 'http://api.spurtcommerce.com/api/product/product-excel-list/?productId=') {
        this.showNotificationError('Please Choose a Valid Data');
      }

      if (responseBody.url === 'http://api.spurtcommerce.com/api/order/order-excel-list/?orderId=') {
        this.showNotificationError('Please Choose a Valid Data');
      }

      if (responseBody.url === 'http://api.spurtcommerce.com/api/customer/customer-excel-list/?customerId=') {
        this.showNotificationError('Please Choose a Valid Data');
      }

      if (responseBody.error) {
        try {
          // if(responseBody.)
          const bodyParsed = responseBody.error;
          this.handleErrorMessages(bodyParsed);
        } catch (error) {// this.handleServerError();
        }
      } // else this.handleServerError();

    }

    handleErrorMessages(response) {
      if (!response) {
        return;
      }

      if (!response.message) {
        return;
      }

      this.showNotificationError(response.message);
    }

    handleUnProcessableEntry(error) {
      if (error && error.data && error.data.message) {
        this.showNotificationError(Array.isArray(error.data.message) ? error.data.message[0] : error.data.message);
      }
    }
    /**
     * redirect to login if un authorized
     *
     */


    handleUnAuthorized() {
      localStorage.clear();
      this.router.navigate(['/auth/login']);
    }

    handleServerError() {
      this.showNotificationError('Server Error');
    }
    /**
     * Shows error notification with given title and message
     *
     * @params message
     */


    showNotificationError(message) {
      console.log('error');
      this.toastr.error(message);
    }

    showSuccess(message) {
      console.log('login');
      this.toastr.success(message);
    }

  }

  RequestInterceptor.ɵfac = function RequestInterceptor_Factory(t) {
    return new (t || RequestInterceptor)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_core__WEBPACK_IMPORTED_MODULE_1__.PLATFORM_ID), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](ngx_toastr__WEBPACK_IMPORTED_MODULE_7__.ToastrService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](HTTPStatus));
  };

  RequestInterceptor.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: RequestInterceptor,
    factory: RequestInterceptor.ɵfac
  });
  return RequestInterceptor;
})();

/***/ }),

/***/ 65818:
/*!****************************************************************************!*\
  !*** ./src/app/views/app-forgot-password/app-forgot-password.component.ts ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AppForgotPasswordComponent": function() { return /* binding */ AppForgotPasswordComponent; }
/* harmony export */ });
/* harmony import */ var _views_services_login_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../views/services/login.service */ 39582);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 63237);









function AppForgotPasswordComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "h3", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Invalid Request!.");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "h3", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Password changed successfully.");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_16_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "*Password cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_16_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must be 8 digits");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_16_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must contain uppercase, lowercase, special character and numbers");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, AppForgotPasswordComponent_div_2_div_16_div_1_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, AppForgotPasswordComponent_div_2_div_16_div_2_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, AppForgotPasswordComponent_div_2_div_16_div_3_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.minlength);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.pattern);
  }
}

function AppForgotPasswordComponent_div_2_div_22_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "*Password cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_22_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must be a 8 digits");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_22_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must contain uppercase, lowercase, special character and numbers");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function AppForgotPasswordComponent_div_2_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, AppForgotPasswordComponent_div_2_div_22_div_1_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, AppForgotPasswordComponent_div_2_div_22_div_2_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, AppForgotPasswordComponent_div_2_div_22_div_3_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.password.errors.minlength);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.firstName.errors.pattern);
  }
}

function AppForgotPasswordComponent_div_2_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Please make sure your passwords match ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

const _c0 = function (a0) {
  return {
    "is-invalid": a0
  };
};

function AppForgotPasswordComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();

    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "main", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function AppForgotPasswordComponent_div_2_Template_form_ngSubmit_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r12.onSubmit();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "h1", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "Reset Password");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](12, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](13, "span", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](14, "i", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "input", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function AppForgotPasswordComponent_div_2_Template_input_click_15_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r14.getfocus();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](16, AppForgotPasswordComponent_div_2_div_16_Template, 4, 3, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](18, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "span", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](20, "i", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "input", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function AppForgotPasswordComponent_div_2_Template_input_click_21_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r15.getfocus();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](22, AppForgotPasswordComponent_div_2_div_22_Template, 4, 3, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](23, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](24, AppForgotPasswordComponent_div_2_div_24_Template, 2, 0, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](25, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](28, "Submit");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx_r2.validForm);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](6, _c0, ctx_r2.submitted && ctx_r2.f.firstName.errors));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.submitted && ctx_r2.f.firstName.errors);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](8, _c0, ctx_r2.submitted && ctx_r2.f.password.errors));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.submitted && ctx_r2.f.password.errors);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.failed);
  }
}

let AppForgotPasswordComponent = /*#__PURE__*/(() => {
  class AppForgotPasswordComponent {
    constructor(formBuilder, userservice, route, router) {
      this.formBuilder = formBuilder;
      this.userservice = userservice;
      this.route = route;
      this.router = router;
      this.token = '';
      this.TokenFailed = false;
      this.TokenPass = false;
      this.password = {
        firstName: '',
        password: ''
      };
      this.errormessage = '';
      this.required = false;
      this.isLoginFailed = false;
      this.response = [];
      this.isFormReady = false;
      this.submitted = false;
      this.show = false;
      this.failed = false;
      this.success = false;
    }

    ngOnInit() {
      this.route.queryParams.subscribe(params => {
        const data = {
          token: params['token']
        }; // console.log('data-->',data)

        this.userservice.userPasswordToken(data).subscribe(res => {
          if (res.code === 100) {
            this.TokenFailed = true;
          } else {
            this.TokenPass = true;
            this.token = params['token'];
            this.valid();
          } // console.log(res)

        });
      });
      this.valid();
    }

    valid() {
      this.validForm = this.formBuilder.group({
        firstName: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(8)]],
        password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(8)]]
      });
    } //Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]'),


    getfocus() {
      this.failed = false;
    }

    get f() {
      return this.validForm.controls;
    }

    onSubmit() {
      this.submitted = true;

      if (this.validForm.invalid) {
        return;
      } else {
        if (this.validForm.value.firstName != '' && this.validForm.value.password != '' && this.validForm.value.firstName.length >= 8 && this.validForm.value.password.length >= 8) {
          if (this.validForm.value.firstName === this.validForm.value.password) {
            this.submitted = true;
            this.isFormReady = true;
            const inputRequest = {
              newPassword: this.validForm.value.firstName,
              confirmPassword: this.validForm.value.password,
              token: this.token
            }; // console.log('result-->', inputRequest);

            this.userservice.userChangePassword(inputRequest).subscribe(result => {
              // console.log('result-->', result);
              if (result.code === 200) {
                this.success = true;
                this.TokenPass = false;
              } else {
                this.errormessage = result.data;
              }
            });
          } else {
            this.failed = true;
          }
        }
      }
    }

  }

  AppForgotPasswordComponent.ɵfac = function AppForgotPasswordComponent_Factory(t) {
    return new (t || AppForgotPasswordComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_views_services_login_service__WEBPACK_IMPORTED_MODULE_0__.Loginservice), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
  };

  AppForgotPasswordComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
    type: AppForgotPasswordComponent,
    selectors: [["app-app-forgot-password"]],
    decls: 3,
    vars: 3,
    consts: [[4, "ngIf"], ["class", "app-body auth-login-sign", "style", "margin-top: 172px", 4, "ngIf"], [2, "color", "red", "font-weight", "600", "text-align", "center", "padding", "200px"], [2, "color", "green", "font-weight", "600", "text-align", "center", "padding", "250px 50px 0px"], [1, "app-body", "auth-login-sign", 2, "margin-top", "172px"], [1, "main", "d-flex", "align-items-center"], [1, "container"], [1, "row"], [1, "col-md-5", "mx-auto"], [1, "card-group"], [1, "card", "p-4"], [1, "card-body"], ["autocomplete", "off", 1, "form", 3, "formGroup", "ngSubmit"], [2, "margin-bottom", "20px"], [1, "input-group", "mb-3"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-lock", "icons"], ["type", "password", "placeholder", "New Password", "required", "", "formControlName", "firstName", 1, "form-control", 3, "ngClass", "click"], ["class", "invalid-feedback", 4, "ngIf"], ["type", "password", "placeholder", "Confirm New Password", "required", "", "formControlName", "password", 1, "form-control", 3, "ngClass", "click"], [1, "form-group"], ["class", "alert alert-danger", "role", "alert", 4, "ngIf"], [1, "col-12"], [1, "btn", "btn-primary", "px-4"], [1, "invalid-feedback"], ["role", "alert", 1, "alert", "alert-danger"]],
    template: function AppForgotPasswordComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, AppForgotPasswordComponent_div_0_Template, 3, 0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, AppForgotPasswordComponent_div_1_Template, 3, 0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, AppForgotPasswordComponent_div_2_Template, 29, 10, "div", 1);
      }

      if (rf & 2) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.TokenFailed);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.success);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.TokenPass);
      }
    },
    directives: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass],
    styles: [""]
  });
  return AppForgotPasswordComponent;
})();

/***/ }),

/***/ 46659:
/*!**********************************************!*\
  !*** ./src/app/views/error/404.component.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "P404Component": function() { return /* binding */ P404Component; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 3048);

let P404Component = /*#__PURE__*/(() => {
  class P404Component {
    constructor() {}

  }

  P404Component.ɵfac = function P404Component_Factory(t) {
    return new (t || P404Component)();
  };

  P404Component.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
    type: P404Component,
    selectors: [["ng-component"]],
    decls: 19,
    vars: 0,
    consts: [[1, "app", "flex-row", "align-items-center"], [1, "container"], [1, "row", "justify-content-center"], [1, "col-md-6"], [1, "clearfix"], [1, "float-left", "display-3", "mr-4"], [1, "pt-3"], [1, "text-muted"], [1, "input-prepend", "input-group"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "fa", "fa-search"], ["id", "prependedInput", "size", "16", "type", "text", "placeholder", "What are you looking for?", 1, "form-control"], [1, "input-group-append"], ["type", "button", 1, "btn", "btn-info"]],
    template: function P404Component_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "div", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "h1", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, "404");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "h4", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8, "Oops! You're lost.");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "p", 7);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10, "The page you are looking for was not found.");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "div", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](14, "i", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](15, "input", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "span", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "button", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18, "Search");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      }
    },
    encapsulation: 2
  });
  return P404Component;
})();

/***/ }),

/***/ 14885:
/*!**********************************************!*\
  !*** ./src/app/views/error/500.component.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "P500Component": function() { return /* binding */ P500Component; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 3048);

let P500Component = /*#__PURE__*/(() => {
  class P500Component {
    constructor() {}

  }

  P500Component.ɵfac = function P500Component_Factory(t) {
    return new (t || P500Component)();
  };

  P500Component.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
    type: P500Component,
    selectors: [["ng-component"]],
    decls: 19,
    vars: 0,
    consts: [[1, "app", "flex-row", "align-items-center"], [1, "container"], [1, "row", "justify-content-center"], [1, "col-md-6"], [1, "clearfix"], [1, "float-left", "display-3", "mr-4"], [1, "pt-3"], [1, "text-muted"], [1, "input-prepend", "input-group"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "fa", "fa-search"], ["id", "prependedInput", "size", "16", "type", "text", "placeholder", "What are you looking for?", 1, "form-control"], [1, "input-group-append"], ["type", "button", 1, "btn", "btn-info"]],
    template: function P500Component_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "div", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "h1", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, "500");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "h4", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8, "Houston, we have a problem!");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "p", 7);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10, "The page you are looking for is temporarily unavailable.");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "div", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](14, "i", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](15, "input", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](16, "span", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "button", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](18, "Search");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      }
    },
    encapsulation: 2
  });
  return P500Component;
})();

/***/ }),

/***/ 19992:
/*!********************************************************************!*\
  !*** ./src/app/views/forgot-password/forgot-password.component.ts ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ForgotPasswordComponent": function() { return /* binding */ ForgotPasswordComponent; }
/* harmony export */ });
/* harmony import */ var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/token-storage.service */ 11192);
/* harmony import */ var _services_login_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/login.service */ 39582);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 63237);










function ForgotPasswordComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r0.data, " ");
  }
}

const _c0 = function () {
  return {
    standalone: true
  };
};

const _c1 = function () {
  return ["/login"];
};

let ForgotPasswordComponent = /*#__PURE__*/(() => {
  class ForgotPasswordComponent {
    constructor(userservice, route, router, tokenStorage) {
      this.userservice = userservice;
      this.route = route;
      this.router = router;
      this.tokenStorage = tokenStorage;
      this.forgot = {
        email: ''
      };
      this.isFailed = true;
      this.isLoginFailed = false;
    }

    ngOnInit() {}

    getfocus() {
      this.isLoginFailed = false;
    }

    ForgotPassword() {
      if (this.forgot.email != '') {
        const data = {
          email: this.forgot.email
        };
        console.log("data", data);
        this.userservice.ForgotPassword(data).subscribe(res => {
          console.log("testtttt");

          if (res.code === 200) {
            // this.router.navigate(['/users']);
            // this.tokenStorage.saveToken(res.data.tokens);
            //this.tokenStorage.saveUser(res.data);
            // console.log('err', res);
            this.data = res.message;
            this.isLoginFailed = true;
          } else {
            // console.log('err', res.message);
            this.data = res.message;
            this.isLoginFailed = true;
          }
        });
      }
    }

  }

  ForgotPasswordComponent.ɵfac = function ForgotPasswordComponent_Factory(t) {
    return new (t || ForgotPasswordComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_login_service__WEBPACK_IMPORTED_MODULE_1__.Loginservice), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__.TokenStorageService));
  };

  ForgotPasswordComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
    type: ForgotPasswordComponent,
    selectors: [["app-forgot-password"]],
    decls: 26,
    vars: 6,
    consts: [[1, "app-body", "auth-login-sign"], [1, "main", "d-flex", "align-items-center"], [1, "container"], [1, "row"], [1, "col-md-5", "mx-auto"], [1, "card-group"], [1, "card", "p-4"], [1, "card-body"], [2, "margin-bottom", "20px"], [1, "input-group", "mb-3"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-envelope-letter", "icons"], ["type", "email", "placeholder", "Email", "autocomplete", "Email", "required", "", "id", "email", 1, "form-control", "form-control-lg", 3, "ngModel", "ngModelOptions", "ngModelChange", "click"], [1, "col-12"], ["type", "button", 1, "btn", "btn-primary", "px-4", 3, "click"], [1, "col-12", "text-right"], ["type", "submit", 1, "btn", "btn-link", "px-0", 3, "routerLink"], ["href", "javascript:;"], ["class", "alert alert-danger", "role", "alert", "style", "margin-top: 10px;", 4, "ngIf"], ["role", "alert", 1, "alert", "alert-danger", 2, "margin-top", "10px"]],
    template: function ForgotPasswordComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "main", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "div", 7);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "form");
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "h1", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, "Forgot Password");
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "div", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "span", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](14, "i", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "input", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngModelChange", function ForgotPasswordComponent_Template_input_ngModelChange_15_listener($event) {
          return ctx.forgot.email = $event;
        })("click", function ForgotPasswordComponent_Template_input_click_15_listener() {
          return ctx.getfocus();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "div", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "button", 15);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function ForgotPasswordComponent_Template_button_click_18_listener() {
          return ctx.ForgotPassword();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "Submit");
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "div", 16);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "button", 17);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "a", 18);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, "Login");
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](25, ForgotPasswordComponent_div_25_Template, 2, 1, "div", 19);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
      }

      if (rf & 2) {
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](15);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", ctx.forgot.email)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction0"](4, _c0));
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction0"](5, _c1));
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isLoginFailed);
      }
    },
    directives: [_angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgForm, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgModel, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf],
    styles: [""]
  });
  return ForgotPasswordComponent;
})();

/***/ }),

/***/ 77123:
/*!************************************************!*\
  !*** ./src/app/views/login/login.component.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "LoginComponent": function() { return /* binding */ LoginComponent; }
/* harmony export */ });
/* harmony import */ var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/token-storage.service */ 11192);
/* harmony import */ var _services_login_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/login.service */ 39582);
/* harmony import */ var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/permission.service */ 6642);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../app.component */ 20721);
/* harmony import */ var _nav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../_nav */ 75874);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 63237);

















function LoginComponent_div_18_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Email cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function LoginComponent_div_18_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Please enter a valid Email address");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function LoginComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, LoginComponent_div_18_div_1_Template, 2, 0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, LoginComponent_div_18_div_2_Template, 2, 0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r0.f.email.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r0.f.email.errors.email || ctx_r0.f.email.errors.pattern);
  }
}

function LoginComponent_div_24_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function LoginComponent_div_24_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Password must be at least 8 characters");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}

function LoginComponent_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, LoginComponent_div_24_div_1_Template, 2, 0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, LoginComponent_div_24_div_2_Template, 2, 0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.f.password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.f.password.errors.minlength);
  }
}

function LoginComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r2.errormessage, " ");
  }
}

const _c0 = function (a0) {
  return {
    "is-invalid": a0
  };
};

const _c1 = function () {
  return ["/forgot-password"];
};

let LoginComponent = /*#__PURE__*/(() => {
  class LoginComponent {
    constructor(userservice, permissionService, route, router, tokenStorage, appComponent, formBuilder, _compiler) {
      this.userservice = userservice;
      this.permissionService = permissionService;
      this.route = route;
      this.router = router;
      this.tokenStorage = tokenStorage;
      this.appComponent = appComponent;
      this.formBuilder = formBuilder;
      this._compiler = _compiler;
      this.login = {
        email: '',
        password: ''
      };
      this.errormessage = '';
      this.required = false;
      this.isLoginFailed = false;
      this.isFormReady = false;
      this.submitted = false;
      this.loader = false;
    }

    ngOnInit() {
      const key = this.tokenStorage.getModule(); // console.log(key)

      if (key != null) {
        // return
        this.router.navigate([key[0].url]);
      } else {
        this.SignForm();
      }

      this.SignForm();
    }

    getfocus() {
      this.isLoginFailed = false;
    }

    SignForm() {
      this.loginForm = this.formBuilder.group({
        email: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.email, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
        password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(8)]]
      });
    }

    get f() {
      return this.loginForm.controls;
    }

    onSubmit() {
      this.submitted = true;

      if (this.loginForm.invalid) {
        return;
      }

      const inputRequest = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password
      }; // console.log('result-->', inputRequest);
      // this.loader = true;

      this.userservice.AdminLogin(inputRequest).subscribe(result => {
        // console.log(result)
        if (result.code === 200) {
          this._compiler.clearCache(); // console.log('result-->', result);


          this.resultName = result;
          this.tokenStorage.saveToken(result.data.tokens);
          this.tokenStorage.saveUser(result.data); //search the role id in the permission collection

          this.permissionService.GetModule(result.data.role_id._id).subscribe(res => {
            let navitem = [];
            let store = [];

            for (var i = 0; i < res.data.length; i++) {
              for (var j = 0; j < _nav__WEBPACK_IMPORTED_MODULE_4__.navItems.length; j++) {
                if (res.data[i].module_name === _nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].name) {
                  navitem.push(_nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j]);
                  store.push(_nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j]);

                  if (_nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children) {
                    const Arr = [];

                    for (var k = 0; k < _nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children.length; k++) {
                      for (var h = 0; h < res.data.length; h++) {
                        if (res.data[h].module_name === _nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children[k].name) {
                          Arr.push(_nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children[k]);
                          store.push(_nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children[k]);
                        }
                      }
                    } // console.log("AAARR", Arr)


                    _nav__WEBPACK_IMPORTED_MODULE_4__.navItems[j].children = Arr;
                  }
                }
              }
            }

            const navItem = navitem.reverse();
            this.loginForm.reset();
            const Verify = store.map(element => {
              return element.url;
            });
            localStorage.setItem('Verify', JSON.stringify(Verify)); // console.log(navItem)
            // this.router.navigate(['./dashboard']);

            if (navItem.length != 0) {
              this.tokenStorage.saveModule(navItem); // console.log(navItem)

              this.submitted = false;
              this.loginForm.reset();
              this.isFormReady = true; // console.log(navItem[0].url)
              // window.history.go(-(history.length - 1));

              this._compiler.clearCache();

              this.router.navigate([navItem[0].url]); //navigate to home page
              // this.location.replaceState('questions/1');
              // window.location.replace("/#"+navItem[0].url);
              // this.loader = false
            }
          });
        } else {
          // this.loader = false
          // console.log('err', result.message);
          this.errormessage = result.message;
          this.isLoginFailed = true;
        }
      });
    }

  }

  LoginComponent.ɵfac = function LoginComponent_Factory(t) {
    return new (t || LoginComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_login_service__WEBPACK_IMPORTED_MODULE_1__.Loginservice), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_component__WEBPACK_IMPORTED_MODULE_3__.AppComponent), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_5__.Compiler));
  };

  LoginComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
    type: LoginComponent,
    selectors: [["app-dashboard"]],
    decls: 35,
    vars: 12,
    consts: [[1, "app-body", "auth-login-sign"], [1, "main", "d-flex", "align-items-center"], [1, "container"], [1, "row"], [1, "col-md-5", "mx-auto"], [1, "card-group"], ["visible", "true", 1, "card", "p-4"], [1, "card-body"], ["autocomplete", "off", 1, "form", 3, "formGroup", "ngSubmit"], [1, "text-muted"], [1, "input-group", "mb-4"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-user"], ["type", "email", "placeholder", "Email", "formControlName", "email", 1, "form-control", 3, "ngClass", "click"], ["class", "invalid-feedback", 4, "ngIf"], [1, "icon-lock"], ["type", "password", "placeholder", "Password", "formControlName", "password", 1, "form-control", 3, "ngClass", "click"], [1, "form-group"], ["class", "alert alert-danger", "role", "alert", 4, "ngIf"], [1, "col-12", "text-right"], ["type", "button", 1, "btn", "btn-link", "px-0", 3, "routerLink"], [1, "col-12"], [1, "btn", "btn-primary", "px-4"], [1, "invalid-feedback"], [4, "ngIf"], ["role", "alert", 1, "alert", "alert-danger"]],
    template: function LoginComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "main", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 7);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "form", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngSubmit", function LoginComponent_Template_form_ngSubmit_8_listener() {
          return ctx.onSubmit();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "h1");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10, "Login");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "p", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](12, "Sign In to your account");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "div", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "div", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "span", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](16, "i", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "input", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LoginComponent_Template_input_click_17_listener() {
          return ctx.getfocus();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](18, LoginComponent_div_18_Template, 3, 2, "div", 15);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](19, "div", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "div", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "span", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](22, "i", 16);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "input", 17);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LoginComponent_Template_input_click_23_listener() {
          return ctx.getfocus();
        });
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](24, LoginComponent_div_24_Template, 3, 2, "div", 15);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](25, "div", 18);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](26, LoginComponent_div_26_Template, 2, 1, "div", 19);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "div", 20);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "button", 21);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](30, "Forgot password?");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 22);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "button", 23);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, "Login");
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      }

      if (rf & 2) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](7, _c0, ctx.submitted && ctx.f.email.errors));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.email.errors);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](9, _c0, ctx.submitted && ctx.f.password.errors));
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.password.errors);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isLoginFailed);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](11, _c1));
      }
    },
    directives: [_angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink],
    encapsulation: 2
  });
  return LoginComponent;
})();

/***/ }),

/***/ 4140:
/*!******************************************************!*\
  !*** ./src/app/views/register/register.component.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "RegisterComponent": function() { return /* binding */ RegisterComponent; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/forms */ 33865);


let RegisterComponent = /*#__PURE__*/(() => {
  class RegisterComponent {
    constructor() {}

  }

  RegisterComponent.ɵfac = function RegisterComponent_Factory(t) {
    return new (t || RegisterComponent)();
  };

  RegisterComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
    type: RegisterComponent,
    selectors: [["app-dashboard"]],
    decls: 34,
    vars: 0,
    consts: [[1, "app-body", "auth-login-sign"], [1, "main", "d-flex", "align-items-center"], [1, "container"], [1, "row"], [1, "col-md-6", "mx-auto"], [1, "card", "mx-4"], [1, "card-body", "p-4"], [1, "text-muted"], [1, "input-group", "mb-3"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-user"], ["type", "text", "placeholder", "Username", "autocomplete", "username", "required", "", 1, "form-control"], ["type", "text", "placeholder", "Email", "autocomplete", "email", "required", "", 1, "form-control"], [1, "icon-lock"], ["type", "password", "placeholder", "Password", "autocomplete", "new-password", "required", "", 1, "form-control"], [1, "input-group", "mb-4"], ["type", "password", "placeholder", "Repeat password", "autocomplete", "new-password", "required", "", 1, "form-control"], ["type", "button", 1, "btn", "btn-block", "btn-success"]],
    template: function RegisterComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "main", 1);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div", 2);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 3);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "div", 4);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "div", 5);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 6);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "form");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "h1");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](9, "Register");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "p", 7);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](11, "Create your account");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "div", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](14, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](15, "i", 11);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](16, "input", 12);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "div", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](19, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](20, "@");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](21, "input", 13);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](22, "div", 8);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](23, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](24, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](25, "i", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](26, "input", 15);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](27, "div", 16);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](28, "div", 9);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](29, "span", 10);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](30, "i", 14);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](31, "input", 17);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](32, "button", 18);
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](33, "Create Account");
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      }
    },
    directives: [_angular_forms__WEBPACK_IMPORTED_MODULE_1__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_1__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_1__.NgForm],
    encapsulation: 2
  });
  return RegisterComponent;
})();

/***/ }),

/***/ 95540:
/*!******************************************************************!*\
  !*** ./src/app/views/reset-password/reset-password.component.ts ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ResetPasswordComponent": function() { return /* binding */ ResetPasswordComponent; }
/* harmony export */ });
/* harmony import */ var _views_services_login_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../views/services/login.service */ 39582);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 33865);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 3079);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 63237);









function ResetPasswordComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "h3", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Invalid Request!.");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "h3", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Password changed successfully.");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_16_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "*Password cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_16_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must be 8 digits");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_16_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must contain uppercase, lowercase, special character and numbers");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, ResetPasswordComponent_div_2_div_16_div_1_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, ResetPasswordComponent_div_2_div_16_div_2_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, ResetPasswordComponent_div_2_div_16_div_3_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.minlength);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.f.firstName.errors.pattern);
  }
}

function ResetPasswordComponent_div_2_div_22_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "*Password cannot be empty");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_22_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must be a 8 digits");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_22_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Password must contain uppercase, lowercase, special character and numbers");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

function ResetPasswordComponent_div_2_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, ResetPasswordComponent_div_2_div_22_div_1_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, ResetPasswordComponent_div_2_div_22_div_2_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, ResetPasswordComponent_div_2_div_22_div_3_Template, 2, 0, "div", 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.password.errors.required);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.password.errors.minlength);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.f.firstName.errors.pattern);
  }
}

function ResetPasswordComponent_div_2_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Please make sure your passwords match ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}

const _c0 = function (a0) {
  return {
    "is-invalid": a0
  };
};

function ResetPasswordComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();

    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "main", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function ResetPasswordComponent_div_2_Template_form_ngSubmit_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r12.onSubmit();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "h1", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "Reset Password");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](12, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](13, "span", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](14, "i", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "input", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function ResetPasswordComponent_div_2_Template_input_click_15_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r14.getfocus();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](16, ResetPasswordComponent_div_2_div_16_Template, 4, 3, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](18, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "span", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](20, "i", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "input", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function ResetPasswordComponent_div_2_Template_input_click_21_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r13);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return ctx_r15.getfocus();
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](22, ResetPasswordComponent_div_2_div_22_Template, 4, 3, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](23, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](24, ResetPasswordComponent_div_2_div_24_Template, 2, 0, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](25, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](28, "Submit");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }

  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx_r2.validForm);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](6, _c0, ctx_r2.submitted && ctx_r2.f.firstName.errors));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.submitted && ctx_r2.f.firstName.errors);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](8, _c0, ctx_r2.submitted && ctx_r2.f.password.errors));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.submitted && ctx_r2.f.password.errors);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r2.failed);
  }
}

let ResetPasswordComponent = /*#__PURE__*/(() => {
  class ResetPasswordComponent {
    constructor(formBuilder, userservice, route, router) {
      this.formBuilder = formBuilder;
      this.userservice = userservice;
      this.route = route;
      this.router = router;
      this.token = '';
      this.TokenFailed = false;
      this.TokenPass = false;
      this.password = {
        firstName: '',
        password: ''
      };
      this.errormessage = '';
      this.required = false;
      this.isLoginFailed = false;
      this.response = [];
      this.isFormReady = false;
      this.submitted = false;
      this.show = false;
      this.failed = false;
      this.success = false;
    }

    ngOnInit() {
      this.route.queryParams.subscribe(params => {
        const data = {
          token: params['token']
        }; // console.log('data-->',data)

        this.userservice.PasswordToken(data).subscribe(res => {
          if (res.code === 100) {
            this.TokenFailed = true;
          } else {
            this.TokenPass = true;
            this.token = params['token'];
            this.valid();
          } // console.log(res)

        });
      });
      this.valid();
    }

    valid() {
      this.validForm = this.formBuilder.group({
        firstName: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(8)]],
        password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(8)]]
      });
    } //Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]'),


    getfocus() {
      this.failed = false;
    }

    get f() {
      return this.validForm.controls;
    }

    onSubmit() {
      this.submitted = true;

      if (this.validForm.value.firstName != '' && this.validForm.value.password != '' && this.validForm.value.firstName.length >= 8 && this.validForm.value.password.length >= 8) {
        if (this.validForm.value.firstName === this.validForm.value.password) {
          this.isFormReady = true;
          const inputRequest = {
            newPassword: this.validForm.value.firstName,
            confirmPassword: this.validForm.value.password,
            token: this.token
          }; // console.log('result-->', inputRequest);

          this.userservice.ChangePassword(inputRequest).subscribe(result => {
            // console.log('result-->', result);
            if (result.code === 200) {
              this.success = true;
              this.TokenPass = false;
            } else {
              this.errormessage = result.data;
            }
          });
        } else {
          this.failed = true;
        }
      }
    }

  }

  ResetPasswordComponent.ɵfac = function ResetPasswordComponent_Factory(t) {
    return new (t || ResetPasswordComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_views_services_login_service__WEBPACK_IMPORTED_MODULE_0__.Loginservice), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
  };

  ResetPasswordComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
    type: ResetPasswordComponent,
    selectors: [["app-reset-password"]],
    decls: 3,
    vars: 3,
    consts: [[4, "ngIf"], ["class", "app-body auth-login-sign", 4, "ngIf"], [2, "color", "red", "font-weight", "600", "text-align", "center", "padding", "200px"], [2, "color", "green", "font-weight", "600", "text-align", "center", "padding", "250px 50px 0px"], [1, "app-body", "auth-login-sign"], [1, "main", "d-flex", "align-items-center"], [1, "container"], [1, "row"], [1, "col-md-5", "mx-auto"], [1, "card-group"], [1, "card", "p-4"], [1, "card-body"], ["autocomplete", "off", 1, "form", 3, "formGroup", "ngSubmit"], [2, "margin-bottom", "20px"], [1, "input-group", "mb-3"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "icon-lock", "icons"], ["type", "password", "placeholder", "New Password", "required", "", "formControlName", "firstName", 1, "form-control", 3, "ngClass", "click"], ["class", "invalid-feedback", 4, "ngIf"], ["type", "password", "placeholder", "Confirm New Password", "required", "", "formControlName", "password", 1, "form-control", 3, "ngClass", "click"], [1, "form-group"], ["class", "alert alert-danger", "role", "alert", 4, "ngIf"], [1, "col-12"], [1, "btn", "btn-primary", "px-4"], [1, "invalid-feedback"], ["role", "alert", 1, "alert", "alert-danger"]],
    template: function ResetPasswordComponent_Template(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, ResetPasswordComponent_div_0_Template, 3, 0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, ResetPasswordComponent_div_1_Template, 3, 0, "div", 0);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, ResetPasswordComponent_div_2_Template, 29, 10, "div", 1);
      }

      if (rf & 2) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.TokenFailed);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.success);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.TokenPass);
      }
    },
    directives: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass],
    styles: [""]
  });
  return ResetPasswordComponent;
})();

/***/ }),

/***/ 50022:
/*!********************************************************!*\
  !*** ./src/app/views/services/animal_type.services.ts ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AnimalTypeService": function() { return /* binding */ AnimalTypeService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let AnimalTypeService = /*#__PURE__*/(() => {
  class AnimalTypeService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New animal 
    Newtype(data) {
      return this.http.post(`${this.config.APIUrl}/animal?token=${localStorage.auth_token}`, data);
    } //Get All Animal Type


    GetTypesList(params, data) {
      return this.http.get(`${this.config.APIUrl}/animal?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular animal by using animal id 


    GetTypeDetail(id) {
      return this.http.get(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit animal details


    UpdateType(id, data) {
      return this.http.put(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`, data);
    } //Delete animal by using id


    Deletetype(id) {
      return this.http.delete(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`);
    }

  }

  AnimalTypeService.ɵfac = function AnimalTypeService_Factory(t) {
    return ɵAnimalTypeService_BaseFactory(t || AnimalTypeService);
  };

  AnimalTypeService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: AnimalTypeService,
    factory: AnimalTypeService.ɵfac,
    providedIn: 'root'
  });
  return AnimalTypeService;
})();
const ɵAnimalTypeService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](AnimalTypeService);

/***/ }),

/***/ 79306:
/*!*********************************************************!*\
  !*** ./src/app/views/services/appointments.services.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AppointmentService": function() { return /* binding */ AppointmentService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let AppointmentService = /*#__PURE__*/(() => {
  class AppointmentService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Doctor 
    GetDoctorlist(params) {
      const param = params;
      return this.http.get(`${this.config.APIUrl}/Employee`, {
        params: param
      });
    } //Get Doctor details in Appointment collection


    GetDoctorDetails(id, data, start, end, params) {
      return this.http.get(`${this.config.APIUrl}/bookedAppointment/${id}?search=${data}&start=${start}&end=${end}&token=${localStorage.auth_token}`, {
        params
      });
    } //Delete booked appointment by using appointmnet id 


    DeleteBooked(id) {
      return this.http.delete(`${this.config.APIUrl}/DeleteBookedAppointment/${id}?token=${localStorage.auth_token}`);
    } //Get All Doctor Type 


    GetDoctorsList(param) {
      const params = param;
      return this.http.get(`${this.config.APIUrl}/Employee`, {
        params: params
      });
    } //Get the doctor schedule by using doctor id


    GetDoctor(id) {
      return this.http.get(`${this.config.APIUrl}/app_schedule/${id}?token=${localStorage.auth_token}`);
    } //Update the doctor schedule by using doctor id


    UpdateDoctor(id, data) {
      return this.http.put(`${this.config.APIUrl}/app_schedule/${id}?token=${localStorage.auth_token}`, data);
    } //Get the doctor schedule by using doctor id


    GetAllappointment(data) {
      return this.http.get(`${this.config.APIUrl}/allappointment?token=${localStorage.auth_token}`, {
        params: data
      });
    } //get Past visits by using user id


    GetPastVisit(id, data) {
      // console.log('res',id)
      return this.http.get(`${this.config.APIUrl3}/v1/PastVisit/${id}?token=${localStorage.auth_token}`, {
        params: data
      });
    } // https://reedapp.net:3000/api/v1/cms/appointmentDetail/65f1465a20ab5cb675e86e7d


    appointmentDetail(id) {
      // console.log('res',id)
      return this.http.get(`${this.config.APIUrl}/appointmentDetail/${id}?token=${localStorage.auth_token}`);
    }

    getappointment(id, data) {
      return this.http.get(`${this.config.APIUrl4}/getAppointment/${id}?token=${localStorage.auth_token}`, {
        params: data
      });
    }

    update_appointment(id, data) {
      return this.http.put(`${this.config.APIUrl4}/appointment/${id}?token=${localStorage.auth_token}`, data);
    }

    update_appointment_Reson(id, data, param) {
      return this.http.put(`${this.config.APIUrl4}/appointment/${id}?token=${localStorage.auth_token}`, data, param);
    }

    getReson(data) {
      return this.http.get(`${this.config.APIUrl}/treatment/?token=${localStorage.auth_token}`, {
        params: data
      });
    }

    getbreed(data) {
      return this.http.get(`${this.config.APIUrl}/breeding/?token=${localStorage.auth_token}`, {
        params: data
      });
    }

    getDoctor(data) {
      return this.http.get(`${this.config.APIUrl}/Doctor?search=${data}`);
    } // uploadPetImage(data:any):Observable<any>{
    //     return this.http.post(`${this.config.APIUrl4}/pet/PetImage`, data,{
    //         headers:{
    //             "Content-Type": "multipart/form-data",
    //             "Accept": "application/json"
    //         }
    //     });
    // }


    uploadFile(data) {
      const formData = new FormData();
      formData.append('file', data);
      return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
        reportProgress: true,
        responseType: 'json'
      });
    }

    backendappointment(data) {
      return this.http.post(`${this.config.APIUrl4}/addCustomAppointment`, data);
    }

    GetUserSearch(data) {
      return this.http.post(`${this.config.APIUrl4}/user/getUsersByEmail?token=${localStorage.auth_token}`, data);
    }

    emailSearchData(data) {
      return this.http.post(`${this.config.APIUrl4}/user/findUserByEmailId`, data);
    }

    Cancleappointment(id, status) {
      return this.http.put(`${this.config.APIUrl4}/cancelAppointment/${id}`, status);
    }

  }

  AppointmentService.ɵfac = function AppointmentService_Factory(t) {
    return ɵAppointmentService_BaseFactory(t || AppointmentService);
  };

  AppointmentService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: AppointmentService,
    factory: AppointmentService.ɵfac,
    providedIn: 'root'
  });
  return AppointmentService;
})();
const ɵAppointmentService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](AppointmentService);

/***/ }),

/***/ 74143:
/*!*****************************************************!*\
  !*** ./src/app/views/services/breeding.services.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "BreedingService": function() { return /* binding */ BreedingService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let BreedingService = /*#__PURE__*/(() => {
  class BreedingService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New Breeding 
    NewBreeding(data) {
      return this.http.post(`${this.config.APIUrl}/Breeding?token=${localStorage.auth_token}`, data);
    } //Get All Breeding Type 


    GetBreedingsList(params, data) {
      return this.http.get(`${this.config.APIUrl}/Breeding?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular Breeding by using Breeding id 


    GetBreedingDetail(id) {
      return this.http.get(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit Breeding details


    UpdateBreeding(id, data) {
      return this.http.put(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Breeding by using id


    DeleteBreeding(id) {
      return this.http.delete(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`);
    } //Get All Animal Type


    GetTypesList() {
      return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`);
    }

  }

  BreedingService.ɵfac = function BreedingService_Factory(t) {
    return ɵBreedingService_BaseFactory(t || BreedingService);
  };

  BreedingService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: BreedingService,
    factory: BreedingService.ɵfac,
    providedIn: 'root'
  });
  return BreedingService;
})();
const ɵBreedingService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](BreedingService);

/***/ }),

/***/ 72945:
/*!*****************************************************!*\
  !*** ./src/app/views/services/covertus.services.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "CovertusService": function() { return /* binding */ CovertusService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let CovertusService = /*#__PURE__*/(() => {
  class CovertusService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Covertus List
    GetCovertusList(params) {
      return this.http.get(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`, {
        params
      });
    } //Edit or update Covertus


    UpdateCovertus(data) {
      return this.http.post(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`, data);
    }

  }

  CovertusService.ɵfac = function CovertusService_Factory(t) {
    return ɵCovertusService_BaseFactory(t || CovertusService);
  };

  CovertusService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: CovertusService,
    factory: CovertusService.ɵfac,
    providedIn: 'root'
  });
  return CovertusService;
})();
const ɵCovertusService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](CovertusService);

/***/ }),

/***/ 59815:
/*!*****************************************************!*\
  !*** ./src/app/views/services/customer.services.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "CustomerService": function() { return /* binding */ CustomerService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let CustomerService = /*#__PURE__*/(() => {
  class CustomerService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Animal Type
    GetCustomerList(params, data) {
      return this.http.get(`${this.config.APIUrl}/user?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Edit or update user


    UpdateUser(id, data) {
      // console.log('daat->',data)
      return this.http.put(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`, data);
    } //Get Pets Details by using user_id


    FindById(id) {
      return this.http.get(`${this.config.APIUrl}/pet/${id}?token=${localStorage.auth_token}`);
    } //Get particular pet details by using pet id


    GetPetDetails(id) {
      return this.http.get(`${this.config.APIUrl}/petDetails/${id}?token=${localStorage.auth_token}`);
    } //Get upcoming appointments by using user id


    GetUpcomingAppoint(id, data) {
      // console.log('res',id)
      return this.http.get(`${this.config.APIUrl}/pet/upcomingAppointment/${id}?token=${localStorage.auth_token}&apt_date_time=${data}`);
    } //get Past visits by using user id


    GetPastVisit(id, data) {
      // console.log('res',id)
      return this.http.get(`${this.config.APIUrl}/pet/PastVisit/${id}?token=${localStorage.auth_token}&apt_date_time=${data}`);
    } //get Past visits by using user id


    AddCustomer(data) {
      return this.http.post(`${this.config.APIUrl3}/signUp/`, data);
    } //Get User Details by using user_id


    FindByUserId(id) {
      return this.http.get(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`);
    } //Delete User by using user_id


    DeleteCustomer(id) {
      return this.http.delete(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`);
    } //Get All Breeding Type 


    GetBreedingsList(data) {
      return this.http.get(`${this.config.APIUrl}/Breeding?search=${data}&token=${localStorage.auth_token}`);
    } //Get All Animal Type


    GetTypesList() {
      return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`);
    } //Picutre upload


    uploadFile(data) {
      const formData = new FormData();
      formData.append('file', data);
      return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
        reportProgress: true,
        responseType: 'json'
      });
    } //Add pet 


    AddPet(data) {
      return this.http.post(`${this.config.APIUrl3}/v1/pet?token=${localStorage.auth_token}`, data);
    } //Delete Pet by using user_id


    Deletepet(id) {
      return this.http.delete(`${this.config.APIUrl3}/v1/pet/${id}?token=${localStorage.auth_token}`);
    }

  }

  CustomerService.ɵfac = function CustomerService_Factory(t) {
    return ɵCustomerService_BaseFactory(t || CustomerService);
  };

  CustomerService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: CustomerService,
    factory: CustomerService.ɵfac,
    providedIn: 'root'
  });
  return CustomerService;
})();
const ɵCustomerService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](CustomerService);

/***/ }),

/***/ 43092:
/*!***************************************************!*\
  !*** ./src/app/views/services/doctor.services.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DoctorService": function() { return /* binding */ DoctorService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let DoctorService = /*#__PURE__*/(() => {
  class DoctorService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New Doctor 
    NewDoctor(data) {
      return this.http.post(`${this.config.APIUrl}/Doctor?token=${localStorage.auth_token}`, data);
    } //Get All Doctor Type 


    GetDoctorsList(params, data) {
      return this.http.get(`${this.config.APIUrl}/Doctor?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular Doctor by using Doctor id 


    GetDoctorDetail(id) {
      return this.http.get(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit Doctor details


    UpdateDoctor(id, data) {
      return this.http.put(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Doctor by using id


    DeleteDoctor(id) {
      return this.http.delete(`${this.config.APIUrl}/Doctor/${id}?token=${localStorage.auth_token}`);
    }

  }

  DoctorService.ɵfac = function DoctorService_Factory(t) {
    return ɵDoctorService_BaseFactory(t || DoctorService);
  };

  DoctorService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: DoctorService,
    factory: DoctorService.ɵfac,
    providedIn: 'root'
  });
  return DoctorService;
})();
const ɵDoctorService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](DoctorService);

/***/ }),

/***/ 26415:
/*!*****************************************************!*\
  !*** ./src/app/views/services/employee.services.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Employeeservice": function() { return /* binding */ Employeeservice; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let Employeeservice = /*#__PURE__*/(() => {
  class Employeeservice extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All employee
    GetEmployeeList(params) {
      return this.http.get(`${this.config.APIUrl}/employee`, {
        params
      });
    }

    GetViewLog(id) {
      return this.http.get(`${this.config.APIUrl5}/${id}`);
    } //Get All Module 


    GetRoleList() {
      return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`);
    } //Get All Module 


    NewEmployee(data) {
      return this.http.post(`${this.config.APIUrl}/employee?token=${localStorage.auth_token}`, data);
    } //Get Employee details by using Employee id


    GetEmployeeDetail(id) {
      return this.http.get(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`);
    } //Edit or Update Employee Details


    EditEmployeeDetail(id, params) {
      return this.http.put(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`, params);
    } //Delete Employee by using id


    DeleteEmployee(id) {
      return this.http.delete(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`);
    }

  }

  Employeeservice.ɵfac = function Employeeservice_Factory(t) {
    return ɵEmployeeservice_BaseFactory(t || Employeeservice);
  };

  Employeeservice.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: Employeeservice,
    factory: Employeeservice.ɵfac,
    providedIn: 'root'
  });
  return Employeeservice;
})();
const ɵEmployeeservice_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](Employeeservice);

/***/ }),

/***/ 87188:
/*!****************************************************!*\
  !*** ./src/app/views/services/location.sevices.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "LocationService": function() { return /* binding */ LocationService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let LocationService = /*#__PURE__*/(() => {
  class LocationService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New Location 
    NewLocation(data) {
      return this.http.post(`${this.config.APIUrl}/Location?token=${localStorage.auth_token}`, data);
    } //Get All Location Type 


    GetLocationsList(params, data) {
      return this.http.get(`${this.config.APIUrl}/Location?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular Location by using Location id 


    GetLocationDetail(id) {
      return this.http.get(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit Location details


    UpdateLocation(id, data) {
      return this.http.put(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Location by using id


    DeleteLocation(id) {
      return this.http.delete(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`);
    }

  }

  LocationService.ɵfac = function LocationService_Factory(t) {
    return ɵLocationService_BaseFactory(t || LocationService);
  };

  LocationService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: LocationService,
    factory: LocationService.ɵfac,
    providedIn: 'root'
  });
  return LocationService;
})();
const ɵLocationService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](LocationService);

/***/ }),

/***/ 39582:
/*!*************************************************!*\
  !*** ./src/app/views/services/login.service.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Loginservice": function() { return /* binding */ Loginservice; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let Loginservice = /*#__PURE__*/(() => {
  class Loginservice extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    constructor() {
      super(...arguments);
      this.basUrl = this.getBaseUrl();
    } // constructor(private http: HttpClient, private tokenStorage: TokenStorageService, private config: Configuration) { }
    //Admin login 


    AdminLogin(data) {
      // const token_key = this.tokenStorage.getToken();
      return this.http.post(this.config.APIUrl1 + '/signin', data);
    } //forgotpassword 


    ForgotPassword(data) {
      // const token_key = this.tokenStorage.getToken();
      return this.http.post(`${this.config.APIUrl1}/forgotpassword`, data);
    } //Token Checking in Backend at admin 


    PasswordToken(params) {
      return this.http.get(`${this.config.APIUrl2}/emp/reset_password?token=${params.token}`);
    } //Change password by using new password and conirm password


    ChangePassword(data) {
      return this.http.post(`${this.config.APIUrl2}/emp/reset_password`, data);
    } //Token Checking in Backend at user 


    userPasswordToken(params) {
      console.log("params-->", params);
      return this.http.get(`${this.config.APIUrl2}/reset_password?token=${params.token}`);
    } //Change password by using new password and conirm password at user


    userChangePassword(data) {
      return this.http.post(`${this.config.APIUrl2}/reset_password`, data);
    } //Get User Details by using user id


    GetUser(Id, data) {
      // const token_key = this.tokenStorage.getToken();
      return this.http.post(`${this.config.APIUrl}/check_password/${Id}`, data);
    } //Edit or Update Employee Details


    EditEmployeeDetail(id, params) {
      // const token_key = this.tokenStorage.getToken();
      return this.http.put(`${this.config.APIUrl}/employee/${id}`, params);
    }

  }

  Loginservice.ɵfac = function Loginservice_Factory(t) {
    return ɵLoginservice_BaseFactory(t || Loginservice);
  };

  Loginservice.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: Loginservice,
    factory: Loginservice.ɵfac,
    providedIn: 'root'
  });
  return Loginservice;
})();
const ɵLoginservice_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](Loginservice);

/***/ }),

/***/ 49533:
/*!**************************************************!*\
  !*** ./src/app/views/services/module.service.ts ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ModuleService": function() { return /* binding */ ModuleService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let ModuleService = /*#__PURE__*/(() => {
  class ModuleService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New Module 
    NewModule(data) {
      return this.http.post(`${this.config.APIUrl}/module?token=${localStorage.auth_token}`, data);
    } //Get All Module 


    GetModuleList(params, data) {
      return this.http.get(`${this.config.APIUrl}/module?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular Module by using module id 


    GetModuleDetail(id) {
      return this.http.get(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit Module details


    UpdateModule(id, data) {
      return this.http.put(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Module by using id


    DeleteModule(id) {
      return this.http.delete(`${this.config.APIUrl}/module/${id}?token=${localStorage.auth_token}`);
    }

  }

  ModuleService.ɵfac = function ModuleService_Factory(t) {
    return ɵModuleService_BaseFactory(t || ModuleService);
  };

  ModuleService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: ModuleService,
    factory: ModuleService.ɵfac,
    providedIn: 'root'
  });
  return ModuleService;
})();
const ɵModuleService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](ModuleService);

/***/ }),

/***/ 5929:
/*!*************************************************!*\
  !*** ./src/app/views/services/order.service.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "OrderService": function() { return /* binding */ OrderService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let OrderService = /*#__PURE__*/(() => {
  class OrderService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Order
    GetOrderList(params) {
      return this.http.get(`${this.config.APIUrl}/allorder?token=${localStorage.auth_token}`, {
        params
      });
    } //Update or Edit Pelfie


    UpdatePelfie(id, data) {
      return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${id}?token=${localStorage.auth_token}`, data);
    } //Get Single Order Details using Order ID


    OrderDetail(id) {
      return this.http.get(`${this.config.APIUrl}/order/${id}?token=${localStorage.auth_token}`);
    } //Update Order status by using order id


    UpdateOrderDetail(id, data) {
      return this.http.put(`${this.config.APIUrl}/order/${id}?token=${localStorage.auth_token}`, data);
    } //Delete All orders


    DeleteAllOrders() {
      return this.http.delete(`${this.config.APIUrl}/delete_all_orders?token=${localStorage.auth_token}`);
    }

  }

  OrderService.ɵfac = function OrderService_Factory(t) {
    return ɵOrderService_BaseFactory(t || OrderService);
  };

  OrderService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: OrderService,
    factory: OrderService.ɵfac,
    providedIn: 'root'
  });
  return OrderService;
})();
const ɵOrderService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](OrderService);

/***/ }),

/***/ 52831:
/*!****************************************************!*\
  !*** ./src/app/views/services/pelfies.services.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PelfieService": function() { return /* binding */ PelfieService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let PelfieService = /*#__PURE__*/(() => {
  class PelfieService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Pelfies
    GetPelfiesList(data, params) {
      return this.http.get(`${this.config.APIUrl}/pelfie?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Update or Edit Pelfie


    UpdatePelfie(id, data) {
      return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${id}?token=${localStorage.auth_token}`, data);
    }

  }

  PelfieService.ɵfac = function PelfieService_Factory(t) {
    return ɵPelfieService_BaseFactory(t || PelfieService);
  };

  PelfieService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: PelfieService,
    factory: PelfieService.ɵfac,
    providedIn: 'root'
  });
  return PelfieService;
})();
const ɵPelfieService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](PelfieService);

/***/ }),

/***/ 6642:
/*!******************************************************!*\
  !*** ./src/app/views/services/permission.service.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PermissionService": function() { return /* binding */ PermissionService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let PermissionService = /*#__PURE__*/(() => {
  class PermissionService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Module 
    GetRolelist() {
      return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`);
    } //Get role details in Permission collection


    GetRoleDetails(id, params) {
      return this.http.get(`${this.config.APIUrl}/permission?search=${id}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get role details in Permission collection


    UpdatePermission(id, params) {
      return this.http.put(`${this.config.APIUrl}/permission/${id}?token=${localStorage.auth_token}`, params);
    } //Get Module details in Permission collection


    GetModule(id) {
      return this.http.get(`${this.config.APIUrl}/permission/${id}?token=${localStorage.auth_token}`);
    }

  }

  PermissionService.ɵfac = function PermissionService_Factory(t) {
    return ɵPermissionService_BaseFactory(t || PermissionService);
  };

  PermissionService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: PermissionService,
    factory: PermissionService.ɵfac,
    providedIn: 'root'
  });
  return PermissionService;
})();
const ɵPermissionService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](PermissionService);

/***/ }),

/***/ 9499:
/*!****************************************************!*\
  !*** ./src/app/views/services/product.services.ts ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ProductService": function() { return /* binding */ ProductService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let ProductService = /*#__PURE__*/(() => {
  class ProductService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    // Image upload 
    ImageUpload(data) {
      return this.http.post(`${this.config.APIUrl4}/pet/petimage?token=${localStorage.auth_token}`, data);
    } //Add new category


    AddCategory(data) {
      return this.http.post(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`, data);
    } //Delete Category by using category id


    DeleteCategory(id) {
      return this.http.delete(`${this.config.APIUrl}/category/${id}?token=${localStorage.auth_token}`);
    } //Update Category by using category id


    UpdateCategory(id, params) {
      return this.http.put(`${this.config.APIUrl}/category/${id}?token=${localStorage.auth_token}`, params);
    } //Get all category list


    GetCategory(params) {
      return this.http.get(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`, {
        params
      });
    } //Add new Brand


    AddBrand(data) {
      return this.http.post(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`, data);
    } //Get all Brand list


    GetBrand(params) {
      return this.http.get(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`, {
        params
      });
    } //Update Brand by using Brand id


    UpdateBrand(id, params) {
      return this.http.put(`${this.config.APIUrl}/brand/${id}?token=${localStorage.auth_token}`, params);
    } //Delete Brand by using Brand id


    DeleteBrand(id) {
      return this.http.delete(`${this.config.APIUrl}/brand/${id}?token=${localStorage.auth_token}`);
    } //Add new Variant


    AddVariant(data) {
      return this.http.post(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`, data);
    } //Get all Variant list


    GetVariant(params) {
      return this.http.get(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`, {
        params
      });
    } //Update Variant by using Variant id


    UpdateVariant(id, params) {
      return this.http.put(`${this.config.APIUrl}/Variant/${id}?token=${localStorage.auth_token}`, params);
    } //Delete Variant by using Variant id


    DeleteVariant(id) {
      return this.http.delete(`${this.config.APIUrl}/Variant/${id}?token=${localStorage.auth_token}`);
    } //Picutre upload


    uploadFile(data) {
      const formData = new FormData();
      formData.append('file', data);
      return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
        reportProgress: true,
        responseType: 'json'
      });
    } //Add new product


    AddProduct(data) {
      return this.http.post(`${this.config.APIUrl}/Product?token=${localStorage.auth_token}`, data);
    } //Add new banners


    AddBanners(data) {
      return this.http.post(`${this.config.APIUrl}/Banner?token=${localStorage.auth_token}`, data);
    } //Edit banners


    EditBanners(data, id) {
      return this.http.put(`${this.config.APIUrl}/Banner/${id}`, data);
    } //List all products


    GetProduct(name, params) {
      return this.http.get(`${this.config.APIUrl}/Product?search=${name}&token=${localStorage.auth_token}`, {
        params
      });
    }

    GetallProduct(name, params) {
      return this.http.get(`${this.config.APIUrl}/Product?skip=0&limit=0&search=${name}&token=${localStorage.auth_token}`, {
        params
      });
    } // Get banners.


    GetBanners(params) {
      return this.http.get(`${this.config.APIUrl}/Banner?&token=${localStorage.auth_token}`, {
        params
      });
    } // get product


    getallproduct(name, params) {
      return this.http.get(`${this.config.APIUrl}/products?skip=0&limit=0&search=${name}&token=${localStorage.auth_token}`, {
        params
      });
    } //Delete product by using product id


    DeleteProduct(id) {
      return this.http.delete(`${this.config.APIUrl}/Product/${id}?token=${localStorage.auth_token}`);
    } //Delete banner


    DeleteBanner(id) {
      return this.http.delete(`${this.config.APIUrl}/Banner/${id}?token=${localStorage.auth_token}`);
    } //Update Product by using Product id


    UpdateProduct(id, params) {
      return this.http.put(`${this.config.APIUrl}/Product/${id}?token=${localStorage.auth_token}`, params);
    } //Get Product by using product id


    GetProductById(Id) {
      return this.http.get(`${this.config.APIUrl}/Product/${Id}?token=${localStorage.auth_token}`);
    } //Update Product by using Product id


    UpdateBanner(id, params) {
      return this.http.put(`${this.config.APIUrl}/Banner/${id}?token=${localStorage.auth_token}`, params);
    }

  }

  ProductService.ɵfac = function ProductService_Factory(t) {
    return ɵProductService_BaseFactory(t || ProductService);
  };

  ProductService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: ProductService,
    factory: ProductService.ɵfac,
    providedIn: 'root'
  });
  return ProductService;
})();
const ɵProductService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](ProductService);

/***/ }),

/***/ 86207:
/*!******************************************************!*\
  !*** ./src/app/views/services/resources.services.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ResourcesService": function() { return /* binding */ ResourcesService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let ResourcesService = /*#__PURE__*/(() => {
  class ResourcesService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Get All Health tips
    GetTips(params) {
      return this.http.get(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`, {
        params
      });
    } //Add new health tips


    AddTips(data) {
      return this.http.post(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`, data);
    } //Update health tips


    Updatetips(id, data) {
      return this.http.put(`${this.config.APIUrl}/health_tip/${id}?token=${localStorage.auth_token}`, data);
    } //Picutre upload


    uploadFile(data) {
      const formData = new FormData();
      formData.append('file', data);
      return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
        reportProgress: true,
        responseType: 'json'
      });
    } //Delete Tips by using id


    DeleteTips(id) {
      return this.http.delete(`${this.config.APIUrl}/health_tip/${id}?token=${localStorage.auth_token}`);
    } //Add new Video


    AddVideo(data) {
      return this.http.post(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`, data);
    } //Get All Video


    GetVideos(params) {
      return this.http.get(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`, {
        params
      });
    } //Update video


    UpdateVideo(id, data) {
      return this.http.put(`${this.config.APIUrl}/video/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Videos by using id


    DeleteVideo(id) {
      return this.http.delete(`${this.config.APIUrl}/video/${id}?token=${localStorage.auth_token}`);
    } //Add new Audio


    AddAudio(data) {
      return this.http.post(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`, data);
    } //Get All Audio


    GetAudios(params) {
      return this.http.get(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`, {
        params
      });
    } //Update Audio


    UpdateAudio(id, data) {
      return this.http.put(`${this.config.APIUrl}/audio/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Audios by using id


    DeleteAudio(id) {
      return this.http.delete(`${this.config.APIUrl}/audio/${id}?token=${localStorage.auth_token}`);
    } //Add new FAQ


    AddFAQ(data) {
      return this.http.post(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`, data);
    } //Get All faqs


    GetFAQs(params) {
      return this.http.get(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`, {
        params
      });
    } //update FAQs by using id


    UpdateFAQ(id, data) {
      return this.http.put(`${this.config.APIUrl}/FAQ/${id}?token=${localStorage.auth_token}`, data);
    } //Delete FAQs by using id


    DeleteFAQ(id) {
      return this.http.delete(`${this.config.APIUrl}/FAQ/${id}?token=${localStorage.auth_token}`);
    }

  }

  ResourcesService.ɵfac = function ResourcesService_Factory(t) {
    return ɵResourcesService_BaseFactory(t || ResourcesService);
  };

  ResourcesService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: ResourcesService,
    factory: ResourcesService.ɵfac,
    providedIn: 'root'
  });
  return ResourcesService;
})();
const ɵResourcesService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](ResourcesService);

/***/ }),

/***/ 83711:
/*!************************************************!*\
  !*** ./src/app/views/services/role.service.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "RoleService": function() { return /* binding */ RoleService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let RoleService = /*#__PURE__*/(() => {
  class RoleService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New Role 
    NewRole(data) {
      return this.http.post(`${this.config.APIUrl}/role?token=${localStorage.auth_token}`, data);
    } //Get All Role 


    GetRoleList(params, data) {
      return this.http.get(`${this.config.APIUrl}/role?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular Role by using role id 


    GetRoleDetail(id) {
      return this.http.get(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit role details


    UpdateRole(id, data) {
      return this.http.put(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`, data);
    } //Delete Role by using id


    DeleteRole(id) {
      return this.http.delete(`${this.config.APIUrl}/role/${id}?token=${localStorage.auth_token}`);
    }

  }

  RoleService.ɵfac = function RoleService_Factory(t) {
    return ɵRoleService_BaseFactory(t || RoleService);
  };

  RoleService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: RoleService,
    factory: RoleService.ɵfac,
    providedIn: 'root'
  });
  return RoleService;
})();
const ɵRoleService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](RoleService);

/***/ }),

/***/ 11192:
/*!*********************************************************!*\
  !*** ./src/app/views/services/token-storage.service.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "TokenStorageService": function() { return /* binding */ TokenStorageService; }
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/router */ 3079);




const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth-user';
const MODULE_KEY = 'auth-module';
let TokenStorageService = /*#__PURE__*/(() => {
  class TokenStorageService {
    constructor(_compiler, router) {
      this._compiler = _compiler;
      this.router = router;
    }

    signOut() {
      window.localStorage.clear();

      this._compiler.clearCache();

      this.router.navigate(['./login']);
    }

    saveToken(token) {
      window.localStorage.removeItem(TOKEN_KEY);
      window.localStorage.setItem(TOKEN_KEY, token);
    }

    getToken() {
      return localStorage.getItem(TOKEN_KEY);
    }

    saveUser(user) {
      window.localStorage.removeItem(USER_KEY);
      window.localStorage.setItem(USER_KEY, JSON.stringify(user));
    }

    getUser() {
      return JSON.parse(localStorage.getItem(USER_KEY));
    }

    saveModule(module) {
      window.localStorage.removeItem(MODULE_KEY);
      window.localStorage.setItem(MODULE_KEY, JSON.stringify(module));
    }

    getModule() {
      return JSON.parse(localStorage.getItem(MODULE_KEY));
    }

  }

  TokenStorageService.ɵfac = function TokenStorageService_Factory(t) {
    return new (t || TokenStorageService)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵinject"](_angular_core__WEBPACK_IMPORTED_MODULE_0__.Compiler), _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_1__.Router));
  };

  TokenStorageService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjectable"]({
    token: TokenStorageService,
    factory: TokenStorageService.ɵfac,
    providedIn: 'root'
  });
  return TokenStorageService;
})();

/***/ }),

/***/ 21771:
/*!******************************************************!*\
  !*** ./src/app/views/services/treatment.services.ts ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "treatmentService": function() { return /* binding */ treatmentService; }
/* harmony export */ });
/* harmony import */ var _Api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Api */ 31681);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 3048);



let treatmentService = /*#__PURE__*/(() => {
  class treatmentService extends _Api__WEBPACK_IMPORTED_MODULE_0__.Api {
    //Add New treatment 
    NewTreatment(data) {
      return this.http.post(`${this.config.APIUrl}/treatment?token=${localStorage.auth_token}`, data);
    } //Get All treatment Type 


    GetTreatmentsList(params, data) {
      return this.http.get(`${this.config.APIUrl}/treatment?search=${data}&token=${localStorage.auth_token}`, {
        params
      });
    } //Get Particular treatment by using treatment id 


    GetTreatmentDetail(id) {
      return this.http.get(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`);
    } //Update or Edit treatment details


    UpdateTreatment(id, data) {
      return this.http.put(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`, data);
    } //Delete treatment by using id


    DeleteTreatment(id) {
      return this.http.delete(`${this.config.APIUrl}/treatment/${id}?token=${localStorage.auth_token}`);
    }

  }

  treatmentService.ɵfac = function treatmentService_Factory(t) {
    return ɵtreatmentService_BaseFactory(t || treatmentService);
  };

  treatmentService.ɵprov = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
    token: treatmentService,
    factory: treatmentService.ɵfac,
    providedIn: 'root'
  });
  return treatmentService;
})();
const ɵtreatmentService_BaseFactory = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetInheritedFactory"](treatmentService);

/***/ }),

/***/ 49731:
/*!******************************!*\
  !*** ./src/configuration.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Configuration": function() { return /* binding */ Configuration; }
/* harmony export */ });
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./environments/environment */ 24766);

class Configuration {
  constructor() {
    this.APIUrl = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + '/api/v1/cms';
    this.APIUrl1 = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + '/api/admin';
    this.APIUrl2 = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + ''; //Non authentication

    this.APIUrl3 = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + '/api';
    this.APIUrl4 = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + '/api/v1';
    this.APIUrl5 = _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.serverUrl + '/api/v1/log';
  }

}

/***/ }),

/***/ 93963:
/*!**********************************************!*\
  !*** ./src/environments/environment.prod.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "environment": function() { return /* binding */ environment; }
/* harmony export */ });
const environment = {
  production: true,
  //serverUrl : 'http://143.198.11.140:3000'
  serverUrl: 'https://reedapp.net:3000'
};

/***/ }),

/***/ 24766:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "environment": function() { return /* binding */ environment; }
/* harmony export */ });
const environment = {
  production: true,
  //serverUrl : 'http://143.198.11.140:3000'
  serverUrl: 'https://reedapp.net:3000'
};

/***/ }),

/***/ 8835:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/platform-browser */ 54464);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 3048);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 50023);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environments/environment */ 24766);





if (_environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.production) {
  (0,_angular_core__WEBPACK_IMPORTED_MODULE_2__.enableProdMode)();
}

_angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule, {
  useJit: true,
  preserveWhitespaces: true
}).catch(err => console.log(err));

/***/ }),

/***/ 46700:
/*!***************************************************!*\
  !*** ./node_modules/moment/locale/ sync ^\.\/.*$ ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./af": 74511,
	"./af.js": 74511,
	"./ar": 46371,
	"./ar-dz": 1586,
	"./ar-dz.js": 1586,
	"./ar-kw": 36674,
	"./ar-kw.js": 36674,
	"./ar-ly": 40234,
	"./ar-ly.js": 40234,
	"./ar-ma": 28280,
	"./ar-ma.js": 28280,
	"./ar-ps": 94784,
	"./ar-ps.js": 94784,
	"./ar-sa": 58961,
	"./ar-sa.js": 58961,
	"./ar-tn": 3579,
	"./ar-tn.js": 3579,
	"./ar.js": 46371,
	"./az": 46308,
	"./az.js": 46308,
	"./be": 28212,
	"./be.js": 28212,
	"./bg": 36850,
	"./bg.js": 36850,
	"./bm": 47231,
	"./bm.js": 47231,
	"./bn": 40968,
	"./bn-bd": 31848,
	"./bn-bd.js": 31848,
	"./bn.js": 40968,
	"./bo": 20977,
	"./bo.js": 20977,
	"./br": 65466,
	"./br.js": 65466,
	"./bs": 38481,
	"./bs.js": 38481,
	"./ca": 81598,
	"./ca.js": 81598,
	"./cs": 69192,
	"./cs.js": 69192,
	"./cv": 2744,
	"./cv.js": 2744,
	"./cy": 52387,
	"./cy.js": 52387,
	"./da": 56056,
	"./da.js": 56056,
	"./de": 97809,
	"./de-at": 15455,
	"./de-at.js": 15455,
	"./de-ch": 9938,
	"./de-ch.js": 9938,
	"./de.js": 97809,
	"./dv": 16389,
	"./dv.js": 16389,
	"./el": 69062,
	"./el.js": 69062,
	"./en-au": 52038,
	"./en-au.js": 52038,
	"./en-ca": 25418,
	"./en-ca.js": 25418,
	"./en-gb": 74204,
	"./en-gb.js": 74204,
	"./en-ie": 99882,
	"./en-ie.js": 99882,
	"./en-il": 20385,
	"./en-il.js": 20385,
	"./en-in": 46715,
	"./en-in.js": 46715,
	"./en-nz": 31629,
	"./en-nz.js": 31629,
	"./en-sg": 79478,
	"./en-sg.js": 79478,
	"./eo": 26069,
	"./eo.js": 26069,
	"./es": 41550,
	"./es-do": 1049,
	"./es-do.js": 1049,
	"./es-mx": 66750,
	"./es-mx.js": 66750,
	"./es-us": 57634,
	"./es-us.js": 57634,
	"./es.js": 41550,
	"./et": 87469,
	"./et.js": 87469,
	"./eu": 52481,
	"./eu.js": 52481,
	"./fa": 75539,
	"./fa.js": 75539,
	"./fi": 84220,
	"./fi.js": 84220,
	"./fil": 25743,
	"./fil.js": 25743,
	"./fo": 83610,
	"./fo.js": 83610,
	"./fr": 16981,
	"./fr-ca": 19572,
	"./fr-ca.js": 19572,
	"./fr-ch": 91067,
	"./fr-ch.js": 91067,
	"./fr.js": 16981,
	"./fy": 9556,
	"./fy.js": 9556,
	"./ga": 94798,
	"./ga.js": 94798,
	"./gd": 89058,
	"./gd.js": 89058,
	"./gl": 22457,
	"./gl.js": 22457,
	"./gom-deva": 39161,
	"./gom-deva.js": 39161,
	"./gom-latn": 22332,
	"./gom-latn.js": 22332,
	"./gu": 85223,
	"./gu.js": 85223,
	"./he": 85940,
	"./he.js": 85940,
	"./hi": 73902,
	"./hi.js": 73902,
	"./hr": 23801,
	"./hr.js": 23801,
	"./hu": 3521,
	"./hu.js": 3521,
	"./hy-am": 58614,
	"./hy-am.js": 58614,
	"./id": 32087,
	"./id.js": 32087,
	"./is": 77382,
	"./is.js": 77382,
	"./it": 68176,
	"./it-ch": 46489,
	"./it-ch.js": 46489,
	"./it.js": 68176,
	"./ja": 86925,
	"./ja.js": 86925,
	"./jv": 55237,
	"./jv.js": 55237,
	"./ka": 82353,
	"./ka.js": 82353,
	"./kk": 97336,
	"./kk.js": 97336,
	"./km": 71526,
	"./km.js": 71526,
	"./kn": 25523,
	"./kn.js": 25523,
	"./ko": 35731,
	"./ko.js": 35731,
	"./ku": 10604,
	"./ku-kmr": 62387,
	"./ku-kmr.js": 62387,
	"./ku.js": 10604,
	"./ky": 67227,
	"./ky.js": 67227,
	"./lb": 28976,
	"./lb.js": 28976,
	"./lo": 7629,
	"./lo.js": 7629,
	"./lt": 77082,
	"./lt.js": 77082,
	"./lv": 89795,
	"./lv.js": 89795,
	"./me": 74718,
	"./me.js": 74718,
	"./mi": 84604,
	"./mi.js": 84604,
	"./mk": 692,
	"./mk.js": 692,
	"./ml": 54971,
	"./ml.js": 54971,
	"./mn": 65202,
	"./mn.js": 65202,
	"./mr": 94512,
	"./mr.js": 94512,
	"./ms": 94471,
	"./ms-my": 72840,
	"./ms-my.js": 72840,
	"./ms.js": 94471,
	"./mt": 97438,
	"./mt.js": 97438,
	"./my": 37323,
	"./my.js": 37323,
	"./nb": 98782,
	"./nb.js": 98782,
	"./ne": 52842,
	"./ne.js": 52842,
	"./nl": 23419,
	"./nl-be": 83245,
	"./nl-be.js": 83245,
	"./nl.js": 23419,
	"./nn": 60219,
	"./nn.js": 60219,
	"./oc-lnc": 92717,
	"./oc-lnc.js": 92717,
	"./pa-in": 8301,
	"./pa-in.js": 8301,
	"./pl": 12568,
	"./pl.js": 12568,
	"./pt": 91318,
	"./pt-br": 96858,
	"./pt-br.js": 96858,
	"./pt.js": 91318,
	"./ro": 30490,
	"./ro.js": 30490,
	"./ru": 38520,
	"./ru.js": 38520,
	"./sd": 84467,
	"./sd.js": 84467,
	"./se": 75774,
	"./se.js": 75774,
	"./si": 29352,
	"./si.js": 29352,
	"./sk": 9639,
	"./sk.js": 9639,
	"./sl": 69767,
	"./sl.js": 69767,
	"./sq": 83421,
	"./sq.js": 83421,
	"./sr": 55443,
	"./sr-cyrl": 18228,
	"./sr-cyrl.js": 18228,
	"./sr.js": 55443,
	"./ss": 46985,
	"./ss.js": 46985,
	"./sv": 3060,
	"./sv.js": 3060,
	"./sw": 45093,
	"./sw.js": 45093,
	"./ta": 66988,
	"./ta.js": 66988,
	"./te": 8146,
	"./te.js": 8146,
	"./tet": 40839,
	"./tet.js": 40839,
	"./tg": 47386,
	"./tg.js": 47386,
	"./th": 93378,
	"./th.js": 93378,
	"./tk": 61924,
	"./tk.js": 61924,
	"./tl-ph": 75053,
	"./tl-ph.js": 75053,
	"./tlh": 29913,
	"./tlh.js": 29913,
	"./tr": 56594,
	"./tr.js": 56594,
	"./tzl": 59439,
	"./tzl.js": 59439,
	"./tzm": 89711,
	"./tzm-latn": 94166,
	"./tzm-latn.js": 94166,
	"./tzm.js": 89711,
	"./ug-cn": 81509,
	"./ug-cn.js": 81509,
	"./uk": 1485,
	"./uk.js": 1485,
	"./ur": 98955,
	"./ur.js": 98955,
	"./uz": 36067,
	"./uz-latn": 51527,
	"./uz-latn.js": 51527,
	"./uz.js": 36067,
	"./vi": 98501,
	"./vi.js": 98501,
	"./x-pseudo": 67666,
	"./x-pseudo.js": 67666,
	"./yo": 94031,
	"./yo.js": 94031,
	"./zh-cn": 74660,
	"./zh-cn.js": 74660,
	"./zh-hk": 46383,
	"./zh-hk.js": 46383,
	"./zh-mo": 86961,
	"./zh-mo.js": 86961,
	"./zh-tw": 58771,
	"./zh-tw.js": 58771
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 46700;

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, [736], function() { return __webpack_exec__(8835); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);