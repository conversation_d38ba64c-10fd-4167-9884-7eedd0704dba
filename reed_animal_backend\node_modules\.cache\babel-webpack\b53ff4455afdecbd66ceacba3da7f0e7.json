{"ast": null, "code": "import { Router } from '@angular/router'; // import { AuthService } from './auth.service';\n\nimport { Employeeservice } from './views/services/employee.services';\nimport { PermissionService } from './views/services/permission.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./views/services/employee.services\";\nimport * as i3 from \"./views/services/permission.service\";\nexport let AuthGuardService = /*#__PURE__*/(() => {\n  class AuthGuardService {\n    constructor(router, EmployeeService, Permission) {\n      this.router = router;\n      this.EmployeeService = EmployeeService;\n      this.Permission = Permission;\n    }\n\n    canActivate(arr) {\n      const Item = JSON.parse(localStorage.getItem('Verify'));\n      this.verifyCustomer();\n      const Verify = Item.includes(arr.data.path);\n\n      if (Verify) {\n        // logged in so return true\n        return true;\n      } // not logged in so redirect to login page\n\n\n      this.router.navigate(['/login']);\n      return false;\n    }\n\n    verifyCustomer() {\n      const Item = JSON.parse(localStorage.getItem('auth-user'));\n      this.EmployeeService.GetEmployeeDetail(Item._id).subscribe(res => {\n        if (res.data.status === false) {\n          localStorage.clear();\n          this.router.navigate(['/login']);\n        }\n      });\n    }\n\n  }\n\n  AuthGuardService.ɵfac = function AuthGuardService_Factory(t) {\n    return new (t || AuthGuardService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.Employeeservice), i0.ɵɵinject(i3.PermissionService));\n  };\n\n  AuthGuardService.ɵprov = i0.ɵɵdefineInjectable({\n    token: AuthGuardService,\n    factory: AuthGuardService.ɵfac,\n    providedIn: 'root'\n  });\n  return AuthGuardService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}