!function(){"use strict";function e(e,i){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,i){if(!e)return;if("string"==typeof e)return t(e,i);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return t(e,i)}(e))||i&&e&&"number"==typeof e.length){n&&(e=n);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,d=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return d=e.done,e},e:function(e){l=!0,r=e},f:function(){try{d||null==n.return||n.return()}finally{if(l)throw r}}}}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),e}(self.webpackChunkDr_Reed_Admin_Panel=self.webpackChunkDr_Reed_Admin_Panel||[]).push([[959],{28959:function(t,n,a){a.r(n),a.d(n,{MasterModule:function(){return pt}});var r=a(63237),d=a(30386),l=a(45055),u=a(74875),s=a(99777),c=a(83711),g=a(6642),Z=a(26415),p=a(90658),m=a(11192),f=["AddModal"],h=["primaryModal"],v=["deleteModal"];function A(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"button",38),p.NdJ("click",function(){return p.CHM(i),p.oxw(),p.MAs(28).show()}),p._uU(1," Add Role "),p.qZA()}}function q(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",40),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(48).show(),t.GetRole(e._id),t.onChange(e._id)}),p.TgZ(1,"span",41),p._UZ(2,"i",42),p._uU(3," Edit"),p.qZA(),p.qZA()}}function T(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",40),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(86).show(),t.GetRolee(e._id)}),p.TgZ(1,"span",43),p._UZ(2,"i",44),p._uU(3," Delete"),p.qZA(),p.qZA()}}function b(e,t){if(1&e&&(p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.TgZ(3,"td"),p.YNc(4,q,4,0,"a",39),p.YNc(5,T,4,0,"a",39),p.qZA(),p.qZA()),2&e){var i=t.$implicit,n=p.oxw();p.xp6(2),p.Oqu(i.name),p.xp6(2),p.Q6J("ngIf",n.Edit),p.xp6(1),p.Q6J("ngIf",n.Delete)}}function y(e,t){1&e&&(p.TgZ(0,"div",45),p._uU(1,"*Role is mandatory"),p.qZA())}function M(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.TgZ(3,"td"),p.TgZ(4,"label",46),p.TgZ(5,"input",47),p.NdJ("change",function(){p.CHM(i);var e=t.$implicit,n=t.index;return p.oxw().changed("add",e.add,e._id,n)})("ngModelChange",function(e){return t.$implicit.add=e}),p.qZA(),p._UZ(6,"span",48),p.qZA(),p.qZA(),p.TgZ(7,"td"),p.TgZ(8,"label",46),p.TgZ(9,"input",47),p.NdJ("change",function(){p.CHM(i);var e=t.$implicit,n=t.index;return p.oxw().changed("edit",e.edit,e._id,n)})("ngModelChange",function(e){return t.$implicit.edit=e}),p.qZA(),p._UZ(10,"span",48),p.qZA(),p.qZA(),p.TgZ(11,"td"),p.TgZ(12,"label",46),p.TgZ(13,"input",47),p.NdJ("change",function(){p.CHM(i);var e=t.$implicit,n=t.index;return p.oxw().changed("delete",e.delete,e._id,n)})("ngModelChange",function(e){return t.$implicit.delete=e}),p.qZA(),p._UZ(14,"span",48),p.qZA(),p.qZA(),p.TgZ(15,"td"),p.TgZ(16,"label",46),p.TgZ(17,"input",47),p.NdJ("change",function(){p.CHM(i);var e=t.$implicit,n=t.index;return p.oxw().changed("all",e.status,e._id,n)})("ngModelChange",function(e){return t.$implicit.status=e}),p.qZA(),p._UZ(18,"span",48),p.qZA(),p.qZA(),p.qZA()}if(2&e){var n=t.$implicit;p.xp6(2),p.Oqu(n.module_name),p.xp6(3),p.Q6J("ngModel",n.add),p.xp6(4),p.Q6J("ngModel",n.edit),p.xp6(4),p.Q6J("ngModel",n.delete),p.xp6(4),p.Q6J("ngModel",n.status)}}var _=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},x=function(){return{backdrop:"static",keyboard:!1}},k=function(){return{standalone:!0}},U=function(e,t){return{id:"listing_paginations",itemsPerPage:6,currentPage:e,totalItems:t}},C=function(){var e=function(){function e(t,n,o,a,r,d){i(this,e),this.roleService=t,this.route=n,this.router=o,this.tokenStorage=a,this.Permission=r,this.EmployeeService=d,this.permissions=[],this.roles=[],this.page=1,this.count=0,this.search="",this.name="",this.role={},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.page1=1,this.count1=0}return o(e,[{key:"ngOnInit",value:function(){this.tokens()}},{key:"clear",value:function(){this.role={},this.AddModal.hide()}},{key:"tokens",value:function(){var e=this;this.tokenStorage.getToken();var t=this.tokenStorage.getUser();this.Permission.GetModule(t.role_id._id).subscribe(function(t){for(var i=0;i<t.data.length;i++)"Role"==t.data[i].module_name&&(e.Add=t.data[i].add,e.Edit=t.data[i].edit,e.Delete=t.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(t._id).subscribe(function(t){0==t.data.status&&e.tokenStorage.signOut()}),this.GetRoleLists()}},{key:"getrequestparams",value:function(e){var t={};return t.skip=10*(e-1),t}},{key:"getrequestparams1",value:function(e){var t={};return t.skip=6*(e-1),t}},{key:"GetRoleLists",value:function(){var e=this,t=this.getrequestparams(this.page);this.roleService.GetRoleList(t,this.name).subscribe(function(t){e.count=t.count-1;for(var i=[],n=0;n<t.data.length;n++)"Super Admin"!==t.data[n].name&&i.push(t.data[n]);e.roles=i})}},{key:"handlePageChange",value:function(e){this.page=e,this.GetRoleLists()}},{key:"GetRole",value:function(e){var t=this;this.Id=e,this.page1=1,this.count1=0,this.roleService.GetRoleDetail(e).subscribe(function(e){t.role=e.data[0]})}},{key:"onChange",value:function(e){var t=this,i=this.getrequestparams1(this.page1);this.Permission.GetRoleDetails(e,i).subscribe(function(e){t.permissions=e.data,t.count1=e.count})}},{key:"handlePageChange1",value:function(e){this.page1=e,this.onChange(this.Id)}},{key:"EditRole",value:function(e){var t=this;this.roleService.UpdateRole(e,{name:this.role.name}).subscribe(function(e){t.role={},t.GetRoleLists()})}},{key:"changeing",value:function(e,t){this.roleService.UpdateRole(t,{acc_activation:e}).subscribe(function(e){})}},{key:"AddRole",value:function(){var e=this;null!=this.role.name&&""!=this.role.name&&this.roleService.NewRole({name:this.role.name}).subscribe(function(t){e.role={},e.rolefailed=!1,e.AddModal.hide(),e.GetRoleLists()}),this.rolefailed=!0}},{key:"Deleterole",value:function(){var e=this;this.roleService.DeleteRole(this.Id).subscribe(function(t){e.GetRoleLists()})}},{key:"GetRolee",value:function(e){this.Id=e}},{key:"changed",value:function(e,t,i,n){var o,a,r,d=(r=t,(a=e)in(o={})?Object.defineProperty(o,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):o[a]=r,o);if("all"==e){var l={status:t,delete:t,edit:t,add:t};this.permissions[n].delete=t,this.permissions[n].edit=t,this.permissions[n].add=t,this.Permission.UpdatePermission(i,l).subscribe(function(e){})}this.Permission.UpdatePermission(i,d).subscribe(function(e){})}}]),e}();return e.\u0275fac=function(t){return new(t||e)(p.Y36(c.N),p.Y36(s.gz),p.Y36(s.F0),p.Y36(m.i),p.Y36(g.$),p.Y36(Z.d))},e.\u0275cmp=p.Xpm({type:e,selectors:[["app-role"]],viewQuery:function(e,t){var i;(1&e&&(p.Gf(f,1),p.Gf(h,1),p.Gf(v,1)),2&e)&&(p.iGM(i=p.CRH())&&(t.AddModal=i.first),p.iGM(i=p.CRH())&&(t.primaryModal=i.first),p.iGM(i=p.CRH())&&(t.deleteModal=i.first))},decls:102,vars:29,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","role-name","placeholder","e.g. Manager, Doctor","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["type","text","id","edit-name","placeholder","e.g. Manager, Doctor","autocomplete","off","required","","readonly","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["id","listing_paginations","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["deleteModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"],[1,"switch"],["type","checkbox",3,"ngModel","change","ngModelChange"],[1,"slider","round"]],template:function(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Role "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.YNc(8,A,2,0,"button",6),p.TgZ(9,"div",7),p.TgZ(10,"div",8),p.TgZ(11,"div",9),p.TgZ(12,"span",10),p.TgZ(13,"i",11),p.NdJ("click",function(){return t.GetRoleLists()}),p.qZA(),p.qZA(),p.qZA(),p.TgZ(14,"input",12),p.NdJ("input",function(){return t.GetRoleLists()})("ngModelChange",function(e){return t.name=e}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(15,"table",13),p.TgZ(16,"thead"),p.TgZ(17,"tr"),p.TgZ(18,"th"),p._uU(19,"Role Name"),p.qZA(),p.TgZ(20,"th"),p._uU(21,"Action"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(22,"tbody"),p.YNc(23,b,6,3,"tr",14),p.ALo(24,"paginate"),p.qZA(),p.qZA(),p.TgZ(25,"div"),p.TgZ(26,"pagination-controls",15),p.NdJ("pageChange",function(e){return t.handlePageChange(e)}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(27,"div",16,17),p.TgZ(29,"div",18),p.TgZ(30,"div",19),p.TgZ(31,"div",20),p.TgZ(32,"h4",21),p._uU(33,"Add Role"),p.qZA(),p.qZA(),p.TgZ(34,"div",22),p.TgZ(35,"div",0),p.TgZ(36,"div",23),p.TgZ(37,"div",24),p.TgZ(38,"label",25),p._uU(39,"Role Name*"),p.qZA(),p.TgZ(40,"input",26),p.NdJ("ngModelChange",function(e){return t.role.name=e})("keydown.enter",function(){return t.AddRole()}),p.qZA(),p.YNc(41,y,2,0,"div",27),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(42,"div",28),p.TgZ(43,"button",29),p.NdJ("click",function(){return p.CHM(i),p.MAs(28).hide(),t.clear()}),p._uU(44,"Cancel"),p.qZA(),p.TgZ(45,"button",30),p.NdJ("click",function(){return t.AddRole()}),p._uU(46,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(47,"div",16,31),p.TgZ(49,"div",18),p.TgZ(50,"div",19),p.TgZ(51,"div",20),p.TgZ(52,"h4",21),p._uU(53,"Edit Role"),p.qZA(),p.qZA(),p.TgZ(54,"div",22),p.TgZ(55,"div",0),p.TgZ(56,"div",23),p.TgZ(57,"div",24),p.TgZ(58,"label",25),p._uU(59,"Role Name*"),p.qZA(),p.TgZ(60,"input",32),p.NdJ("ngModelChange",function(e){return t.role.name=e})("keydown.enter",function(){p.CHM(i);var e=p.MAs(48);return t.EditRole(t.role._id),e.hide()}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(61,"div",4),p.TgZ(62,"table",13),p.TgZ(63,"thead"),p.TgZ(64,"tr"),p.TgZ(65,"th"),p._uU(66,"Module Name"),p.qZA(),p.TgZ(67,"th"),p._uU(68,"Add"),p.qZA(),p.TgZ(69,"th"),p._uU(70,"Edit"),p.qZA(),p.TgZ(71,"th"),p._uU(72,"Delete"),p.qZA(),p.TgZ(73,"th"),p._uU(74,"View"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(75,"tbody"),p.YNc(76,M,19,5,"tr",14),p.ALo(77,"paginate"),p.qZA(),p.qZA(),p.TgZ(78,"div"),p.TgZ(79,"pagination-controls",33),p.NdJ("pageChange",function(e){return t.handlePageChange1(e)}),p.qZA(),p.qZA(),p.qZA(),p.TgZ(80,"div",28),p.TgZ(81,"button",29),p.NdJ("click",function(){return p.CHM(i),p.MAs(48).hide(),t.clear()}),p._uU(82,"Cancel"),p.qZA(),p.TgZ(83,"button",30),p.NdJ("click",function(){p.CHM(i);var e=p.MAs(48);return t.EditRole(t.role._id),e.hide()}),p._uU(84,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(85,"div",34,35),p.TgZ(87,"div",36),p.TgZ(88,"div",19),p.TgZ(89,"div",20),p.TgZ(90,"h4",21),p._uU(91,"Are you sure ?"),p.qZA(),p.qZA(),p.TgZ(92,"div",22),p.TgZ(93,"div",0),p.TgZ(94,"div",23),p.TgZ(95,"p"),p._uU(96,"Do you want to delete this Role?"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(97,"div",28),p.TgZ(98,"button",29),p.NdJ("click",function(){return p.CHM(i),p.MAs(86).hide()}),p._uU(99,"Cancel"),p.qZA(),p.TgZ(100,"button",37),p.NdJ("click",function(){p.CHM(i);var e=p.MAs(86);return t.Deleterole(),e.hide()}),p._uU(101,"Delete"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()}2&e&&(p.xp6(8),p.Q6J("ngIf",t.Add),p.xp6(6),p.Q6J("ngModel",t.name),p.xp6(9),p.Q6J("ngForOf",p.xi3(24,12,t.roles,p.WLB(18,_,t.page,t.count))),p.xp6(4),p.Q6J("config",p.DdM(21,x)),p.xp6(13),p.Q6J("ngModel",t.role.name)("ngModelOptions",p.DdM(22,k)),p.xp6(1),p.Q6J("ngIf",t.rolefailed),p.xp6(6),p.Q6J("config",p.DdM(23,x)),p.xp6(13),p.Q6J("ngModel",t.role.name)("ngModelOptions",p.DdM(24,k)),p.xp6(16),p.Q6J("ngForOf",p.xi3(77,15,t.permissions,p.WLB(25,U,t.page1,t.count1))),p.xp6(9),p.Q6J("config",p.DdM(28,x)))},directives:[r.O5,u.Fj,u.JJ,u.On,r.sg,l.LS,d.oB,u.Q7,u.Wl],pipes:[l._s],styles:[""]}),e}(),J=a(49533),N=["AddModal"],w=["primaryModal"],I=["deleteModal"];function E(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"button",40),p.NdJ("click",function(){return p.CHM(i),p.oxw(),p.MAs(29).show()}),p._uU(1," Add Module "),p.qZA()}}function L(e,t){1&e&&(p.TgZ(0,"th"),p._uU(1,"Status"),p.qZA())}function S(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"td"),p.TgZ(1,"label",42),p.TgZ(2,"input",43),p.NdJ("change",function(){p.CHM(i);var e=p.oxw().$implicit;return p.oxw().changed(e.acc_activation,e._id)})("ngModelChange",function(e){return p.CHM(i),p.oxw().$implicit.acc_activation=e}),p.qZA(),p._UZ(3,"span",44),p.qZA(),p.qZA()}if(2&e){var n=p.oxw().$implicit;p.xp6(2),p.Q6J("ngModel",n.acc_activation)}}function Q(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",45),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(49).show(),t.GetModule(e._id)}),p.TgZ(1,"span",46),p._UZ(2,"i",47),p._uU(3," Edit"),p.qZA(),p.qZA()}}function G(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",45),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(68).show(),t.GetModulee(e._id)}),p.TgZ(1,"span",48),p._UZ(2,"i",49),p._uU(3," Delete"),p.qZA(),p.qZA()}}function F(e,t){if(1&e&&(p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.YNc(3,S,4,1,"td",14),p.TgZ(4,"td"),p.YNc(5,Q,4,0,"a",41),p.YNc(6,G,4,0,"a",41),p.qZA(),p.qZA()),2&e){var i=t.$implicit,n=p.oxw();p.xp6(2),p.Oqu(i.name),p.xp6(1),p.Q6J("ngIf",n.Edit),p.xp6(2),p.Q6J("ngIf",n.Edit),p.xp6(1),p.Q6J("ngIf",n.Delete)}}function Y(e,t){1&e&&(p.TgZ(0,"div",50),p._uU(1,"*please enter Module"),p.qZA())}var D=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},O=function(){return{backdrop:"static",keyboard:!1}},P=function(){return{standalone:!0}},R=function(){var e=function(){function e(t,n,o,a,r,d){i(this,e),this.moduleService=t,this.route=n,this.router=o,this.tokenStorage=a,this.Permission=r,this.EmployeeService=d,this.modules=[],this.page=1,this.count=0,this.search="",this.name="",this.module={id:"",name:"",acc_activation:""},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}return o(e,[{key:"ngOnInit",value:function(){this.tokens()}},{key:"show",value:function(){this.AddModal.show(),this.primaryModal.show()}},{key:"hide",value:function(){this.AddModal.hide(),this.primaryModal.hide()}},{key:"clear",value:function(){this.module={}}},{key:"tokens",value:function(){var e=this,t=this.tokenStorage.getToken(),i=this.tokenStorage.getUser();null!=t?(this.Permission.GetModule(i.role_id._id).subscribe(function(t){for(var i=0;i<t.data.length;i++)"Module"==t.data[i].module_name&&(e.Add=t.data[i].add,e.Edit=t.data[i].edit,e.Delete=t.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(i._id).subscribe(function(t){0==t.data.status&&e.tokenStorage.signOut()}),this.GetModuleLists()):this.router.navigate(["/login"])}},{key:"getrequestparams",value:function(e){var t={};return t.skip=10*(e-1),t}},{key:"GetModuleLists",value:function(){var e=this,t=this.getrequestparams(this.page);this.moduleService.GetModuleList(t,this.name).subscribe(function(t){e.modules=t.data,e.count=t.count})}},{key:"handlePageChange",value:function(e){this.page=e,this.GetModuleLists()}},{key:"GetModule",value:function(e){var t=this;this.moduleService.GetModuleDetail(e).subscribe(function(e){t.module=e.data[0]})}},{key:"EditModule",value:function(e){var t=this;this.moduleService.UpdateModule(e,{name:this.module.name}).subscribe(function(e){t.module={},t.GetModuleLists()})}},{key:"changed",value:function(e,t){this.moduleService.UpdateModule(t,{acc_activation:e}).subscribe(function(e){})}},{key:"AddModule",value:function(){var e=this;null!=this.module.name&&""!=this.module.name&&(this.AddModal.hide(),this.moduleService.NewModule({name:this.module.name}).subscribe(function(t){e.module={},e.rolefailed=!1,e.GetModuleLists()})),this.rolefailed=!0}},{key:"DeleteModule",value:function(){var e=this;this.moduleService.DeleteModule(this.Id).subscribe(function(t){e.GetModuleLists()})}},{key:"GetModulee",value:function(e){this.Id=e}}]),e}();return e.\u0275fac=function(t){return new(t||e)(p.Y36(J.C),p.Y36(s.gz),p.Y36(s.F0),p.Y36(m.i),p.Y36(g.$),p.Y36(Z.d))},e.\u0275cmp=p.Xpm({type:e,selectors:[["app-module"]],viewQuery:function(e,t){var i;(1&e&&(p.Gf(N,1),p.Gf(w,1),p.Gf(I,1)),2&e)&&(p.iGM(i=p.CRH())&&(t.AddModal=i.first),p.iGM(i=p.CRH())&&(t.primaryModal=i.first),p.iGM(i=p.CRH())&&(t.deleteModal=i.first))},decls:84,vars:23,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["rodule","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","module-name","placeholder","Enter your Module Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],["type","text","id","edit-name","placeholder","Enter your Module Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["deleteModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.acc_activation",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"]],template:function(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Module "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.YNc(8,E,2,0,"button",6),p.TgZ(9,"div",7),p.TgZ(10,"div",8),p.TgZ(11,"div",9),p.TgZ(12,"span",10),p.TgZ(13,"i",11),p.NdJ("click",function(){return t.GetModuleLists()}),p.qZA(),p.qZA(),p.qZA(),p.TgZ(14,"input",12),p.NdJ("input",function(){return t.GetModuleLists()})("ngModelChange",function(e){return t.name=e}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(15,"table",13),p.TgZ(16,"thead"),p.TgZ(17,"tr"),p.TgZ(18,"th"),p._uU(19,"Module Name"),p.qZA(),p.YNc(20,L,2,0,"th",14),p.TgZ(21,"th"),p._uU(22,"Action"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(23,"tbody"),p.YNc(24,F,7,4,"tr",15),p.ALo(25,"paginate"),p.qZA(),p.qZA(),p.TgZ(26,"div"),p.TgZ(27,"pagination-controls",16),p.NdJ("pageChange",function(e){return t.handlePageChange(e)}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(28,"div",17,18),p.TgZ(30,"div",19),p.TgZ(31,"div",20),p.TgZ(32,"div",21),p.TgZ(33,"h4",22),p._uU(34,"Add Module"),p.qZA(),p.qZA(),p.TgZ(35,"div",23),p.TgZ(36,"div",0),p.TgZ(37,"div",24),p.TgZ(38,"div",25),p.TgZ(39,"label",26),p._uU(40,"Module Name"),p.qZA(),p.TgZ(41,"input",27),p.NdJ("ngModelChange",function(e){return t.module.name=e})("keydown.enter",function(){return t.AddModule()}),p.qZA(),p.YNc(42,Y,2,0,"div",28),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(43,"div",29),p.TgZ(44,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(29).hide(),t.clear()}),p._uU(45,"Cancel"),p.qZA(),p.TgZ(46,"button",31),p.NdJ("click",function(){return t.AddModule()}),p._uU(47,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(48,"div",32,33),p.TgZ(50,"div",34),p.TgZ(51,"div",20),p.TgZ(52,"div",21),p.TgZ(53,"h4",22),p._uU(54,"Edit Module"),p.qZA(),p.qZA(),p.TgZ(55,"div",23),p.TgZ(56,"div",0),p.TgZ(57,"div",24),p.TgZ(58,"div",25),p.TgZ(59,"label",26),p._uU(60,"Module Name"),p.qZA(),p.TgZ(61,"input",35),p.NdJ("ngModelChange",function(e){return t.module.name=e})("keydown.enter",function(){p.CHM(i);var e=p.MAs(49);return t.EditModule(t.module.id),e.hide()}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(62,"div",29),p.TgZ(63,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(49).hide(),t.clear()}),p._uU(64,"Cancel"),p.qZA(),p.TgZ(65,"button",31),p.NdJ("click",function(){p.CHM(i);var e=p.MAs(49);return t.EditModule(t.module.id),e.hide()}),p._uU(66,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(67,"div",36,37),p.TgZ(69,"div",38),p.TgZ(70,"div",20),p.TgZ(71,"div",21),p.TgZ(72,"h4",22),p._uU(73,"Are you sure ?"),p.qZA(),p.qZA(),p.TgZ(74,"div",23),p.TgZ(75,"div",0),p.TgZ(76,"div",24),p.TgZ(77,"p"),p._uU(78,"Do you want to delete this Module?"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(79,"div",29),p.TgZ(80,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(68).hide()}),p._uU(81,"Cancel"),p.qZA(),p.TgZ(82,"button",39),p.NdJ("click",function(){p.CHM(i);var e=p.MAs(68);return t.DeleteModule(),e.hide()}),p._uU(83,"Delete"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()}2&e&&(p.xp6(8),p.Q6J("ngIf",t.Add),p.xp6(6),p.Q6J("ngModel",t.name),p.xp6(6),p.Q6J("ngIf",t.Edit),p.xp6(4),p.Q6J("ngForOf",p.xi3(25,12,t.modules,p.WLB(15,D,t.page,t.count))),p.xp6(4),p.Q6J("config",p.DdM(18,O)),p.xp6(13),p.Q6J("ngModel",t.module.name)("ngModelOptions",p.DdM(19,P)),p.xp6(1),p.Q6J("ngIf",t.rolefailed),p.xp6(6),p.Q6J("config",p.DdM(20,O)),p.xp6(13),p.Q6J("ngModel",t.module.name)("ngModelOptions",p.DdM(21,P)),p.xp6(6),p.Q6J("config",p.DdM(22,O)))},directives:[r.O5,u.Fj,u.JJ,u.On,r.sg,l.LS,d.oB,u.Q7,u.Wl],pipes:[l._s],styles:[""]}),e}(),H=a(19148),$=a(21771),z=["primaryModal"],B=["AddModal"],V=["removeModal"];function j(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"button",38),p.NdJ("click",function(){return p.CHM(i),p.oxw(),p.MAs(29).show()}),p._uU(1," Add Type "),p.qZA()}}function W(e,t){1&e&&(p.TgZ(0,"th"),p._uU(1,"Status"),p.qZA())}function K(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"td"),p.TgZ(1,"label",40),p.TgZ(2,"input",41),p.NdJ("change",function(){p.CHM(i);var e=p.oxw().$implicit;return p.oxw().changed(e.status,e._id)})("ngModelChange",function(e){return p.CHM(i),p.oxw().$implicit.status=e}),p.qZA(),p._UZ(3,"span",42),p.qZA(),p.qZA()}if(2&e){var n=p.oxw().$implicit;p.xp6(2),p.Q6J("ngModel",n.status)}}function X(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",43),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(50).show(),t.GetTreatment(e._id)}),p.TgZ(1,"span",44),p._UZ(2,"i",45),p._uU(3," Edit"),p.qZA(),p.qZA()}}function ee(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",43),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(71).show(),t.GetTreatment(e._id)}),p.TgZ(1,"span",46),p._UZ(2,"i",47),p._uU(3," Delete"),p.qZA(),p.qZA()}}function te(e,t){if(1&e&&(p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.YNc(3,K,4,1,"td",14),p.TgZ(4,"td"),p.YNc(5,X,4,0,"a",39),p.YNc(6,ee,4,0,"a",39),p.qZA(),p.qZA()),2&e){var i=t.$implicit,n=p.oxw();p.xp6(2),p.Oqu(i.name),p.xp6(1),p.Q6J("ngIf",n.Edit),p.xp6(2),p.Q6J("ngIf",n.Edit),p.xp6(1),p.Q6J("ngIf",n.Delete)}}function ie(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Type is mandatory"),p.qZA())}function ne(e,t){if(1&e&&(p.TgZ(0,"div",48),p.YNc(1,ie,2,0,"div",14),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.name.errors.required)}}function oe(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Type is mandatory"),p.qZA())}function ae(e,t){if(1&e&&(p.TgZ(0,"div",48),p.YNc(1,oe,2,0,"div",14),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.name.errors.required)}}var re=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},de=function(){return{backdrop:"static",keyboard:!1}},le=function(e){return{"is-invalid":e}},ue=function(){var e=function(){function e(t,n,o,a,r,d,l){i(this,e),this.formBuilder=t,this.TreatmentService=n,this.route=o,this.router=a,this.tokenStorage=r,this.Permission=d,this.EmployeeService=l,this.isFormReady=!1,this.submitted=!1,this.treatments=[],this.page=1,this.count=0,this.search="",this.name="",this.treatment={},this.nameFailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}return o(e,[{key:"ngOnInit",value:function(){this.tokens(),this.SignForm()}},{key:"clear",value:function(){this.treatment={},this.nameFailed=!1,this.isFormReady=!1,this.submitted=!1,this.loginForm.reset()}},{key:"getfocus",value:function(){this.nameFailed=!1}},{key:"tokens",value:function(){var e=this;this.tokenStorage.getToken();var t=this.tokenStorage.getUser();this.Permission.GetModule(t.role_id._id).subscribe(function(t){for(var i=0;i<t.data.length;i++)"Treatments"==t.data[i].module_name&&(e.Add=t.data[i].add,e.Edit=t.data[i].edit,e.Delete=t.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(t._id).subscribe(function(t){0==t.data.status&&e.tokenStorage.signOut()}),this.treatmentLists()}},{key:"getrequestparams",value:function(e){var t={};return t.skip=10*(e-1),t}},{key:"treatmentLists",value:function(){var e=this,t=this.getrequestparams(this.page);this.TreatmentService.GetTreatmentsList(t,this.name).subscribe(function(t){e.treatments=t.data,e.count=t.count})}},{key:"handlePageChange",value:function(e){this.page=e,this.treatmentLists()}},{key:"GetTreatment",value:function(e){var t=this;this.TreatmentService.GetTreatmentDetail(e).subscribe(function(e){t.treatment=e.data[0],t.f.name.setValue(e.data[0].name,{onlySelf:!0})})}},{key:"EditTreatment",value:function(e){var t=this;this.submitted=!0,this.loginForm.invalid||this.TreatmentService.UpdateTreatment(e,{name:this.loginForm.value.name}).subscribe(function(e){t.primaryModal.hide(),t.clear(),t.treatmentLists()})}},{key:"changed",value:function(e,t){this.TreatmentService.UpdateTreatment(t,{status:e}).subscribe(function(e){})}},{key:"SignForm",value:function(){this.loginForm=this.formBuilder.group({name:["",[u.kI.required]]})}},{key:"f",get:function(){return this.loginForm.controls}},{key:"AddTreatment",value:function(){var e=this;if(this.submitted=!0,!this.loginForm.invalid){var t={name:this.loginForm.value.name};this.AddModal.hide(),this.TreatmentService.NewTreatment(t).subscribe(function(t){e.clear(),e.treatmentLists()})}}},{key:"DeleteTreatment",value:function(e){var t=this;this.TreatmentService.DeleteTreatment(e).subscribe(function(e){t.removeModal.hide(),t.treatmentLists()})}}]),e}();return e.\u0275fac=function(t){return new(t||e)(p.Y36(u.qu),p.Y36($.J),p.Y36(s.gz),p.Y36(s.F0),p.Y36(m.i),p.Y36(g.$),p.Y36(Z.d))},e.\u0275cmp=p.Xpm({type:e,selectors:[["app-treatments"]],viewQuery:function(e,t){var i;(1&e&&(p.Gf(z,1),p.Gf(B,1),p.Gf(V,1)),2&e)&&(p.iGM(i=p.CRH())&&(t.primaryModal=i.first),p.iGM(i=p.CRH())&&(t.AddModal=i.first),p.iGM(i=p.CRH())&&(t.removeModal=i.first))},decls:87,vars:26,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","name"],["type","text","placeholder","e.g. Injury, UTI, Vaccine","formControlName","name",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"]],template:function(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Appointment Types "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.YNc(8,j,2,0,"button",6),p.TgZ(9,"div",7),p.TgZ(10,"div",8),p.TgZ(11,"div",9),p.TgZ(12,"span",10),p.NdJ("click",function(){return t.treatmentLists()}),p._UZ(13,"i",11),p.qZA(),p.qZA(),p.TgZ(14,"input",12),p.NdJ("input",function(){return t.treatmentLists()})("ngModelChange",function(e){return t.name=e}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(15,"table",13),p.TgZ(16,"thead"),p.TgZ(17,"tr"),p.TgZ(18,"th"),p._uU(19,"Type"),p.qZA(),p.YNc(20,W,2,0,"th",14),p.TgZ(21,"th"),p._uU(22,"Action"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(23,"tbody"),p.YNc(24,te,7,4,"tr",15),p.ALo(25,"paginate"),p.qZA(),p.qZA(),p.TgZ(26,"div"),p.TgZ(27,"pagination-controls",16),p.NdJ("pageChange",function(e){return t.handlePageChange(e)}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(28,"div",17,18),p.TgZ(30,"div",19),p.TgZ(31,"div",20),p.TgZ(32,"div",21),p.TgZ(33,"h4",22),p._uU(34,"Add Type"),p.qZA(),p.qZA(),p.TgZ(35,"div",23),p.TgZ(36,"div",0),p.TgZ(37,"div",24),p.TgZ(38,"form",25),p.TgZ(39,"div",26),p.TgZ(40,"label",27),p._uU(41,"Type*"),p.qZA(),p._UZ(42,"input",28),p.YNc(43,ne,2,1,"div",29),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(44,"div",30),p.TgZ(45,"button",31),p.NdJ("click",function(){return p.CHM(i),p.MAs(29).hide(),t.clear()}),p._uU(46,"Cancel"),p.qZA(),p.TgZ(47,"button",32),p.NdJ("click",function(){return t.AddTreatment()}),p._uU(48,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(49,"div",17,33),p.TgZ(51,"div",19),p.TgZ(52,"div",20),p.TgZ(53,"div",21),p.TgZ(54,"h4",22),p._uU(55,"Edit Type"),p.qZA(),p.qZA(),p.TgZ(56,"div",23),p.TgZ(57,"div",0),p.TgZ(58,"div",24),p.TgZ(59,"form",25),p.TgZ(60,"div",26),p.TgZ(61,"label",27),p._uU(62,"Type*"),p.qZA(),p._UZ(63,"input",28),p.YNc(64,ae,2,1,"div",29),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(65,"div",30),p.TgZ(66,"button",31),p.NdJ("click",function(){return p.CHM(i),p.MAs(50).hide(),t.clear()}),p._uU(67,"Cancel"),p.qZA(),p.TgZ(68,"button",32),p.NdJ("click",function(){return t.EditTreatment(t.treatment._id)}),p._uU(69,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(70,"div",34,35),p.TgZ(72,"div",36),p.TgZ(73,"div",20),p.TgZ(74,"div",21),p.TgZ(75,"h4",22),p._uU(76,"Are you sure ?"),p.qZA(),p.qZA(),p.TgZ(77,"div",23),p.TgZ(78,"div",0),p.TgZ(79,"div",24),p.TgZ(80,"p"),p._uU(81,"Do you want to delete this Appointment?"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(82,"div",30),p.TgZ(83,"button",31),p.NdJ("click",function(){return p.CHM(i),p.MAs(71).hide(),t.clear()}),p._uU(84,"Cancel"),p.qZA(),p.TgZ(85,"button",37),p.NdJ("click",function(){return t.DeleteTreatment(t.treatment._id)}),p._uU(86,"Delete"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()}2&e&&(p.xp6(8),p.Q6J("ngIf",t.Add),p.xp6(6),p.Q6J("ngModel",t.name),p.xp6(6),p.Q6J("ngIf",t.Edit),p.xp6(4),p.Q6J("ngForOf",p.xi3(25,13,t.treatments,p.WLB(16,re,t.page,t.count))),p.xp6(4),p.Q6J("config",p.DdM(19,de)),p.xp6(10),p.Q6J("formGroup",t.loginForm),p.xp6(4),p.Q6J("ngClass",p.VKq(20,le,t.submitted&&t.f.name.errors)),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.name.errors),p.xp6(6),p.Q6J("config",p.DdM(22,de)),p.xp6(10),p.Q6J("formGroup",t.loginForm),p.xp6(4),p.Q6J("ngClass",p.VKq(23,le,t.submitted&&t.f.name.errors)),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.name.errors),p.xp6(6),p.Q6J("config",p.DdM(25,de)))},directives:[r.O5,u.Fj,u.JJ,u.On,r.sg,l.LS,d.oB,u.vK,u.JL,u.sg,u.u,r.mk,u.Wl],pipes:[l._s],styles:[""]}),e}(),se=a(87188),ce=["primaryModal"],ge=["AddModal"],Ze=["removeModal"];function pe(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"button",38),p.NdJ("click",function(){return p.CHM(i),p.oxw(),p.MAs(29).show()}),p._uU(1," Add Location "),p.qZA()}}function me(e,t){1&e&&(p.TgZ(0,"th"),p._uU(1,"Status"),p.qZA())}function fe(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"td"),p.TgZ(1,"label",40),p.TgZ(2,"input",41),p.NdJ("change",function(){p.CHM(i);var e=p.oxw().$implicit;return p.oxw().changed(e.status,e._id)})("ngModelChange",function(e){return p.CHM(i),p.oxw().$implicit.status=e}),p.qZA(),p._UZ(3,"span",42),p.qZA(),p.qZA()}if(2&e){var n=p.oxw().$implicit;p.xp6(2),p.Q6J("ngModel",n.status)}}function he(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",43),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(49).show(),t.Getlocation(e._id)}),p.TgZ(1,"span",44),p._UZ(2,"i",45),p._uU(3," Edit"),p.qZA(),p.qZA()}}function ve(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",43),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw();return p.MAs(68).show(),t.Getlocation(e._id)}),p.TgZ(1,"span",46),p._UZ(2,"i",47),p._uU(3," Delete"),p.qZA(),p.qZA()}}function Ae(e,t){if(1&e&&(p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.YNc(3,fe,4,1,"td",14),p.TgZ(4,"td"),p.YNc(5,he,4,0,"a",39),p.YNc(6,ve,4,0,"a",39),p.qZA(),p.qZA()),2&e){var i=t.$implicit,n=p.oxw();p.xp6(2),p.Oqu(i.name),p.xp6(1),p.Q6J("ngIf",n.Edit),p.xp6(2),p.Q6J("ngIf",n.Edit),p.xp6(1),p.Q6J("ngIf",n.Delete)}}function qe(e,t){1&e&&(p.TgZ(0,"div",48),p._uU(1,"*please enter location"),p.qZA())}var Te=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},be=function(){return{backdrop:"static",keyboard:!1}},ye=function(){return{standalone:!0}},Me=function(){var e=function(){function e(t,n,o,a,r,d){i(this,e),this.locationservice=t,this.route=n,this.router=o,this.tokenStorage=a,this.Permission=r,this.EmployeeService=d,this.locations=[],this.page=1,this.count=0,this.search="",this.name="",this.location={},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}return o(e,[{key:"ngOnInit",value:function(){this.tokens()}},{key:"show",value:function(){this.AddModal.show(),this.primaryModal.show(),this.removeModal.show()}},{key:"hide",value:function(){this.AddModal.hide(),this.primaryModal.hide(),this.removeModal.hide()}},{key:"clear",value:function(){this.location={},this.rolefailed=!1}},{key:"tokens",value:function(){var e=this,t=this.tokenStorage.getToken(),i=this.tokenStorage.getUser();null!=t?(this.Permission.GetModule(i.role_id._id).subscribe(function(t){for(var i=0;i<t.data.length;i++)"Location"==t.data[i].module_name&&(e.Add=t.data[i].add,e.Edit=t.data[i].edit,e.Delete=t.data[i].delete)}),this.EmployeeService.GetEmployeeDetail(i._id).subscribe(function(t){0==t.data[0].status&&e.tokenStorage.signOut()}),this.locationLists()):this.router.navigate(["/login"])}},{key:"getfocus",value:function(){this.rolefailed=!1}},{key:"getrequestparams",value:function(e){var t={};return t.skip=10*(e-1),t}},{key:"locationLists",value:function(){var e=this,t=this.getrequestparams(this.page);this.locationservice.GetLocationsList(t,this.name).subscribe(function(t){e.locations=t.data,e.count=t.count})}},{key:"handlePageChange",value:function(e){this.page=e,this.locationLists()}},{key:"Getlocation",value:function(e){var t=this;this.locationservice.GetLocationDetail(e).subscribe(function(e){t.location=e.data[0]})}},{key:"Editlocation",value:function(e){var t=this;this.locationservice.UpdateLocation(e,{name:this.location.name}).subscribe(function(e){t.location={},t.locationLists()})}},{key:"changed",value:function(e,t){this.locationservice.UpdateLocation(t,{status:e}).subscribe(function(e){})}},{key:"Addlocation",value:function(){var e=this;null!=this.location.name&&""!=this.location.name&&(this.AddModal.hide(),this.locationservice.NewLocation({name:this.location.name}).subscribe(function(t){e.location={},e.locationLists()})),this.rolefailed=!0}},{key:"Deletelocation",value:function(e){var t=this;this.locationservice.DeleteLocation(e).subscribe(function(e){t.removeModal.hide(),t.locationLists()})}}]),e}();return e.\u0275fac=function(t){return new(t||e)(p.Y36(se.a),p.Y36(s.gz),p.Y36(s.F0),p.Y36(m.i),p.Y36(g.$),p.Y36(Z.d))},e.\u0275cmp=p.Xpm({type:e,selectors:[["app-location"]],viewQuery:function(e,t){var i;(1&e&&(p.Gf(ce,1),p.Gf(ge,1),p.Gf(Ze,1)),2&e)&&(p.iGM(i=p.CRH())&&(t.primaryModal=i.first),p.iGM(i=p.CRH())&&(t.AddModal=i.first),p.iGM(i=p.CRH())&&(t.removeModal=i.first))},decls:84,vars:23,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","Type-name","placeholder","Enter Location Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter","click"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["type","text","id","edit-name","placeholder","Location Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"]],template:function(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Location "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.YNc(8,pe,2,0,"button",6),p.TgZ(9,"div",7),p.TgZ(10,"div",8),p.TgZ(11,"div",9),p.TgZ(12,"span",10),p.NdJ("click",function(){return t.locationLists()}),p._UZ(13,"i",11),p.qZA(),p.qZA(),p.TgZ(14,"input",12),p.NdJ("input",function(){return t.locationLists()})("ngModelChange",function(e){return t.name=e}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(15,"table",13),p.TgZ(16,"thead"),p.TgZ(17,"tr"),p.TgZ(18,"th"),p._uU(19,"Location Name"),p.qZA(),p.YNc(20,me,2,0,"th",14),p.TgZ(21,"th"),p._uU(22,"Action"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(23,"tbody"),p.YNc(24,Ae,7,4,"tr",15),p.ALo(25,"paginate"),p.qZA(),p.qZA(),p.TgZ(26,"div"),p.TgZ(27,"pagination-controls",16),p.NdJ("pageChange",function(e){return t.handlePageChange(e)}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(28,"div",17,18),p.TgZ(30,"div",19),p.TgZ(31,"div",20),p.TgZ(32,"div",21),p.TgZ(33,"h4",22),p._uU(34,"Add Location"),p.qZA(),p.qZA(),p.TgZ(35,"div",23),p.TgZ(36,"div",0),p.TgZ(37,"div",24),p.TgZ(38,"div",25),p.TgZ(39,"label",26),p._uU(40,"Location Name"),p.qZA(),p.TgZ(41,"input",27),p.NdJ("ngModelChange",function(e){return t.location.name=e})("keydown.enter",function(){return t.Addlocation()})("click",function(){return t.getfocus()}),p.qZA(),p.YNc(42,qe,2,0,"div",28),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(43,"div",29),p.TgZ(44,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(29).hide(),t.clear()}),p._uU(45,"Cancel"),p.qZA(),p.TgZ(46,"button",31),p.NdJ("click",function(){return t.Addlocation()}),p._uU(47,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(48,"div",17,32),p.TgZ(50,"div",19),p.TgZ(51,"div",20),p.TgZ(52,"div",21),p.TgZ(53,"h4",22),p._uU(54,"Edit Location"),p.qZA(),p.qZA(),p.TgZ(55,"div",23),p.TgZ(56,"div",0),p.TgZ(57,"div",24),p.TgZ(58,"div",25),p.TgZ(59,"label",26),p._uU(60,"Location Name"),p.qZA(),p.TgZ(61,"input",33),p.NdJ("ngModelChange",function(e){return t.location.name=e})("keydown.enter",function(){p.CHM(i);var e=p.MAs(49);return t.Editlocation(t.location._id),e.hide()}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(62,"div",29),p.TgZ(63,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(49).hide(),t.clear()}),p._uU(64,"Cancel"),p.qZA(),p.TgZ(65,"button",31),p.NdJ("click",function(){p.CHM(i);var e=p.MAs(49);return t.Editlocation(t.location._id),e.hide()}),p._uU(66,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(67,"div",34,35),p.TgZ(69,"div",36),p.TgZ(70,"div",20),p.TgZ(71,"div",21),p.TgZ(72,"h4",22),p._uU(73,"Are you sure ?"),p.qZA(),p.qZA(),p.TgZ(74,"div",23),p.TgZ(75,"div",0),p.TgZ(76,"div",24),p.TgZ(77,"p"),p._uU(78,"Do you want to delete this Location?"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(79,"div",29),p.TgZ(80,"button",30),p.NdJ("click",function(){return p.CHM(i),p.MAs(68).hide(),t.clear()}),p._uU(81,"Cancel"),p.qZA(),p.TgZ(82,"button",37),p.NdJ("click",function(){return t.Deletelocation(t.location._id)}),p._uU(83,"Delete"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()}2&e&&(p.xp6(8),p.Q6J("ngIf",t.Add),p.xp6(6),p.Q6J("ngModel",t.name),p.xp6(6),p.Q6J("ngIf",t.Edit),p.xp6(4),p.Q6J("ngForOf",p.xi3(25,12,t.locations,p.WLB(15,Te,t.page,t.count))),p.xp6(4),p.Q6J("config",p.DdM(18,be)),p.xp6(13),p.Q6J("ngModel",t.location.name)("ngModelOptions",p.DdM(19,ye)),p.xp6(1),p.Q6J("ngIf",t.rolefailed),p.xp6(6),p.Q6J("config",p.DdM(20,be)),p.xp6(13),p.Q6J("ngModel",t.location.name)("ngModelOptions",p.DdM(21,ye)),p.xp6(6),p.Q6J("config",p.DdM(22,be)))},directives:[r.O5,u.Fj,u.JJ,u.On,r.sg,l.LS,d.oB,u.Q7,u.Wl],pipes:[l._s],styles:[""]}),e}(),_e=a(72945);function xe(e,t){if(1&e&&(p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.TgZ(3,"td"),p._uU(4),p.qZA(),p.TgZ(5,"td"),p._uU(6),p.qZA(),p.TgZ(7,"td"),p._uU(8),p.qZA(),p.TgZ(9,"td"),p._uU(10),p.qZA(),p.TgZ(11,"td"),p._uU(12),p.qZA(),p.TgZ(13,"td"),p._uU(14),p.qZA(),p.TgZ(15,"td"),p._uU(16),p.qZA(),p.TgZ(17,"td"),p._uU(18),p.qZA(),p.TgZ(19,"td"),p._uU(20),p.qZA(),p.qZA()),2&e){var i=t.$implicit;p.xp6(2),p.Oqu(i.DBID),p.xp6(2),p.Oqu(i.Id),p.xp6(2),p.Oqu(i.Code),p.xp6(2),p.Oqu(i.CodeCategory),p.xp6(2),p.Oqu(i.CodeCategoryDescription),p.xp6(2),p.Oqu(i.CodeDescription),p.xp6(2),p.Oqu(i.CodeType),p.xp6(2),p.Oqu(i.MinimumPrice),p.xp6(2),p.Oqu(i.MaximumPrice),p.xp6(2),p.Oqu("false"===i.Inactive?"Active":"Inactive")}}var ke=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},Ue=function(){var e=function(){function e(t){i(this,e),this.covertusService=t,this.page=1,this.search="",this.filter="",this.status="",this.CovertusList=[],this.count=0}return o(e,[{key:"ngOnInit",value:function(){this.GetCovertus()}},{key:"GetCovertus",value:function(){var e=this;this.covertusService.GetCovertusList({skip:10*(this.page-1),limit:10,search:this.search,filter:this.filter,status:this.status}).subscribe(function(t){console.log("covertusList--\x3e",t),e.CovertusList=t.data,e.count=t.count})}},{key:"UpdateCovertus",value:function(){var e=this;this.covertusService.UpdateCovertus({}).subscribe(function(t){console.log("covertusList--\x3e",t),e.GetCovertus()})}},{key:"handlePageChange",value:function(e){this.page=e,this.GetCovertus()}}]),e}();return e.\u0275fac=function(t){return new(t||e)(p.Y36(_e.x))},e.\u0275cmp=p.Xpm({type:e,selectors:[["app-covertus"]],decls:74,vars:8,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col",2,"margin-bottom","12px"],["type","button","data-toggle","modal",1,"btn","btn-primary",3,"click"],[1,"col"],["id","select1","name","select1",1,"form-control",2,"width","100%",3,"change"],["value",""],["value","false"],["value","true"],["value","Diagnostic"],["value","Inventory"],["value","Service"],["value","Payment"],["value","Problem"],[1,"col","input-group"],[1,"input-group-prepend"],[1,"input-group-text",2,"height","35px"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"]],template:function(e,t){1&e&&(p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Covertus "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.TgZ(8,"button",6),p.NdJ("click",function(){return t.UpdateCovertus()}),p._uU(9," Update List "),p.qZA(),p.qZA(),p._UZ(10,"div",7),p.TgZ(11,"div",7),p.TgZ(12,"select",8),p.NdJ("change",function(e){return t.status=e.target.value,t.GetCovertus()}),p.TgZ(13,"option",9),p._uU(14,"--Status--"),p.qZA(),p.TgZ(15,"option",10),p._uU(16,"Active"),p.qZA(),p.TgZ(17,"option",11),p._uU(18,"Inactive"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(19,"div",7),p.TgZ(20,"select",8),p.NdJ("change",function(e){return t.filter=e.target.value,t.GetCovertus()}),p.TgZ(21,"option",9),p._uU(22,"--Code Type--"),p.qZA(),p.TgZ(23,"option",12),p._uU(24,"Diagnostic"),p.qZA(),p.TgZ(25,"option",13),p._uU(26,"Inventory"),p.qZA(),p.TgZ(27,"option",14),p._uU(28,"Service"),p.qZA(),p.TgZ(29,"option",15),p._uU(30,"Payment"),p.qZA(),p.TgZ(31,"option",16),p._uU(32,"Problem"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(33,"div",17),p.TgZ(34,"div",18),p.TgZ(35,"span",19),p.TgZ(36,"i",20),p.NdJ("click",function(){return t.page=1,t.GetCovertus()}),p.qZA(),p.qZA(),p.qZA(),p.TgZ(37,"input",21),p.NdJ("input",function(){return t.page=1,t.GetCovertus()})("ngModelChange",function(e){return t.search=e}),p.qZA(),p.qZA(),p.qZA(),p.TgZ(38,"table",22),p.TgZ(39,"thead"),p.TgZ(40,"tr"),p.TgZ(41,"th"),p._uU(42,"DBID"),p.qZA(),p.TgZ(43,"th"),p._uU(44,"ID"),p.qZA(),p.TgZ(45,"th"),p._uU(46,"Code"),p.qZA(),p.TgZ(47,"th"),p._uU(48,"Code "),p._UZ(49,"br"),p._uU(50,"Category"),p.qZA(),p.TgZ(51,"th"),p._uU(52,"Code Category "),p._UZ(53,"br"),p._uU(54,"Description"),p.qZA(),p.TgZ(55,"th"),p._uU(56,"Code Description"),p.qZA(),p.TgZ(57,"th"),p._uU(58,"Code Type"),p.qZA(),p.TgZ(59,"th"),p._uU(60,"Minimum "),p._UZ(61,"br"),p._uU(62,"Price"),p.qZA(),p.TgZ(63,"th"),p._uU(64,"Maximum "),p._UZ(65,"br"),p._uU(66,"Price"),p.qZA(),p.TgZ(67,"th"),p._uU(68,"Status"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(69,"tbody"),p.YNc(70,xe,21,10,"tr",23),p.ALo(71,"paginate"),p.qZA(),p.qZA(),p.TgZ(72,"div"),p.TgZ(73,"pagination-controls",24),p.NdJ("pageChange",function(e){return t.handlePageChange(e)}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()),2&e&&(p.xp6(37),p.Q6J("ngModel",t.search),p.xp6(33),p.Q6J("ngForOf",p.xi3(71,2,t.CovertusList,p.WLB(5,ke,t.page,t.count))))},directives:[u.YN,u.ks,u.Fj,u.JJ,u.On,r.sg,l.LS],pipes:[l._s],styles:[""]}),e}(),Ce=["primaryModal"],Je=["removeModal"];function Ne(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"button",62),p.NdJ("click",function(){p.CHM(i);var e=p.oxw(),t=p.MAs(40);return e.EditId="",e.RemoveEmpText(),t.show()}),p._uU(1," Add Admin User "),p.qZA()}}function we(e,t){if(1&e&&(p.TgZ(0,"option",63),p._uU(1),p.qZA()),2&e){var i=t.$implicit;p.Q6J("value",i.name),p.xp6(1),p.Oqu(i.name)}}function Ie(e,t){1&e&&(p.TgZ(0,"th"),p._uU(1,"Status"),p.qZA())}function Ee(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"td"),p.TgZ(1,"label",69),p.TgZ(2,"input",70),p.NdJ("change",function(){p.CHM(i);var e=p.oxw().$implicit;return p.oxw().changed(e.status,e._id)})("ngModelChange",function(e){return p.CHM(i),p.oxw().$implicit.status=e}),p.qZA(),p._UZ(3,"span",71),p.qZA(),p.qZA()}if(2&e){var n=p.oxw().$implicit;p.xp6(2),p.Q6J("ngModel",n.status)}}function Le(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",72),p.NdJ("click",function(){p.CHM(i);var e=p.oxw(),t=e.$implicit,n=e.index,o=p.oxw(),a=p.MAs(40);return o.EditId=t._id,o.Index=n,o.EditEmployee(),a.show()}),p.TgZ(1,"span",73),p._UZ(2,"i",74),p._uU(3," Edit"),p.qZA(),p.qZA()}}function Se(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"a",66),p.NdJ("click",function(){p.CHM(i);var e=p.oxw().$implicit,t=p.oxw(),n=p.MAs(135);return t.EditId=e._id,n.show()}),p.TgZ(1,"span",75),p._UZ(2,"i",76),p._uU(3," Delete"),p.qZA(),p.qZA()}}function Qe(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"tr"),p.TgZ(1,"td"),p._uU(2),p.qZA(),p.TgZ(3,"td"),p._uU(4),p.qZA(),p.TgZ(5,"td"),p._uU(6),p.qZA(),p.TgZ(7,"td"),p._uU(8),p.qZA(),p.YNc(9,Ee,4,1,"td",18),p.TgZ(10,"td"),p.YNc(11,Le,4,0,"a",64),p.YNc(12,Se,4,0,"a",65),p._uU(13,"\xa0 "),p.TgZ(14,"a",66),p.NdJ("click",function(){p.CHM(i);var e=t.$implicit,n=p.oxw(),o=p.MAs(101);return n.EditId=e._id,n.ViewLog(n.EditId),o.show()}),p.TgZ(15,"span",67),p._UZ(16,"i",68),p._uU(17,"\xa0 view log"),p.qZA(),p.qZA(),p.qZA(),p.qZA()}if(2&e){var n=t.$implicit,o=p.oxw();p.xp6(2),p.Oqu(n.name),p.xp6(2),p.Oqu(n.email),p.xp6(2),p.Oqu(n.role_name),p.xp6(2),p.Oqu(n.location),p.xp6(1),p.Q6J("ngIf",o.Edit),p.xp6(2),p.Q6J("ngIf",o.Edit),p.xp6(1),p.Q6J("ngIf",o.Delete)}}function Ge(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Name is mandatory"),p.qZA())}function Fe(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Alphabet characters only"),p.qZA())}function Ye(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Name isn't long enough, minimum of 3 characters"),p.qZA())}function De(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,Ge,2,0,"div",18),p.YNc(2,Fe,2,0,"div",18),p.YNc(3,Ye,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.name.errors.required),p.xp6(1),p.Q6J("ngIf",i.f.name.errors.pattern),p.xp6(1),p.Q6J("ngIf",i.f.name.errors.minlength)}}function Oe(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Email is mandatory"),p.qZA())}function Pe(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"*Please enter a valid Email address"),p.qZA())}function Re(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,Oe,2,0,"div",18),p.YNc(2,Pe,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.email.errors.required),p.xp6(1),p.Q6J("ngIf",i.f.email.errors.email||i.f.email.errors.pattern)}}function He(e,t){1&e&&(p.TgZ(0,"div",78),p._uU(1,"*Email is already registered with us"),p.qZA())}function $e(e,t){if(1&e&&(p.TgZ(0,"option",63),p._uU(1),p.qZA()),2&e){var i=t.$implicit;p.Q6J("value",i.name),p.xp6(1),p.hij(" ",i.name,"")}}function ze(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Role is mandatory"),p.qZA())}function Be(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,ze,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.role_name.errors.required)}}function Ve(e,t){if(1&e&&(p.TgZ(0,"option",63),p._uU(1),p.qZA()),2&e){var i=t.$implicit;p.Q6J("value",i.name),p.xp6(1),p.Oqu(i.name)}}function je(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Location is mandatory"),p.qZA())}function We(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,je,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.location.errors.required)}}function Ke(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Phone number isn't long enough, minimum of 8 characters"),p.qZA())}function Xe(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Numberic characters only"),p.qZA())}function et(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,Ke,2,0,"div",18),p.YNc(2,Xe,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.phone_no.errors.minlength),p.xp6(1),p.Q6J("ngIf",i.f.phone_no.errors.pattern)}}function tt(e,t){1&e&&(p.TgZ(0,"div"),p._uU(1,"Address isn't long enough, minimum of 12 characters"),p.qZA())}function it(e,t){if(1&e&&(p.TgZ(0,"div",77),p.YNc(1,tt,2,0,"div",18),p.qZA()),2&e){var i=p.oxw();p.xp6(1),p.Q6J("ngIf",i.f.address.errors.minlength)}}function nt(e,t){if(1&e&&(p.TgZ(0,"td"),p._uU(1),p.qZA()),2&e){var i=p.oxw().$implicit;p.xp6(1),p.AsE("",i.description," to ",i.additional_info.status,"")}}function ot(e,t){if(1&e&&(p.TgZ(0,"td"),p._uU(1),p.qZA()),2&e){var i=p.oxw().$implicit;p.xp6(1),p.Oqu(i.description)}}function at(e,t){if(1&e&&(p.TgZ(0,"tr",53),p.YNc(1,nt,2,2,"td",79),p.YNc(2,ot,2,1,"ng-template",null,80,p.W1O),p.TgZ(4,"td"),p._uU(5),p.ALo(6,"date"),p.qZA(),p.qZA()),2&e){var i=t.$implicit,n=p.MAs(3);p.xp6(1),p.Q6J("ngIf",i.additional_info)("ngIfElse",n),p.xp6(4),p.Oqu(p.xi3(6,3,i.updatedAt,"short"))}}var rt=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},dt=function(){return{backdrop:"static",keyboard:!1}},lt=function(e){return{"is-invalid":e}},ut=function(){var t=function(){function t(e,n,o,a,r,d,l,u){i(this,t),this.employeeService=e,this.route=n,this.router=o,this.tokenStorage=a,this.EmployeeService=r,this.formBuilder=d,this.Permission=l,this.LocationService=u,this.employees=[],this.viewlog=[],this.roles=[],this.locations=[],this.page=1,this.count=0,this.search="",this.name="",this.email=!1,this.phone_no="",this.submitted=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.location="",this.EditId="",this.Index=0,this.GetRoleLists(),this.GetLocation(),this.GetEmployeeLists()}return o(t,[{key:"ngOnInit",value:function(){var e=this;this.SignForm();var t=this.tokenStorage.getUser();this.Permission.GetModule(t.role_id._id).subscribe(function(t){for(var i=0;i<t.data.length;i++)"Admin-Users"==t.data[i].module_name&&(e.Add=t.data[i].add,e.Edit=t.data[i].edit,e.Delete=t.data[i].delete)})}},{key:"clear",value:function(){this.email=!1,this.loginForm.reset(),this.submitted=!1}},{key:"GetRoleLists",value:function(){var e=this;this.employeeService.GetRoleList().subscribe(function(t){e.roles=t.data})}},{key:"RemoveEmpText",value:function(){console.log("phone test"),this.phone_no=""}},{key:"GetLocation",value:function(){var t=this;this.LocationService.GetLocationsList("","").subscribe(function(e){t.locations=e.data,t.locations.filter(function(e){return!0===e.status}),t.filteredLocations=t.locations.filter(function(e){return!0===e.status}),console.log("testing locationnnnnnnnn",t.filteredLocations)});var i,n=e(this.locations);try{for(n.s();!(i=n.n()).done;){var o=i.value;console.log("forloop",o[0])}}catch(a){n.e(a)}finally{n.f()}}},{key:"GetEmployeeLists",value:function(){var e=this;this.employeeService.GetEmployeeList({skip:10*(this.page-1),search:this.name,location:this.location}).subscribe(function(t){console.log("res",t),e.count=t.count,e.employees=t.data})}},{key:"handlePageChange",value:function(e){this.page=e,this.GetEmployeeLists()}},{key:"SignForm",value:function(){this.loginForm=this.formBuilder.group({name:["",[u.kI.required,u.kI.minLength(3),u.kI.pattern("[a-zA-Z. -_]*$")]],role_id:[""],role_name:["",[u.kI.required]],email:["",[u.kI.required,u.kI.email,u.kI.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]],location:["",[u.kI.required]],address:["",[u.kI.minLength(12)]],phone_no:[[u.kI.minLength(8),u.kI.pattern("[0-9-]*$")]]})}},{key:"f",get:function(){return this.loginForm.controls}},{key:"onSubmit",value:function(){var e=this;console.log(this.loginForm.value),this.submitted=!0,!this.loginForm.invalid&&(""==this.EditId?this.employeeService.NewEmployee(this.loginForm.value).subscribe(function(t){200==t.code?(e.submitted=!1,e.loginForm.reset(),e.primaryModal.hide(),e.GetRoleLists()):e.email=!0}):this.UpdateEmployee(this.EditId,this.loginForm.value))}},{key:"UpdateEmployee",value:function(e,t){var i=this;this.employeeService.EditEmployeeDetail(e,t).subscribe(function(e){200==e.code&&(i.submitted=!1,i.loginForm.reset(),i.primaryModal.hide(),""!=i.EditId&&i.GetEmployeeLists())})}},{key:"ViewLog",value:function(e){var t=this;console.log("Admin id",e),this.employeeService.GetViewLog(e).subscribe(function(e){t.viewlog=e.data,console.log(t.viewlog)})}},{key:"EditEmployee",value:function(){this.loginForm.controls.name.setValue(this.employees[this.Index].name),this.loginForm.controls.role_id.setValue(this.employees[this.Index].role_id),this.loginForm.controls.role_name.setValue(this.employees[this.Index].role_name),this.loginForm.controls.email.setValue(this.employees[this.Index].email),this.loginForm.controls.location.setValue(this.employees[this.Index].location),this.loginForm.controls.address.setValue(this.employees[this.Index].address),this.loginForm.controls.phone_no.setValue(this.employees[this.Index].phone_no)}},{key:"DeleteEmployee",value:function(e){var t=this;this.employeeService.DeleteEmployee(e).subscribe(function(e){t.removeModal.hide(),t.GetEmployeeLists()})}},{key:"changed",value:function(e,t){this.UpdateEmployee(t,{status:e})}},{key:"RoleSetValue",value:function(e){var t=this;this.roles.forEach(function(i){i.name===e&&t.loginForm.controls.role_id.setValue(i._id)})}}]),t}();return t.\u0275fac=function(e){return new(e||t)(p.Y36(Z.d),p.Y36(s.gz),p.Y36(s.F0),p.Y36(m.i),p.Y36(Z.d),p.Y36(u.qu),p.Y36(g.$),p.Y36(se.a))},t.\u0275cmp=p.Xpm({type:t,selectors:[["app-employee"]],viewQuery:function(e,t){var i;(1&e&&(p.Gf(Ce,1),p.Gf(Je,1)),2&e)&&(p.iGM(i=p.CRH())&&(t.primaryModal=i.first),p.iGM(i=p.CRH())&&(t.removeModal=i.first))},decls:151,vars:53,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"filter"],[1,"form-control",3,"ngModel","ngModelChange","change"],["value","","selected",""],[3,"value",4,"ngFor","ngForOf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["autocomplete","off",1,"form",3,"formGroup"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],[2,"color","red"],["type","text","placeholder","Name","formControlName","name","autocomplete","off",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],["for","email"],["type","email","placeholder","e.g. <EMAIL>","formControlName","email","autocomplete","off",1,"form-control",3,"ngClass","readonly"],["style","font-size: smaller;color: #f86c6b;margin-top: -14px;\n              margin-bottom: 12px;",4,"ngIf"],["for","select2"],["id","Role","name","select1","formControlName","role_name",1,"form-control",3,"ngClass","change"],["value","","selected","",3,"hidden"],["for","location"],["id","location","name","location","formControlName","location",1,"form-control",3,"ngClass"],["for","phone_no"],["id","phone_no","type","text","autocomplete","off","placeholder","e.g. 9874563210","formControlName","phone_no",1,"form-control",3,"ngModel","ngClass","ngModelChange"],["for","Address"],["type","text","autocomplete","off","placeholder","e.g. No.70,Mission street, Florida","formControlName","address",1,"form-control",3,"ngClass"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","submit",1,"btn","btn-primary",3,"click"],["primaryModal1","bs-modal"],["role","document",1,"modal-dialog","modal-primary",2,"max-width","60%"],[1,"modal-title",2,"padding","2%"],[2,"text-align","center"],["style","text-align: center;",4,"ngFor","ngForOf"],["type","button",1,"btn","btn-primary",2,"width","15%","padding","2px","margin-left","83%",3,"click"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade"],["okayModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],[3,"value"],["data-toggle","modal","style","cursor: pointer; margin-right: 10px;",3,"click",4,"ngIf"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-secondary","sucpad",2,"padding","7px","font-size","small","color","white","background-color","#2f23ef"],[1,"fa","fa-list"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer","margin-right","10px",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"],[2,"font-size","smaller","color","#f86c6b","margin-top","-14px","margin-bottom","12px"],[4,"ngIf","ngIfElse"],["elseBlock",""]],template:function(e,t){if(1&e){var i=p.EpF();p.TgZ(0,"div",0),p.TgZ(1,"div",1),p.TgZ(2,"div",2),p.TgZ(3,"div",3),p._uU(4," Admin Users "),p.qZA(),p.TgZ(5,"div",4),p.TgZ(6,"div",0),p.TgZ(7,"div",5),p.YNc(8,Ne,2,0,"button",6),p.TgZ(9,"div",7),p.TgZ(10,"select",8),p.NdJ("ngModelChange",function(e){return t.location=e})("change",function(){return t.page=1,t.GetEmployeeLists()}),p.TgZ(11,"option",9),p._uU(12,"--Location--"),p.qZA(),p.YNc(13,we,2,2,"option",10),p.qZA(),p.qZA(),p.TgZ(14,"div",11),p.TgZ(15,"div",12),p.TgZ(16,"div",13),p.TgZ(17,"span",14),p.NdJ("click",function(){return t.page=1,t.GetEmployeeLists()}),p._UZ(18,"i",15),p.qZA(),p.qZA(),p.TgZ(19,"input",16),p.NdJ("input",function(){return t.page=1,t.GetEmployeeLists()})("ngModelChange",function(e){return t.name=e}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(20,"table",17),p.TgZ(21,"thead"),p.TgZ(22,"tr"),p.TgZ(23,"th"),p._uU(24,"Name"),p.qZA(),p.TgZ(25,"th"),p._uU(26,"E-mail"),p.qZA(),p.TgZ(27,"th"),p._uU(28,"Role"),p.qZA(),p.TgZ(29,"th"),p._uU(30,"Location"),p.qZA(),p.YNc(31,Ie,2,0,"th",18),p.TgZ(32,"th"),p._uU(33,"Action"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(34,"tbody"),p.YNc(35,Qe,18,7,"tr",19),p.ALo(36,"paginate"),p.qZA(),p.qZA(),p.TgZ(37,"div"),p.TgZ(38,"pagination-controls",20),p.NdJ("pageChange",function(e){return t.page=e,t.GetEmployeeLists()}),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(39,"div",21,22),p.TgZ(41,"div",23),p.TgZ(42,"div",24),p.TgZ(43,"div",25),p.TgZ(44,"h4",26),p._uU(45),p.qZA(),p.qZA(),p.TgZ(46,"form",27),p.TgZ(47,"div",28),p.TgZ(48,"div",0),p.TgZ(49,"div",29),p.TgZ(50,"div",30),p.TgZ(51,"label",31),p._uU(52,"Name "),p.TgZ(53,"span",32),p._uU(54,"*"),p.qZA(),p.qZA(),p._UZ(55,"input",33),p.YNc(56,De,4,3,"div",34),p.qZA(),p.TgZ(57,"div",30),p.TgZ(58,"label",35),p._uU(59,"Email "),p.TgZ(60,"span",32),p._uU(61,"*"),p.qZA(),p.qZA(),p._UZ(62,"input",36),p.YNc(63,Re,3,2,"div",34),p.qZA(),p.YNc(64,He,2,0,"div",37),p.TgZ(65,"div",30),p.TgZ(66,"label",38),p._uU(67,"Role "),p.TgZ(68,"span",32),p._uU(69,"*"),p.qZA(),p.qZA(),p.TgZ(70,"select",39),p.NdJ("change",function(e){return t.RoleSetValue(e.target.value)}),p.TgZ(71,"option",40),p._uU(72,"--Select Role--"),p.qZA(),p.YNc(73,$e,2,2,"option",10),p.qZA(),p.YNc(74,Be,2,1,"div",34),p.qZA(),p.TgZ(75,"div",30),p.TgZ(76,"label",41),p._uU(77,"Location "),p.TgZ(78,"span",32),p._uU(79,"*"),p.qZA(),p.qZA(),p.TgZ(80,"select",42),p.TgZ(81,"option",40),p._uU(82,"--Select--"),p.qZA(),p.YNc(83,Ve,2,2,"option",10),p.qZA(),p.YNc(84,We,2,1,"div",34),p.qZA(),p.TgZ(85,"div",30),p.TgZ(86,"label",43),p._uU(87,"Phone Number"),p.qZA(),p.TgZ(88,"input",44),p.NdJ("ngModelChange",function(e){return t.phone_no=e}),p.qZA(),p.YNc(89,et,3,2,"div",34),p.qZA(),p.TgZ(90,"div",30),p.TgZ(91,"label",45),p._uU(92,"Address"),p.qZA(),p._UZ(93,"textarea",46),p.YNc(94,it,2,1,"div",34),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(95,"div",47),p.TgZ(96,"button",48),p.NdJ("click",function(){return p.CHM(i),p.MAs(40).hide(),t.clear()}),p._uU(97,"Cancel"),p.qZA(),p.TgZ(98,"button",49),p.NdJ("click",function(){return t.onSubmit()}),p._uU(99,"Save"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(100,"div",21,50),p.TgZ(102,"div",51),p.TgZ(103,"div",24),p.TgZ(104,"div",25),p.TgZ(105,"h4",52),p._uU(106,"Employee Activity"),p.qZA(),p.qZA(),p.TgZ(107,"form",27),p.TgZ(108,"div",28),p.TgZ(109,"div",0),p.TgZ(110,"div",29),p.TgZ(111,"div",30),p.TgZ(112,"table",17),p.TgZ(113,"thead",53),p.TgZ(114,"tr"),p.TgZ(115,"th"),p._uU(116,"Description"),p.qZA(),p.TgZ(117,"th"),p._uU(118,"Timestamp"),p.qZA(),p.qZA(),p.qZA(),p.TgZ(119,"tbody"),p.YNc(120,at,7,6,"tr",54),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(121,"div",47),p.TgZ(122,"button",55),p.NdJ("click",function(){return p.CHM(i),p.MAs(101).hide()}),p._uU(123,"Close"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(124,"div",56,57),p.TgZ(126,"div",23),p.TgZ(127,"div",24),p.TgZ(128,"h4",26),p._uU(129,"Admin User Created Successfully"),p.qZA(),p.TgZ(130,"p"),p._uU(131,"please, Check the mail and set new password"),p.qZA(),p.TgZ(132,"button",48),p.NdJ("click",function(){return p.CHM(i),p.MAs(125).hide(),t.clear()}),p._uU(133,"ok"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(134,"div",58,59),p.TgZ(136,"div",60),p.TgZ(137,"div",24),p.TgZ(138,"div",25),p.TgZ(139,"h4",26),p._uU(140,"Are you sure ?"),p.qZA(),p.qZA(),p.TgZ(141,"div",28),p.TgZ(142,"div",0),p.TgZ(143,"div",29),p.TgZ(144,"p"),p._uU(145,"Do you want to delete this User?"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.TgZ(146,"div",47),p.TgZ(147,"button",48),p.NdJ("click",function(){return p.CHM(i),p.MAs(135).hide()}),p._uU(148,"Cancel"),p.qZA(),p.TgZ(149,"button",61),p.NdJ("click",function(){return t.DeleteEmployee(t.EditId)}),p._uU(150,"Delete"),p.qZA(),p.qZA(),p.qZA(),p.qZA(),p.qZA()}2&e&&(p.xp6(8),p.Q6J("ngIf",t.Add),p.xp6(2),p.Q6J("ngModel",t.location),p.xp6(3),p.Q6J("ngForOf",t.locations),p.xp6(6),p.Q6J("ngModel",t.name),p.xp6(12),p.Q6J("ngIf",t.Edit),p.xp6(4),p.Q6J("ngForOf",p.xi3(36,32,t.employees,p.WLB(35,rt,t.page,t.count))),p.xp6(4),p.Q6J("config",p.DdM(38,dt)),p.xp6(6),p.Oqu(""==t.EditId?"Add Admin User":"Edit Admin User"),p.xp6(1),p.Q6J("formGroup",t.loginForm),p.xp6(9),p.Q6J("ngClass",p.VKq(39,lt,t.submitted&&t.f.name.errors)),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.name.errors),p.xp6(6),p.Q6J("ngClass",p.VKq(41,lt,t.submitted&&t.f.email.errors))("readonly",""!=t.EditId),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.email.errors),p.xp6(1),p.Q6J("ngIf",t.email),p.xp6(6),p.Q6J("ngClass",p.VKq(43,lt,t.submitted&&t.f.role_name.errors)),p.xp6(1),p.Q6J("hidden",""!=t.EditId),p.xp6(2),p.Q6J("ngForOf",t.roles),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.role_name.errors),p.xp6(6),p.Q6J("ngClass",p.VKq(45,lt,t.submitted&&t.f.location.errors)),p.xp6(1),p.Q6J("hidden",""!=t.EditId),p.xp6(2),p.Q6J("ngForOf",t.filteredLocations),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.location.errors),p.xp6(4),p.Q6J("ngModel",t.phone_no)("ngClass",p.VKq(47,lt,t.submitted&&t.f.phone_no.errors)),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.phone_no.errors),p.xp6(4),p.Q6J("ngClass",p.VKq(49,lt,t.submitted&&t.f.address.errors)),p.xp6(1),p.Q6J("ngIf",t.submitted&&t.f.address.errors),p.xp6(6),p.Q6J("config",p.DdM(51,dt)),p.xp6(7),p.Q6J("formGroup",t.loginForm),p.xp6(13),p.Q6J("ngForOf",t.viewlog),p.xp6(14),p.Q6J("config",p.DdM(52,dt)))},directives:[r.O5,u.EJ,u.JJ,u.On,u.YN,u.ks,r.sg,u.Fj,l.LS,d.oB,u.vK,u.JL,u.sg,u.u,r.mk,u.Wl],pipes:[l._s,r.uU],styles:["#select1[_ngcontent-%COMP%]{width:100%}.filter[_ngcontent-%COMP%]{width:127px;display:inline-block;margin-left:45%;margin-top:3px}"]}),t}(),st=a(8816),ct=a(57481),gt=[{path:"",data:{title:"Settings"},children:[{path:"role",component:C,data:{title:"Role",path:"/settings/role"},canActivate:[ct.P]},{path:"module",component:R,data:{title:"Module",path:"/settings"},canActivate:[ct.P]},{path:"animal-type",component:H.q,data:{title:"Species",path:"/settings/animal-type"},canActivate:[ct.P]},{path:"appointment-types",component:ue,data:{title:"Appointment Types",path:"/settings/appointment-types"},canActivate:[ct.P]},{path:"breed",component:st.g,data:{title:"Breed",path:"/settings/breed"},canActivate:[ct.P]},{path:"location",component:Me,data:{title:"Location",path:"/settings/location"}},{path:"covetrus",component:Ue,data:{title:"Covetrus",path:"/settings/covetrus"},canActivate:[ct.P]},{path:"employee",component:ut,data:{title:"Admin Users",path:"/settings/employee"},canActivate:[ct.P]}]}],Zt=function(){var e=function e(){i(this,e)};return e.\u0275mod=p.oAB({type:e}),e.\u0275inj=p.cJS({factory:function(t){return new(t||e)},imports:[[s.Bz.forChild(gt)],s.Bz]}),e}(),pt=function(){var e=function e(){i(this,e)};return e.\u0275mod=p.oAB({type:e}),e.\u0275inj=p.cJS({factory:function(t){return new(t||e)},imports:[[r.ez,Zt,d.zk.forRoot(),l.JX,u.u5,u.UX]]}),e}()}}])}();