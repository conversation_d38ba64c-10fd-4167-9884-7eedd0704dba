import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class AnimalTypeService extends Api {

    //Add New animal 
    Newtype(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/animal?token=${localStorage.auth_token}`, data);
    }

    //Get All Animal Type
    GetTypesList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/animal?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular animal by using animal id 
    GetTypeDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit animal details
    UpdateType(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete animal by using id
    Deletetype(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/animal/${id}?token=${localStorage.auth_token}`);
    }
}