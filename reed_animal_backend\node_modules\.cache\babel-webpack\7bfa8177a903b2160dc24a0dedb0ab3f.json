{"ast": null, "code": "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeFloor = Math.floor;\n/**\n * The base implementation of `_.repeat` which doesn't coerce arguments.\n *\n * @private\n * @param {string} string The string to repeat.\n * @param {number} n The number of times to repeat the string.\n * @returns {string} Returns the repeated string.\n */\n\nfunction baseRepeat(string, n) {\n  var result = '';\n\n  if (!string || n < 1 || n > MAX_SAFE_INTEGER) {\n    return result;\n  } // Leverage the exponentiation by squaring algorithm for a faster repeat.\n  // See https://en.wikipedia.org/wiki/Exponentiation_by_squaring for more details.\n\n\n  do {\n    if (n % 2) {\n      result += string;\n    }\n\n    n = nativeFloor(n / 2);\n\n    if (n) {\n      string += string;\n    }\n  } while (n);\n\n  return result;\n}\n\nexport default baseRepeat;", "map": null, "metadata": {}, "sourceType": "module"}