import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductService } from '../../services/product.services'
 
@Component({
  selector: 'app-shopping',
  templateUrl: './shopping.component.html',
  styleUrls: ['./shopping.component.scss']
})
export class ShoppingComponent implements OnInit {
  @ViewChild('removeModal') public removeModal: ModalDirective;
  @ViewChild('notesModal') public notesModal: ModalDirective;
 
  Add = true;
  Edit = true;
  Delete = true;
  Products = [];
  page = 1;
  count = 0;
  id = ''
  name = ''
  currentNote = ''
  currentProductId = ''
  currentProductName = ''
 
  constructor(private productService: ProductService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }
  ngOnInit(): void {
    this.tokens();
  }
  //token verified type
  tokens(): void {
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Shopping") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        } else {
          this.ListProduct();
        }
      })
  }
 
  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    return skip;
  }
 
  //List all Product  getallproduct
  ListProduct() {
    console.log("@@@",this.name)
    const skip = this.getrequestparams(this.page);
    this.productService.GetallProduct(this.name,skip)
      .subscribe((res: any) => {
        this.Products = res.data;
        console.log("list of proudct record",this.Products)
        this.count = res.count;
      })
  }
 
  //Page handle
  handlePageChange(event: number) {
    this.page = event;
    this.ListProduct();
  }
 
  //Get Product
  GetProductById(index, param) {
    if (param == "Delete") {
      this.id = this.Products[index]._id
      this.removeModal.show();
    }
  }
 
  //Delete single product by using id
  DeleteProduct(id) {
    this.productService.DeleteProduct(id)
      .subscribe((res: any) => {
        this.removeModal.hide();
        this.ListProduct();
      })
  }
 
  //Open notes modal for a product
  openNotes(product) {
    this.currentProductId = product._id;
    this.currentProductName = product.title;
    this.currentNote = product.notes || '';
    this.notesModal.show();
  }
 
  //Save note for a product
  saveNote() {
    const data = {
      notes: this.currentNote
    };
    this.productService.UpdateProduct(this.currentProductId, data)
      .subscribe((res: any) => {
        // Update the product in the local array
        const productIndex = this.Products.findIndex(p => p._id === this.currentProductId);
        if (productIndex !== -1) {
          this.Products[productIndex].notes = this.currentNote;
        }
        this.notesModal.hide();
      });
  }
 
  //Status changed by using id
  changed(event, id) {
    console.log("valuesssssss",event.target.checked,"iddddddd",id)
    const data = {
      status: event.target.checked
    }
    console.log("value",data)
    this.productService.UpdateProduct(id, data)
      .subscribe((res: any) => {
        // this.ListProduct();
      })
  }
 
  //Edit product and navigation to add product
  EditProduct(Id,user) {
    console.log("detail of users", user)
    this.router.navigate(['/pages/products'], { queryParams: { 'search': Id } });
  }
 
}