{"ast": null, "code": "import isFunction from './isFunction.js';\nimport isMasked from './_isMasked.js';\nimport isObject from './isObject.js';\nimport toSource from './_toSource.js';\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\n\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to detect host constructors (Safari). */\n\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used for built-in method references. */\n\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n/** Used to resolve the decompiled source of functions. */\n\nvar funcToString = funcProto.toString;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/** Used to detect if a method is native. */\n\nvar reIsNative = RegExp('^' + funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&').replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$');\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\n\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nexport default baseIsNative;", "map": null, "metadata": {}, "sourceType": "module"}