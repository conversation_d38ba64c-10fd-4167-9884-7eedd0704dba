import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class OrderService extends Api {

    //Get All Order
    GetOrderList(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/allorder?token=${localStorage.auth_token}`, { params });
    }

    //Update or Edit Pelfie
    UpdatePelfie(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl3}/v1/pelfie/${id}?token=${localStorage.auth_token}`, data);
    }

    //Get Single Order Details using Order ID
    OrderDetail(id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/order/${id}?token=${localStorage.auth_token}`);
    }

    //Update Order status by using order id
    UpdateOrderDetail(id: any, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/order/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete All orders
    DeleteAllOrders(): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/delete_all_orders?token=${localStorage.auth_token}`);
    }
}