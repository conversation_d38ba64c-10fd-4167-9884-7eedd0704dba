<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Admin Users
            </div>
            <div class="card-body">
                <div class="row">

                    <div class="col-lg-12 my-3">
                        <button *ngIf="Add" type="button" class="btn btn-primary mr-1" data-toggle="modal" (click)="EditId=''; RemoveEmpText();primaryModal.show()">
                        Add Admin User
                    </button>
                        <div class="filter">
                            <select class="form-control" [(ngModel)]="location" (change)="page=1;GetEmployeeLists()">
                <option value='' selected>--Location--</option>
                <option *ngFor="let item of locations" [value]="item.name">{{item.name}}</option>
              </select>
                        </div>
                        <div class="form-group table-search">
                            <div class="input-group" style="top: 3px;">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" (click)="page=1;GetEmployeeLists()"><i class="fa fa-search"></i></span>
                                </div>
                                <input type="text" id="Search" name="Search" placeholder="Search" class="form-control" autocomplete="off" class="form-control" (input)="page=1;GetEmployeeLists()" [(ngModel)]="name">
                            </div>
                        </div>
                    </div>
                </div>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>E-mail</th>
                            <th>Role</th>
                            <th>Location</th>
                            <th *ngIf="Edit">Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of employees| paginate: { id: 'listing_pagination',
               itemsPerPage: 10,
               currentPage: page,
               totalItems: count };let i =index;">

                            <td>{{user.name}}</td>
                            <td>{{user.email}}</td>
                            <td>{{user.role_name}}</td>
                            <td>{{user.location}}</td>
                            <td *ngIf="Edit"><label class="switch">
                  <input type="checkbox" checked="user.status" (change)="changed(user.status,user._id)"
                    [(ngModel)]="user.status">
                  <span class="slider round"></span>
                </label></td>
                            <td>
                                <a data-toggle="modal" *ngIf="Edit" (click)="EditId=user._id;Index=i;EditEmployee();primaryModal.show();" style="cursor: pointer; margin-right: 10px;"><span
                    class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                <a data-toggle="modal" *ngIf="Delete" (click)="EditId=user._id;removeModal.show();" style="cursor: pointer;"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                    Delete</span></a>&nbsp;

                                <a data-toggle="modal" (click)="EditId=user._id; ViewLog(EditId); primaryModal1.show()" style="cursor: pointer;">
                                    <span class="badge badge-secondary sucpad" style="padding: 7px; font-size: small; color: white; background-color: #2f23ef;"><i class="fa fa-list"></i>&nbsp; view log</span>
                                </a>

                            </td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="page = $event;GetEmployeeLists();">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>













<!-- Add Modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h4 class="modal-title">{{EditId==''?'Add Admin User':'Edit Admin User'}}</h4>
            </div>

            <form class="form" [formGroup]="loginForm" autocomplete="off">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label for="name">Name <span style="color: red;">*</span></label>
                                <input type="text" class="form-control" [ngClass]="{ 'is-invalid': submitted && f.name.errors }" placeholder="Name" formControlName="name" autocomplete="off" />
                                <div *ngIf="submitted && f.name.errors" class="invalid-feedback">
                                    <div *ngIf="f.name.errors.required">Name is mandatory</div>
                                    <div *ngIf="f.name.errors.pattern">Alphabet characters only</div>
                                    <div *ngIf="f.name.errors.minlength">Name isn't long enough, minimum of 3 characters</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email <span style="color: red;">*</span></label>
                                <input type="email" class="form-control" placeholder="e.g. <EMAIL>" formControlName="email" [ngClass]="{ 'is-invalid': submitted && f.email.errors }" autocomplete="off" [readonly]="EditId!=''" />
                                <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                                    <div *ngIf="f.email.errors.required">Email is mandatory</div>
                                    <div *ngIf="f.email.errors.email || f.email.errors.pattern">*Please enter a valid Email address</div>
                                </div>
                            </div>
                            <div *ngIf="email" style="font-size: smaller;color: #f86c6b;margin-top: -14px;
              margin-bottom: 12px;">*Email is already registered with us</div>

                            <div class="form-group">
                                <label for="select2">Role <span style="color: red;">*</span></label>
                                <select id="Role" name="select1" class="form-control" formControlName="role_name" [ngClass]="{ 'is-invalid': submitted && f.role_name.errors }" (change)="RoleSetValue($event.target.value)">
                  <option value='' selected [hidden]="EditId!=''">--Select Role--</option>
                  <option *ngFor="let role of roles;let i=index;" [value]="role.name">
                    {{role.name}}</option>
                </select>
                                <div *ngIf="submitted && f.role_name.errors" class="invalid-feedback">
                                    <div *ngIf="f.role_name.errors.required">Role is mandatory</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="location">Location <span style="color: red;">*</span></label>
                                <select id="location" name="location" [ngClass]="{ 'is-invalid': submitted && f.location.errors }" class="form-control" formControlName="location">
                  <option value='' selected [hidden]="EditId!=''">--Select--</option>
                  <option *ngFor="let item of filteredLocations" [value]="item.name"  >{{item.name}}</option>
                </select>
                                <div *ngIf="submitted && f.location.errors" class="invalid-feedback">
                                    <div *ngIf="f.location.errors.required">Location is mandatory</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="phone_no">Phone Number</label>
                                <input id="phone_no" type="text" [(ngModel)]="phone_no" [ngClass]="{ 'is-invalid': submitted && f.phone_no.errors }" autocomplete="off" class="form-control" placeholder="e.g. 9874563210" formControlName="phone_no" />
                                <div *ngIf="submitted && f.phone_no.errors" class="invalid-feedback">
                                    <div *ngIf="f.phone_no.errors.minlength">Phone number isn't long enough, minimum of 8 characters</div>
                                    <div *ngIf="f.phone_no.errors.pattern">Numberic characters only</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="Address">Address</label>
                                <textarea type="text" [ngClass]="{ 'is-invalid': submitted && f.address.errors }" autocomplete="off" class="form-control" placeholder="e.g. No.70,Mission street, Florida" formControlName="address"></textarea>
                                <div *ngIf="submitted && f.address.errors" class="invalid-feedback">
                                    <div *ngIf="f.address.errors.minlength">Address isn't long enough, minimum of 12 characters</div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
                    <button class="btn btn-primary" type="submit" (click)="onSubmit();">Save</button>
                </div>
            </form>

        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->







<!-- Add Modal -->
<div bsModal #primaryModal1="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" style="max-width: 60%;" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h4 class="modal-title" style="padding: 2%;">Employee Activity</h4>
            </div>

            <form class="form" [formGroup]="loginForm" autocomplete="off">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">


                            <div class="form-group">
                                <table class="table table-striped">
                                    <thead style="text-align: center;">
                                        <tr>
                                            <th>Description</th>
                                            <th>Timestamp</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of viewlog" style="text-align: center;">
                                            <td *ngIf="user.additional_info; else elseBlock">{{user.description}} to {{user.additional_info.status}}</td>
                                            <ng-template #elseBlock>
                                                <td>{{user.description}}</td>
                                            </ng-template>
                                            <td>{{ user.updatedAt | date :'short'}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>


                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" style="width: 15%; padding: 2px; margin-left: 83%;" (click)="primaryModal1.hide();">Close</button>
                </div>
            </form>

        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->













<div bsModal #okayModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <h4 class="modal-title">Admin User Created Successfully</h4>
            <p>please, Check the mail and set new password</p>
            <button type="button" class="btn btn-secondary" (click)="okayModal.hide();clear();">ok</button>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>








<!-- Delete Modal -->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this User?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteEmployee(EditId);">Delete</button>


            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->