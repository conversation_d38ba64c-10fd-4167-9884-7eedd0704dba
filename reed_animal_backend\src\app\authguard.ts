import { Injectable } from '@angular/core';
import { Router, CanActivate, ActivatedRouteSnapshot } from '@angular/router';
// import { AuthService } from './auth.service';
import { Employeeservice } from './views/services/employee.services';
import { PermissionService } from './views/services/permission.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuardService implements CanActivate {

  constructor(public router: Router, private EmployeeService: Employeeservice, private Permission: PermissionService) { }

  canActivate(arr): boolean {
    const Item = JSON.parse(localStorage.getItem('Verify'));
    this.verifyCustomer();
    const Verify = Item.includes(arr.data.path);
    if (Verify) {
      // logged in so return true
      return true;
    }
    // not logged in so redirect to login page
    this.router.navigate(['/login']);
    return false;
  }


  verifyCustomer(): void {
    const Item = JSON.parse(localStorage.getItem('auth-user'));
    this.EmployeeService.GetEmployeeDetail(Item._id)
      .subscribe((res) => {
        if (res.data.status == false) {
          localStorage.clear();
          this.router.navigate(['/login']);
        }
      })
  }

}