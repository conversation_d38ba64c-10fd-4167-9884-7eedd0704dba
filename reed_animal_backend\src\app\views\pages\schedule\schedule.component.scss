.confirm-color { background-color: green;
    padding:7px;
    color:#fff;
    font-weight: 700;
    border-radius: 0.25rem;
    font-size: 75%;
   }
   .available-color { background-color: red;
       padding:7px;
       color:#fff;
       font-weight: 700;
       border-radius: 0.25rem;
       font-size: 75%;
   }
   .schedule-delete .dropdown-menu {
    min-width: 6.5rem;
   }
   .schedule-delete .dropdown-item.active, .schedule-delete .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #f86c6b;
}
.schedule-delete .dropdown-item {
    padding: 4px 20px;
}
.Time-section,.Available-section {
border: 1px solid #c8ced3;
height: 300px;
overflow-y: auto;
}
.Time-section ul,.Available-section ul {padding-left: 0;}
.Time-section ul li {
    list-style: none;
    padding: 5px;
    border-bottom: 2px solid #568d2c;
    color: #fff;
    font-weight: bold;
    background: #3a4248;
    text-align: center;
}
.Time-section ul li.time-selected,.Time-section ul li:hover,.Time-section ul li:focus {
    background: #568d2c;
    border-bottom: 2px solid #3a4248;
}
.Available-section ul li.Available-selected,.Available-section ul li:hover,.Available-section ul li:focus {
    border-bottom: 2px solid #568d2c; 
    background: #3a4248; 
}

.Available-section ul li {
    list-style: none;
    padding: 5px;
    border-bottom: 2px solid #3a4248;
    color: #fff;
    font-weight: bold;
    background: #568d2c;
    text-align: center;
}

.arrow-right {
    width: 0; 
    height: 0; 
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    cursor: pointer;
    border-left: 25px solid #c1c1c1;
    margin: auto;

  }
  
  .arrow-left {
    width: 0;
    height: 0;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    border-right: 25px solid #c1c1c1;
    cursor: pointer;
    margin: auto;

  }
  .arrow-middle {
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 39%;
  }
  .weekDays-selector input {
    display: none;
  }
  .fromtimepicker {display: block  !important;}
  .weekDays-selector input[type=checkbox] + label {
    display: block;
    border-radius: 6px;
    background: #dddddd;
    height: 35px;
    width: 50px;
    margin-right: 3px;
    line-height: 35px;
  }


  
  .weekDays-selector input[type=checkbox]:checked + label {
    background: #568d2c;
    color: #ffffff;
  }
  .Delete-selection i,.add-time i,.weekly-days i {
  position: relative;
  top: 2px;
  position: relative;
  top: 8px;
  font-size: 18px;
}
.center-icon {
    vertical-align: bottom;
    position: relative;
    top: 4px;
}
.set-weekly {
    border-top: 1px solid #e5e5e5;
    margin-top: 30px;}
.date-overrides {
        border-bottom: 1px solid #e5e5e5;
        padding-bottom: 35px;}

 .add-overrides {padding: 25px;
 border-bottom: 1px solid #e5e5e5;  } 
 .add-overrides p span {float:right}  
 
 .override-popup .modal-footer {
  justify-content: center;
  border-top: none;
}
.override-popup .modal-footer .btn {
  width:100%;
} 

.Available-hours  {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 33px;
}

// ::ng-deep .bs-datepicker-body table td span.custom-selected-date,
// ::ng-deep .bs-datepicker-body table td span.custom-selected-date.selected {
//   background-color: red !important;
//   color: white !important;
// }

// ::ng-deep .bs-datepicker-body table td span.selected {
//   background-color: transparent !important;
//   color: #54708b !important;
// }

// ::ng-deep .bs-datepicker-body table.days span.in-range:before {
//   background-color: transparent !important;
// }



::ng-deep .bs-datepicker-body table td span.custom-selected-date,
::ng-deep .bs-datepicker-body table td span.custom-selected-date.selected {
  background-color: #568d2c !important;
  color: white !important;
}

::ng-deep .bs-datepicker-body table td span.selected {
  background-color: transparent !important;
  color: #54708b !important;
}

::ng-deep .bs-datepicker-body table.days span.in-range:before {
  background-color: transparent !important;
}
      
.timepicker tr:nth-child(2) td:last-child {
  display: none;
}

.weekly-days.dropdown-toggle::after {
  display: none;
  }
  .dropdown-menu.show {
    top:30px !important;
  }
  .weekDays-selector .dropdown-menu .dropdown-item input {
    display: inline-block !important;
    right: 12px;
  }
.all-day-week .form-check-label {
  display: inline-block;
}
  // ::ng-deep .bs-datepicker-body table td span.custom-selected-date,
  // ::ng-deep .bs-datepicker-body table td span.custom-selected-date.selected {
  //   background-color: red !important;
  //   color: white !important;
  // }
  .all-day-week .dropdown-menu > span {
    text-transform: uppercase;
    font-size: 11px;
    text-align: center;
    padding-left: 17px;
  padding-top: 15px;
  }
  .apply-btn {    color: #fff !important;
    width: 85%;
    margin: auto;
    text-align: center;
    display: block;
  margin-bottom: 15px;
  }


  // ::ng-deep .ng-star-inserted:hover,::ng-deep .ng-star-inserted:active,::ng-deep .ng-star-inserted:focus{
  //   color: red!important;
  // }

  // ::ng-deep .ng-tns-c102-1 .ng-star-inserted:active{
  //   color: red!important; 
  // }

/* Time Slot Vertical Layout */
.time-slot-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.time-input-stack {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.time-input {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  
  &:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.time-separator {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin: 2px 0;
}