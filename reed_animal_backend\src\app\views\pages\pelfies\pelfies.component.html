<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        Pelfies
      </div>

      <div class="card-body">
        <div class="row">
          <div class="col-sm-6 col-lg-6" (click)="Showing('today')">
            <div class="card text-white badge-success">
              <div class="card-body pb-3">
                <div class="text-value">Today upload's</div>
                <div>{{today.length}}</div>
              </div>
            </div>
          </div>
          <!--/.col-->
          <div class="col-sm-6 col-lg-6" (click)="Showing('total')">
            <div class="card text-white bg-primary">
              <div class="card-body pb-3">
                <div class="text-value">Total upload's</div>
                <div>{{total}}</div>
              </div>
            </div>
          </div>
          <!--/.col-->
          <!-- <div class="col-sm-6 col-lg-3" (click)="Showing('blocked')">
            <div class="card text-white bg-primary">
              <div class="card-body pb-3">
                <div class="text-value">{{total}}</div>
                <div>Total upload's</div>
              </div>
            </div>
          </div> -->
          <!--/.col-->
          <!-- <div class="col-sm-6 col-lg-3" (click)="Showing('unblocked')">
            <div class="card text-white bg-primary">
              <div class="card-body pb-3">
                <div class="text-value">{{total}}</div>
                <div>Total upload's</div>
              </div>
            </div>
          </div> -->
        </div>
        <div class="row">
          <div class="col-lg-12 my-3">
            <!-- <button type="button" class="btn btn-primary mr-1" data-toggle="modal" (click)="primaryModal.show()">
                   Add Customer
               </button> -->
            <div class="form-group table-search">
              <div class="input-group" style="top: 3px;">
                <div class="input-group-prepend">
                  <span class="input-group-text"><i class="fa fa-search"></i></span>
                </div>
                <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off"
                  class="form-control" (input)="GetPeflies()" [(ngModel)]="name" />
              </div>
            </div>

          </div>
        </div>

        <table class="table table-striped">
          <thead>
            <tr>
              <th>Pelfies</th>
              <th>User Name</th>
              <th>Pelfie Name</th>
              <th>Likes</th>
              <th>Upload Date</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of Pelfies| paginate: { id: 'listing_video',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };let i=index">
              <td><img [src]="user.image_url" width="65px" (click)="Image(i);" /></td>
              <td>{{user.user_name}}</td>
              <td>{{user.pelfie_name}}</td>
              <td>{{user.pelfie_likes.length}}</td>
              <td>{{user.createdAt|date:'dd MMM yyyy'}}</td>
              <td>
                <label class="switch">
                  <input type="checkbox" [checked]="user.status" (change)="Status(user._id,$event.target.checked)">
                  <span class="slider round"></span>
                </label>
              </td>
            </tr>
          </tbody>
        </table>
        <div>
          <pagination-controls id="listing_video" style="text-align:right;" maxSize="5" directionLinks="true"
            (pageChange)="handlePageChange($event)">
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>
  <!--/.col-->
</div>

<!-- Delete Modal-->
<div bsModal #PosterModal="bs-modal" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-danger modal-sm" role="document">
    <!-- <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Are you sure ?</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <p>Do you want to delete this Product?</p>
            
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="DeleteProduct(id)">Delete</button>
      </div>
    </div>/.modal-content -->
    <img [src]="image" width="100%" height="100%" />
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->