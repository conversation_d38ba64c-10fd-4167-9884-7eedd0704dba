"use strict";(self.webpackChunkDr_Reed_Admin_Panel=self.webpackChunkDr_Reed_Admin_Panel||[]).push([[959],{28959:function(e,t,i){i.r(t),i.d(t,{MasterModule:function(){return gt}});var o=i(63237),n=i(30386),a=i(45055),d=i(74875),r=i(99777),s=i(83711),l=i(6642),c=i(26415),g=i(90658),u=i(11192);const Z=["AddModal"],p=["primaryModal"],m=["deleteModal"];function h(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"button",38),g.NdJ("click",function(){return g.CHM(e),g.oxw(),g.<PERSON><PERSON>(28).show()}),g._uU(1," Add Role "),g.qZA()}}function f(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",40),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(48).show(),i.GetRole(t._id),i.onChange(t._id)}),g.TgZ(1,"span",41),g._UZ(2,"i",42),g._uU(3," Edit"),g.qZA(),g.qZA()}}function A(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",40),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(86).show(),i.GetRolee(t._id)}),g.TgZ(1,"span",43),g._UZ(2,"i",44),g._uU(3," Delete"),g.qZA(),g.qZA()}}function q(e,t){if(1&e&&(g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.TgZ(3,"td"),g.YNc(4,f,4,0,"a",39),g.YNc(5,A,4,0,"a",39),g.qZA(),g.qZA()),2&e){const e=t.$implicit,i=g.oxw();g.xp6(2),g.Oqu(e.name),g.xp6(2),g.Q6J("ngIf",i.Edit),g.xp6(1),g.Q6J("ngIf",i.Delete)}}function T(e,t){1&e&&(g.TgZ(0,"div",45),g._uU(1,"*Role is mandatory"),g.qZA())}function v(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.TgZ(3,"td"),g.TgZ(4,"label",46),g.TgZ(5,"input",47),g.NdJ("change",function(){g.CHM(e);const i=t.$implicit,o=t.index;return g.oxw().changed("add",i.add,i._id,o)})("ngModelChange",function(e){return t.$implicit.add=e}),g.qZA(),g._UZ(6,"span",48),g.qZA(),g.qZA(),g.TgZ(7,"td"),g.TgZ(8,"label",46),g.TgZ(9,"input",47),g.NdJ("change",function(){g.CHM(e);const i=t.$implicit,o=t.index;return g.oxw().changed("edit",i.edit,i._id,o)})("ngModelChange",function(e){return t.$implicit.edit=e}),g.qZA(),g._UZ(10,"span",48),g.qZA(),g.qZA(),g.TgZ(11,"td"),g.TgZ(12,"label",46),g.TgZ(13,"input",47),g.NdJ("change",function(){g.CHM(e);const i=t.$implicit,o=t.index;return g.oxw().changed("delete",i.delete,i._id,o)})("ngModelChange",function(e){return t.$implicit.delete=e}),g.qZA(),g._UZ(14,"span",48),g.qZA(),g.qZA(),g.TgZ(15,"td"),g.TgZ(16,"label",46),g.TgZ(17,"input",47),g.NdJ("change",function(){g.CHM(e);const i=t.$implicit,o=t.index;return g.oxw().changed("all",i.status,i._id,o)})("ngModelChange",function(e){return t.$implicit.status=e}),g.qZA(),g._UZ(18,"span",48),g.qZA(),g.qZA(),g.qZA()}if(2&e){const e=t.$implicit;g.xp6(2),g.Oqu(e.module_name),g.xp6(3),g.Q6J("ngModel",e.add),g.xp6(4),g.Q6J("ngModel",e.edit),g.xp6(4),g.Q6J("ngModel",e.delete),g.xp6(4),g.Q6J("ngModel",e.status)}}const b=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},M=function(){return{backdrop:"static",keyboard:!1}},_=function(){return{standalone:!0}},y=function(e,t){return{id:"listing_paginations",itemsPerPage:6,currentPage:e,totalItems:t}};let x=(()=>{class e{constructor(e,t,i,o,n,a){this.roleService=e,this.route=t,this.router=i,this.tokenStorage=o,this.Permission=n,this.EmployeeService=a,this.permissions=[],this.roles=[],this.page=1,this.count=0,this.search="",this.name="",this.role={},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.page1=1,this.count1=0}ngOnInit(){this.tokens()}clear(){this.role={},this.AddModal.hide()}tokens(){this.tokenStorage.getToken();const e=this.tokenStorage.getUser();this.Permission.GetModule(e.role_id._id).subscribe(e=>{for(var t=0;t<e.data.length;t++)"Role"==e.data[t].module_name&&(this.Add=e.data[t].add,this.Edit=e.data[t].edit,this.Delete=e.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(e._id).subscribe(e=>{0==e.data.status&&this.tokenStorage.signOut()}),this.GetRoleLists()}getrequestparams(e){let t={};return t.skip=10*(e-1),t}getrequestparams1(e){let t={};return t.skip=6*(e-1),t}GetRoleLists(){const e=this.getrequestparams(this.page);this.roleService.GetRoleList(e,this.name).subscribe(e=>{this.count=e.count-1;for(var t=[],i=0;i<e.data.length;i++)"Super Admin"!==e.data[i].name&&t.push(e.data[i]);this.roles=t})}handlePageChange(e){this.page=e,this.GetRoleLists()}GetRole(e){this.Id=e,this.page1=1,this.count1=0,this.roleService.GetRoleDetail(e).subscribe(e=>{this.role=e.data[0]})}onChange(e){const t=this.getrequestparams1(this.page1);this.Permission.GetRoleDetails(e,t).subscribe(e=>{this.permissions=e.data,this.count1=e.count})}handlePageChange1(e){this.page1=e,this.onChange(this.Id)}EditRole(e){this.roleService.UpdateRole(e,{name:this.role.name}).subscribe(e=>{this.role={},this.GetRoleLists()})}changeing(e,t){this.roleService.UpdateRole(t,{acc_activation:e}).subscribe(e=>{})}AddRole(){null!=this.role.name&&""!=this.role.name&&this.roleService.NewRole({name:this.role.name}).subscribe(e=>{this.role={},this.rolefailed=!1,this.AddModal.hide(),this.GetRoleLists()}),this.rolefailed=!0}Deleterole(){this.roleService.DeleteRole(this.Id).subscribe(e=>{this.GetRoleLists()})}GetRolee(e){this.Id=e}changed(e,t,i,o){const n={[e]:t};if("all"==e){const e={status:t,delete:t,edit:t,add:t};this.permissions[o].delete=t,this.permissions[o].edit=t,this.permissions[o].add=t,this.Permission.UpdatePermission(i,e).subscribe(e=>{})}this.Permission.UpdatePermission(i,n).subscribe(e=>{})}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(s.N),g.Y36(r.gz),g.Y36(r.F0),g.Y36(u.i),g.Y36(l.$),g.Y36(c.d))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-role"]],viewQuery:function(e,t){if(1&e&&(g.Gf(Z,1),g.Gf(p,1),g.Gf(m,1)),2&e){let e;g.iGM(e=g.CRH())&&(t.AddModal=e.first),g.iGM(e=g.CRH())&&(t.primaryModal=e.first),g.iGM(e=g.CRH())&&(t.deleteModal=e.first)}},decls:102,vars:29,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","role-name","placeholder","e.g. Manager, Doctor","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["type","text","id","edit-name","placeholder","e.g. Manager, Doctor","autocomplete","off","required","","readonly","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["id","listing_paginations","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["deleteModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"],[1,"switch"],["type","checkbox",3,"ngModel","change","ngModelChange"],[1,"slider","round"]],template:function(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Role "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.YNc(8,h,2,0,"button",6),g.TgZ(9,"div",7),g.TgZ(10,"div",8),g.TgZ(11,"div",9),g.TgZ(12,"span",10),g.TgZ(13,"i",11),g.NdJ("click",function(){return t.GetRoleLists()}),g.qZA(),g.qZA(),g.qZA(),g.TgZ(14,"input",12),g.NdJ("input",function(){return t.GetRoleLists()})("ngModelChange",function(e){return t.name=e}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(15,"table",13),g.TgZ(16,"thead"),g.TgZ(17,"tr"),g.TgZ(18,"th"),g._uU(19,"Role Name"),g.qZA(),g.TgZ(20,"th"),g._uU(21,"Action"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(22,"tbody"),g.YNc(23,q,6,3,"tr",14),g.ALo(24,"paginate"),g.qZA(),g.qZA(),g.TgZ(25,"div"),g.TgZ(26,"pagination-controls",15),g.NdJ("pageChange",function(e){return t.handlePageChange(e)}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(27,"div",16,17),g.TgZ(29,"div",18),g.TgZ(30,"div",19),g.TgZ(31,"div",20),g.TgZ(32,"h4",21),g._uU(33,"Add Role"),g.qZA(),g.qZA(),g.TgZ(34,"div",22),g.TgZ(35,"div",0),g.TgZ(36,"div",23),g.TgZ(37,"div",24),g.TgZ(38,"label",25),g._uU(39,"Role Name*"),g.qZA(),g.TgZ(40,"input",26),g.NdJ("ngModelChange",function(e){return t.role.name=e})("keydown.enter",function(){return t.AddRole()}),g.qZA(),g.YNc(41,T,2,0,"div",27),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(42,"div",28),g.TgZ(43,"button",29),g.NdJ("click",function(){return g.CHM(e),g.MAs(28).hide(),t.clear()}),g._uU(44,"Cancel"),g.qZA(),g.TgZ(45,"button",30),g.NdJ("click",function(){return t.AddRole()}),g._uU(46,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(47,"div",16,31),g.TgZ(49,"div",18),g.TgZ(50,"div",19),g.TgZ(51,"div",20),g.TgZ(52,"h4",21),g._uU(53,"Edit Role"),g.qZA(),g.qZA(),g.TgZ(54,"div",22),g.TgZ(55,"div",0),g.TgZ(56,"div",23),g.TgZ(57,"div",24),g.TgZ(58,"label",25),g._uU(59,"Role Name*"),g.qZA(),g.TgZ(60,"input",32),g.NdJ("ngModelChange",function(e){return t.role.name=e})("keydown.enter",function(){g.CHM(e);const i=g.MAs(48);return t.EditRole(t.role._id),i.hide()}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(61,"div",4),g.TgZ(62,"table",13),g.TgZ(63,"thead"),g.TgZ(64,"tr"),g.TgZ(65,"th"),g._uU(66,"Module Name"),g.qZA(),g.TgZ(67,"th"),g._uU(68,"Add"),g.qZA(),g.TgZ(69,"th"),g._uU(70,"Edit"),g.qZA(),g.TgZ(71,"th"),g._uU(72,"Delete"),g.qZA(),g.TgZ(73,"th"),g._uU(74,"View"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(75,"tbody"),g.YNc(76,v,19,5,"tr",14),g.ALo(77,"paginate"),g.qZA(),g.qZA(),g.TgZ(78,"div"),g.TgZ(79,"pagination-controls",33),g.NdJ("pageChange",function(e){return t.handlePageChange1(e)}),g.qZA(),g.qZA(),g.qZA(),g.TgZ(80,"div",28),g.TgZ(81,"button",29),g.NdJ("click",function(){return g.CHM(e),g.MAs(48).hide(),t.clear()}),g._uU(82,"Cancel"),g.qZA(),g.TgZ(83,"button",30),g.NdJ("click",function(){g.CHM(e);const i=g.MAs(48);return t.EditRole(t.role._id),i.hide()}),g._uU(84,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(85,"div",34,35),g.TgZ(87,"div",36),g.TgZ(88,"div",19),g.TgZ(89,"div",20),g.TgZ(90,"h4",21),g._uU(91,"Are you sure ?"),g.qZA(),g.qZA(),g.TgZ(92,"div",22),g.TgZ(93,"div",0),g.TgZ(94,"div",23),g.TgZ(95,"p"),g._uU(96,"Do you want to delete this Role?"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(97,"div",28),g.TgZ(98,"button",29),g.NdJ("click",function(){return g.CHM(e),g.MAs(86).hide()}),g._uU(99,"Cancel"),g.qZA(),g.TgZ(100,"button",37),g.NdJ("click",function(){g.CHM(e);const i=g.MAs(86);return t.Deleterole(),i.hide()}),g._uU(101,"Delete"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()}2&e&&(g.xp6(8),g.Q6J("ngIf",t.Add),g.xp6(6),g.Q6J("ngModel",t.name),g.xp6(9),g.Q6J("ngForOf",g.xi3(24,12,t.roles,g.WLB(18,b,t.page,t.count))),g.xp6(4),g.Q6J("config",g.DdM(21,M)),g.xp6(13),g.Q6J("ngModel",t.role.name)("ngModelOptions",g.DdM(22,_)),g.xp6(1),g.Q6J("ngIf",t.rolefailed),g.xp6(6),g.Q6J("config",g.DdM(23,M)),g.xp6(13),g.Q6J("ngModel",t.role.name)("ngModelOptions",g.DdM(24,_)),g.xp6(16),g.Q6J("ngForOf",g.xi3(77,15,t.permissions,g.WLB(25,y,t.page1,t.count1))),g.xp6(9),g.Q6J("config",g.DdM(28,M)))},directives:[o.O5,d.Fj,d.JJ,d.On,o.sg,a.LS,n.oB,d.Q7,d.Wl],pipes:[a._s],styles:[""]}),e})();var U=i(49533);const J=["AddModal"],C=["primaryModal"],k=["deleteModal"];function N(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"button",40),g.NdJ("click",function(){return g.CHM(e),g.oxw(),g.MAs(29).show()}),g._uU(1," Add Module "),g.qZA()}}function w(e,t){1&e&&(g.TgZ(0,"th"),g._uU(1,"Status"),g.qZA())}function E(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"td"),g.TgZ(1,"label",42),g.TgZ(2,"input",43),g.NdJ("change",function(){g.CHM(e);const t=g.oxw().$implicit;return g.oxw().changed(t.acc_activation,t._id)})("ngModelChange",function(t){return g.CHM(e),g.oxw().$implicit.acc_activation=t}),g.qZA(),g._UZ(3,"span",44),g.qZA(),g.qZA()}if(2&e){const e=g.oxw().$implicit;g.xp6(2),g.Q6J("ngModel",e.acc_activation)}}function I(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",45),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(49).show(),i.GetModule(t._id)}),g.TgZ(1,"span",46),g._UZ(2,"i",47),g._uU(3," Edit"),g.qZA(),g.qZA()}}function L(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",45),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(68).show(),i.GetModulee(t._id)}),g.TgZ(1,"span",48),g._UZ(2,"i",49),g._uU(3," Delete"),g.qZA(),g.qZA()}}function S(e,t){if(1&e&&(g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.YNc(3,E,4,1,"td",14),g.TgZ(4,"td"),g.YNc(5,I,4,0,"a",41),g.YNc(6,L,4,0,"a",41),g.qZA(),g.qZA()),2&e){const e=t.$implicit,i=g.oxw();g.xp6(2),g.Oqu(e.name),g.xp6(1),g.Q6J("ngIf",i.Edit),g.xp6(2),g.Q6J("ngIf",i.Edit),g.xp6(1),g.Q6J("ngIf",i.Delete)}}function Q(e,t){1&e&&(g.TgZ(0,"div",50),g._uU(1,"*please enter Module"),g.qZA())}const G=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},F=function(){return{backdrop:"static",keyboard:!1}},Y=function(){return{standalone:!0}};let D=(()=>{class e{constructor(e,t,i,o,n,a){this.moduleService=e,this.route=t,this.router=i,this.tokenStorage=o,this.Permission=n,this.EmployeeService=a,this.modules=[],this.page=1,this.count=0,this.search="",this.name="",this.module={id:"",name:"",acc_activation:""},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}ngOnInit(){this.tokens()}show(){this.AddModal.show(),this.primaryModal.show()}hide(){this.AddModal.hide(),this.primaryModal.hide()}clear(){this.module={}}tokens(){const e=this.tokenStorage.getToken(),t=this.tokenStorage.getUser();null!=e?(this.Permission.GetModule(t.role_id._id).subscribe(e=>{for(var t=0;t<e.data.length;t++)"Module"==e.data[t].module_name&&(this.Add=e.data[t].add,this.Edit=e.data[t].edit,this.Delete=e.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(t._id).subscribe(e=>{0==e.data.status&&this.tokenStorage.signOut()}),this.GetModuleLists()):this.router.navigate(["/login"])}getrequestparams(e){let t={};return t.skip=10*(e-1),t}GetModuleLists(){const e=this.getrequestparams(this.page);this.moduleService.GetModuleList(e,this.name).subscribe(e=>{this.modules=e.data,this.count=e.count})}handlePageChange(e){this.page=e,this.GetModuleLists()}GetModule(e){this.moduleService.GetModuleDetail(e).subscribe(e=>{this.module=e.data[0]})}EditModule(e){this.moduleService.UpdateModule(e,{name:this.module.name}).subscribe(e=>{this.module={},this.GetModuleLists()})}changed(e,t){this.moduleService.UpdateModule(t,{acc_activation:e}).subscribe(e=>{})}AddModule(){null!=this.module.name&&""!=this.module.name&&(this.AddModal.hide(),this.moduleService.NewModule({name:this.module.name}).subscribe(e=>{this.module={},this.rolefailed=!1,this.GetModuleLists()})),this.rolefailed=!0}DeleteModule(){this.moduleService.DeleteModule(this.Id).subscribe(e=>{this.GetModuleLists()})}GetModulee(e){this.Id=e}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(U.C),g.Y36(r.gz),g.Y36(r.F0),g.Y36(u.i),g.Y36(l.$),g.Y36(c.d))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-module"]],viewQuery:function(e,t){if(1&e&&(g.Gf(J,1),g.Gf(C,1),g.Gf(k,1)),2&e){let e;g.iGM(e=g.CRH())&&(t.AddModal=e.first),g.iGM(e=g.CRH())&&(t.primaryModal=e.first),g.iGM(e=g.CRH())&&(t.deleteModal=e.first)}},decls:84,vars:23,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["rodule","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","module-name","placeholder","Enter your Module Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],["type","text","id","edit-name","placeholder","Enter your Module Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["deleteModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.acc_activation",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"]],template:function(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Module "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.YNc(8,N,2,0,"button",6),g.TgZ(9,"div",7),g.TgZ(10,"div",8),g.TgZ(11,"div",9),g.TgZ(12,"span",10),g.TgZ(13,"i",11),g.NdJ("click",function(){return t.GetModuleLists()}),g.qZA(),g.qZA(),g.qZA(),g.TgZ(14,"input",12),g.NdJ("input",function(){return t.GetModuleLists()})("ngModelChange",function(e){return t.name=e}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(15,"table",13),g.TgZ(16,"thead"),g.TgZ(17,"tr"),g.TgZ(18,"th"),g._uU(19,"Module Name"),g.qZA(),g.YNc(20,w,2,0,"th",14),g.TgZ(21,"th"),g._uU(22,"Action"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(23,"tbody"),g.YNc(24,S,7,4,"tr",15),g.ALo(25,"paginate"),g.qZA(),g.qZA(),g.TgZ(26,"div"),g.TgZ(27,"pagination-controls",16),g.NdJ("pageChange",function(e){return t.handlePageChange(e)}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(28,"div",17,18),g.TgZ(30,"div",19),g.TgZ(31,"div",20),g.TgZ(32,"div",21),g.TgZ(33,"h4",22),g._uU(34,"Add Module"),g.qZA(),g.qZA(),g.TgZ(35,"div",23),g.TgZ(36,"div",0),g.TgZ(37,"div",24),g.TgZ(38,"div",25),g.TgZ(39,"label",26),g._uU(40,"Module Name"),g.qZA(),g.TgZ(41,"input",27),g.NdJ("ngModelChange",function(e){return t.module.name=e})("keydown.enter",function(){return t.AddModule()}),g.qZA(),g.YNc(42,Q,2,0,"div",28),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(43,"div",29),g.TgZ(44,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(29).hide(),t.clear()}),g._uU(45,"Cancel"),g.qZA(),g.TgZ(46,"button",31),g.NdJ("click",function(){return t.AddModule()}),g._uU(47,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(48,"div",32,33),g.TgZ(50,"div",34),g.TgZ(51,"div",20),g.TgZ(52,"div",21),g.TgZ(53,"h4",22),g._uU(54,"Edit Module"),g.qZA(),g.qZA(),g.TgZ(55,"div",23),g.TgZ(56,"div",0),g.TgZ(57,"div",24),g.TgZ(58,"div",25),g.TgZ(59,"label",26),g._uU(60,"Module Name"),g.qZA(),g.TgZ(61,"input",35),g.NdJ("ngModelChange",function(e){return t.module.name=e})("keydown.enter",function(){g.CHM(e);const i=g.MAs(49);return t.EditModule(t.module.id),i.hide()}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(62,"div",29),g.TgZ(63,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(49).hide(),t.clear()}),g._uU(64,"Cancel"),g.qZA(),g.TgZ(65,"button",31),g.NdJ("click",function(){g.CHM(e);const i=g.MAs(49);return t.EditModule(t.module.id),i.hide()}),g._uU(66,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(67,"div",36,37),g.TgZ(69,"div",38),g.TgZ(70,"div",20),g.TgZ(71,"div",21),g.TgZ(72,"h4",22),g._uU(73,"Are you sure ?"),g.qZA(),g.qZA(),g.TgZ(74,"div",23),g.TgZ(75,"div",0),g.TgZ(76,"div",24),g.TgZ(77,"p"),g._uU(78,"Do you want to delete this Module?"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(79,"div",29),g.TgZ(80,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(68).hide()}),g._uU(81,"Cancel"),g.qZA(),g.TgZ(82,"button",39),g.NdJ("click",function(){g.CHM(e);const i=g.MAs(68);return t.DeleteModule(),i.hide()}),g._uU(83,"Delete"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()}2&e&&(g.xp6(8),g.Q6J("ngIf",t.Add),g.xp6(6),g.Q6J("ngModel",t.name),g.xp6(6),g.Q6J("ngIf",t.Edit),g.xp6(4),g.Q6J("ngForOf",g.xi3(25,12,t.modules,g.WLB(15,G,t.page,t.count))),g.xp6(4),g.Q6J("config",g.DdM(18,F)),g.xp6(13),g.Q6J("ngModel",t.module.name)("ngModelOptions",g.DdM(19,Y)),g.xp6(1),g.Q6J("ngIf",t.rolefailed),g.xp6(6),g.Q6J("config",g.DdM(20,F)),g.xp6(13),g.Q6J("ngModel",t.module.name)("ngModelOptions",g.DdM(21,Y)),g.xp6(6),g.Q6J("config",g.DdM(22,F)))},directives:[o.O5,d.Fj,d.JJ,d.On,o.sg,a.LS,n.oB,d.Q7,d.Wl],pipes:[a._s],styles:[""]}),e})();var O=i(19148),P=i(21771);const R=["primaryModal"],H=["AddModal"],$=["removeModal"];function z(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"button",38),g.NdJ("click",function(){return g.CHM(e),g.oxw(),g.MAs(29).show()}),g._uU(1," Add Type "),g.qZA()}}function B(e,t){1&e&&(g.TgZ(0,"th"),g._uU(1,"Status"),g.qZA())}function V(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"td"),g.TgZ(1,"label",40),g.TgZ(2,"input",41),g.NdJ("change",function(){g.CHM(e);const t=g.oxw().$implicit;return g.oxw().changed(t.status,t._id)})("ngModelChange",function(t){return g.CHM(e),g.oxw().$implicit.status=t}),g.qZA(),g._UZ(3,"span",42),g.qZA(),g.qZA()}if(2&e){const e=g.oxw().$implicit;g.xp6(2),g.Q6J("ngModel",e.status)}}function W(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",43),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(50).show(),i.GetTreatment(t._id)}),g.TgZ(1,"span",44),g._UZ(2,"i",45),g._uU(3," Edit"),g.qZA(),g.qZA()}}function j(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",43),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(71).show(),i.GetTreatment(t._id)}),g.TgZ(1,"span",46),g._UZ(2,"i",47),g._uU(3," Delete"),g.qZA(),g.qZA()}}function K(e,t){if(1&e&&(g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.YNc(3,V,4,1,"td",14),g.TgZ(4,"td"),g.YNc(5,W,4,0,"a",39),g.YNc(6,j,4,0,"a",39),g.qZA(),g.qZA()),2&e){const e=t.$implicit,i=g.oxw();g.xp6(2),g.Oqu(e.name),g.xp6(1),g.Q6J("ngIf",i.Edit),g.xp6(2),g.Q6J("ngIf",i.Edit),g.xp6(1),g.Q6J("ngIf",i.Delete)}}function X(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Type is mandatory"),g.qZA())}function ee(e,t){if(1&e&&(g.TgZ(0,"div",48),g.YNc(1,X,2,0,"div",14),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.name.errors.required)}}function te(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Type is mandatory"),g.qZA())}function ie(e,t){if(1&e&&(g.TgZ(0,"div",48),g.YNc(1,te,2,0,"div",14),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.name.errors.required)}}const oe=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},ne=function(){return{backdrop:"static",keyboard:!1}},ae=function(e){return{"is-invalid":e}};let de=(()=>{class e{constructor(e,t,i,o,n,a,d){this.formBuilder=e,this.TreatmentService=t,this.route=i,this.router=o,this.tokenStorage=n,this.Permission=a,this.EmployeeService=d,this.isFormReady=!1,this.submitted=!1,this.treatments=[],this.page=1,this.count=0,this.search="",this.name="",this.treatment={},this.nameFailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}ngOnInit(){this.tokens(),this.SignForm()}clear(){this.treatment={},this.nameFailed=!1,this.isFormReady=!1,this.submitted=!1,this.loginForm.reset()}getfocus(){this.nameFailed=!1}tokens(){this.tokenStorage.getToken();const e=this.tokenStorage.getUser();this.Permission.GetModule(e.role_id._id).subscribe(e=>{for(var t=0;t<e.data.length;t++)"Treatments"==e.data[t].module_name&&(this.Add=e.data[t].add,this.Edit=e.data[t].edit,this.Delete=e.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(e._id).subscribe(e=>{0==e.data.status&&this.tokenStorage.signOut()}),this.treatmentLists()}getrequestparams(e){let t={};return t.skip=10*(e-1),t}treatmentLists(){const e=this.getrequestparams(this.page);this.TreatmentService.GetTreatmentsList(e,this.name).subscribe(e=>{this.treatments=e.data,this.count=e.count})}handlePageChange(e){this.page=e,this.treatmentLists()}GetTreatment(e){this.TreatmentService.GetTreatmentDetail(e).subscribe(e=>{this.treatment=e.data[0],this.f.name.setValue(e.data[0].name,{onlySelf:!0})})}EditTreatment(e){this.submitted=!0,this.loginForm.invalid||this.TreatmentService.UpdateTreatment(e,{name:this.loginForm.value.name}).subscribe(e=>{this.primaryModal.hide(),this.clear(),this.treatmentLists()})}changed(e,t){this.TreatmentService.UpdateTreatment(t,{status:e}).subscribe(e=>{})}SignForm(){this.loginForm=this.formBuilder.group({name:["",[d.kI.required]]})}get f(){return this.loginForm.controls}AddTreatment(){if(this.submitted=!0,!this.loginForm.invalid){const e={name:this.loginForm.value.name};this.AddModal.hide(),this.TreatmentService.NewTreatment(e).subscribe(e=>{this.clear(),this.treatmentLists()})}}DeleteTreatment(e){this.TreatmentService.DeleteTreatment(e).subscribe(e=>{this.removeModal.hide(),this.treatmentLists()})}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(d.qu),g.Y36(P.J),g.Y36(r.gz),g.Y36(r.F0),g.Y36(u.i),g.Y36(l.$),g.Y36(c.d))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-treatments"]],viewQuery:function(e,t){if(1&e&&(g.Gf(R,1),g.Gf(H,1),g.Gf($,1)),2&e){let e;g.iGM(e=g.CRH())&&(t.primaryModal=e.first),g.iGM(e=g.CRH())&&(t.AddModal=e.first),g.iGM(e=g.CRH())&&(t.removeModal=e.first)}},decls:87,vars:26,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","name"],["type","text","placeholder","e.g. Injury, UTI, Vaccine","formControlName","name",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"]],template:function(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Appointment Types "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.YNc(8,z,2,0,"button",6),g.TgZ(9,"div",7),g.TgZ(10,"div",8),g.TgZ(11,"div",9),g.TgZ(12,"span",10),g.NdJ("click",function(){return t.treatmentLists()}),g._UZ(13,"i",11),g.qZA(),g.qZA(),g.TgZ(14,"input",12),g.NdJ("input",function(){return t.treatmentLists()})("ngModelChange",function(e){return t.name=e}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(15,"table",13),g.TgZ(16,"thead"),g.TgZ(17,"tr"),g.TgZ(18,"th"),g._uU(19,"Type"),g.qZA(),g.YNc(20,B,2,0,"th",14),g.TgZ(21,"th"),g._uU(22,"Action"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(23,"tbody"),g.YNc(24,K,7,4,"tr",15),g.ALo(25,"paginate"),g.qZA(),g.qZA(),g.TgZ(26,"div"),g.TgZ(27,"pagination-controls",16),g.NdJ("pageChange",function(e){return t.handlePageChange(e)}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(28,"div",17,18),g.TgZ(30,"div",19),g.TgZ(31,"div",20),g.TgZ(32,"div",21),g.TgZ(33,"h4",22),g._uU(34,"Add Type"),g.qZA(),g.qZA(),g.TgZ(35,"div",23),g.TgZ(36,"div",0),g.TgZ(37,"div",24),g.TgZ(38,"form",25),g.TgZ(39,"div",26),g.TgZ(40,"label",27),g._uU(41,"Type*"),g.qZA(),g._UZ(42,"input",28),g.YNc(43,ee,2,1,"div",29),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(44,"div",30),g.TgZ(45,"button",31),g.NdJ("click",function(){return g.CHM(e),g.MAs(29).hide(),t.clear()}),g._uU(46,"Cancel"),g.qZA(),g.TgZ(47,"button",32),g.NdJ("click",function(){return t.AddTreatment()}),g._uU(48,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(49,"div",17,33),g.TgZ(51,"div",19),g.TgZ(52,"div",20),g.TgZ(53,"div",21),g.TgZ(54,"h4",22),g._uU(55,"Edit Type"),g.qZA(),g.qZA(),g.TgZ(56,"div",23),g.TgZ(57,"div",0),g.TgZ(58,"div",24),g.TgZ(59,"form",25),g.TgZ(60,"div",26),g.TgZ(61,"label",27),g._uU(62,"Type*"),g.qZA(),g._UZ(63,"input",28),g.YNc(64,ie,2,1,"div",29),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(65,"div",30),g.TgZ(66,"button",31),g.NdJ("click",function(){return g.CHM(e),g.MAs(50).hide(),t.clear()}),g._uU(67,"Cancel"),g.qZA(),g.TgZ(68,"button",32),g.NdJ("click",function(){return t.EditTreatment(t.treatment._id)}),g._uU(69,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(70,"div",34,35),g.TgZ(72,"div",36),g.TgZ(73,"div",20),g.TgZ(74,"div",21),g.TgZ(75,"h4",22),g._uU(76,"Are you sure ?"),g.qZA(),g.qZA(),g.TgZ(77,"div",23),g.TgZ(78,"div",0),g.TgZ(79,"div",24),g.TgZ(80,"p"),g._uU(81,"Do you want to delete this Appointment?"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(82,"div",30),g.TgZ(83,"button",31),g.NdJ("click",function(){return g.CHM(e),g.MAs(71).hide(),t.clear()}),g._uU(84,"Cancel"),g.qZA(),g.TgZ(85,"button",37),g.NdJ("click",function(){return t.DeleteTreatment(t.treatment._id)}),g._uU(86,"Delete"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()}2&e&&(g.xp6(8),g.Q6J("ngIf",t.Add),g.xp6(6),g.Q6J("ngModel",t.name),g.xp6(6),g.Q6J("ngIf",t.Edit),g.xp6(4),g.Q6J("ngForOf",g.xi3(25,13,t.treatments,g.WLB(16,oe,t.page,t.count))),g.xp6(4),g.Q6J("config",g.DdM(19,ne)),g.xp6(10),g.Q6J("formGroup",t.loginForm),g.xp6(4),g.Q6J("ngClass",g.VKq(20,ae,t.submitted&&t.f.name.errors)),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.name.errors),g.xp6(6),g.Q6J("config",g.DdM(22,ne)),g.xp6(10),g.Q6J("formGroup",t.loginForm),g.xp6(4),g.Q6J("ngClass",g.VKq(23,ae,t.submitted&&t.f.name.errors)),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.name.errors),g.xp6(6),g.Q6J("config",g.DdM(25,ne)))},directives:[o.O5,d.Fj,d.JJ,d.On,o.sg,a.LS,n.oB,d.vK,d.JL,d.sg,d.u,o.mk,d.Wl],pipes:[a._s],styles:[""]}),e})();var re=i(87188);const se=["primaryModal"],le=["AddModal"],ce=["removeModal"];function ge(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"button",38),g.NdJ("click",function(){return g.CHM(e),g.oxw(),g.MAs(29).show()}),g._uU(1," Add Location "),g.qZA()}}function ue(e,t){1&e&&(g.TgZ(0,"th"),g._uU(1,"Status"),g.qZA())}function Ze(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"td"),g.TgZ(1,"label",40),g.TgZ(2,"input",41),g.NdJ("change",function(){g.CHM(e);const t=g.oxw().$implicit;return g.oxw().changed(t.status,t._id)})("ngModelChange",function(t){return g.CHM(e),g.oxw().$implicit.status=t}),g.qZA(),g._UZ(3,"span",42),g.qZA(),g.qZA()}if(2&e){const e=g.oxw().$implicit;g.xp6(2),g.Q6J("ngModel",e.status)}}function pe(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",43),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(49).show(),i.Getlocation(t._id)}),g.TgZ(1,"span",44),g._UZ(2,"i",45),g._uU(3," Edit"),g.qZA(),g.qZA()}}function me(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",43),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw();return g.MAs(68).show(),i.Getlocation(t._id)}),g.TgZ(1,"span",46),g._UZ(2,"i",47),g._uU(3," Delete"),g.qZA(),g.qZA()}}function he(e,t){if(1&e&&(g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.YNc(3,Ze,4,1,"td",14),g.TgZ(4,"td"),g.YNc(5,pe,4,0,"a",39),g.YNc(6,me,4,0,"a",39),g.qZA(),g.qZA()),2&e){const e=t.$implicit,i=g.oxw();g.xp6(2),g.Oqu(e.name),g.xp6(1),g.Q6J("ngIf",i.Edit),g.xp6(2),g.Q6J("ngIf",i.Edit),g.xp6(1),g.Q6J("ngIf",i.Delete)}}function fe(e,t){1&e&&(g.TgZ(0,"div",48),g._uU(1,"*please enter location"),g.qZA())}const Ae=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},qe=function(){return{backdrop:"static",keyboard:!1}},Te=function(){return{standalone:!0}};let ve=(()=>{class e{constructor(e,t,i,o,n,a){this.locationservice=e,this.route=t,this.router=i,this.tokenStorage=o,this.Permission=n,this.EmployeeService=a,this.locations=[],this.page=1,this.count=0,this.search="",this.name="",this.location={},this.rolefailed=!1,this.Add=!0,this.Edit=!0,this.Delete=!0}ngOnInit(){this.tokens()}show(){this.AddModal.show(),this.primaryModal.show(),this.removeModal.show()}hide(){this.AddModal.hide(),this.primaryModal.hide(),this.removeModal.hide()}clear(){this.location={},this.rolefailed=!1}tokens(){const e=this.tokenStorage.getToken(),t=this.tokenStorage.getUser();null!=e?(this.Permission.GetModule(t.role_id._id).subscribe(e=>{for(var t=0;t<e.data.length;t++)"Location"==e.data[t].module_name&&(this.Add=e.data[t].add,this.Edit=e.data[t].edit,this.Delete=e.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(t._id).subscribe(e=>{0==e.data[0].status&&this.tokenStorage.signOut()}),this.locationLists()):this.router.navigate(["/login"])}getfocus(){this.rolefailed=!1}getrequestparams(e){let t={};return t.skip=10*(e-1),t}locationLists(){const e=this.getrequestparams(this.page);this.locationservice.GetLocationsList(e,this.name).subscribe(e=>{this.locations=e.data,this.count=e.count})}handlePageChange(e){this.page=e,this.locationLists()}Getlocation(e){this.locationservice.GetLocationDetail(e).subscribe(e=>{this.location=e.data[0]})}Editlocation(e){this.locationservice.UpdateLocation(e,{name:this.location.name}).subscribe(e=>{this.location={},this.locationLists()})}changed(e,t){this.locationservice.UpdateLocation(t,{status:e}).subscribe(e=>{})}Addlocation(){null!=this.location.name&&""!=this.location.name&&(this.AddModal.hide(),this.locationservice.NewLocation({name:this.location.name}).subscribe(e=>{this.location={},this.locationLists()})),this.rolefailed=!0}Deletelocation(e){this.locationservice.DeleteLocation(e).subscribe(e=>{this.removeModal.hide(),this.locationLists()})}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(re.a),g.Y36(r.gz),g.Y36(r.F0),g.Y36(u.i),g.Y36(l.$),g.Y36(c.d))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-location"]],viewQuery:function(e,t){if(1&e&&(g.Gf(se,1),g.Gf(le,1),g.Gf(ce,1)),2&e){let e;g.iGM(e=g.CRH())&&(t.primaryModal=e.first),g.iGM(e=g.CRH())&&(t.AddModal=e.first),g.iGM(e=g.CRH())&&(t.removeModal=e.first)}},decls:84,vars:23,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],["type","text","id","Type-name","placeholder","Enter Location Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter","click"],["style","font-size: smaller;color: red;",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["type","text","id","edit-name","placeholder","Location Name","autocomplete","off","required","",1,"form-control",3,"ngModel","ngModelOptions","ngModelChange","keydown.enter"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[2,"font-size","smaller","color","red"]],template:function(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Location "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.YNc(8,ge,2,0,"button",6),g.TgZ(9,"div",7),g.TgZ(10,"div",8),g.TgZ(11,"div",9),g.TgZ(12,"span",10),g.NdJ("click",function(){return t.locationLists()}),g._UZ(13,"i",11),g.qZA(),g.qZA(),g.TgZ(14,"input",12),g.NdJ("input",function(){return t.locationLists()})("ngModelChange",function(e){return t.name=e}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(15,"table",13),g.TgZ(16,"thead"),g.TgZ(17,"tr"),g.TgZ(18,"th"),g._uU(19,"Location Name"),g.qZA(),g.YNc(20,ue,2,0,"th",14),g.TgZ(21,"th"),g._uU(22,"Action"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(23,"tbody"),g.YNc(24,he,7,4,"tr",15),g.ALo(25,"paginate"),g.qZA(),g.qZA(),g.TgZ(26,"div"),g.TgZ(27,"pagination-controls",16),g.NdJ("pageChange",function(e){return t.handlePageChange(e)}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(28,"div",17,18),g.TgZ(30,"div",19),g.TgZ(31,"div",20),g.TgZ(32,"div",21),g.TgZ(33,"h4",22),g._uU(34,"Add Location"),g.qZA(),g.qZA(),g.TgZ(35,"div",23),g.TgZ(36,"div",0),g.TgZ(37,"div",24),g.TgZ(38,"div",25),g.TgZ(39,"label",26),g._uU(40,"Location Name"),g.qZA(),g.TgZ(41,"input",27),g.NdJ("ngModelChange",function(e){return t.location.name=e})("keydown.enter",function(){return t.Addlocation()})("click",function(){return t.getfocus()}),g.qZA(),g.YNc(42,fe,2,0,"div",28),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(43,"div",29),g.TgZ(44,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(29).hide(),t.clear()}),g._uU(45,"Cancel"),g.qZA(),g.TgZ(46,"button",31),g.NdJ("click",function(){return t.Addlocation()}),g._uU(47,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(48,"div",17,32),g.TgZ(50,"div",19),g.TgZ(51,"div",20),g.TgZ(52,"div",21),g.TgZ(53,"h4",22),g._uU(54,"Edit Location"),g.qZA(),g.qZA(),g.TgZ(55,"div",23),g.TgZ(56,"div",0),g.TgZ(57,"div",24),g.TgZ(58,"div",25),g.TgZ(59,"label",26),g._uU(60,"Location Name"),g.qZA(),g.TgZ(61,"input",33),g.NdJ("ngModelChange",function(e){return t.location.name=e})("keydown.enter",function(){g.CHM(e);const i=g.MAs(49);return t.Editlocation(t.location._id),i.hide()}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(62,"div",29),g.TgZ(63,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(49).hide(),t.clear()}),g._uU(64,"Cancel"),g.qZA(),g.TgZ(65,"button",31),g.NdJ("click",function(){g.CHM(e);const i=g.MAs(49);return t.Editlocation(t.location._id),i.hide()}),g._uU(66,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(67,"div",34,35),g.TgZ(69,"div",36),g.TgZ(70,"div",20),g.TgZ(71,"div",21),g.TgZ(72,"h4",22),g._uU(73,"Are you sure ?"),g.qZA(),g.qZA(),g.TgZ(74,"div",23),g.TgZ(75,"div",0),g.TgZ(76,"div",24),g.TgZ(77,"p"),g._uU(78,"Do you want to delete this Location?"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(79,"div",29),g.TgZ(80,"button",30),g.NdJ("click",function(){return g.CHM(e),g.MAs(68).hide(),t.clear()}),g._uU(81,"Cancel"),g.qZA(),g.TgZ(82,"button",37),g.NdJ("click",function(){return t.Deletelocation(t.location._id)}),g._uU(83,"Delete"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()}2&e&&(g.xp6(8),g.Q6J("ngIf",t.Add),g.xp6(6),g.Q6J("ngModel",t.name),g.xp6(6),g.Q6J("ngIf",t.Edit),g.xp6(4),g.Q6J("ngForOf",g.xi3(25,12,t.locations,g.WLB(15,Ae,t.page,t.count))),g.xp6(4),g.Q6J("config",g.DdM(18,qe)),g.xp6(13),g.Q6J("ngModel",t.location.name)("ngModelOptions",g.DdM(19,Te)),g.xp6(1),g.Q6J("ngIf",t.rolefailed),g.xp6(6),g.Q6J("config",g.DdM(20,qe)),g.xp6(13),g.Q6J("ngModel",t.location.name)("ngModelOptions",g.DdM(21,Te)),g.xp6(6),g.Q6J("config",g.DdM(22,qe)))},directives:[o.O5,d.Fj,d.JJ,d.On,o.sg,a.LS,n.oB,d.Q7,d.Wl],pipes:[a._s],styles:[""]}),e})();var be=i(72945);function Me(e,t){if(1&e&&(g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.TgZ(3,"td"),g._uU(4),g.qZA(),g.TgZ(5,"td"),g._uU(6),g.qZA(),g.TgZ(7,"td"),g._uU(8),g.qZA(),g.TgZ(9,"td"),g._uU(10),g.qZA(),g.TgZ(11,"td"),g._uU(12),g.qZA(),g.TgZ(13,"td"),g._uU(14),g.qZA(),g.TgZ(15,"td"),g._uU(16),g.qZA(),g.TgZ(17,"td"),g._uU(18),g.qZA(),g.TgZ(19,"td"),g._uU(20),g.qZA(),g.qZA()),2&e){const e=t.$implicit;g.xp6(2),g.Oqu(e.DBID),g.xp6(2),g.Oqu(e.Id),g.xp6(2),g.Oqu(e.Code),g.xp6(2),g.Oqu(e.CodeCategory),g.xp6(2),g.Oqu(e.CodeCategoryDescription),g.xp6(2),g.Oqu(e.CodeDescription),g.xp6(2),g.Oqu(e.CodeType),g.xp6(2),g.Oqu(e.MinimumPrice),g.xp6(2),g.Oqu(e.MaximumPrice),g.xp6(2),g.Oqu("false"===e.Inactive?"Active":"Inactive")}}const _e=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}};let ye=(()=>{class e{constructor(e){this.covertusService=e,this.page=1,this.search="",this.filter="",this.status="",this.CovertusList=[],this.count=0}ngOnInit(){this.GetCovertus()}GetCovertus(){this.covertusService.GetCovertusList({skip:10*(this.page-1),limit:10,search:this.search,filter:this.filter,status:this.status}).subscribe(e=>{console.log("covertusList--\x3e",e),this.CovertusList=e.data,this.count=e.count})}UpdateCovertus(){this.covertusService.UpdateCovertus({}).subscribe(e=>{console.log("covertusList--\x3e",e),this.GetCovertus()})}handlePageChange(e){this.page=e,this.GetCovertus()}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(be.x))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-covertus"]],decls:74,vars:8,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col",2,"margin-bottom","12px"],["type","button","data-toggle","modal",1,"btn","btn-primary",3,"click"],[1,"col"],["id","select1","name","select1",1,"form-control",2,"width","100%",3,"change"],["value",""],["value","false"],["value","true"],["value","Diagnostic"],["value","Inventory"],["value","Service"],["value","Payment"],["value","Problem"],[1,"col","input-group"],[1,"input-group-prepend"],[1,"input-group-text",2,"height","35px"],[1,"fa","fa-search",3,"click"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"]],template:function(e,t){1&e&&(g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Covertus "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.TgZ(8,"button",6),g.NdJ("click",function(){return t.UpdateCovertus()}),g._uU(9," Update List "),g.qZA(),g.qZA(),g._UZ(10,"div",7),g.TgZ(11,"div",7),g.TgZ(12,"select",8),g.NdJ("change",function(e){return t.status=e.target.value,t.GetCovertus()}),g.TgZ(13,"option",9),g._uU(14,"--Status--"),g.qZA(),g.TgZ(15,"option",10),g._uU(16,"Active"),g.qZA(),g.TgZ(17,"option",11),g._uU(18,"Inactive"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(19,"div",7),g.TgZ(20,"select",8),g.NdJ("change",function(e){return t.filter=e.target.value,t.GetCovertus()}),g.TgZ(21,"option",9),g._uU(22,"--Code Type--"),g.qZA(),g.TgZ(23,"option",12),g._uU(24,"Diagnostic"),g.qZA(),g.TgZ(25,"option",13),g._uU(26,"Inventory"),g.qZA(),g.TgZ(27,"option",14),g._uU(28,"Service"),g.qZA(),g.TgZ(29,"option",15),g._uU(30,"Payment"),g.qZA(),g.TgZ(31,"option",16),g._uU(32,"Problem"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(33,"div",17),g.TgZ(34,"div",18),g.TgZ(35,"span",19),g.TgZ(36,"i",20),g.NdJ("click",function(){return t.page=1,t.GetCovertus()}),g.qZA(),g.qZA(),g.qZA(),g.TgZ(37,"input",21),g.NdJ("input",function(){return t.page=1,t.GetCovertus()})("ngModelChange",function(e){return t.search=e}),g.qZA(),g.qZA(),g.qZA(),g.TgZ(38,"table",22),g.TgZ(39,"thead"),g.TgZ(40,"tr"),g.TgZ(41,"th"),g._uU(42,"DBID"),g.qZA(),g.TgZ(43,"th"),g._uU(44,"ID"),g.qZA(),g.TgZ(45,"th"),g._uU(46,"Code"),g.qZA(),g.TgZ(47,"th"),g._uU(48,"Code "),g._UZ(49,"br"),g._uU(50,"Category"),g.qZA(),g.TgZ(51,"th"),g._uU(52,"Code Category "),g._UZ(53,"br"),g._uU(54,"Description"),g.qZA(),g.TgZ(55,"th"),g._uU(56,"Code Description"),g.qZA(),g.TgZ(57,"th"),g._uU(58,"Code Type"),g.qZA(),g.TgZ(59,"th"),g._uU(60,"Minimum "),g._UZ(61,"br"),g._uU(62,"Price"),g.qZA(),g.TgZ(63,"th"),g._uU(64,"Maximum "),g._UZ(65,"br"),g._uU(66,"Price"),g.qZA(),g.TgZ(67,"th"),g._uU(68,"Status"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(69,"tbody"),g.YNc(70,Me,21,10,"tr",23),g.ALo(71,"paginate"),g.qZA(),g.qZA(),g.TgZ(72,"div"),g.TgZ(73,"pagination-controls",24),g.NdJ("pageChange",function(e){return t.handlePageChange(e)}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()),2&e&&(g.xp6(37),g.Q6J("ngModel",t.search),g.xp6(33),g.Q6J("ngForOf",g.xi3(71,2,t.CovertusList,g.WLB(5,_e,t.page,t.count))))},directives:[d.YN,d.ks,d.Fj,d.JJ,d.On,o.sg,a.LS],pipes:[a._s],styles:[""]}),e})();const xe=["primaryModal"],Ue=["removeModal"];function Je(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"button",62),g.NdJ("click",function(){g.CHM(e);const t=g.oxw(),i=g.MAs(40);return t.EditId="",t.RemoveEmpText(),i.show()}),g._uU(1," Add Admin User "),g.qZA()}}function Ce(e,t){if(1&e&&(g.TgZ(0,"option",63),g._uU(1),g.qZA()),2&e){const e=t.$implicit;g.Q6J("value",e.name),g.xp6(1),g.Oqu(e.name)}}function ke(e,t){1&e&&(g.TgZ(0,"th"),g._uU(1,"Status"),g.qZA())}function Ne(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"td"),g.TgZ(1,"label",69),g.TgZ(2,"input",70),g.NdJ("change",function(){g.CHM(e);const t=g.oxw().$implicit;return g.oxw().changed(t.status,t._id)})("ngModelChange",function(t){return g.CHM(e),g.oxw().$implicit.status=t}),g.qZA(),g._UZ(3,"span",71),g.qZA(),g.qZA()}if(2&e){const e=g.oxw().$implicit;g.xp6(2),g.Q6J("ngModel",e.status)}}function we(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",72),g.NdJ("click",function(){g.CHM(e);const t=g.oxw(),i=t.$implicit,o=t.index,n=g.oxw(),a=g.MAs(40);return n.EditId=i._id,n.Index=o,n.EditEmployee(),a.show()}),g.TgZ(1,"span",73),g._UZ(2,"i",74),g._uU(3," Edit"),g.qZA(),g.qZA()}}function Ee(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"a",66),g.NdJ("click",function(){g.CHM(e);const t=g.oxw().$implicit,i=g.oxw(),o=g.MAs(135);return i.EditId=t._id,o.show()}),g.TgZ(1,"span",75),g._UZ(2,"i",76),g._uU(3," Delete"),g.qZA(),g.qZA()}}function Ie(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"tr"),g.TgZ(1,"td"),g._uU(2),g.qZA(),g.TgZ(3,"td"),g._uU(4),g.qZA(),g.TgZ(5,"td"),g._uU(6),g.qZA(),g.TgZ(7,"td"),g._uU(8),g.qZA(),g.YNc(9,Ne,4,1,"td",18),g.TgZ(10,"td"),g.YNc(11,we,4,0,"a",64),g.YNc(12,Ee,4,0,"a",65),g._uU(13,"\xa0 "),g.TgZ(14,"a",66),g.NdJ("click",function(){g.CHM(e);const i=t.$implicit,o=g.oxw(),n=g.MAs(101);return o.EditId=i._id,o.ViewLog(o.EditId),n.show()}),g.TgZ(15,"span",67),g._UZ(16,"i",68),g._uU(17,"\xa0 view log"),g.qZA(),g.qZA(),g.qZA(),g.qZA()}if(2&e){const e=t.$implicit,i=g.oxw();g.xp6(2),g.Oqu(e.name),g.xp6(2),g.Oqu(e.email),g.xp6(2),g.Oqu(e.role_name),g.xp6(2),g.Oqu(e.location),g.xp6(1),g.Q6J("ngIf",i.Edit),g.xp6(2),g.Q6J("ngIf",i.Edit),g.xp6(1),g.Q6J("ngIf",i.Delete)}}function Le(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Name is mandatory"),g.qZA())}function Se(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Alphabet characters only"),g.qZA())}function Qe(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Name isn't long enough, minimum of 3 characters"),g.qZA())}function Ge(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,Le,2,0,"div",18),g.YNc(2,Se,2,0,"div",18),g.YNc(3,Qe,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.name.errors.required),g.xp6(1),g.Q6J("ngIf",e.f.name.errors.pattern),g.xp6(1),g.Q6J("ngIf",e.f.name.errors.minlength)}}function Fe(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Email is mandatory"),g.qZA())}function Ye(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"*Please enter a valid Email address"),g.qZA())}function De(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,Fe,2,0,"div",18),g.YNc(2,Ye,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.email.errors.required),g.xp6(1),g.Q6J("ngIf",e.f.email.errors.email||e.f.email.errors.pattern)}}function Oe(e,t){1&e&&(g.TgZ(0,"div",78),g._uU(1,"*Email is already registered with us"),g.qZA())}function Pe(e,t){if(1&e&&(g.TgZ(0,"option",63),g._uU(1),g.qZA()),2&e){const e=t.$implicit;g.Q6J("value",e.name),g.xp6(1),g.hij(" ",e.name,"")}}function Re(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Role is mandatory"),g.qZA())}function He(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,Re,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.role_name.errors.required)}}function $e(e,t){if(1&e&&(g.TgZ(0,"option",63),g._uU(1),g.qZA()),2&e){const e=t.$implicit;g.Q6J("value",e.name),g.xp6(1),g.Oqu(e.name)}}function ze(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Location is mandatory"),g.qZA())}function Be(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,ze,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.location.errors.required)}}function Ve(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Phone number isn't long enough, minimum of 8 characters"),g.qZA())}function We(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Numberic characters only"),g.qZA())}function je(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,Ve,2,0,"div",18),g.YNc(2,We,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.phone_no.errors.minlength),g.xp6(1),g.Q6J("ngIf",e.f.phone_no.errors.pattern)}}function Ke(e,t){1&e&&(g.TgZ(0,"div"),g._uU(1,"Address isn't long enough, minimum of 12 characters"),g.qZA())}function Xe(e,t){if(1&e&&(g.TgZ(0,"div",77),g.YNc(1,Ke,2,0,"div",18),g.qZA()),2&e){const e=g.oxw();g.xp6(1),g.Q6J("ngIf",e.f.address.errors.minlength)}}function et(e,t){if(1&e&&(g.TgZ(0,"td"),g._uU(1),g.qZA()),2&e){const e=g.oxw().$implicit;g.xp6(1),g.AsE("",e.description," to ",e.additional_info.status,"")}}function tt(e,t){if(1&e&&(g.TgZ(0,"td"),g._uU(1),g.qZA()),2&e){const e=g.oxw().$implicit;g.xp6(1),g.Oqu(e.description)}}function it(e,t){if(1&e&&(g.TgZ(0,"tr",53),g.YNc(1,et,2,2,"td",79),g.YNc(2,tt,2,1,"ng-template",null,80,g.W1O),g.TgZ(4,"td"),g._uU(5),g.ALo(6,"date"),g.qZA(),g.qZA()),2&e){const e=t.$implicit,i=g.MAs(3);g.xp6(1),g.Q6J("ngIf",e.additional_info)("ngIfElse",i),g.xp6(4),g.Oqu(g.xi3(6,3,e.updatedAt,"short"))}}const ot=function(e,t){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:t}},nt=function(){return{backdrop:"static",keyboard:!1}},at=function(e){return{"is-invalid":e}};let dt=(()=>{class e{constructor(e,t,i,o,n,a,d,r){this.employeeService=e,this.route=t,this.router=i,this.tokenStorage=o,this.EmployeeService=n,this.formBuilder=a,this.Permission=d,this.LocationService=r,this.employees=[],this.viewlog=[],this.roles=[],this.locations=[],this.page=1,this.count=0,this.search="",this.name="",this.email=!1,this.phone_no="",this.submitted=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.location="",this.EditId="",this.Index=0,this.GetRoleLists(),this.GetLocation(),this.GetEmployeeLists()}ngOnInit(){this.SignForm();const e=this.tokenStorage.getUser();this.Permission.GetModule(e.role_id._id).subscribe(e=>{for(var t=0;t<e.data.length;t++)"Admin-Users"==e.data[t].module_name&&(this.Add=e.data[t].add,this.Edit=e.data[t].edit,this.Delete=e.data[t].delete)})}clear(){this.email=!1,this.loginForm.reset(),this.submitted=!1}GetRoleLists(){this.employeeService.GetRoleList().subscribe(e=>{this.roles=e.data})}RemoveEmpText(){console.log("phone test"),this.phone_no=""}GetLocation(){this.LocationService.GetLocationsList("","").subscribe(e=>{this.locations=e.data,this.locations.filter(e=>!0===e.status),this.filteredLocations=this.locations.filter(e=>!0===e.status),console.log("testing locationnnnnnnnn",this.filteredLocations)});for(let e of this.locations)console.log("forloop",e[0])}GetEmployeeLists(){this.employeeService.GetEmployeeList({skip:10*(this.page-1),search:this.name,location:this.location}).subscribe(e=>{console.log("res",e),this.count=e.count,this.employees=e.data})}handlePageChange(e){this.page=e,this.GetEmployeeLists()}SignForm(){this.loginForm=this.formBuilder.group({name:["",[d.kI.required,d.kI.minLength(3),d.kI.pattern("[a-zA-Z. -_]*$")]],role_id:[""],role_name:["",[d.kI.required]],email:["",[d.kI.required,d.kI.email,d.kI.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]],location:["",[d.kI.required]],address:["",[d.kI.minLength(12)]],phone_no:[[d.kI.minLength(8),d.kI.pattern("[0-9-]*$")]]})}get f(){return this.loginForm.controls}onSubmit(){console.log(this.loginForm.value),this.submitted=!0,!this.loginForm.invalid&&(""==this.EditId?this.employeeService.NewEmployee(this.loginForm.value).subscribe(e=>{200==e.code?(this.submitted=!1,this.loginForm.reset(),this.primaryModal.hide(),this.GetRoleLists()):this.email=!0}):this.UpdateEmployee(this.EditId,this.loginForm.value))}UpdateEmployee(e,t){this.employeeService.EditEmployeeDetail(e,t).subscribe(e=>{200==e.code&&(this.submitted=!1,this.loginForm.reset(),this.primaryModal.hide(),""!=this.EditId&&this.GetEmployeeLists())})}ViewLog(e){console.log("Admin id",e),this.employeeService.GetViewLog(e).subscribe(e=>{this.viewlog=e.data,console.log(this.viewlog)})}EditEmployee(){this.loginForm.controls.name.setValue(this.employees[this.Index].name),this.loginForm.controls.role_id.setValue(this.employees[this.Index].role_id),this.loginForm.controls.role_name.setValue(this.employees[this.Index].role_name),this.loginForm.controls.email.setValue(this.employees[this.Index].email),this.loginForm.controls.location.setValue(this.employees[this.Index].location),this.loginForm.controls.address.setValue(this.employees[this.Index].address),this.loginForm.controls.phone_no.setValue(this.employees[this.Index].phone_no)}DeleteEmployee(e){this.employeeService.DeleteEmployee(e).subscribe(e=>{this.removeModal.hide(),this.GetEmployeeLists()})}changed(e,t){this.UpdateEmployee(t,{status:e})}RoleSetValue(e){this.roles.forEach(t=>{t.name===e&&this.loginForm.controls.role_id.setValue(t._id)})}}return e.\u0275fac=function(t){return new(t||e)(g.Y36(c.d),g.Y36(r.gz),g.Y36(r.F0),g.Y36(u.i),g.Y36(c.d),g.Y36(d.qu),g.Y36(l.$),g.Y36(re.a))},e.\u0275cmp=g.Xpm({type:e,selectors:[["app-employee"]],viewQuery:function(e,t){if(1&e&&(g.Gf(xe,1),g.Gf(Ue,1)),2&e){let e;g.iGM(e=g.CRH())&&(t.primaryModal=e.first),g.iGM(e=g.CRH())&&(t.removeModal=e.first)}},decls:151,vars:53,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"filter"],[1,"form-control",3,"ngModel","ngModelChange","change"],["value","","selected",""],[3,"value",4,"ngFor","ngForOf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["autocomplete","off",1,"form",3,"formGroup"],[1,"modal-body"],[1,"col-sm-12"],[1,"form-group"],["for","name"],[2,"color","red"],["type","text","placeholder","Name","formControlName","name","autocomplete","off",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],["for","email"],["type","email","placeholder","e.g. <EMAIL>","formControlName","email","autocomplete","off",1,"form-control",3,"ngClass","readonly"],["style","font-size: smaller;color: #f86c6b;margin-top: -14px;\n              margin-bottom: 12px;",4,"ngIf"],["for","select2"],["id","Role","name","select1","formControlName","role_name",1,"form-control",3,"ngClass","change"],["value","","selected","",3,"hidden"],["for","location"],["id","location","name","location","formControlName","location",1,"form-control",3,"ngClass"],["for","phone_no"],["id","phone_no","type","text","autocomplete","off","placeholder","e.g. 9874563210","formControlName","phone_no",1,"form-control",3,"ngModel","ngClass","ngModelChange"],["for","Address"],["type","text","autocomplete","off","placeholder","e.g. No.70,Mission street, Florida","formControlName","address",1,"form-control",3,"ngClass"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","submit",1,"btn","btn-primary",3,"click"],["primaryModal1","bs-modal"],["role","document",1,"modal-dialog","modal-primary",2,"max-width","60%"],[1,"modal-title",2,"padding","2%"],[2,"text-align","center"],["style","text-align: center;",4,"ngFor","ngForOf"],["type","button",1,"btn","btn-primary",2,"width","15%","padding","2px","margin-left","83%",3,"click"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade"],["okayModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],[3,"value"],["data-toggle","modal","style","cursor: pointer; margin-right: 10px;",3,"click",4,"ngIf"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-secondary","sucpad",2,"padding","7px","font-size","small","color","white","background-color","#2f23ef"],[1,"fa","fa-list"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer","margin-right","10px",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"],[2,"font-size","smaller","color","#f86c6b","margin-top","-14px","margin-bottom","12px"],[4,"ngIf","ngIfElse"],["elseBlock",""]],template:function(e,t){if(1&e){const e=g.EpF();g.TgZ(0,"div",0),g.TgZ(1,"div",1),g.TgZ(2,"div",2),g.TgZ(3,"div",3),g._uU(4," Admin Users "),g.qZA(),g.TgZ(5,"div",4),g.TgZ(6,"div",0),g.TgZ(7,"div",5),g.YNc(8,Je,2,0,"button",6),g.TgZ(9,"div",7),g.TgZ(10,"select",8),g.NdJ("ngModelChange",function(e){return t.location=e})("change",function(){return t.page=1,t.GetEmployeeLists()}),g.TgZ(11,"option",9),g._uU(12,"--Location--"),g.qZA(),g.YNc(13,Ce,2,2,"option",10),g.qZA(),g.qZA(),g.TgZ(14,"div",11),g.TgZ(15,"div",12),g.TgZ(16,"div",13),g.TgZ(17,"span",14),g.NdJ("click",function(){return t.page=1,t.GetEmployeeLists()}),g._UZ(18,"i",15),g.qZA(),g.qZA(),g.TgZ(19,"input",16),g.NdJ("input",function(){return t.page=1,t.GetEmployeeLists()})("ngModelChange",function(e){return t.name=e}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(20,"table",17),g.TgZ(21,"thead"),g.TgZ(22,"tr"),g.TgZ(23,"th"),g._uU(24,"Name"),g.qZA(),g.TgZ(25,"th"),g._uU(26,"E-mail"),g.qZA(),g.TgZ(27,"th"),g._uU(28,"Role"),g.qZA(),g.TgZ(29,"th"),g._uU(30,"Location"),g.qZA(),g.YNc(31,ke,2,0,"th",18),g.TgZ(32,"th"),g._uU(33,"Action"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(34,"tbody"),g.YNc(35,Ie,18,7,"tr",19),g.ALo(36,"paginate"),g.qZA(),g.qZA(),g.TgZ(37,"div"),g.TgZ(38,"pagination-controls",20),g.NdJ("pageChange",function(e){return t.page=e,t.GetEmployeeLists()}),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(39,"div",21,22),g.TgZ(41,"div",23),g.TgZ(42,"div",24),g.TgZ(43,"div",25),g.TgZ(44,"h4",26),g._uU(45),g.qZA(),g.qZA(),g.TgZ(46,"form",27),g.TgZ(47,"div",28),g.TgZ(48,"div",0),g.TgZ(49,"div",29),g.TgZ(50,"div",30),g.TgZ(51,"label",31),g._uU(52,"Name "),g.TgZ(53,"span",32),g._uU(54,"*"),g.qZA(),g.qZA(),g._UZ(55,"input",33),g.YNc(56,Ge,4,3,"div",34),g.qZA(),g.TgZ(57,"div",30),g.TgZ(58,"label",35),g._uU(59,"Email "),g.TgZ(60,"span",32),g._uU(61,"*"),g.qZA(),g.qZA(),g._UZ(62,"input",36),g.YNc(63,De,3,2,"div",34),g.qZA(),g.YNc(64,Oe,2,0,"div",37),g.TgZ(65,"div",30),g.TgZ(66,"label",38),g._uU(67,"Role "),g.TgZ(68,"span",32),g._uU(69,"*"),g.qZA(),g.qZA(),g.TgZ(70,"select",39),g.NdJ("change",function(e){return t.RoleSetValue(e.target.value)}),g.TgZ(71,"option",40),g._uU(72,"--Select Role--"),g.qZA(),g.YNc(73,Pe,2,2,"option",10),g.qZA(),g.YNc(74,He,2,1,"div",34),g.qZA(),g.TgZ(75,"div",30),g.TgZ(76,"label",41),g._uU(77,"Location "),g.TgZ(78,"span",32),g._uU(79,"*"),g.qZA(),g.qZA(),g.TgZ(80,"select",42),g.TgZ(81,"option",40),g._uU(82,"--Select--"),g.qZA(),g.YNc(83,$e,2,2,"option",10),g.qZA(),g.YNc(84,Be,2,1,"div",34),g.qZA(),g.TgZ(85,"div",30),g.TgZ(86,"label",43),g._uU(87,"Phone Number"),g.qZA(),g.TgZ(88,"input",44),g.NdJ("ngModelChange",function(e){return t.phone_no=e}),g.qZA(),g.YNc(89,je,3,2,"div",34),g.qZA(),g.TgZ(90,"div",30),g.TgZ(91,"label",45),g._uU(92,"Address"),g.qZA(),g._UZ(93,"textarea",46),g.YNc(94,Xe,2,1,"div",34),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(95,"div",47),g.TgZ(96,"button",48),g.NdJ("click",function(){return g.CHM(e),g.MAs(40).hide(),t.clear()}),g._uU(97,"Cancel"),g.qZA(),g.TgZ(98,"button",49),g.NdJ("click",function(){return t.onSubmit()}),g._uU(99,"Save"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(100,"div",21,50),g.TgZ(102,"div",51),g.TgZ(103,"div",24),g.TgZ(104,"div",25),g.TgZ(105,"h4",52),g._uU(106,"Employee Activity"),g.qZA(),g.qZA(),g.TgZ(107,"form",27),g.TgZ(108,"div",28),g.TgZ(109,"div",0),g.TgZ(110,"div",29),g.TgZ(111,"div",30),g.TgZ(112,"table",17),g.TgZ(113,"thead",53),g.TgZ(114,"tr"),g.TgZ(115,"th"),g._uU(116,"Description"),g.qZA(),g.TgZ(117,"th"),g._uU(118,"Timestamp"),g.qZA(),g.qZA(),g.qZA(),g.TgZ(119,"tbody"),g.YNc(120,it,7,6,"tr",54),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(121,"div",47),g.TgZ(122,"button",55),g.NdJ("click",function(){return g.CHM(e),g.MAs(101).hide()}),g._uU(123,"Close"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(124,"div",56,57),g.TgZ(126,"div",23),g.TgZ(127,"div",24),g.TgZ(128,"h4",26),g._uU(129,"Admin User Created Successfully"),g.qZA(),g.TgZ(130,"p"),g._uU(131,"please, Check the mail and set new password"),g.qZA(),g.TgZ(132,"button",48),g.NdJ("click",function(){return g.CHM(e),g.MAs(125).hide(),t.clear()}),g._uU(133,"ok"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(134,"div",58,59),g.TgZ(136,"div",60),g.TgZ(137,"div",24),g.TgZ(138,"div",25),g.TgZ(139,"h4",26),g._uU(140,"Are you sure ?"),g.qZA(),g.qZA(),g.TgZ(141,"div",28),g.TgZ(142,"div",0),g.TgZ(143,"div",29),g.TgZ(144,"p"),g._uU(145,"Do you want to delete this User?"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.TgZ(146,"div",47),g.TgZ(147,"button",48),g.NdJ("click",function(){return g.CHM(e),g.MAs(135).hide()}),g._uU(148,"Cancel"),g.qZA(),g.TgZ(149,"button",61),g.NdJ("click",function(){return t.DeleteEmployee(t.EditId)}),g._uU(150,"Delete"),g.qZA(),g.qZA(),g.qZA(),g.qZA(),g.qZA()}2&e&&(g.xp6(8),g.Q6J("ngIf",t.Add),g.xp6(2),g.Q6J("ngModel",t.location),g.xp6(3),g.Q6J("ngForOf",t.locations),g.xp6(6),g.Q6J("ngModel",t.name),g.xp6(12),g.Q6J("ngIf",t.Edit),g.xp6(4),g.Q6J("ngForOf",g.xi3(36,32,t.employees,g.WLB(35,ot,t.page,t.count))),g.xp6(4),g.Q6J("config",g.DdM(38,nt)),g.xp6(6),g.Oqu(""==t.EditId?"Add Admin User":"Edit Admin User"),g.xp6(1),g.Q6J("formGroup",t.loginForm),g.xp6(9),g.Q6J("ngClass",g.VKq(39,at,t.submitted&&t.f.name.errors)),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.name.errors),g.xp6(6),g.Q6J("ngClass",g.VKq(41,at,t.submitted&&t.f.email.errors))("readonly",""!=t.EditId),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.email.errors),g.xp6(1),g.Q6J("ngIf",t.email),g.xp6(6),g.Q6J("ngClass",g.VKq(43,at,t.submitted&&t.f.role_name.errors)),g.xp6(1),g.Q6J("hidden",""!=t.EditId),g.xp6(2),g.Q6J("ngForOf",t.roles),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.role_name.errors),g.xp6(6),g.Q6J("ngClass",g.VKq(45,at,t.submitted&&t.f.location.errors)),g.xp6(1),g.Q6J("hidden",""!=t.EditId),g.xp6(2),g.Q6J("ngForOf",t.filteredLocations),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.location.errors),g.xp6(4),g.Q6J("ngModel",t.phone_no)("ngClass",g.VKq(47,at,t.submitted&&t.f.phone_no.errors)),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.phone_no.errors),g.xp6(4),g.Q6J("ngClass",g.VKq(49,at,t.submitted&&t.f.address.errors)),g.xp6(1),g.Q6J("ngIf",t.submitted&&t.f.address.errors),g.xp6(6),g.Q6J("config",g.DdM(51,nt)),g.xp6(7),g.Q6J("formGroup",t.loginForm),g.xp6(13),g.Q6J("ngForOf",t.viewlog),g.xp6(14),g.Q6J("config",g.DdM(52,nt)))},directives:[o.O5,d.EJ,d.JJ,d.On,d.YN,d.ks,o.sg,d.Fj,a.LS,n.oB,d.vK,d.JL,d.sg,d.u,o.mk,d.Wl],pipes:[a._s,o.uU],styles:["#select1[_ngcontent-%COMP%]{width:100%}.filter[_ngcontent-%COMP%]{width:127px;display:inline-block;margin-left:45%;margin-top:3px}"]}),e})();var rt=i(8816),st=i(57481);const lt=[{path:"",data:{title:"Settings"},children:[{path:"role",component:x,data:{title:"Role",path:"/settings/role"},canActivate:[st.P]},{path:"module",component:D,data:{title:"Module",path:"/settings"},canActivate:[st.P]},{path:"animal-type",component:O.q,data:{title:"Species",path:"/settings/animal-type"},canActivate:[st.P]},{path:"appointment-types",component:de,data:{title:"Appointment Types",path:"/settings/appointment-types"},canActivate:[st.P]},{path:"breed",component:rt.g,data:{title:"Breed",path:"/settings/breed"},canActivate:[st.P]},{path:"location",component:ve,data:{title:"Location",path:"/settings/location"}},{path:"covetrus",component:ye,data:{title:"Covetrus",path:"/settings/covetrus"},canActivate:[st.P]},{path:"employee",component:dt,data:{title:"Admin Users",path:"/settings/employee"},canActivate:[st.P]}]}];let ct=(()=>{class e{}return e.\u0275mod=g.oAB({type:e}),e.\u0275inj=g.cJS({factory:function(t){return new(t||e)},imports:[[r.Bz.forChild(lt)],r.Bz]}),e})(),gt=(()=>{class e{}return e.\u0275mod=g.oAB({type:e}),e.\u0275inj=g.cJS({factory:function(t){return new(t||e)},imports:[[o.ez,ct,n.zk.forRoot(),a.JX,d.u5,d.UX]]}),e})()}}]);