import { INavData } from '@coreui/angular';

export const navItems: INavData[] = [
  // {
  //   name: 'Dashboard',
  //   url: '/dashboard',
  //   icon: 'icon-speedometer',
  //   // badge: {
  //   //   variant: 'info',
  //   //   text: 'NEW'
  //   // }
  // },
  {
    name: 'Customers',
    url: '/pages/customers',
    icon: 'fa fa-group'
  },
  
  {
    name: 'Appointments',
    url: '/pages/appointments',
    icon: 'cil-calendar-check'
  },
  {
    name: 'Availability',
    url: '/pages/availability',
    icon: 'fa fa-calendar'
  },
  {
    name: 'Resources',
    url: '/pages/resources',
    icon: 'cil-puzzle'
  },
  {
    name: 'Pelfies',
    url: '/pages/pelfies',
    icon: 'fa fa-picture-o'
  },
  {
    name: 'Shopping',
    url: '/pages/shopping',
    icon: 'fa fa-shopping-cart',
    children: [
      {
        name: 'Products',
        url: '/pages/shopping',
        icon: 'fa fa-id-card'
      },
      {
        name: 'Banners',
        url: '/pages/banners',
        icon: 'fa fa-id-card'
      },
      {
        name: 'Shop Setting',
        url: '/pages/shop-setting',
        icon: 'fa fa-id-card'
      }]
  },
  {
    name: 'Orders',
    url: '/pages/orders',
    icon: 'cil-task'
  },
  // {
  //   name: 'Report',
  //   url: '/pages/report',
  //   icon: 'fa fa-file-text-o'
  // },
  {
    name: 'Change Password',
    url: '/pages/change-password',
    icon: 'fa fa-key'
  },
  {
    name: 'Settings',
    url: '/settings',
    icon: 'fa fa-mortar-board',
    children: [
      {
        name: 'Admin Users',
        url: '/settings/employee',
        icon: 'fa fa-id-card'
      },
      {
        name: 'Role',
        url: '/settings/role',
        icon: 'fa fa-cogs'
      },
      // {
      //   name: 'Module',
      //   url: '/settings/module',
      //   icon: 'fa fa-cubes'
      // },
      // {
      //   name: 'Permission',
      //   url: '/settings/permission',
      //   icon: 'fa fa-unlock-alt'
      // },
      // {
      //   name: 'Doctor',
      //   url: '/settings/doctor',
      //   icon: 'fa fa-hospital-o'
      // },
      {
        name: 'Species',
        url: '/settings/animal-type',
        icon: 'fa fa-paw'
      },
      {
        name: 'Appointment Types',
        url: '/settings/appointment-types',
        icon: 'fa fa-user-md'
      },
      {
        name: 'Location',
        url: '/settings/location',
        icon: 'fa fa-location-arrow'
      },
      {
        name: 'Breed',
        url: '/settings/breed',
        icon: 'fa fa-paw'
      },
      {
        name: 'Covetrus',
        url: '/settings/covetrus',
        icon: 'fa fa-unlock-alt'
      },
      // {
      //   name: 'Popovers',
      //   url: '/base/popovers',
      //   icon: 'icon-puzzle'
      // },
      // {
      //   name: 'Progress',
      //   url: '/base/progress',
      //   icon: 'icon-puzzle'
      // },
      // {
      //   name: 'Switches',
      //   url: '/base/switches',
      //   icon: 'icon-puzzle'
      // },
      // {
      //   name: 'Tables',
      //   url: '/base/tables',
      //   icon: 'icon-puzzle'
      // },
      // {
      //   name: 'Tabs',
      //   url: '/base/tabs',
      //   icon: 'icon-puzzle'
      // },
      // {
      //   name: 'Tooltips',
      //   url: '/base/tooltips',
      //   icon: 'icon-puzzle'
      // }
    ]
  },
  // {
  //   name: 'Buttons',
  //   url: '/buttons',
  //   icon: 'icon-cursor',
  //   children: [
  //     {
  //       name: 'Buttons',
  //       url: '/buttons/buttons',
  //       icon: 'icon-cursor'
  //     },
  //     {
  //       name: 'Dropdowns',
  //       url: '/buttons/dropdowns',
  //       icon: 'icon-cursor'
  //     },
  //     {
  //       name: 'Brand Buttons',
  //       url: '/buttons/brand-buttons',
  //       icon: 'icon-cursor'
  //     }
  //   ]
  // },
  // {
  //   name: 'Charts',
  //   url: '/charts',
  //   icon: 'icon-pie-chart'
  // },
  // {
  //   name: 'Icons',
  //   url: '/icons',
  //   icon: 'icon-star',
  //   children: [
  //     {
  //       name: 'CoreUI Icons',
  //       url: '/icons/coreui-icons',
  //       icon: 'icon-star',
  //       badge: {
  //         variant: 'success',
  //         text: 'NEW'
  //       }
  //     },
  //     {
  //       name: 'Flags',
  //       url: '/icons/flags',
  //       icon: 'icon-star'
  //     },
  //     {
  //       name: 'Font Awesome',
  //       url: '/icons/font-awesome',
  //       icon: 'icon-star',
  //       badge: {
  //         variant: 'secondary',
  //         text: '4.7'
  //       }
  //     },
  //     {
  //       name: 'Simple Line Icons',
  //       url: '/icons/simple-line-icons',
  //       icon: 'icon-star'
  //     }
  //   ]
  // },
  // {
  //   name: 'Notifications',
  //   url: '/notifications',
  //   icon: 'icon-bell',
  //   children: [
  //     {
  //       name: 'Alerts',
  //       url: '/notifications/alerts',
  //       icon: 'icon-bell'
  //     },
  //     {
  //       name: 'Badges',
  //       url: '/notifications/badges',
  //       icon: 'icon-bell'
  //     },
  //     {
  //       name: 'Modals',
  //       url: '/notifications/modals',
  //       icon: 'icon-bell'
  //     }
  //   ]
  // },
  // {
  //   name: 'Widgets',
  //   url: '/widgets',
  //   icon: 'icon-calculator',
  //   badge: {
  //     variant: 'info',
  //     text: 'NEW'
  //   }
  // },
  // {
  //   divider: true
  // },
  // {
  //   title: true,
  //   name: 'Extras',
  // },
  // {
  //   name: 'Pages',
  //   url: '/pages',
  //   icon: 'icon-star',
  //   children: [
  //     {
  //       name: 'Login',
  //       url: '/login',
  //       icon: 'icon-star'
  //     },
  //     {
  //       name: 'Register',
  //       url: '/register',
  //       icon: 'icon-star'
  //     },
  //     {
  //       name: 'Error 404',
  //       url: '/404',
  //       icon: 'icon-star'
  //     },
  //     {
  //       name: 'Error 500',
  //       url: '/500',
  //       icon: 'icon-star'
  //     }
  //   ]
  // },
  // {
  //   name: 'Disabled',
  //   url: '/dashboard',
  //   icon: 'icon-ban',
  //   badge: {
  //     variant: 'secondary',
  //     text: 'NEW'
  //   },
  //   attributes: { disabled: true },
  // },
  // {
  //   name: 'Download CoreUI',
  //   url: 'http://coreui.io/angular/',
  //   icon: 'icon-cloud-download',
  //   class: 'mt-auto',
  //   variant: 'success',
  //   attributes: { target: '_blank', rel: 'noopener' }
  // },
  // {
  //   name: 'Try CoreUI PRO',
  //   url: 'http://coreui.io/pro/angular/',
  //   icon: 'icon-layers',
  //   variant: 'danger',
  //   attributes: { target: '_blank', rel: 'noopener' }
  // }
];
