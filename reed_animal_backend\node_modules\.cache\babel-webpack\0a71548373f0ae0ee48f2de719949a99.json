{"ast": null, "code": "import basePropertyOf from './_basePropertyOf.js';\n/** Used to map HTML entities to characters. */\n\nvar htmlUnescapes = {\n  '&amp;': '&',\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&#39;': \"'\"\n};\n/**\n * Used by `_.unescape` to convert HTML entities to characters.\n *\n * @private\n * @param {string} chr The matched character to unescape.\n * @returns {string} Returns the unescaped character.\n */\n\nvar unescapeHtmlChar = basePropertyOf(htmlUnescapes);\nexport default unescapeHtmlChar;", "map": null, "metadata": {}, "sourceType": "module"}