{"ast": null, "code": "import { hostReportError } from './hostReportError';\nexport const subscribeToPromise = promise => subscriber => {\n  promise.then(value => {\n    if (!subscriber.closed) {\n      subscriber.next(value);\n      subscriber.complete();\n    }\n  }, err => subscriber.error(err)).then(null, hostReportError);\n  return subscriber;\n}; //# sourceMappingURL=subscribeToPromise.js.map", "map": null, "metadata": {}, "sourceType": "module"}