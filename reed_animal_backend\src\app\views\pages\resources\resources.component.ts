import { Component, OnInit, ViewChild, ChangeDetectorRef, } from '@angular/core';
import { ResourcesService } from '../../services/resources.services'
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';

@Component({
  selector: 'app-resources',
  templateUrl: './resources.component.html',
  styleUrls: ['./resources.component.scss']
})

export class ResourcesComponent implements OnInit {

  @ViewChild('primaryModaltips') public primaryModaltips: ModalDirective;
  @ViewChild('removeTips') public removeTips: ModalDirective;

  @ViewChild('primaryModalvideo') public primaryModalvideo: ModalDirective;
  @ViewChild('removevideo') public removevideo: ModalDirective;

  @ViewChild('primaryModalaudio') public primaryModalaudio: ModalDirective;
  @ViewChild('removeaudio') public removeaudio: ModalDirective;

  @ViewChild('primaryModalfaq') public primaryModalfaq: ModalDirective;
  @ViewChild('removefaq') public removefaq: ModalDirective;

  public EditId = '';
  public ImageUrl: any = '';
  public image_url: any = '';
  public VideoUrl: any = '';
  public video_url: any = '';
  public AudioUrl: any = '';
  public audio_url: any = '';

  //Health Tips
  public TipsList = [];
  public loginForm: FormGroup;
  public submitted = false;
  public page = 1;
  public count = 0;

  //Video
  public Videos = [];
  public videoForm: FormGroup;
  public videosubmitted = false;
  public pageV = 1;
  public countV = 0;

  //Audio
  public Audios = []
  public audioForm: FormGroup;
  public audiosubmitted = false;
  public pageA = 1;
  public countA = 0;

  //FAQ
  public FAQs = [];
  public FAQForm: FormGroup;
  public FAQsubmitted = false;
  public pageFAQ = 1;
  public countFAQ = 0;

  public Add = true;
  public Edit = true;
  public Delete = true;
  constructor(private ResourceService: ResourcesService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice, private tokenStorage: TokenStorageService, private changeDetectRef: ChangeDetectorRef,
  ) {
    this.GetTips();
    this.Getvideos();
    this.GetFAQs();
    this.Getaudios();
  }

  ngOnInit(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // console.log(Role.role_id._id)
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Resources") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.SignForm();
    this.video();
    this.audio();
    this.faq();
  }


  SignForm() {
    this.loginForm = this.formBuilder.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.pattern('[a-zA-Z .]*$')]],
      description: ['', [Validators.required, Validators.minLength(20)]],
      poster_image: ['', [Validators.required]]
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  //Add new Health tips
  async Addtips() {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    this.loginForm.value.poster_image = this.ImageUrl;
    if (this.image_url !== '')
      this.loginForm.value.poster_image = await this.uploadFile(this.image_url);
    if (this.EditId == '') {
      this.ResourceService.AddTips(this.loginForm.value)
        .subscribe((res: any) => {
          if (res.code !== 200) {
            return
          }
        })
    } else {
      this.ResourceService.Updatetips(this.EditId, this.loginForm.value)
        .subscribe((res: any) => {
          if (res.code !== 200) {
            return
          }
        })
    }
    this.primaryModaltips.hide();
    this.GetTips();
    this.clear();
  }

  // Image Upload Service
  public async uploadFile(file): Promise<any> {
    return new Promise((resolve, reject) => {
      this.ResourceService.uploadFile(file).subscribe((res: any) => {
        return resolve(res.data);
      })
    })
  }

  //get all Health Tips
  GetTips() {
    const data = {
      skip: (this.page - 1) * 10,
    }
    this.ResourceService.GetTips(data)
      .subscribe((res: any) => {
        // console.log(res)
        this.TipsList = res.data;
      })
  }

  //Get Row details by using index
  GetTipsBy(i) {
    this.loginForm.controls['title'].setValue(this.TipsList[i].title);
    this.loginForm.controls['description'].setValue(this.TipsList[i].description);
    this.loginForm.controls['poster_image'].setErrors(null);
    this.ImageUrl = this.TipsList[i].poster_image;
  }

  //******** Video ***********/

  video() {
    this.videoForm = this.formBuilder.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.pattern('[a-zA-Z .]*$')]],
      video: ['', [Validators.required]],
      poster_image: ['', [Validators.required]]
    });
  }

  get v() {
    return this.videoForm.controls;
  }

  //Add new Health tips
  async AddVideo() {
    this.videosubmitted = true;
    if (this.videoForm.invalid) {
      return
    }
    this.videoForm.value.poster_image = this.ImageUrl;
    this.videoForm.value.video = this.VideoUrl;
    if (this.image_url !== '')
      this.videoForm.value.poster_image = await this.uploadFile(this.image_url);

    if (this.video_url !== '')
      this.videoForm.value.video = await this.uploadFile(this.video_url);

    if (this.EditId == '') {
      this.ResourceService.AddVideo(this.videoForm.value)
        .subscribe((res: any) => {
          if (res.code != 200)
            return
        })
    } else {
      this.ResourceService.UpdateVideo(this.EditId, this.videoForm.value)
        .subscribe((res: any) => {
          if (res.code != 200)
            return
        })
    }
    this.primaryModalvideo.hide();
    this.Getvideos();
    this.clear();
  }

  //get all videos
  Getvideos() {
    const QueryParams = {
      skip: (this.pageV - 1) * 10,
    }
    this.ResourceService.GetVideos(QueryParams)
      .subscribe((res: any) => {
        this.Videos = res.data;
        this.countV = res.count;
      })
  }


  //Get Row details by using index
  GetVideoBy(i) {
    this.videoForm.controls['title'].setValue(this.Videos[i].title);
    this.videoForm.controls['poster_image'].setErrors(null);
    this.videoForm.controls['video'].setErrors(null);
    this.ImageUrl = this.Videos[i].poster_image;
    this.VideoUrl = this.Videos[i].video;
  }


  //Delete Video
  DeleteVideo(id) {
    this.ResourceService.DeleteVideo(id)
      .subscribe((res: any) => {
        this.removevideo.hide();
        this.Getvideos();
        this.clear();
      })
  }


  //******** Audio ***********/

  audio() {
    this.audioForm = this.formBuilder.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.pattern('[a-zA-Z .]*$')]],
      audio: ['', [Validators.required]],
      poster_image: ['', [Validators.required]]
    });
  }

  get A() {
    return this.audioForm.controls;
  }

  //Add new audio
  async Addaudios() {
    this.audiosubmitted = true;
    if (this.audioForm.invalid) {
      return
    }
    this.audioForm.value.poster_image = this.ImageUrl;
    this.audioForm.value.audio = this.AudioUrl;
    if (this.image_url !== '')
      this.audioForm.value.poster_image = await this.uploadFile(this.image_url);
    if (this.audio_url !== '')
      this.audioForm.value.audio = await this.uploadFile(this.audio_url);
    if (this.EditId == '') {
      this.ResourceService.AddAudio(this.audioForm.value)
        .subscribe((res: any) => {
          if (res.status !== 200)
            return
        })
    } else {
      this.ResourceService.UpdateAudio(this.EditId, this.audioForm.value)
        .subscribe((res: any) => {
          if (res.status !== 200)
            return
        })
    }
    this.primaryModalaudio.hide();
    this.Getaudios();
    this.clear();
  }

  //get all audios
  Getaudios() {
    const QueryParams = {
      skip: (this.pageA - 1) * 10
    }
    this.ResourceService.GetAudios(QueryParams)
      .subscribe((res: any) => {
        this.Audios = res.data;
        this.countA = res.count;
      })
  }

  //Get Row details by using index
  GetaudioBy(i) {
    this.audioForm.controls['title'].setValue(this.Audios[i].title);
    this.audioForm.controls['poster_image'].setErrors(null);
    this.audioForm.controls['audio'].setErrors(null);
    this.ImageUrl = this.Audios[i].poster_image;
    this.AudioUrl = this.Audios[i].audio;
  }

  //Delete audio
  DeleteAudio(id) {
    this.ResourceService.DeleteAudio(id)
      .subscribe((res: any) => {
        this.removeaudio.hide();
        this.Getaudios();
        this.clear();
      })
  }

  //******** FAQ ***********/

  faq() {
    this.FAQForm = this.formBuilder.group({
      question: ['', [Validators.required, Validators.minLength(5)]],
      answer: ['', [Validators.required, Validators.minLength(5)]]
    });
  }

  get F() {
    return this.FAQForm.controls;
  }

  //Add new FAQ
  AddFAQs() {
    this.FAQsubmitted = true;
    if (this.FAQForm.invalid) {
      return
    }
    if (this.EditId == '') {
      this.ResourceService.AddFAQ(this.FAQForm.value)
        .subscribe((res: any) => {
          if (res.status !== 200)
            return
        })
    } else {
      this.ResourceService.UpdateFAQ(this.EditId, this.FAQForm.value)
        .subscribe((res: any) => {
          if (res.status !== 200)
            return
        })
    }
    this.primaryModalfaq.hide();
    this.clear();
    this.GetFAQs();
  }
  //get all FAQs
  GetFAQs() {
    const QueryParams = {
      skip: (this.pageFAQ - 1) * 10
    }
    this.ResourceService.GetFAQs(QueryParams)
      .subscribe((res: any) => {
        this.FAQs = res.data;
        this.countFAQ = res.count;
      })
  }

  //Get Row details by using index
  GetFAQBy(i) {
    this.FAQForm.controls['question'].setValue(this.FAQs[i].question);
    this.FAQForm.controls['answer'].setValue(this.FAQs[i].answer);
  }

  //Delete Video
  DeleteFAQ(id) {
    this.ResourceService.DeleteFAQ(id)
      .subscribe((res: any) => {
        this.removefaq.hide();
        this.GetFAQs();
        this.clear();
      })
  }

  clear() {
    this.submitted = false;
    this.loginForm.reset();
    this.videosubmitted = false;
    this.videoForm.reset();
    this.audiosubmitted = false;
    this.audioForm.reset();
    this.FAQsubmitted = false;
    this.FAQForm.reset();
    this.ImageUrl = '';
    this.EditId = '';
    this.image_url = '';
    this.VideoUrl = '';
    this.video_url = '';
    this.AudioUrl = '';
    this.audio_url = '';
  }

  // A Function 'onUpload' getting upload file
  onUpload(event, param): void {
    this.convertBase64(event.target, param);
  }
  /**
     * A function 'convertBase64' for converting image file into base64
     * @param imageFile getting image file from input
     */
  convertBase64(imageFile: any, param) {
    const URL = window.URL || window.webkitURL;
    const Img = new Image();
    const filesToUpload = (imageFile.files);
    Img.src = URL.createObjectURL(filesToUpload[0]);

    Img.onload = (e: any) => {
      const file: File = imageFile.files[0];
      const myReader: FileReader = new FileReader();
      myReader.onloadend = (e: any) => {
        this.ImageUrl = myReader.result;
        this[param].controls['poster_image'].setErrors(null);
        this.changeDetectRef.detectChanges();
        this.image_url = file;
      };
      myReader.readAsDataURL(file);
    }
  }

  VideoUpload(event) {
    const file: File = event.target.files[0];
    const myReader: FileReader = new FileReader();
    myReader.onloadend = (e: any) => {
      this.VideoUrl = myReader.result;
      this.videoForm.controls['video'].setErrors(null);
      this.changeDetectRef.detectChanges();
      this.video_url = file;
    };
    myReader.readAsDataURL(file);
  }

  AudioUpload(event) {
    const file: File = event.target.files[0];
    const myReader: FileReader = new FileReader();
    myReader.onloadend = (e: any) => {
      this.AudioUrl = myReader.result;
      this.audioForm.controls['audio'].setErrors(null);
      this.changeDetectRef.detectChanges();
      this.audio_url = file;
      console.log(this.AudioUrl, this.audio_url)
    };
    myReader.readAsDataURL(file);
  }

  //Delete Health Tips
  DeleteTips(id) {
    this.ResourceService.DeleteTips(id)
      .subscribe((res: any) => {
        this.removeTips.hide();
        this.GetTips();
        this.clear();
      })
  }

}