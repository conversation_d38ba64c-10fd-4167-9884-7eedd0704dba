{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Italian [it]\n//! author : <PERSON> : https://github.com/aliem\n//! author: <PERSON><PERSON>: https://github.com/nostalgiaz\n//! author: <PERSON> : https://github.com/Manfre98\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var it = moment.defineLocale('it', {\n    months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split('_'),\n    monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n    weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split('_'),\n    weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n    weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[Oggi a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      nextDay: function () {\n        return '[Domani a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      lastDay: function () {\n        return '[Ieri a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n      },\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[La scorsa] dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n\n          default:\n            return '[Lo scorso] dddd [a' + (this.hours() > 1 ? 'lle ' : this.hours() === 0 ? ' ' : \"ll'\") + ']LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'tra %s',\n      past: '%s fa',\n      s: 'alcuni secondi',\n      ss: '%d secondi',\n      m: 'un minuto',\n      mm: '%d minuti',\n      h: \"un'ora\",\n      hh: '%d ore',\n      d: 'un giorno',\n      dd: '%d giorni',\n      w: 'una settimana',\n      ww: '%d settimane',\n      M: 'un mese',\n      MM: '%d mesi',\n      y: 'un anno',\n      yy: '%d anni'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return it;\n});", "map": null, "metadata": {}, "sourceType": "script"}