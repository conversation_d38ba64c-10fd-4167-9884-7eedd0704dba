!function(){"use strict";function e(e,i){if(!(e instanceof i))throw new TypeError("Cannot call a class as a function")}function i(e,i){for(var t=0;t<i.length;t++){var n=i[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function t(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}(self.webpackChunkDr_Reed_Admin_Panel=self.webpackChunkDr_Reed_Admin_Panel||[]).push([[354],{19148:function(i,n,r){r.d(n,{q:function(){return C}});var o=r(50022),d=r(6642),a=r(26415),s=r(74875),u=r(90658),l=r(99777),c=r(11192),g=r(63237),Z=r(45055),f=r(30386),p=["primaryModal"],m=["AddModal"],v=["removeModal"];function h(e,i){if(1&e){var t=u.EpF();u.TgZ(0,"button",38),u.NdJ("click",function(){return u.CHM(t),u.oxw(),u.MAs(29).show()}),u._uU(1," Add Species "),u.qZA()}}function q(e,i){1&e&&(u.TgZ(0,"th"),u._uU(1,"Status"),u.qZA())}function A(e,i){if(1&e){var t=u.EpF();u.TgZ(0,"td"),u.TgZ(1,"label",40),u.TgZ(2,"input",41),u.NdJ("change",function(){u.CHM(t);var e=u.oxw().$implicit;return u.oxw().changed(e.status,e._id)})("ngModelChange",function(e){return u.CHM(t),u.oxw().$implicit.status=e}),u.qZA(),u._UZ(3,"span",42),u.qZA(),u.qZA()}if(2&e){var n=u.oxw().$implicit;u.xp6(2),u.Q6J("ngModel",n.status)}}function T(e,i){if(1&e){var t=u.EpF();u.TgZ(0,"a",43),u.NdJ("click",function(){u.CHM(t);var e=u.oxw().$implicit,i=u.oxw();return u.MAs(50).show(),i.GetType(e._id)}),u.TgZ(1,"span",44),u._UZ(2,"i",45),u._uU(3," Edit"),u.qZA(),u.qZA()}}function b(e,i){if(1&e){var t=u.EpF();u.TgZ(0,"a",43),u.NdJ("click",function(){u.CHM(t);var e=u.oxw().$implicit,i=u.oxw();return u.MAs(71).show(),i.GetType(e._id)}),u.TgZ(1,"span",46),u._UZ(2,"i",47),u._uU(3," Delete"),u.qZA(),u.qZA()}}function y(e,i){if(1&e&&(u.TgZ(0,"tr"),u.TgZ(1,"td"),u._uU(2),u.qZA(),u.YNc(3,A,4,1,"td",14),u.TgZ(4,"td"),u.YNc(5,T,4,0,"a",39),u.YNc(6,b,4,0,"a",39),u.qZA(),u.qZA()),2&e){var t=i.$implicit,n=u.oxw();u.xp6(2),u.Oqu(t.name),u.xp6(1),u.Q6J("ngIf",n.Edit),u.xp6(2),u.Q6J("ngIf",n.Edit),u.xp6(1),u.Q6J("ngIf",n.Delete)}}function x(e,i){1&e&&(u.TgZ(0,"div"),u._uU(1,"Name is mandatory"),u.qZA())}function _(e,i){if(1&e&&(u.TgZ(0,"div",48),u.YNc(1,x,2,0,"div",14),u.qZA()),2&e){var t=u.oxw();u.xp6(1),u.Q6J("ngIf",t.f.name.errors.required)}}function k(e,i){1&e&&(u.TgZ(0,"div"),u._uU(1,"Name is mandatory"),u.qZA())}function J(e,i){if(1&e&&(u.TgZ(0,"div",48),u.YNc(1,k,2,0,"div",14),u.qZA()),2&e){var t=u.oxw();u.xp6(1),u.Q6J("ngIf",t.f.name.errors.required)}}var M=function(e,i){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:i}},U=function(){return{backdrop:"static",keyboard:!1}},N=function(e){return{"is-invalid":e}},C=function(){var i=function(){function i(t,n,r,o,d,a,s){e(this,i),this.animalservice=t,this.route=n,this.router=r,this.tokenStorage=o,this.formBuilder=d,this.Permission=a,this.EmployeeService=s,this.isFormReady=!1,this.submitted=!1,this.types=[],this.page=1,this.count=0,this.search="",this.name="",this.type={},this.nameFailed=!1,this.modal=!0,this.Add=!0,this.Edit=!0,this.Delete=!0}return t(i,[{key:"ngOnInit",value:function(){this.tokens(),this.SignForm()}},{key:"clear",value:function(){this.type={},this.loginForm.reset(),this.nameFailed=!1,this.isFormReady=!1,this.submitted=!1}},{key:"getfocus",value:function(){this.nameFailed=!1}},{key:"tokens",value:function(){var e=this,i=this.tokenStorage.getUser();this.Permission.GetModule(i.role_id._id).subscribe(function(i){for(var t=0;t<i.data.length;t++)"Species"==i.data[t].module_name&&(e.Add=i.data[t].add,e.Edit=i.data[t].edit,e.Delete=i.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(i._id).subscribe(function(i){0==i.data.status?e.tokenStorage.signOut():e.typeLists()})}},{key:"getrequestparams",value:function(e){var i={};return i.skip=10*(e-1),i}},{key:"typeLists",value:function(){var e=this,i=this.getrequestparams(this.page);this.animalservice.GetTypesList(i,this.name).subscribe(function(i){e.types=i.data,e.count=i.count})}},{key:"handlePageChange",value:function(e){this.page=e,this.typeLists()}},{key:"GetType",value:function(e){var i=this;this.animalservice.GetTypeDetail(e).subscribe(function(e){i.type=e.data[0],i.f.name.setValue(e.data[0].name,{onlySelf:!0})})}},{key:"EditType",value:function(e){var i=this;this.submitted=!0,this.loginForm.invalid||this.animalservice.UpdateType(e,{name:this.loginForm.value.name}).subscribe(function(e){i.primaryModal.hide(),i.type={},i.typeLists()})}},{key:"changed",value:function(e,i){this.animalservice.UpdateType(i,{status:e}).subscribe(function(e){})}},{key:"SignForm",value:function(){this.loginForm=this.formBuilder.group({name:["",[s.kI.required]]})}},{key:"f",get:function(){return this.loginForm.controls}},{key:"AddType",value:function(){var e=this;this.submitted=!0,this.loginForm.invalid||(this.animalservice.Newtype({name:this.loginForm.value.name}).subscribe(function(i){e.AddModal.hide(),e.clear(),e.typeLists()}),this.nameFailed=!0)}},{key:"DeleteType",value:function(e){var i=this;this.animalservice.Deletetype(e).subscribe(function(e){i.removeModal.hide(),i.type={},i.typeLists()})}}]),i}();return i.\u0275fac=function(e){return new(e||i)(u.Y36(o.l),u.Y36(l.gz),u.Y36(l.F0),u.Y36(c.i),u.Y36(s.qu),u.Y36(d.$),u.Y36(a.d))},i.\u0275cmp=u.Xpm({type:i,selectors:[["app-animal-type"]],viewQuery:function(e,i){var t;(1&e&&(u.Gf(p,1),u.Gf(m,1),u.Gf(v,1)),2&e)&&(u.iGM(t=u.CRH())&&(i.primaryModal=t.first),u.iGM(t=u.CRH())&&(i.AddModal=t.first),u.iGM(t=u.CRH())&&(i.removeModal=t.first))},decls:87,vars:26,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"click"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","input","ngModelChange"],[1,"table","table-striped"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","Module","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["AddModal","bs-modal"],["Module","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","firstName"],["type","text","placeholder","e.g. Dog, Cat","formControlName","name",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["primaryModal","bs-modal"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"switch"],["type","checkbox","checked","user.status",3,"ngModel","change","ngModelChange"],[1,"slider","round"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[1,"invalid-feedback"]],template:function(e,i){if(1&e){var t=u.EpF();u.TgZ(0,"div",0),u.TgZ(1,"div",1),u.TgZ(2,"div",2),u.TgZ(3,"div",3),u._uU(4," Species "),u.qZA(),u.TgZ(5,"div",4),u.TgZ(6,"div",0),u.TgZ(7,"div",5),u.YNc(8,h,2,0,"button",6),u.TgZ(9,"div",7),u.TgZ(10,"div",8),u.TgZ(11,"div",9),u.TgZ(12,"span",10),u.NdJ("click",function(){return i.typeLists()}),u._UZ(13,"i",11),u.qZA(),u.qZA(),u.TgZ(14,"input",12),u.NdJ("input",function(){return i.typeLists()})("ngModelChange",function(e){return i.name=e}),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(15,"table",13),u.TgZ(16,"thead"),u.TgZ(17,"tr"),u.TgZ(18,"th"),u._uU(19,"Species Name"),u.qZA(),u.YNc(20,q,2,0,"th",14),u.TgZ(21,"th"),u._uU(22,"Action"),u.qZA(),u.qZA(),u.qZA(),u.TgZ(23,"tbody"),u.YNc(24,y,7,4,"tr",15),u.ALo(25,"paginate"),u.qZA(),u.qZA(),u.TgZ(26,"div"),u.TgZ(27,"pagination-controls",16),u.NdJ("pageChange",function(e){return i.handlePageChange(e)}),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(28,"div",17,18),u.TgZ(30,"div",19),u.TgZ(31,"div",20),u.TgZ(32,"div",21),u.TgZ(33,"h4",22),u._uU(34,"Add Species"),u.qZA(),u.qZA(),u.TgZ(35,"div",23),u.TgZ(36,"div",0),u.TgZ(37,"div",24),u.TgZ(38,"form",25),u.TgZ(39,"div",26),u.TgZ(40,"label",27),u._uU(41,"Species Name*"),u.qZA(),u._UZ(42,"input",28),u.YNc(43,_,2,1,"div",29),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(44,"div",30),u.TgZ(45,"button",31),u.NdJ("click",function(){return u.CHM(t),u.MAs(29).hide(),i.clear()}),u._uU(46,"Cancel"),u.qZA(),u.TgZ(47,"button",32),u.NdJ("click",function(){return i.AddType()}),u._uU(48,"Save"),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(49,"div",17,33),u.TgZ(51,"div",19),u.TgZ(52,"div",20),u.TgZ(53,"div",21),u.TgZ(54,"h4",22),u._uU(55,"Edit Species"),u.qZA(),u.qZA(),u.TgZ(56,"div",23),u.TgZ(57,"div",0),u.TgZ(58,"div",24),u.TgZ(59,"form",25),u.TgZ(60,"div",26),u.TgZ(61,"label",27),u._uU(62,"Species Name*"),u.qZA(),u._UZ(63,"input",28),u.YNc(64,J,2,1,"div",29),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(65,"div",30),u.TgZ(66,"button",31),u.NdJ("click",function(){return u.CHM(t),u.MAs(50).hide(),i.clear()}),u._uU(67,"Cancel"),u.qZA(),u.TgZ(68,"button",32),u.NdJ("click",function(){return i.EditType(i.type._id)}),u._uU(69,"Save"),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(70,"div",34,35),u.TgZ(72,"div",36),u.TgZ(73,"div",20),u.TgZ(74,"div",21),u.TgZ(75,"h4",22),u._uU(76,"Are you sure ?"),u.qZA(),u.qZA(),u.TgZ(77,"div",23),u.TgZ(78,"div",0),u.TgZ(79,"div",24),u.TgZ(80,"p"),u._uU(81,"Do you want to delete this Species?"),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.TgZ(82,"div",30),u.TgZ(83,"button",31),u.NdJ("click",function(){return u.CHM(t),u.MAs(71).hide(),i.clear()}),u._uU(84,"Cancel"),u.qZA(),u.TgZ(85,"button",37),u.NdJ("click",function(){return i.DeleteType(i.type._id)}),u._uU(86,"Delete"),u.qZA(),u.qZA(),u.qZA(),u.qZA(),u.qZA()}2&e&&(u.xp6(8),u.Q6J("ngIf",i.Add),u.xp6(6),u.Q6J("ngModel",i.name),u.xp6(6),u.Q6J("ngIf",i.Edit),u.xp6(4),u.Q6J("ngForOf",u.xi3(25,13,i.types,u.WLB(16,M,i.page,i.count))),u.xp6(4),u.Q6J("config",u.DdM(19,U)),u.xp6(10),u.Q6J("formGroup",i.loginForm),u.xp6(4),u.Q6J("ngClass",u.VKq(20,N,i.submitted&&i.f.name.errors)),u.xp6(1),u.Q6J("ngIf",i.submitted&&i.f.name.errors),u.xp6(6),u.Q6J("config",u.DdM(22,U)),u.xp6(10),u.Q6J("formGroup",i.loginForm),u.xp6(4),u.Q6J("ngClass",u.VKq(23,N,i.submitted&&i.f.name.errors)),u.xp6(1),u.Q6J("ngIf",i.submitted&&i.f.name.errors),u.xp6(6),u.Q6J("config",u.DdM(25,U)))},directives:[g.O5,s.Fj,s.JJ,s.On,g.sg,Z.LS,f.oB,s.vK,s.JL,s.sg,s.u,g.mk,s.Wl],pipes:[Z._s],styles:[""]}),i}()},8816:function(i,n,r){r.d(n,{g:function(){return P}});var o=r(75874),d=r(50022),a=r(6642),s=r(26415),u=r(74875),l=r(90658),c=r(99777),g=r(11192),Z=r(63237),f=r(45055),p=r(30386),m=["primaryModal"],v=["deleteModal"],h=["removeModal"];function q(e,i){if(1&e){var t=l.EpF();l.TgZ(0,"button",53),l.NdJ("click",function(){return l.CHM(t),l.oxw(),l.MAs(38).show()}),l._uU(1," Add Breed "),l.qZA()}}function A(e,i){if(1&e){var t=l.EpF();l.TgZ(0,"a",55),l.NdJ("click",function(){l.CHM(t);var e=l.oxw().index,i=l.oxw();return l.MAs(81).show(),i.GetBreeding(e)}),l.TgZ(1,"span",56),l._UZ(2,"i",57),l._uU(3," Edit"),l.qZA(),l.qZA()}}function T(e,i){if(1&e){var t=l.EpF();l.TgZ(0,"a",55),l.NdJ("click",function(){l.CHM(t);var e=l.oxw().$implicit,i=l.oxw();return l.MAs(123).show(),i.GetBreeding(e._id)}),l.TgZ(1,"span",58),l._UZ(2,"i",59),l._uU(3," Delete"),l.qZA(),l.qZA()}}function b(e,i){if(1&e&&(l.TgZ(0,"tr"),l.TgZ(1,"td"),l._uU(2),l.qZA(),l.TgZ(3,"td"),l._uU(4),l.qZA(),l.TgZ(5,"td"),l._uU(6),l.qZA(),l.TgZ(7,"td"),l._uU(8),l.qZA(),l.TgZ(9,"td"),l.YNc(10,A,4,0,"a",54),l.YNc(11,T,4,0,"a",54),l.qZA(),l.qZA()),2&e){var t=i.$implicit,n=l.oxw();l.xp6(2),l.Oqu(t.name),l.xp6(2),l.Oqu(t.code),l.xp6(2),l.Oqu(t.size),l.xp6(2),l.Oqu(t.type),l.xp6(2),l.Q6J("ngIf",n.Edit),l.xp6(1),l.Q6J("ngIf",n.Delete)}}function y(e,i){if(1&e&&(l.TgZ(0,"option",60),l._uU(1),l.qZA()),2&e){var t=i.$implicit;l.Q6J("value",t.name),l.xp6(1),l.Oqu(t.name)}}function x(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Species is mandatory"),l.qZA())}function _(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,x,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.species.errors.required)}}function k(e,i){if(1&e&&(l.TgZ(0,"option",60),l._uU(1),l.qZA()),2&e){var t=i.$implicit;l.Q6J("value",t),l.xp6(1),l.Oqu(t)}}function J(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Size is mandatory"),l.qZA())}function M(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,J,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.size.errors.required)}}function U(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Breed name is mandatory"),l.qZA())}function N(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,U,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.breed.errors.required)}}function C(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Breed code is mandatory"),l.qZA())}function B(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,C,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.code.errors.required)}}function F(e,i){if(1&e&&(l.TgZ(0,"option",60),l._uU(1),l.qZA()),2&e){var t=i.$implicit;l.Q6J("value",t.name),l.xp6(1),l.Oqu(t.name)}}function S(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Species is mandatory"),l.qZA())}function Q(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,S,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.species.errors.required)}}function w(e,i){if(1&e&&(l.TgZ(0,"option",60),l._uU(1),l.qZA()),2&e){var t=i.$implicit;l.Q6J("value",t),l.xp6(1),l.Oqu(t)}}function G(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Size is mandatory"),l.qZA())}function Y(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,G,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.size.errors.required)}}function I(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Breed name is mandatory"),l.qZA())}function L(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,I,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.breed.errors.required)}}function E(e,i){1&e&&(l.TgZ(0,"div"),l._uU(1,"Breed code is mandatory"),l.qZA())}function z(e,i){if(1&e&&(l.TgZ(0,"div",61),l.YNc(1,E,2,0,"div",62),l.qZA()),2&e){var t=l.oxw();l.xp6(1),l.Q6J("ngIf",t.f.code.errors.required)}}var D=function(e,i){return{id:"listing_pagination",itemsPerPage:10,currentPage:e,totalItems:i}},O=function(){return{backdrop:"static",keyboard:!1}},H=function(e){return{"is-invalid":e}},P=function(){var i=function(){function i(t,n,r,o,d,a,s,u){e(this,i),this.BreedingService=t,this.AnimaltypeService=n,this.route=r,this.router=o,this.tokenStorage=d,this.Permission=a,this.EmployeeService=s,this.formBuilder=u,this.Types=[],this.Breedings=[],this.Size=["Toy","Small","Medium","Large","Giant"],this.page=1,this.count=0,this.search="",this.name="",this.ddlFileId="",this.value1="",this.breeding={_id:"",name:"",status:!1,type:"",size:"",code:""},this.isLoginFailed=!1,this.isFormReady=!1,this.submitted=!1,this.Add=!0,this.Edit=!0,this.Delete=!0,this.sort=!1,this.value=1,this.field="name",this.typeFailed=!1}return t(i,[{key:"ngOnInit",value:function(){this.tokens(),this.SignForm()}},{key:"clear",value:function(){this.loginForm.reset(),this.submitted=!1,this.Types=[],this.GetTypeLists(),this.breeding={_id:"",name:"",status:!1,type:"",size:"",code:""}}},{key:"tokens",value:function(){var e=this;this.tokenStorage.getToken();var i=this.tokenStorage.getUser();this.Permission.GetModule(i.role_id._id).subscribe(function(i){for(var t=0;t<i.data.length;t++)"Breed"==i.data[t].module_name&&(e.Add=i.data[t].add,e.Edit=i.data[t].edit,e.Delete=i.data[t].delete)}),this.EmployeeService.GetEmployeeDetail(i._id).subscribe(function(i){0==i.data.status?e.tokenStorage.signOut():e.GetTypeLists()})}},{key:"GetTypeLists",value:function(){var e=this;this.BreedingService.GetTypesList().subscribe(function(i){e.Types=i.data,e.GetBreedingLists()})}},{key:"getrequestparams",value:function(e){var i={};return i.skip=10*(e-1),i.value=this.value,i.field=this.field,i}},{key:"GetBreedingLists",value:function(){var e=this,i=this.getrequestparams(this.page);this.BreedingService.GetBreedingsList(i,this.name).subscribe(function(i){e.Breedings=i.data;var t=i.data;return e.count=i.count,t})}},{key:"handlePageChange",value:function(e){this.page=e,this.GetBreedingLists()}},{key:"searched",value:function(e,i){this.breeding[i]=e}},{key:"SignForm",value:function(){this.loginForm=this.formBuilder.group({species:["",[u.kI.required]],size:["",[u.kI.required]],breed:["",[u.kI.required]],code:["",[u.kI.required]]})}},{key:"f",get:function(){return this.loginForm.controls}},{key:"AddBreeding",value:function(){var e=this;this.submitted=!0,this.loginForm.invalid||this.BreedingService.NewBreeding({name:this.loginForm.value.breed,type:this.loginForm.value.species,code:this.loginForm.value.code,size:this.loginForm.value.size}).subscribe(function(i){e.submitted=!1,e.loginForm.reset(),e.primaryModal.hide(),e.GetTypeLists(),e.GetBreedingLists()})}},{key:"GetBreeding",value:function(e){console.log("id",e),this.delete_id=e,this.breeding=this.Breedings[e],this.f.species.setValue(this.Breedings[e].type,{onlySelf:!0}),this.f.size.setValue(this.Breedings[e].size,{onlySelf:!0}),this.f.breed.setValue(this.Breedings[e].name,{onlySelf:!0}),this.f.code.setValue(this.Breedings[e].code,{onlySelf:!0})}},{key:"EditBreeding",value:function(e){var i=this;this.submitted=!0,this.loginForm.invalid||this.BreedingService.UpdateBreeding(e,{name:this.loginForm.value.breed,type:this.loginForm.value.species,code:this.loginForm.value.code,size:this.loginForm.value.size}).subscribe(function(e){i.clear(),i.deleteModal.hide(),i.GetBreedingLists()})}},{key:"DeleteBreeding",value:function(){var e=this;this.BreedingService.DeleteBreeding(this.delete_id).subscribe(function(i){e.removeModal.hide(),e.GetBreedingLists()})}},{key:"changed",value:function(e,i){this.BreedingService.UpdateBreeding(i,{status:e}).subscribe(function(e){})}},{key:"Field",value:function(e){1==this.sort?(this.sort=!1,this.field=e,this.value=-1,this.GetBreedingLists()):(this.sort=!0,this.field=e,this.value=1,this.GetBreedingLists())}}]),i}();return i.\u0275fac=function(e){return new(e||i)(l.Y36(o.s),l.Y36(d.l),l.Y36(c.gz),l.Y36(c.F0),l.Y36(g.i),l.Y36(a.$),l.Y36(s.d),l.Y36(u.qu))},i.\u0275cmp=l.Xpm({type:i,selectors:[["app-breeding"]],viewQuery:function(e,i){var t;(1&e&&(l.Gf(m,1),l.Gf(v,1),l.Gf(h,1)),2&e)&&(l.iGM(t=l.CRH())&&(i.primaryModal=t.first),l.iGM(t=l.CRH())&&(i.deleteModal=t.first),l.iGM(t=l.CRH())&&(i.removeModal=t.first))},decls:139,vars:53,consts:[[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-body"],[1,"col-lg-12","my-3"],["type","button","class","btn btn-primary mr-1","data-toggle","modal",3,"click",4,"ngIf"],[1,"form-group","table-search"],[1,"input-group",2,"top","3px"],[1,"input-group-prepend"],[1,"input-group-text",3,"Change"],[1,"fa","fa-search"],["type","text","id","Search","name","Search","placeholder","Search","autocomplete","off",1,"form-control",3,"ngModel","keydown.enter","input","ngModelChange"],[1,"table","table-striped"],["checked","sort",1,"fa","fa-sort",3,"click"],[4,"ngFor","ngForOf"],["id","listing_pagination","maxSize","5","directionLinks","true",2,"text-align","right",3,"pageChange"],["bsModal","","id","myModal","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["primaryModal","bs-modal"],["role","document",1,"modal-dialog","modal-primary"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"modal-body"],["ngNativeValidate",""],[1,"col-sm-12"],["autocomplete","off",1,"form",3,"formGroup"],[1,"form-group"],["for","species"],["id","species","name","species","formControlName","species",1,"form-control",3,"ngClass"],["value","","disabled","","selected","","hidden",""],[3,"value",4,"ngFor","ngForOf"],["class","invalid-feedback",4,"ngIf"],["for","size"],["id","size","name","size","formControlName","size",1,"form-control",3,"ngClass"],["for","breed"],["type","text","id","breed","placeholder","e.g. Beagle, Boxer","formControlName","breed",1,"form-control",3,"ngClass"],["for","code"],["type","text","id","code","placeholder","e.g. BEAGLE, BOXER","formControlName","code",1,"form-control",3,"ngClass"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","submit",1,"btn","btn-primary",3,"click"],["deleteModal","bs-modal"],["name","species","formControlName","species",1,"form-control",3,"ngClass"],["hidden",""],["name","size","formControlName","size",1,"form-control",3,"ngClass"],["type","text","placeholder","e.g. Beagle, Boxer","formControlName","breed",1,"form-control",3,"ngClass"],["type","text","placeholder","e.g. BEAGLE, BOXER","formControlName","code",1,"form-control",3,"ngClass"],["type","button",1,"btn","btn-primary",3,"click"],["bsModal","","tabindex","-1","role","dialog","aria-labelledby","myModalLabel","aria-hidden","true",1,"modal","fade",3,"config"],["removeModal","bs-modal"],["role","document",1,"modal-dialog","modal-danger","modal-sm"],["type","button",1,"btn","btn-danger",3,"click"],["type","button","data-toggle","modal",1,"btn","btn-primary","mr-1",3,"click"],["data-toggle","modal","style","cursor: pointer;",3,"click",4,"ngIf"],["data-toggle","modal",2,"cursor","pointer",3,"click"],[1,"badge","badge-success"],[1,"fa","fa-edit"],[1,"badge","badge-danger"],[1,"fa","fa-trash"],[3,"value"],[1,"invalid-feedback"],[4,"ngIf"]],template:function(e,i){if(1&e){var t=l.EpF();l.TgZ(0,"div",0),l.TgZ(1,"div",1),l.TgZ(2,"div",2),l.TgZ(3,"div",3),l._uU(4," Breed "),l.qZA(),l.TgZ(5,"div",4),l.TgZ(6,"div",0),l.TgZ(7,"div",5),l.YNc(8,q,2,0,"button",6),l.TgZ(9,"div",7),l.TgZ(10,"div",8),l.TgZ(11,"div",9),l.TgZ(12,"span",10),l.NdJ("Change",function(){return i.GetBreedingLists()}),l._UZ(13,"i",11),l.qZA(),l.qZA(),l.TgZ(14,"input",12),l.NdJ("keydown.enter",function(){return i.GetBreedingLists()})("input",function(){return i.GetBreedingLists()})("ngModelChange",function(e){return i.name=e}),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(15,"table",13),l.TgZ(16,"thead"),l.TgZ(17,"tr"),l.TgZ(18,"th"),l._uU(19,"Breed Name "),l.TgZ(20,"i",14),l.NdJ("click",function(){return i.Field("name")}),l.qZA(),l.qZA(),l.TgZ(21,"th"),l._uU(22,"Breed Code "),l.TgZ(23,"i",14),l.NdJ("click",function(){return i.Field("code")}),l.qZA(),l.qZA(),l.TgZ(24,"th"),l._uU(25,"Size "),l.TgZ(26,"i",14),l.NdJ("click",function(){return i.Field("size")}),l.qZA(),l.qZA(),l.TgZ(27,"th"),l._uU(28,"Species "),l.TgZ(29,"i",14),l.NdJ("click",function(){return i.Field("type")}),l.qZA(),l.qZA(),l.TgZ(30,"th"),l._uU(31,"Action"),l.qZA(),l.qZA(),l.qZA(),l.TgZ(32,"tbody"),l.YNc(33,b,12,6,"tr",15),l.ALo(34,"paginate"),l.qZA(),l.qZA(),l.TgZ(35,"div"),l.TgZ(36,"pagination-controls",16),l.NdJ("pageChange",function(e){return i.handlePageChange(e)}),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(37,"div",17,18),l.TgZ(39,"div",19),l.TgZ(40,"div",20),l.TgZ(41,"div",21),l.TgZ(42,"h4",22),l._uU(43,"Add Breed"),l.qZA(),l.qZA(),l.TgZ(44,"div",23),l.TgZ(45,"form",24),l.TgZ(46,"div",0),l.TgZ(47,"div",25),l.TgZ(48,"form",26),l.TgZ(49,"div",27),l.TgZ(50,"label",28),l._uU(51,"Species*"),l.qZA(),l.TgZ(52,"select",29),l.TgZ(53,"option",30),l._uU(54,"--Select--"),l.qZA(),l.YNc(55,y,2,2,"option",31),l.qZA(),l.YNc(56,_,2,1,"div",32),l.qZA(),l.TgZ(57,"div",27),l.TgZ(58,"label",33),l._uU(59,"Size*"),l.qZA(),l.TgZ(60,"select",34),l.TgZ(61,"option",30),l._uU(62,"--Select--"),l.qZA(),l.YNc(63,k,2,2,"option",31),l.qZA(),l.YNc(64,M,2,1,"div",32),l.qZA(),l.TgZ(65,"div",27),l.TgZ(66,"label",35),l._uU(67,"Breed Name*"),l.qZA(),l._UZ(68,"input",36),l.YNc(69,N,2,1,"div",32),l.qZA(),l.TgZ(70,"div",27),l.TgZ(71,"label",37),l._uU(72,"Breed Code*"),l.qZA(),l._UZ(73,"input",38),l.YNc(74,B,2,1,"div",32),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(75,"div",39),l.TgZ(76,"button",40),l.NdJ("click",function(){return l.CHM(t),l.MAs(38).hide(),i.clear()}),l._uU(77,"Cancel"),l.qZA(),l.TgZ(78,"button",41),l.NdJ("click",function(){return i.AddBreeding()}),l._uU(79,"Save"),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(80,"div",17,42),l.TgZ(82,"div",19),l.TgZ(83,"div",20),l.TgZ(84,"div",21),l.TgZ(85,"h4",22),l._uU(86,"Edit Breed"),l.qZA(),l.qZA(),l.TgZ(87,"div",23),l.TgZ(88,"div",0),l.TgZ(89,"div",25),l.TgZ(90,"form",26),l.TgZ(91,"div",27),l.TgZ(92,"label",28),l._uU(93,"Species*"),l.qZA(),l.TgZ(94,"select",43),l.TgZ(95,"option",44),l._uU(96,"--Select--"),l.qZA(),l.YNc(97,F,2,2,"option",31),l.qZA(),l.YNc(98,Q,2,1,"div",32),l.qZA(),l.TgZ(99,"div",27),l.TgZ(100,"label",33),l._uU(101,"Size*"),l.qZA(),l.TgZ(102,"select",45),l.TgZ(103,"option",30),l._uU(104,"--Select--"),l.qZA(),l.YNc(105,w,2,2,"option",31),l.qZA(),l.YNc(106,Y,2,1,"div",32),l.qZA(),l.TgZ(107,"div",27),l.TgZ(108,"label",35),l._uU(109,"Breed Name*"),l.qZA(),l._UZ(110,"input",46),l.YNc(111,L,2,1,"div",32),l.qZA(),l.TgZ(112,"div",27),l.TgZ(113,"label",37),l._uU(114,"Breed Code*"),l.qZA(),l._UZ(115,"input",47),l.YNc(116,z,2,1,"div",32),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(117,"div",39),l.TgZ(118,"button",40),l.NdJ("click",function(){return l.CHM(t),l.MAs(81).hide(),i.clear()}),l._uU(119,"Cancel"),l.qZA(),l.TgZ(120,"button",48),l.NdJ("click",function(){return i.EditBreeding(i.breeding._id)}),l._uU(121,"Save"),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(122,"div",49,50),l.TgZ(124,"div",51),l.TgZ(125,"div",20),l.TgZ(126,"div",21),l.TgZ(127,"h4",22),l._uU(128,"Are you sure ?"),l.qZA(),l.qZA(),l.TgZ(129,"div",23),l.TgZ(130,"div",0),l.TgZ(131,"div",25),l.TgZ(132,"p"),l._uU(133,"Do you want to delete this Breed?"),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.TgZ(134,"div",39),l.TgZ(135,"button",40),l.NdJ("click",function(){return l.CHM(t),l.MAs(123).hide(),i.clear()}),l._uU(136,"Cancel"),l.qZA(),l.TgZ(137,"button",52),l.NdJ("click",function(){return i.DeleteBreeding()}),l._uU(138,"Delete"),l.qZA(),l.qZA(),l.qZA(),l.qZA(),l.qZA()}2&e&&(l.xp6(8),l.Q6J("ngIf",i.Add),l.xp6(6),l.Q6J("ngModel",i.name),l.xp6(19),l.Q6J("ngForOf",l.xi3(34,28,i.Breedings,l.WLB(31,D,i.page,i.count))),l.xp6(4),l.Q6J("config",l.DdM(34,O)),l.xp6(11),l.Q6J("formGroup",i.loginForm),l.xp6(4),l.Q6J("ngClass",l.VKq(35,H,i.submitted&&i.f.species.errors)),l.xp6(3),l.Q6J("ngForOf",i.Types),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.species.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(37,H,i.submitted&&i.f.size.errors)),l.xp6(3),l.Q6J("ngForOf",i.Size),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.size.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(39,H,i.submitted&&i.f.breed.errors)),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.breed.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(41,H,i.submitted&&i.f.code.errors)),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.code.errors),l.xp6(6),l.Q6J("config",l.DdM(43,O)),l.xp6(10),l.Q6J("formGroup",i.loginForm),l.xp6(4),l.Q6J("ngClass",l.VKq(44,H,i.submitted&&i.f.species.errors)),l.xp6(3),l.Q6J("ngForOf",i.Types),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.species.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(46,H,i.submitted&&i.f.size.errors)),l.xp6(3),l.Q6J("ngForOf",i.Size),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.size.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(48,H,i.submitted&&i.f.breed.errors)),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.breed.errors),l.xp6(4),l.Q6J("ngClass",l.VKq(50,H,i.submitted&&i.f.code.errors)),l.xp6(1),l.Q6J("ngIf",i.submitted&&i.f.code.errors),l.xp6(6),l.Q6J("config",l.DdM(52,O)))},directives:[Z.O5,u.Fj,u.JJ,u.On,Z.sg,f.LS,p.oB,u.JL,u.F,u.vK,u.sg,u.EJ,u.u,Z.mk,u.YN,u.ks],pipes:[f._s],styles:["#select1[_ngcontent-%COMP%]{width:100%}"]}),i}()}}])}();