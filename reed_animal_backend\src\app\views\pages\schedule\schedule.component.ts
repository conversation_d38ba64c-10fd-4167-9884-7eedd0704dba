import { Component, OnInit, } from '@angular/core';
import * as moment from 'moment';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { AppointmentService } from '../../services/appointments.services';

@Component({
  selector: 'app-schedule',
  templateUrl: './schedule.component.html',
  styleUrls: ['./schedule.component.scss']
})
export class ScheduleComponent implements OnInit {
  //SUNDAY
  Doctors = []
  containers = {
    fri: { flag: false, time: [], slot: [] },
    mon: { flag: false, time: [], slot: [] },
    sat: { flag: false, time: [], slot: [] },
    sun: { flag: false, time: [], slot: [] },
    thu: { flag: false, time: [], slot: [] },
    tue: { flag: false, time: [], slot: [] },
    wed: { flag: false, time: [], slot: [] },
    date: { time: [], slot: [] }
  };

  parameter = {
    date: []
  }
  minDate: Date;
  timepickerValue: any;
  Timeset = '9.00am'
  Timelap = '10.00am'
  option1 = false
  option2 = false
  option3 = false
  option4 = false
  option5 = false
  option6 = false
  data = []
  Sunday = false
  name = ''
  message1 = ''
  Add = false
  Edit = false
  Delete = false
  DoctorId = ""
  private Dynamic: any = {};
  SelectedDate = []
  dateSelected = [];
  selectedClass = [];
  showBottom = false
  myCSSclass = false;
  Field = false
  Selectedtime = []
  datas = false
  datas1 = false
  appointmentType = 'IN-PERSON';
  private updateTimeout: any;
  times = [{ value: "12:00am" }, { value: "12:30am" }, { value: "1:00am" }, { value: "1:30am" }, { value: "2:00am" }, { value: "2:30am" }, { value: "3:00am" }, { value: "3:30am" }, { value: "4:00am" }, { value: "4:30am" }, { value: "5:00am" }, { value: "5:30am" }, { value: "6:00am" }, { value: "6:30am" }, { value: "7:00am" }, { value: "7:30am" }, { value: "8:00am" }, { value: "8:30am" }, { value: "9:00am" }, { value: "9:30am" }, { value: "10:00am" }, { value: "10:30am" }, { value: "11:00am" }, { value: "11:30am" }, { value: "12:00pm" }, { value: "12:30pm" }, { value: "1:00pm" }, { value: "1:30pm" }, { value: "2:00pm" }, { value: "2:30pm" }, { value: "3:00pm" }, { value: "3:30pm" }, { value: "4:00pm" }, { value: "4:30pm" }, { value: "5:00pm" }, { value: "5:30pm" }, { value: "6:00pm" }, { value: "6:30pm" }, { value: "7:00pm" }, { value: "7:30pm" }, { value: "8:00pm" }, { value: "8:30pm" }, { value: "9:00pm" }, { value: "9:30pm" }, { value: "10:00pm" }, { value: "10:30pm" }, { value: "11:00pm" }, { value: "11:30pm" }];
  select = [];
  DoctorDrop = false
  addbind = false
  disabledDates = ['Thu Apr 08 2021 17:34:19 GMT+0530 (India Standard Time)']
  constructor(private tokenStorage: TokenStorageService, private Permission: PermissionService, private EmployeeService: Employeeservice, private route: ActivatedRoute, private router: Router, private AppointmentService: AppointmentService,) {
    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate() + 1);
  }

  ngOnInit() {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // console.log(Role.role_id._id)
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Schedule") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
        this.GetDoctorLists();

      })
  }

  //Get All Doctor list
  GetDoctorLists() {
    const data = {
      search: '',
      limit: 1000
    }
    this.AppointmentService.GetDoctorsList(data)
      .subscribe((res: any) => {
        // console.log(res)
        const Role = this.tokenStorage.getUser();
        if (Role.role_id.name == "Doctor") {
          this.DoctorId = Role._id
          // console.log(this.Doctors)
          this.DoctorDrop = false
          this.GetDoctorDetails()
        } else {
          var arr = []
          for (var i = 0; i < res.data.length; i++) {
            if (res.data[i].role_id.name == "Doctor" && res.data[i].status == true) {
              arr.push(res.data[i])
            }
          }
          this.Doctors = arr
          // console.log(this.Doctors)
          this.DoctorId = arr[0]._id
          this.DoctorDrop = true
          this.GetDoctorDetails()

        }

      })
  }

  //Get particular doctor schedule by using doctor id
  GetDoctorDetails() {
    this.AppointmentService.GetDoctor(this.DoctorId).subscribe((res: any) => {
      this.containers = res.data[0]
      const arr = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
      for (var i = 0; i < arr.length; i++) {
        // Set flag to true if there are existing time slots
        if (this.containers[arr[i]].time && this.containers[arr[i]].time.length > 0) {
          this.containers[arr[i]].flag = true;
        }
        
        for (var j = 0; j < this.containers[arr[i]].time.length; j++) {
          this.containers[arr[i]].time[j].error = false
          // Migrate legacy data: if no mode is set, default to IN-PERSON
          if (!this.containers[arr[i]].time[j].mode) {
            this.containers[arr[i]].time[j].mode = 'IN-PERSON';
          }
        }
      }

      var arr1 = this.containers.date.time.filter(function (a, b) {
        var datee = moment(Object.keys(a)[0], 'MM/DD/YYYY')
        var timee = moment().startOf('day')
        return timee.isSameOrBefore(datee)
      })
      this.containers.date.time = arr1
      
      // Migrate legacy date override data
      for (var i = 0; i < this.containers.date.time.length; i++) {
        const dateKey = Object.keys(this.containers.date.time[i])[0];
        const timeSlots = this.containers.date.time[i][dateKey];
        if (Array.isArray(timeSlots)) {
          for (var j = 0; j < timeSlots.length; j++) {
            if (!timeSlots[j].mode) {
              timeSlots[j].mode = 'IN-PERSON';
            }
          }
        }
      }
      
      // Ensure flags are consistent after loading data
      this.ensureFlagsConsistency();
      
      // console.log(this.containers)
    })
  }

  //Clicked from dropdown list 
  Doctor(i) {
    this.DoctorId = i
    this.GetDoctorDetails()
  }

  //Update the Doctor schedule with debouncing
  UpdateDoctorschedule() {
    console.log('UpdateDoctorschedule called with data:', this.containers);
    console.log('DoctorId:', this.DoctorId);
    console.log('Auth token:', localStorage.getItem('auth_token'));
    
    // Check if DoctorId is valid
    if (!this.DoctorId) {
      console.error('DoctorId is not set, cannot update schedule');
      return;
    }
    
    // Check if auth token is valid
    if (!localStorage.getItem('auth_token')) {
      console.error('Auth token is not set, cannot update schedule');
      return;
    }
    
    // Clear any existing timeout
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
    }
    
    // Set a new timeout to delay the API call
    this.updateTimeout = setTimeout(() => {
      console.log('Calling API to update schedule...');
      this.AppointmentService.UpdateDoctor(this.DoctorId, this.containers).subscribe((res: any) => {
        // Handle success - no page refresh needed
        console.log('Schedule updated successfully:', res);
      }, (error) => {
        // Handle error - no page refresh needed
        console.error('Error updating schedule:', error);
      });
    }, 1000); // 1 second delay
  }

  //Add Div time 
  add(param) {
    var now = moment(this.Timeset, 'LT').add(moment.duration())
    var end = moment(this.Timelap, 'LT').add(moment.duration())
    if (this.containers[param].time.length == 0) {
      this.containers[param].time.length = 0
      this.containers[param].slot.length = 0
      // default start in 9am to 5pm
      this.Dynamic = { from: moment(now).format('h:mma'), to: moment(end).format('h:mma'), error: false, message: '', mode: this.appointmentType };
      this.containers[param].time.push(this.Dynamic);
      this.containers[param].flag = true;
    } else {
      if (this.containers[param].time[this.containers[param].time.length - 1].to == '11:00pm') {
        //get last index end time
        var now1 = moment(this.containers[param].time[this.containers[param].time.length - 1].to, 'LT').add(moment.duration('hours'))
        //add last index end time plus one hour
        var end1 = moment(this.containers[param].time[this.containers[param].time.length - 1].to, 'LT').add(moment.duration(30, 'minutes'))
        this.Dynamic = { from: moment(now1).format('h:mma'), to: moment(end1).format('h:mma'), error: false, message: '', mode: this.appointmentType };
        this.containers[param].time.push(this.Dynamic);
        this.containers[param].flag = true;
      }
      else if (this.containers[param].time[this.containers[param].time.length - 1].to != '11:30pm') {
        //get last index end time
        var now1 = moment(this.containers[param].time[this.containers[param].time.length - 1].to, 'LT').add(moment.duration('hours'))
        //add last index end time plus one hour
        var end1 = moment(this.containers[param].time[this.containers[param].time.length - 1].to, 'LT').add(moment.duration(1, 'hours'))
        this.Dynamic = { from: moment(now1).format('h:mma'), to: moment(end1).format('h:mma'), error: false, message: '', mode: this.appointmentType };
        this.containers[param].time.push(this.Dynamic);
        this.containers[param].flag = true;
      }
    }
    this.UpdateDoctorschedule()
  }

  //Delete Div time
  delete(param, index) {
    this.containers[param].time.splice(index, 1);
    
    // Check if there are any visible time slots for current appointment type
    const visibleSlots = this.containers[param].time.filter(slot => {
      if (!slot.mode) {
        return this.appointmentType === 'IN-PERSON';
      }
      return slot.mode === this.appointmentType;
    });
    
    if (visibleSlots.length == 0) {
      this.containers[param].flag = false;
    }
    // console.log(this.containers)
    this.UpdateDoctorschedule()
  }

  //CheckBox
  point(value, param) {
    // console.log(value, param)
    // this.containers[param].flag = true;
    if (value == true) {
      this.containers[param].flag = true
      if (this.containers[param].time.length == 0) {
        this.add(param)
      }
    } else {
      this.containers[param].flag = false
    }


    this.UpdateDoctorschedule()
  }

  //Copy time Array
  Selectall(param) {
    var a = this.select.indexOf(param);
    if (a == -1) {
      this.select.push(param)
    } else {
      this.select.splice(a, 1);
    }
    // console.log(a, this.select)

    this.UpdateDoctorschedule()
  }

  //Copy the day time and paste clicked days
  PushAll(param) {
    this.data = this.containers[param].time
    // var Slots = this.containers[param].slot
    for (var i = 0; i < this.select.length; i++) {
      var par = this.select[i];
      this.containers[par].slot.length = 0
      for (var j = 0; j < this.data.length; j++) {
        if (j == 0) {
          this.containers[par].time.length = 0
          this.containers[par].time.push({ from: this.data[j].from, to: this.data[j].to, mode: this.data[j].mode || this.appointmentType })
          this.containers[par].flag = true;
        } else {
          this.containers[par].time.push({ from: this.data[j].from, to: this.data[j].to, mode: this.data[j].mode || this.appointmentType })
          this.containers[par].flag = true;
        }
      }
      // for (var k = 0; k < Slots.length; k++) {
      //   this.containers[par].slot.push(Slots[k])
      // }
    }

    this.select.length = 0
    this.option1 = false
    this.option2 = false
    this.option3 = false
    this.option4 = false
    this.option5 = false
    this.option6 = false
    // console.log(this.containers)

    this.UpdateDoctorschedule()
  }

  Before(value1, end) {
    return value1.isBefore(end)
  }
  After(value1, start) {
    return value1.isAfter(start)
  }
  Between(value1, start, end) {
    return value1.isBetween(start, end)
  }
  Issame(value1, start) {
    return value1.isSame(start)
  }

  //edit the existing time slot
  searched(event: any, index: number, param: string, field: string) {
    // Extract value from event
    const value = event?.target?.value || event;
    
    console.log('searched called with:', { value, index, param, field, event });
    
    // Reset validation flags at the start
    this.datas = false;
    this.datas1 = false;
    
    // debugger;
    // console.log(this.containers)
    this.containers[param].time[index][field] = value
    for (var i = 0; i < this.containers[param].time.length; i++) {

      var from = this.containers[param].time[i].from
      var to = this.containers[param].time[i].to
      var start = moment(from, 'hh:mma')//Dynamic change from by using i
      var end = moment(to, 'hh:mma')//Dynamic change to by using i

      var Current_from = this.containers[param].time[index].from
      var Current_to = this.containers[param].time[index].to
      var current_start = moment(Current_from, 'hh:mma')//Current from value by using selected index from html page 
      var current_end = moment(Current_to, 'hh:mma')//Current to value by using selected index from html page

      var value1 = moment(value, 'hh:mma')

      var before = this.Before(value1, start)
      var after = this.After(value1, end)

      var tosame = this.Issame(value1, end)
      var Fromsame = this.Issame(value1, start)

      var between = this.Between(value1, start, end)
      // console.log(start, value1, current_end)

      var FromBetween = this.Between(start, value1, current_end)
      var ToBetween = this.Between(end, current_start, value1)

      if (field == 'from') {
        if (i != index) {
          if (Fromsame == true || between == true || ToBetween == true || FromBetween == true) {
            var conflictingMode = this.containers[param].time[i].mode || 'IN-PERSON';
            var currentMode = this.containers[param].time[index].mode || 'IN-PERSON';
            
            if (conflictingMode !== currentMode) {
              this.containers[param].time[index].message = `Time conflicts with existing ${conflictingMode} appointment.`;
            } else {
              this.containers[param].time[index].message = "Times overlap with another set of times.";
            }
            this.containers[param].time[index].error = true
            this.datas = true
            break;
          } else {
            this.containers[param].time[index].error = false
            this.datas = false
          }
        } else {
          if (tosame == true || after == true) {
            if (tosame == true) {
              this.containers[param].time[index].message = "Choose an start time not equal to the end time."
              this.containers[param].time[index].error = true
              this.datas = true
              break;
            } else if (after == true) {
              this.containers[param].time[index].message = "Choose an start time later than the end time."
              this.containers[param].time[index].error = true
              this.datas = true
              break;
            }
          } else {
            this.containers[param].time[index].error = false
            this.datas = false
          }
        }
      } else {
        if (i != index) {
          if (tosame == true || between == true || FromBetween == true || ToBetween == true) {
            var conflictingMode = this.containers[param].time[i].mode || 'IN-PERSON';
            var currentMode = this.containers[param].time[index].mode || 'IN-PERSON';
            
            if (conflictingMode !== currentMode) {
              this.containers[param].time[index].message = `Time conflicts with existing ${conflictingMode} appointment.`;
            } else {
              this.containers[param].time[index].message = "Times overlap with another set of times.";
            }
            this.containers[param].time[index].error = true
            this.datas1 = true
            break;
          } else {
            this.containers[param].time[index].error = false
            this.containers[param].time[index][field] = value
            this.datas1 = false
          }
        } else {
          if (Fromsame == true || before == true) {
            if (Fromsame == true) {
              this.containers[param].time[index].message = "Choose an end time not equal to the start time."
              this.containers[param].time[index].error = true
              this.datas1 = true
              break;
            } else if (before == true) {
              this.containers[param].time[index].message = "Choose an end time later than the start time."
              this.containers[param].time[index].error = true
              this.datas1 = true
              break;
            }
          } else {
            this.containers[param].time[index].error = false
            this.containers[param].time[index][field] = value
            this.datas1 = false
          }
        }
      }
    }
    // console.log(this.containers)
    
    // Ensure flag is consistent after updating time slots
    const visibleSlots = this.containers[param].time.filter(slot => {
      if (!slot.mode) {
        return this.appointmentType === 'IN-PERSON';
      }
      return slot.mode === this.appointmentType;
    });
    
    // Set flag to true if there are visible time slots
    this.containers[param].flag = visibleSlots.length > 0;
    
    // Only call UpdateDoctorschedule when there are no validation errors
    if (this.datas == false && this.datas1 == false) {
      this.UpdateDoctorschedule()
    }
  }

  getDateItem(date: Date): string {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
  }

  onValueChange(event) {
    if (event.length === undefined) {
      const date = this.getDateItem(event);

      const index = this.dateSelected.findIndex(item => {
        const testDate = this.getDateItem(item);
        return testDate === date;
      });

      // console.log('Date', date, index, event);

      if (index < 0) {
        this.showBottom = true
        this.dateSelected.push(event);
        // console.log(this.dateSelected)
        var data = moment(event, 'MM/DD/YYYY')
        var dates = data.format('MM/DD/YYYY')
        this.SelectedDate.push(dates);
        if (this.Selectedtime.length != 0) {
          this.Field = true
        }
        if (this.SelectedDate.length == 1) {
          var cont: number
          for (var x = 0; x < this.containers.date.time.length; x++) {
            var a = Object.keys(this.containers.date.time[x])
            if (a[0] == dates) {
              cont = x
            }
          }
          // console.log(cont)
          if (cont == undefined) {
            this.Field = true;
            this.showBottom = true;
            this.AddOver()

          } else {
            const date = Object.keys(this.containers.date.time[cont])
            for (var j = 0; j < Object.values(this.containers.date.time[cont][date[0]]).length; j++) {
              const timeSlot = this.containers.date.time[cont][date[0]][j];
              // Ensure mode is set for legacy data
              if (!timeSlot.mode) {
                timeSlot.mode = 'IN-PERSON';
              }
              this.Selectedtime.push(timeSlot)
            }
            this.showBottom = true;
            this.Field = true;
          }
        }
      }
      else {
        this.dateSelected.splice(index, 1);
        this.SelectedDate.splice(index, 1);
        if (this.dateSelected.length == 0) {
          this.showBottom = false
          this.Selectedtime.length = 0
        }
      }
    }


    if (this.dateSelected.length > 0) {
      this.selectedClass = this.dateSelected.map(date => {
        return {
          date,
          classes: ['custom-selected-date']
        }
      })
    }



  }

  AddOver() {
    //Add Div time 
    var now = moment(this.Timeset, 'LT').add(moment.duration())
    var end = moment(this.Timelap, 'LT').add(moment.duration())
    if (this.Selectedtime.length == 0) {
      // default start in 9am to 5pm
      this.Dynamic = { from: moment(now).format('h:mma'), to: moment(end).format('h:mma'), error: false, message: '', mode: this.appointmentType };
      this.Selectedtime.push(this.Dynamic);
      this.Field = true
    } else {
      if (this.Selectedtime[this.Selectedtime.length - 1].to == '11:00pm') {
        //get last index end time
        var now1 = moment(this.Selectedtime[this.Selectedtime.length - 1].to, 'LT').add(moment.duration('hours'))
        //add last index end time plus one hour
        var end1 = moment(this.Selectedtime[this.Selectedtime.length - 1].to, 'LT').add(moment.duration(30, 'minutes'))
        this.Dynamic = { from: moment(now1).format('h:mma'), to: moment(end1).format('h:mma'), error: false, message: '', mode: this.appointmentType };
        this.Selectedtime.push(this.Dynamic);
        this.Field = true
      }
      if (this.Selectedtime[this.Selectedtime.length - 1].to != '11:30pm') {
        //get last index end time
        var now1 = moment(this.Selectedtime[this.Selectedtime.length - 1].to, 'LT').add(moment.duration('hours'))
        //add last index end time plus one hour
        var end1 = moment(this.Selectedtime[this.Selectedtime.length - 1].to, 'LT').add(moment.duration(1, 'hours'))
        this.Dynamic = { from: moment(now1).format('h:mma'), to: moment(end1).format('h:mma'), error: false, message: '', mode: this.appointmentType };
        this.Selectedtime.push(this.Dynamic)
        this.Field = true
      }
    }
    // console.log(this.Selectedtime)
  }

  DeleteTime(index) {
    // console.log(index)
    this.Selectedtime.splice(index, 1);
    if (this.Selectedtime.length == 0) {
      this.Field = false

    }
  }

  //edit the existing time slot
  Datesearched(event: any, index: number, field: string) {
    console.log("day")
    // Extract value from event
    const value = event?.target?.value || event;
    
    console.log('Datesearched called with:', { value, index, field, event });
    
    // Reset validation flags at the start
    this.datas = false;
    this.datas1 = false;
    
    this.Selectedtime[index][field] = value
    for (var i = 0; i < this.Selectedtime.length; i++) {

      var from = this.Selectedtime[i].from
      var to = this.Selectedtime[i].to
      var start = moment(from, 'hh:mma')
      var end = moment(to, 'hh:mma')

      var Current_from = this.Selectedtime[index].from
      var Current_to = this.Selectedtime[index].to
      var current_start = moment(Current_from, 'hh:mma')
      var current_end = moment(Current_to, 'hh:mma')

      var value1 = moment(value, 'hh:mma')

      var before = this.Before(value1, start)

      var after = this.After(value1, end)
      var tosame = this.Issame(value1, end)

      var Fromsame = this.Issame(value1, start)
      var between = this.Between(value1, start, end)

      var FromBetween = this.Between(start, value1, current_end)
      var ToBetween = this.Between(end, current_start, value1)

      if (field == 'from') {
        if (i != index) {
          // console.log('i-->', i, Fromsame, between)
          if (Fromsame == true || between == true || ToBetween == true || FromBetween == true) {
            var conflictingMode = this.Selectedtime[i].mode || 'IN-PERSON';
            var currentMode = this.Selectedtime[index].mode || 'IN-PERSON';
            
            if (conflictingMode !== currentMode) {
              this.Selectedtime[index].message = `Time conflicts with existing ${conflictingMode} appointment.`;
            } else {
              this.Selectedtime[index].message = "Times overlap with another set of times.";
            }
            this.datas = true
            this.Selectedtime[index].error = true
            this.datas = true
            break;
          } else {
            this.Selectedtime[index].error = false
          }
        } else {
          // console.log('i-->', i, tosame, after)
          if (tosame == true || after == true || before == true) {
            if (tosame == true) {
              this.Selectedtime[index].message = "Choose an start time not equal to the end time."
              this.Selectedtime[index].error = true
              this.datas = true
              break;
            } else if (after == true) {
              this.Selectedtime[index].message = "Choose an end time later than the start time."
              this.Selectedtime[index].error = true
              this.datas = true
              break;
            }
          } else {
            this.Selectedtime[index].error = false
          }
        }
      } else {
        if (i != index) {
          // console.log('i-->', i, tosame, between)
          if (tosame == true || between == true || FromBetween == true || ToBetween == true) {
            var conflictingMode = this.Selectedtime[i].mode || 'IN-PERSON';
            var currentMode = this.Selectedtime[index].mode || 'IN-PERSON';
            
            if (conflictingMode !== currentMode) {
              this.Selectedtime[index].message = `Time conflicts with existing ${conflictingMode} appointment.`;
            } else {
              this.Selectedtime[index].error = true
            }
            this.Selectedtime[index].error = true
            this.datas1 = true
            break;
          } else {
            this.Selectedtime[index].error = false
          }
        } else {
          // console.log('i-->', i, Fromsame, after)
          if (Fromsame == true || before == true) {
            if (Fromsame == true) {
              this.Selectedtime[index].message = "Choose an start time not equal to the end time."
              this.Selectedtime[index].error = true
              this.datas1 = true
              break;
            } else if (before == true) {
              this.Selectedtime[index].message = "Choose an end time later than the start time."
              this.Selectedtime[index].error = true
              this.datas1 = true
              break;
            }
          } else {
            this.Selectedtime[index].error = false
          }
        }
      }
    }
    // console.log(this.containers)
    // console.log(this.Selectedtime)
    
    // Only call UpdateDoctorschedule when there are no validation errors
    if (this.datas == false && this.datas1 == false) {
      this.UpdateDoctorschedule()
    }
  }

  //Clear after click cancel button
  clear() {
    this.SelectedDate = []
    this.dateSelected = [];
    this.selectedClass = [];
    this.showBottom = false
    this.Field = false
    this.Selectedtime = []
  }

  //After click the submit button
  OnSubmit() {
    console.log('OnSubmit called, datas:', this.datas, 'datas1:', this.datas1);
    
    // Always proceed with saving date overrides, regardless of validation errors
    for (var i = 0; i < this.SelectedDate.length; i++) {
      for (var j = 0; j < this.containers.date.time.length; j++) {
        if (this.SelectedDate[i] == Object.keys(this.containers.date.time[j])) {
          this.containers.date.time.splice(j, 1)
          // console.log(j)
        }
      }
    }
    for (var i = 0; i < this.SelectedDate.length; i++) {
      const key = { [this.SelectedDate[i]]: this.Selectedtime }
      this.containers.date.time.push(key)

    }
    this.containers.date.time.sort(function (a, b) {
      var c = moment(Object.keys(a)[0]).unix();
      var d = moment(Object.keys(b)[0]).unix();
      return c - d;
    })

    this.clear();
    this.UpdateDoctorschedule()
  }

  //Delete Date in override
  DeleteDate(index) {
    // console.log(index)
    this.containers.date.time.splice(index, 1);
    this.UpdateDoctorschedule()
  }

  //Click the date in row
  modalClick(index) {
    this.SelectedDate = Object.keys(this.containers.date.time[index])
    this.dateSelected.push(new Date(this.SelectedDate[0]))
    this.selectedClass = this.dateSelected.map(date => {
      return {
        date,
        classes: ['custom-selected-date']
      }
    })
    // console.log(this.dateSelected)
    const date = Object.keys(this.containers.date.time[index])
    for (var j = 0; j < Object.values(this.containers.date.time[index][date[0]]).length; j++) {
      const timeSlot = this.containers.date.time[index][date[0]][j];
      // Ensure mode is set for legacy data
      if (!timeSlot.mode) {
        timeSlot.mode = 'IN-PERSON';
      }
      this.Selectedtime.push(timeSlot)
    }
    this.showBottom = true;
    this.Field = true;
    // console.log(this.Selectedtime)
  }

  // Check if current day has time slots for current appointment type
  hasTimeSlotsForCurrentMode(day: string): boolean {
    if (!this.containers[day] || !this.containers[day].time) return false;
    
    // If no time slots exist, return false
    if (this.containers[day].time.length === 0) return false;
    
    // Check if any slot matches current mode OR if slots don't have mode (legacy data)
    return this.containers[day].time.some(slot => {
      // If slot has no mode, treat it as IN-PERSON (legacy data)
      if (!slot.mode) {
        return this.appointmentType === 'IN-PERSON';
      }
      return slot.mode === this.appointmentType;
    });
  }

  // Get time slots filtered by current appointment type
  getTimeSlotsForCurrentMode(day: string): any[] {
    if (!this.containers[day] || !this.containers[day].time) return [];
    
    return this.containers[day].time.filter(slot => {
      // If slot has no mode, treat it as IN-PERSON (legacy data)
      if (!slot.mode) {
        return this.appointmentType === 'IN-PERSON';
      }
      return slot.mode === this.appointmentType;
    });
  }

  // Ensure flags are consistent with visible time slots
  private ensureFlagsConsistency() {
    const arr = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
    for (var i = 0; i < arr.length; i++) {
      const day = arr[i];
      if (this.containers[day] && this.containers[day].time) {
        // Check if there are any visible time slots for current appointment type
        const visibleSlots = this.containers[day].time.filter(slot => {
          if (!slot.mode) {
            return this.appointmentType === 'IN-PERSON';
          }
          return slot.mode === this.appointmentType;
        });
        
        // Set flag to true if there are visible time slots
        this.containers[day].flag = visibleSlots.length > 0;
      }
    }
  }

  // Handle appointment type change
  onAppointmentTypeChange(newType: string) {
    this.appointmentType = newType;
    
    // Ensure flags are properly set based on visible time slots
    this.ensureFlagsConsistency();
    
    // Force change detection to update the view
    this.containers = { ...this.containers };
  }
}

//
  // times = [{ value: "12:00am" }, { value: "12:15am" }, { value: "12:30am" }, { value: "12:45am" }, { value: "1:00am" }, { value: "1:15am" }, { value: "1:30am" }, { value: "1:45am" }, { value: "2:00am" }, { value: "2:15am" }, { value: "2:45am" }, { value: "3:00am" }, { value: "3:15am" }, { value: "3:30am" }, { value: "3:45am" }, { value: "4:00am" }, { value: "4:15am" }, { value: "4:30am" }, { value: "4:45am" }, { value: "5:00am" }, { value: "5:15am" }, { value: "5:30am" }, { value: "5:45am" }, { value: "6:00am" }, { value: "6:15am" }, { value: "6:30am" }, { value: "6:45am" }, { value: "7:00am" }, { value: "7:15am" }, { value: "7:30am" }, { value: "7:45am" }, { value: "8:00am" }, { value: "8:15am" }, { value: "8:30am" }, { value: "8:45am" }, { value: "9:00am" }, { value: "9:15am" }, { value: "9:30am" }, { value: "9:45am" }, { value: "10:00am" }, { value: "10:15am" }, { value: "10:30am" }, { value: "10:45am" }, { value: "11:00am" }, { value: "11:15am" }, { value: "11:30am" }, { value: "11:45am" }, { value: "12:00pm" }, { value: "12:15pm" }, { value: "12:30pm" }, { value: "12:45pm" }, { value: "1:00pm" }, { value: "1:15pm" }, { value: "1:30pm" }, { value: "1:45pm" }, { value: "2:00pm" }, { value: "2:15pm" }, { value: "2:45pm" }, { value: "3:00pm" }, { value: "3:15pm" }, { value: "3:30pm" }, { value: "3:45pm" }, { value: "4:00pm" }, { value: "4:15pm" }, { value: "4:30pm" }, { value: "4:45pm" }, { value: "5:00pm" }, { value: "5:15pm" }, { value: "5:30pm" }, { value: "5:45pm" }, { value: "6:00pm" }, { value: "6:15pm" }, { value: "6:30pm" }, { value: "6:45pm" }, { value: "7:00pm" }, { value: "7:15pm" }, { value: "7:30pm" }, { value: "7:45pm" }, { value: "8:00pm" }, { value: "8:15pm" }, { value: "8:30pm" }, { value: "8:45pm" }, { value: "9:00pm" }, { value: "9:15pm" }, { value: "9:30pm" }, { value: "9:45pm" }, { value: "10:00pm" }, { value: "10:15pm" }, { value: "10:30pm" }, { value: "10:45pm" }, { value: "11:00pm" }, { value: "11:15pm" }, { value: "11:30pm" }, { value: "11:45pm" },];
  // select = [];
