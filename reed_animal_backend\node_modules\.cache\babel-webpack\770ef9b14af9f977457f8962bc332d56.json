{"ast": null, "code": "import { __decorate, __param } from 'tslib';\nimport { ɵɵdefineInjectable, Injectable, Input, Component, Renderer2, ElementRef, Directive, NgModule, Optional, SkipSelf } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport classNames from 'classnames';\nimport { CommonModule } from '@angular/common';\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/platform-browser';\nimport * as ɵngcc2 from '@angular/common';\n\nfunction IconComponent_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"img\", 3);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"cHtmlAttr\", ctx_r0.attributes)(\"src\", ctx_r0.src, ɵngcc0.ɵɵsanitizeUrl);\n    ɵngcc0.ɵɵattribute(\"alt\", ctx_r0.title);\n  }\n}\n\nfunction IconComponent__svg_svg_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelementStart(0, \"svg\", 4);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"innerHtml\", ctx_r1.iconCode, ɵngcc0.ɵɵsanitizeHtml)(\"cHtmlAttr\", ctx_r1.attributes);\n    ɵngcc0.ɵɵattribute(\"width\", ctx_r1.width)(\"height\", ctx_r1.height || ctx_r1.width)(\"viewBox\", ctx_r1.viewBox)(\"class\", ctx_r1.computedClasses);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r1.titleCode, \"\\n\");\n  }\n}\n\nfunction IconComponent__svg_svg_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵnamespaceSVG();\n    ɵngcc0.ɵɵelementStart(0, \"svg\", 5);\n    ɵngcc0.ɵɵelement(1, \"use\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"cHtmlAttr\", ctx_r2.attributes);\n    ɵngcc0.ɵɵattribute(\"width\", ctx_r2.width)(\"height\", ctx_r2.height || ctx_r2.width)(\"class\", ctx_r2.computedClasses);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵattribute(\"href\", ctx_r2.use);\n  }\n}\n\nlet IconSetService = /*#__PURE__*/(() => {\n  let IconSetService = class IconSetService {\n    constructor() {\n      // tslint:disable-next-line:variable-name\n      this._icons = {};\n    }\n\n    set icons(iconSet) {\n      this._icons = iconSet;\n    }\n\n    get icons() {\n      return this._icons;\n    }\n\n    getIcon(name) {\n      const icon = this.icons[name];\n\n      if (!icon) {\n        console.warn(`CoreUI WARN: Icon ${name} is not registered in IconService`);\n      }\n\n      return this.icons[name];\n    }\n\n  };\n\n  IconSetService.ɵfac = function IconSetService_Factory(t) {\n    return new (t || IconSetService)();\n  };\n\n  IconSetService.ɵprov = ɵɵdefineInjectable({\n    factory: function IconSetService_Factory() {\n      return new IconSetService();\n    },\n    token: IconSetService,\n    providedIn: \"root\"\n  });\n  return IconSetService;\n})();\nlet IconComponent = /*#__PURE__*/(() => {\n  let IconComponent = class IconComponent {\n    constructor(sanitizer, iconSet) {\n      this.sanitizer = sanitizer;\n      this.iconSet = iconSet;\n      this.attributes = {\n        role: 'img'\n      };\n      this.size = '';\n      this.use = '';\n    }\n\n    set name(name) {\n      this._name = name;\n    }\n\n    get name() {\n      const nameIsKebabCase = this._name && this._name.includes('-');\n\n      return nameIsKebabCase ? this.toCamelCase(this._name) : this._name;\n    }\n\n    set viewBox(viewBox) {\n      this._viewBox = viewBox;\n    }\n\n    get viewBox() {\n      return this._viewBox || `0 0 ${this.scale}`;\n    }\n\n    get titleCode() {\n      return this.title ? `<title>${this.title}</title>` : '';\n    }\n\n    get code() {\n      if (this.content) {\n        return this.content;\n      } else if (this.iconSet) {\n        return this.iconSet.getIcon(this.name);\n      }\n\n      console.warn(`c-icon component: icon name '${this.name}' does not exist for IconSet service. ` + `To use icon by 'name' prop you need to add it to IconSet service. \\n`, this.name);\n      return undefined;\n    }\n\n    get iconCode() {\n      const code = Array.isArray(this.code) ? this.code[1] || this.code[0] : this.code;\n      return this.sanitizer.bypassSecurityTrustHtml(this.titleCode + code);\n    }\n\n    get scale() {\n      return Array.isArray(this.code) && this.code.length > 1 ? this.code[0] : '64 64';\n    }\n\n    get computedSize() {\n      const addCustom = !this.size && (this.width || this.height);\n      return this.size === 'custom' || addCustom ? 'custom-size' : this.size;\n    }\n\n    get computedClasses() {\n      const classes = classNames(this.customClasses, {\n        'c-icon': true,\n        [`c-icon-${this.computedSize}`]: !!this.computedSize\n      });\n      return classes;\n    }\n\n    toCamelCase(str) {\n      return str.replace(/([-_][a-z0-9])/ig, $1 => {\n        return $1.toUpperCase().replace('-', '');\n      });\n    }\n\n  };\n\n  IconComponent.ɵfac = function IconComponent_Factory(t) {\n    return new (t || IconComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc1.DomSanitizer), ɵngcc0.ɵɵdirectiveInject(IconSetService));\n  };\n\n  IconComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: IconComponent,\n    selectors: [[\"c-icon\"]],\n    inputs: {\n      attributes: \"attributes\",\n      size: \"size\",\n      use: \"use\",\n      name: \"name\",\n      viewBox: \"viewBox\",\n      content: \"content\",\n      src: \"src\",\n      title: \"title\",\n      customClasses: \"customClasses\",\n      width: \"width\",\n      height: \"height\"\n    },\n    decls: 3,\n    vars: 3,\n    consts: [[3, \"cHtmlAttr\", \"src\", 4, \"ngIf\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"role\", \"img\", \"pointer-events\", \"none\", 3, \"innerHtml\", \"cHtmlAttr\", 4, \"ngIf\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"role\", \"img\", \"pointer-events\", \"none\", 3, \"cHtmlAttr\", 4, \"ngIf\"], [3, \"cHtmlAttr\", \"src\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"role\", \"img\", \"pointer-events\", \"none\", 3, \"innerHtml\", \"cHtmlAttr\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"role\", \"img\", \"pointer-events\", \"none\", 3, \"cHtmlAttr\"]],\n    template: function IconComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, IconComponent_img_0_Template, 1, 3, \"img\", 0);\n        ɵngcc0.ɵɵtemplate(1, IconComponent__svg_svg_1_Template, 2, 7, \"svg\", 1);\n        ɵngcc0.ɵɵtemplate(2, IconComponent__svg_svg_2_Template, 2, 5, \"svg\", 2);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.src);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.src && !ctx.use);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.use);\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, HtmlAttributesDirective];\n    },\n    styles: [\".c-icon[_ngcontent-%COMP%]{display:inline-block;color:inherit;text-align:center;fill:currentColor}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size){width:1rem;height:1rem;font-size:1rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-2xl{width:2rem;height:2rem;font-size:2rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-3xl{width:3rem;height:3rem;font-size:3rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-4xl{width:4rem;height:4rem;font-size:4rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-5xl{width:5rem;height:5rem;font-size:5rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-6xl{width:6rem;height:6rem;font-size:6rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-7xl{width:7rem;height:7rem;font-size:7rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-8xl{width:8rem;height:8rem;font-size:8rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-9xl{width:9rem;height:9rem;font-size:9rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-xl{width:1.5rem;height:1.5rem;font-size:1.5rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-lg{width:1.25rem;height:1.25rem;font-size:1.25rem}.c-icon[_ngcontent-%COMP%]:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-sm{width:.875rem;height:.875rem;font-size:.875rem}.c-icon-c-s[_ngcontent-%COMP%], .c-icon-custom-size[_ngcontent-%COMP%]{width:initial!important;height:initial!important}\"]\n  });\n\n  __decorate([Input()], IconComponent.prototype, \"attributes\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"name\", null);\n\n  __decorate([Input()], IconComponent.prototype, \"content\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"size\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"src\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"title\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"use\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"customClasses\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"viewBox\", null);\n\n  __decorate([Input()], IconComponent.prototype, \"width\", void 0);\n\n  __decorate([Input()], IconComponent.prototype, \"height\", void 0);\n\n  return IconComponent;\n})();\nlet HtmlAttributesDirective = /*#__PURE__*/(() => {\n  let HtmlAttributesDirective = class HtmlAttributesDirective {\n    constructor(renderer, el) {\n      this.renderer = renderer;\n      this.el = el;\n    }\n\n    ngOnInit() {\n      const attribs = this.cHtmlAttr;\n\n      for (const attr in attribs) {\n        if (attr === 'style' && typeof attribs[attr] === 'object') {\n          this.setStyle(attribs[attr]);\n        } else if (attr === 'class') {\n          this.addClass(attribs[attr]);\n        } else {\n          this.setAttrib(attr, attribs[attr]);\n        }\n      }\n    }\n\n    setStyle(styles) {\n      // tslint:disable-next-line:forin\n      for (const style in styles) {\n        this.renderer.setStyle(this.el.nativeElement, style, styles[style]);\n      }\n    }\n\n    addClass(classes) {\n      const classArray = Array.isArray(classes) ? classes : classes.split(' ');\n      classArray.filter(element => element.length > 0).forEach(element => {\n        this.renderer.addClass(this.el.nativeElement, element);\n      });\n    }\n\n    setAttrib(key, value) {\n      value !== null ? this.renderer.setAttribute(this.el.nativeElement, key, value) : this.renderer.removeAttribute(this.el.nativeElement, key);\n    }\n\n  };\n\n  HtmlAttributesDirective.ɵfac = function HtmlAttributesDirective_Factory(t) {\n    return new (t || HtmlAttributesDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  HtmlAttributesDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: HtmlAttributesDirective,\n    selectors: [[\"\", \"cHtmlAttr\", \"\"]],\n    inputs: {\n      cHtmlAttr: \"cHtmlAttr\"\n    },\n    exportAs: [\"cHtmlAttr\"]\n  });\n\n  __decorate([Input()], HtmlAttributesDirective.prototype, \"cHtmlAttr\", void 0);\n\n  return HtmlAttributesDirective;\n})();\nlet IconModule = /*#__PURE__*/(() => {\n  let IconModule = class IconModule {};\n  IconModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: IconModule\n  });\n  IconModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function IconModule_Factory(t) {\n      return new (t || IconModule)();\n    },\n    imports: [[CommonModule]]\n  });\n  return IconModule;\n})();\nvar IconSetModule_1;\nlet IconSetModule = IconSetModule_1 = class IconSetModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error('CoreUI IconSetModule is already loaded. Import it in the AppModule only');\n    }\n  }\n\n  static forRoot() {\n    return {\n      ngModule: IconSetModule_1,\n      providers: [{\n        provide: IconSetService\n      }]\n    };\n  }\n\n};\nIconSetModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n  type: IconSetModule\n});\nIconSetModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n  factory: function IconSetModule_Factory(t) {\n    return new (t || IconSetModule)(ɵngcc0.ɵɵinject(IconSetModule, 12));\n  },\n  providers: [IconSetService],\n  imports: [[CommonModule]]\n});\n\nIconSetModule.ctorParameters = () => [{\n  type: IconSetModule,\n  decorators: [{\n    type: Optional\n  }, {\n    type: SkipSelf\n  }]\n}];\n\nIconSetModule = IconSetModule_1 = __decorate([__param(0, Optional()), __param(0, SkipSelf())], IconSetModule);\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(IconModule, {\n    declarations: function () {\n      return [IconComponent, HtmlAttributesDirective];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [IconComponent, HtmlAttributesDirective];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(IconSetModule, {\n    imports: function () {\n      return [CommonModule];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/*\r\n * Public API Surface of @coreui/icons-angular\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { IconComponent, IconModule, IconSetModule, IconSetService, HtmlAttributesDirective as ɵa }; //# sourceMappingURL=coreui-icons-angular.js.map", "map": null, "metadata": {}, "sourceType": "module"}