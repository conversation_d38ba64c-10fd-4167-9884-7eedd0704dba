import { Component, OnInit, ViewChild } from '@angular/core';
import { AppComponent } from '../../app.component'
import { navItems } from '../../_nav';
import { ActivatedRoute, Router } from '@angular/router';
import { TokenStorageService } from '../../../app/views/services/token-storage.service'
import { PermissionService } from '../../../app/views/services/permission.service';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { MustMatch } from './helper';
import { Loginservice } from '../../../app/views/services/login.service'

@Component({
  selector: 'app-dashboard',
  templateUrl: './default-layout.component.html'
})
export class DefaultLayoutComponent implements OnInit {
  @ViewChild('primaryModal') public primaryModal: ModalDirective;

  public sidebarMinimized = false;
  navItems = []
  Name: any;
  Role: any;
  Id: any;
  AddForm: FormGroup;
  isFormReady = false;
  submitted = false;
  fieldTextType: boolean;
  fieldTextType1: boolean;
  fieldTextType2: boolean;
  old=false
  constructor(private appComponet: AppComponent, private tokenStorage: TokenStorageService, private Permissionservice: PermissionService, private router: Router, private formBuilder: FormBuilder, private service: Loginservice) { }
  ngOnInit() {
    this.navItems = this.tokenStorage.getModule();
    const name = this.tokenStorage.getUser()
    this.Name = name.name;
    this.Role = name.role_id.name;
    this.Id = name._id
    this.SignForm();
    // console.log(this.Name)
    // console.log('here');
  }

  toggleMinimize(e) {
    // console.log(this.navItems)
    this.sidebarMinimized = e;
  }

  //Logout
  ClearStorage() {
    this.tokenStorage.signOut();
  }

  //Clear modal data
  clear() {
    this.AddForm.reset();
    this.isFormReady = false;
    this.submitted = false;
    this.old=false

  }

  SignForm() {
    this.AddForm = this.formBuilder.group({
      old_password: ['', [Validators.required, Validators.minLength(8)]],
      new_password: ['', [Validators.required, Validators.minLength(8)]],
      confirm_password: ['', [Validators.required, Validators.minLength(8)]],
    }, {
      validator: MustMatch('new_password', 'confirm_password')
    });
  }

  get f() {
    return this.AddForm.controls;
  }

  AddPassword() {
    this.submitted = true;
    if (this.AddForm.invalid) {
      return
    } else {
      const data = {
        old_password: this.AddForm.value.old_password,
        new_password: this.AddForm.value.new_password,
        password: this.AddForm.value.confirm_password,
      }
      this.service.GetUser(this.Id, data)
        .subscribe((res: any) => {
          if(res.code==200){
            this.old=false
            this.service.EditEmployeeDetail(this.Id,data)
            .subscribe((res1:any)=>{
              this.primaryModal.hide();
              this.clear();
            })
          }else{
            this.old=true
          }
        })
    }
  }

  toggleFieldTextType() {
    this.fieldTextType = !this.fieldTextType;
  }
  toggleFieldTextType1() {
    this.fieldTextType1 = !this.fieldTextType1;
  }
  toggleFieldTextType2() {
    this.fieldTextType2 = !this.fieldTextType2;
  }
}
