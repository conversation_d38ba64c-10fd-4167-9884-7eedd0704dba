<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header" *ngIf='action'>
                Add Product
            </div>
            <div class="card-header" *ngIf='!action'>
                Edit Product
            </div>

            <div class="card-body">
                <form class="form" [formGroup]="loginForm" autocomplete="off">

                    <!-- product details -->
                    <div class="row">

                        <div class="form-group col-lg-6">
                            <label for="Title">Title <span style="color: red;">*</span></label>
                            <input type="text" id="breed" placeholder="Title" class="form-control" formControlName="title" [ngClass]="{ 'is-invalid': submitted && f.title.errors }">
                            <div *ngIf="submitted && f.title.errors" class="invalid-feedback">
                                <div *ngIf="f.title.errors.required">*Title name is mandatory</div>
                            </div>

                        </div>

                        <div class="form-group col-lg-6">
                            <label for="sku">Sku <span style="color: red;">*</span></label>
                            <input type="text" id="sku" placeholder="Sku" class="form-control" formControlName="sku" [ngClass]="{ 'is-invalid': submitted && f.sku.errors }">
                            <div *ngIf="submitted && f.sku.errors" class="invalid-feedback">
                                <div *ngIf="f.sku.errors.required">*Sku is mandatory</div>
                            </div>
                        </div>

                        <div class="form-group col-lg-6">
                            <label for="category">Category <span style="color: red;">*</span></label>
                            <select id="category" name="category" class="form-control" formControlName="category" [ngClass]="{ 'is-invalid': submitted && f.category.errors }">
                                <option value="">--Select--</option>
                                <option *ngFor="let role of Category" [value]="role.name">{{role.name}}</option>
                            </select>
                            <div *ngIf="submitted && f.category.errors" class="invalid-feedback">
                                <div *ngIf="f.category.errors.required">*Category is mandatory</div>
                            </div>
                        </div>

                        <div class="form-group col-lg-6">
                            <label for="brand">Brand <span style="color: red;">*</span></label>
                            <select id="brand" name="brand" class="form-control" formControlName="brand" [ngClass]="{ 'is-invalid': submitted && f.brand.errors }">
                                <option value="">--Select--</option>
                                <option *ngFor="let role of brand" [value]="role.name">{{role.name}}</option>
                            </select>
                            <div *ngIf="submitted && f.brand.errors" class="invalid-feedback">
                                <div *ngIf="f.brand.errors.required">*Brand is mandatory</div>
                            </div>
                        </div>

                        <div class="form-group col-lg-6">
                            <label for="Description">Description <span style="color: red;">*</span></label>
                            <textarea class="form-control" placeholder="Description" rows="3" formControlName="description" [ngClass]="{ 'is-invalid': submitted && f.description.errors }"></textarea>
                            <div *ngIf="submitted && f.description.errors" class="invalid-feedback">
                                <div *ngIf="f.description.errors.required">*Description is mandatory</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <!-- Stock management -->
                    <!-- <div class="row">
            <div class="col-lg-12">
              <h5 style="color:#568d2c;">Stock Management</h5>
              <div class="form-group col-lg-6 my-4">
                <input class="styled-checkbox" id="styled" formControlName="stock_check" type="checkbox" />
                <label for="styled">Enable stock management at product level</label>
                <br>
                <div>
                  <label class=" mt-3" for="quantity">Stock Quantity:</label>
                  <input type="number" class="form-control" formControlName="quantity" placeholder="e.g. 10, 20"
                    [ngClass]="{ 'is-invalid': submitted && f.quantity.errors }" />
                  <div *ngIf="submitted && f.quantity.errors" class="invalid-feedback">
                    <div *ngIf="f.quantity.errors.required">*Stock Quantity is mandatory</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <hr> -->

          <!-- Images -->
          <div class="row my-4">
            <div class="col-lg-4" style="border-right: 2px solid #eee;">
                <h5 style="color:#568d2c;">Product Image <span style="color: red;">*</span></h5>
                <input type="file" id="file" class="form-control errorfileval" (change)="onSelectFile($event)" accept=".jpeg,.jpg,.png" />
                <label for="file" class="btn-2">upload</label>
                <div *ngIf="poster_image.length==0" style="font-size: 80%;
                color: #f86c6b;">*Image is mandatory</div>
                <br>
                <div class="img-wrap" *ngIf="poster_image!=''">
                    <span class="close" (click)="removeImage(-1)">&times;</span>
                    <img [src]="poster_image" height="100" class="my-3" data-id="103">
                </div>
            </div>


            <div class="col-lg-8">
                <h5 style="color:#568d2c;">Product Gallery</h5>
                <input type='file' id="file1" (change)="onSelectFile1($event)" accept=".jpeg,.jpg,.png" multiple>
                <label for="file1" class="btn-2">upload</label>
                <br>
                <div class="img-wrap" *ngFor='let url1 of multiple_image;let i = index;'>
                    <span class="close" (click)="removeImage(i)">&times;</span>
                    <img [src]="url1" height="100" class="my-3" data-id="103">

                </div>
            </div>
        </div>


        <!-- Attributes -->
        <div class="row my-4" *ngIf="showVariant">

            <div class="col-lg-12">
                <h5 style="color:#568d2c;">Attributes</h5>
            </div>

            <div class="col-lg-12" *ngFor="let user of Variant;let i= index;">
                <div class="row my-3">
                    <div class="col-lg-4">
                        <label>{{user.name}}</label>
                    </div>
                    <div class="col-lg-8">
                        <input type="checkbox" [checked]="user.flag" (change)="change(i,user.flag)" />
                        <label for="styled-checkbox">Used for variations</label>
                    </div>
                </div>
                <span class="badge badge-success" *ngFor="let users of user.items">{{users.value}}</span>
            </div>

        </div>


        <div *ngIf="!attributes">
            <button type="btn" class="btn btn-info" style="margin:5px 5px;float:right;font-weight: bold;" (click)="showVariant=true;">Show Variant</button>
        </div>


        <!-- Variant -->
        <table class="table table-striped">
            <thead>
                <tr>
                    <th *ngIf="option">Options</th>
                    <th *ngIf="option">Sku</th>
                    <th>Price</th>
                    <th>Tax(%)</th>
                    <th>Inventory</th>
                    <th>Discount(%)</th>
                    <th>Discount Start Date</th>
                    <th>Discount End Date</th>
                    <th>Status</th>
                    <!-- <th *ngIf="option">Action</th> -->
                </tr>
            </thead>
            <tbody>
                <tr formArrayName="items" *ngFor="let item of loginForm.get('items')['controls']; let i = index;">
                    <td [formGroupName]="i" *ngIf="option"><input type="text" formControlName="options" class="form-control" readonly /></td>
                    <td [formGroupName]="i" *ngIf="option"><input type="text" formControlName="sub_sku" class="form-control" [ngClass]="{'is-invalid':submitted && item.get('sub_sku').errors} "></td>
                    <td [formGroupName]="i"><input type="number" formControlName="price" class="form-control" [ngClass]="{'is-invalid':submitted && item.get('price').errors} "></td>
                    <td [formGroupName]="i"><input type="number" formControlName="tax" class="form-control" [ngClass]="{'is-invalid':submitted && item.get('tax').errors} "></td>
                    <td [formGroupName]="i"><input type="number" formControlName="inventory" class="form-control" [ngClass]="{'is-invalid':submitted && item.get('inventory').errors} "></td>
                    <td [formGroupName]="i"><input type="number" formControlName="dis_price" [ngClass]="{'is-invalid':submitted && item.get('dis_price').errors} " class="form-control"></td>
                    <td [formGroupName]="i"><input type="text" formControlName="dis_start" class="form-control" [minDate]="minDate" bsDatepicker></td>
                    <td [formGroupName]="i"><input type="text" formControlName="dis_end" [minDate]="minDate" class="form-control" bsDatepicker></td>
                    <td [formGroupName]="i"><label class="switch">
                        <input type="checkbox" formControlName="status">
                        <span class="slider round"></span>
                    </label></td>
                                <!-- <td [formGroupName]="i" *ngIf="option">
                  <a style="cursor: pointer;"><span (click)="removeItem(i);" class="badge badge-danger"><i
                        class="fa fa-trash"></i>
                      Delete</span></a>
                  </td> -->
              </tr>

          </tbody>
      </table>
  </form>

  <button type="submit" class="btn btn-primary" style="display: block; width:200px;margin: auto;" (click)="AddProduct();">Save</button>
</div>
</div>
</div>
<!--/.col-->
</div>