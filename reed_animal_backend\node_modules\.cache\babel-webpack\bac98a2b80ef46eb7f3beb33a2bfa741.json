{"ast": null, "code": "import isArray from './isArray.js';\nimport isKey from './_isKey.js';\nimport stringToPath from './_stringToPath.js';\nimport toString from './toString.js';\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\n\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\n\nexport default castPath;", "map": null, "metadata": {}, "sourceType": "module"}