import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class ProductService extends Api {

    // Image upload 
    ImageUpload(data){
        return this.http.post(`${this.config.APIUrl4}/pet/petimage?token=${localStorage.auth_token}`, data);

    }

    //Add new category
    AddCategory(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`, data);
    }

    //Delete Category by using category id
    DeleteCategory(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/category/${id}?token=${localStorage.auth_token}`);
    }

    //Update Category by using category id
    UpdateCategory(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/category/${id}?token=${localStorage.auth_token}`, params);
    }

    //Get all category list
    GetCategory(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/category?token=${localStorage.auth_token}`, { params });
    }

    //Add new Brand
    AddBrand(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`, data);
    }

    //Get all Brand list
    GetBrand(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/brand?token=${localStorage.auth_token}`, { params });
    }

    //Update Brand by using Brand id
    UpdateBrand(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/brand/${id}?token=${localStorage.auth_token}`, params);
    }

    //Delete Brand by using Brand id
    DeleteBrand(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/brand/${id}?token=${localStorage.auth_token}`);
    }

    //Add new Variant
    AddVariant(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`, data);
    }

    //Get all Variant list
    GetVariant(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Variant?token=${localStorage.auth_token}`, { params });
    }

    //Update Variant by using Variant id
    UpdateVariant(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Variant/${id}?token=${localStorage.auth_token}`, params);
    }

    //Delete Variant by using Variant id
    DeleteVariant(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Variant/${id}?token=${localStorage.auth_token}`);
    }

    //Picutre upload
    uploadFile(data: File): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('file', data);
        return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
            reportProgress: true,
            responseType: 'json'
        });
    }

    //Add new product
    AddProduct(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Product?token=${localStorage.auth_token}`, data);
    }

      //Add new banners
      AddBanners(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Banner?token=${localStorage.auth_token}`, data);
    }

      //Edit banners
      EditBanners(data: any,id): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Banner/${id}`, data);
    }

    //List all products
    GetProduct(name: any, params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Product?search=${name}&token=${localStorage.auth_token}`, { params });
    }

    GetallProduct(name: any, params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Product?skip=0&limit=0&search=${name}&token=${localStorage.auth_token}`, { params });
    }
// Get banners.
    GetBanners(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Banner?&token=${localStorage.auth_token}`, { params });
    }
    // get product
    getallproduct(name :any, params : any){
      
        return this.http.get(`${this.config.APIUrl}/products?skip=0&limit=0&search=${name}&token=${localStorage.auth_token}`,{ params });

    }

    //Delete product by using product id
    DeleteProduct(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Product/${id}?token=${localStorage.auth_token}`);
    }

      //Delete banner
      DeleteBanner(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Banner/${id}?token=${localStorage.auth_token}`);
    }

    //Update Product by using Product id
    UpdateProduct(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Product/${id}?token=${localStorage.auth_token}`, params);
    }

    //Get Product by using product id
    GetProductById(Id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Product/${Id}?token=${localStorage.auth_token}`);
    }


     //Update Product by using Product id
     UpdateBanner(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Banner/${id}?token=${localStorage.auth_token}`, params);
    }
}