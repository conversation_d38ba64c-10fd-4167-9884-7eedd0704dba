import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PetDetailComponent } from './pet-detail/pet-detail.component';
import { ResourcesComponent } from './resources/resources.component';
import { CustomersComponent } from './customers/customers.component';
import { AppointmentsComponent } from './appointments/appointments.component';
import { ShoppingComponent } from './shopping/shopping.component';
import { OrdersComponent } from './orders/orders.component';
import { ReportComponent } from './report/report.component';
import { ScheduleComponent } from './schedule/schedule.component';
import { AuthGuardService } from '../../authguard'
import { ChangePasswordComponent } from '../pages/change-password/change-password.component';
import { ShopSettingComponent } from './shop-setting/shop-setting.component';
import { ProductsComponent } from './products/products.component';
import { PelfiesComponent } from './pelfies/pelfies.component';
import { OrderDetailsComponent } from './order-details/order-details.component';
import { BannersComponent } from './banners/banners.component';
import { AddBannersComponent } from './add-banners/add-banners.component';

const routes: Routes = [
  { path: 'customers', component: CustomersComponent, data: { title: 'Customers', path: "/pages/customers" }, canActivate: [AuthGuardService] },
  { path: 'appointments', component: AppointmentsComponent, data: { title: 'Appointments', path: "/pages/appointments" }, canActivate: [AuthGuardService] },
  { path: 'shopping', component: ShoppingComponent, data: { title: 'Products', path: "/pages/shopping" }, canActivate: [AuthGuardService] },
  { path: 'products', component: ProductsComponent, data: { title: 'Add / Edit Product', path: "/pages/shopping" }, canActivate: [AuthGuardService] },
  { path: 'shop-setting', component: ShopSettingComponent, data: { title: 'Shop Setting', path: "/pages/shop-setting" }, canActivate: [AuthGuardService] },

  { path: 'add-banners', component: AddBannersComponent, data: { title: 'Add / Edit Banners', path: "/pages/banners" }, canActivate: [AuthGuardService] },

  { path: 'banners', component: BannersComponent, data: { title: 'Banners', path: "/pages/banners" }, canActivate: [AuthGuardService] },

  { path: 'orders', component: OrdersComponent, data: { title: 'Orders', path: "/pages/orders" }, canActivate: [AuthGuardService] },
  { path: 'report', component: ReportComponent, data: { title: 'Report', path: "/pages/report" }, canActivate: [AuthGuardService] },
  { path: 'pet-detail', component: PetDetailComponent, data: { title: 'Customer Information', path: "/pages/customers" }, canActivate: [AuthGuardService] },
  { path: 'availability', component: ScheduleComponent, data: { title: 'Availability', path: "/pages/availability" }, canActivate: [AuthGuardService] },
  { path: 'resources', component: ResourcesComponent, data: { title: 'Resources', path: "/pages/resources" }, canActivate: [AuthGuardService] },
  { path: 'change-password', component: ChangePasswordComponent, data: { title: 'Change Password' }, canActivate: [AuthGuardService] },
  { path: 'pelfies', component: PelfiesComponent, data: { title: 'Pelfies', path: "/pages/pelfies" }, canActivate: [AuthGuardService] },
  { path: 'order-details', component: OrderDetailsComponent, data: { title: 'Order Detail', path: "/pages/orders" }, canActivate: [AuthGuardService] },
  
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PagesRoutingModule { }
