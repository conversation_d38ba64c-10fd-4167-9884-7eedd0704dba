import { Component, OnInit, ViewChild, } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Module } from '../../models/module.models';
import { TokenStorageService } from '../../services/token-storage.service';
import { ModuleService } from '../../services/module.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';

@Component({
  selector: 'app-module',
  templateUrl: './module.component.html',
  styleUrls: ['./module.component.scss']
})
export class ModuleComponent implements OnInit {
  @ViewChild('AddModal') public AddModal: ModalDirective;
  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('deleteModal') public deleteModal: ModalDirective;

  modules = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  module: Module = {
    id: '',
    name: '',
    acc_activation: '',
  }
  rolefailed = false
  Id:any
  Add = true;
  Edit = true;
  Delete = true;
  constructor(private moduleService: ModuleService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService,private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    this.tokens();
  }

  show() {
    this.AddModal.show();
    this.primaryModal.show();
  }
  hide() {
    this.AddModal.hide();
    this.primaryModal.hide();
  }

  //clear modal window
  clear(): void {
    this.module = {};
  }

  //token verified module
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    if (key != null) {
      this.Permission.GetModule(Role.role_id._id)
        .subscribe((res: any) => {
          // console.log(res)
          for (var i = 0; i < res.data.length; i++) {
            if (res.data[i].module_name == "Module") {
              this.Add = res.data[i].add
              this.Edit = res.data[i].edit
              this.Delete = res.data[i].delete
              // console.log(this.Add, this.Edit, this.Delete)
            }
          }
        })
      this.EmployeeService.GetEmployeeDetail(Role._id)
        .subscribe((res) => {
          // console.log(res.data[0].status)
          if (res.data.status == false) {
            this.tokenStorage.signOut()
          }
        })
      this.GetModuleLists();
    }
    else {
      this.router.navigate(['/login']);
    }
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //Get All module List
  GetModuleLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.moduleService.GetModuleList(skip, this.name)
      .subscribe((res: any) => {
        this.modules = res.data;
        this.count = res.count;
        // console.log(this.modules);
        // console.log(this.count);
      });
  }
  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.GetModuleLists();
  }

  //Edit or update module 
  GetModule(id): void {
    // console.log('id-->', id);
    this.moduleService.GetModuleDetail(id)
      .subscribe((res) => {
        this.module = res.data[0];
        // console.log(res.data)
      })
  }

  EditModule(id): void {
    // console.log('id-->', id, this.module.name)
    const data = {
      name: this.module.name
    }
    this.moduleService.UpdateModule(id, data)
      .subscribe((res) => {
        // console.log('res-->', res);
        this.module = {};
        this.GetModuleLists();
      })
  }

  //Status ON & OFF
  changed(active, id) {
    const data = { acc_activation: active };
    // console.log('data-->', data);
    this.moduleService.UpdateModule(id, data)
      .subscribe((res: any) => {
        // console.log('res-->', res);
      })
  }

  //Add new module
  AddModule(): void {
    // console.log('name-->', this.module.name)
    if (this.module.name != undefined && this.module.name != '') {
      this.AddModal.hide();
      const data = {
        name: this.module.name
      }
      this.moduleService.NewModule(data)
        .subscribe((res) => {
          this.module = {};
          this.rolefailed = false
          // console.log('new-->', res)
          this.GetModuleLists();
        })
    } this.rolefailed = true
  }

  //Delete module using id
  DeleteModule(): void {
    // console.log('id-->', id)
    this.moduleService.DeleteModule(this.Id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.GetModuleLists();
      })
  }
  GetModulee(id){
    this.Id=id
  }
}
