/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[2]!./node_modules/@coreui/icons/css/free.css ***!
  \*********************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/*!
 * CoreUI Icons Free Open Source Icons
 * @version v2.0.0-beta.5
 * @link https://coreui.io/icons
 * Copyright (c) 2020 creativeLabs Łukasz Holeczek
 * Licensed under MIT (https://coreui.io/icons/license)
 */
@font-face {
  font-family: 'CoreUI-Icons-Free';
  src: url('CoreUI-Icons-Free.246845e88bc1880edca6.eot?64h6xh');
  src: url('CoreUI-Icons-Free.246845e88bc1880edca6.eot?64h6xh#iefix') format("embedded-opentype"), url('CoreUI-Icons-Free.68d6a47af77c11c4dc8b.ttf?64h6xh') format("truetype"), url('CoreUI-Icons-Free.7070e73f104dde775400.woff?64h6xh') format("woff"), url('CoreUI-Icons-Free.056d65c989aa546ec057.svg?64h6xh#CoreUI-Icons-Free') format("svg");
  font-weight: normal;
  font-style: normal;
}
[class^="cil-"], [class*=" cil-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'CoreUI-Icons-Free' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-feature-settings: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.cil-3d:before {
  content: "\ea01";
}
.cil-4k:before {
  content: "\ea02";
}
.cil-account-logout:before {
  content: "\ea03";
}
.cil-action-redo:before {
  content: "\ea04";
}
.cil-action-undo:before {
  content: "\ea05";
}
.cil-address-book:before {
  content: "\ea06";
}
.cil-airplane-mode:before {
  content: "\ea07";
}
.cil-airplane-mode-off:before {
  content: "\ea08";
}
.cil-airplay:before {
  content: "\ea09";
}
.cil-alarm:before {
  content: "\ea0a";
}
.cil-album:before {
  content: "\ea0b";
}
.cil-align-center:before {
  content: "\ea0c";
}
.cil-align-left:before {
  content: "\ea0d";
}
.cil-align-right:before {
  content: "\ea0e";
}
.cil-american-football:before {
  content: "\ea0f";
}
.cil-animal:before {
  content: "\ea10";
}
.cil-aperture:before {
  content: "\ea11";
}
.cil-apple:before {
  content: "\ea12";
}
.cil-applications:before {
  content: "\ea13";
}
.cil-applications-settings:before {
  content: "\ea14";
}
.cil-apps:before {
  content: "\ea15";
}
.cil-apps-settings:before {
  content: "\ea16";
}
.cil-arrow-bottom:before {
  content: "\ea17";
}
.cil-arrow-circle-bottom:before {
  content: "\ea18";
}
.cil-arrow-circle-left:before {
  content: "\ea19";
}
.cil-arrow-circle-right:before {
  content: "\ea1a";
}
.cil-arrow-circle-top:before {
  content: "\ea1b";
}
.cil-arrow-left:before {
  content: "\ea1c";
}
.cil-arrow-right:before {
  content: "\ea1d";
}
.cil-arrow-thick-bottom:before {
  content: "\ea1e";
}
.cil-arrow-thick-from-bottom:before {
  content: "\ea1f";
}
.cil-arrow-thick-from-left:before {
  content: "\ea20";
}
.cil-arrow-thick-from-right:before {
  content: "\ea21";
}
.cil-arrow-thick-from-top:before {
  content: "\ea22";
}
.cil-arrow-thick-left:before {
  content: "\ea23";
}
.cil-arrow-thick-right:before {
  content: "\ea24";
}
.cil-arrow-thick-to-bottom:before {
  content: "\ea25";
}
.cil-arrow-thick-to-left:before {
  content: "\ea26";
}
.cil-arrow-thick-to-right:before {
  content: "\ea27";
}
.cil-arrow-thick-to-top:before {
  content: "\ea28";
}
.cil-arrow-thick-top:before {
  content: "\ea29";
}
.cil-arrow-top:before {
  content: "\ea2a";
}
.cil-assistive-listening-system:before {
  content: "\ea2b";
}
.cil-asterisk:before {
  content: "\ea2c";
}
.cil-asterisk-circle:before {
  content: "\ea2d";
}
.cil-at:before {
  content: "\ea2e";
}
.cil-audio:before {
  content: "\ea2f";
}
.cil-audio-description:before {
  content: "\ea30";
}
.cil-audio-spectrum:before {
  content: "\ea31";
}
.cil-av-timer:before {
  content: "\ea32";
}
.cil-baby:before {
  content: "\ea33";
}
.cil-baby-carriage:before {
  content: "\ea34";
}
.cil-backspace:before {
  content: "\ea35";
}
.cil-badge:before {
  content: "\ea36";
}
.cil-balance-scale:before {
  content: "\ea37";
}
.cil-ban:before {
  content: "\ea38";
}
.cil-bank:before {
  content: "\ea39";
}
.cil-bar-chart:before {
  content: "\ea3a";
}
.cil-barcode:before {
  content: "\ea3b";
}
.cil-baseball:before {
  content: "\ea3c";
}
.cil-basket:before {
  content: "\ea3d";
}
.cil-basketball:before {
  content: "\ea3e";
}
.cil-bath:before {
  content: "\ea3f";
}
.cil-bathroom:before {
  content: "\ea40";
}
.cil-battery-0:before {
  content: "\ea41";
}
.cil-battery-3:before {
  content: "\ea42";
}
.cil-battery-5:before {
  content: "\ea43";
}
.cil-battery-alert:before {
  content: "\ea44";
}
.cil-battery-empty:before {
  content: "\ea45";
}
.cil-battery-full:before {
  content: "\ea46";
}
.cil-battery-slash:before {
  content: "\ea47";
}
.cil-beach-access:before {
  content: "\ea48";
}
.cil-beaker:before {
  content: "\ea49";
}
.cil-bed:before {
  content: "\ea4a";
}
.cil-bell:before {
  content: "\ea4b";
}
.cil-bell-exclamation:before {
  content: "\ea4c";
}
.cil-bike:before {
  content: "\ea4d";
}
.cil-birthday-cake:before {
  content: "\ea4e";
}
.cil-blind:before {
  content: "\ea4f";
}
.cil-bluetooth:before {
  content: "\ea50";
}
.cil-blur:before {
  content: "\ea51";
}
.cil-blur-circular:before {
  content: "\ea52";
}
.cil-blur-linear:before {
  content: "\ea53";
}
.cil-boat-alt:before {
  content: "\ea54";
}
.cil-bold:before {
  content: "\ea55";
}
.cil-bolt:before {
  content: "\ea56";
}
.cil-bolt-circle:before {
  content: "\ea57";
}
.cil-book:before {
  content: "\ea58";
}
.cil-bookmark:before {
  content: "\ea59";
}
.cil-border-all:before {
  content: "\ea5a";
}
.cil-border-bottom:before {
  content: "\ea5b";
}
.cil-border-clear:before {
  content: "\ea5c";
}
.cil-border-horizontal:before {
  content: "\ea5d";
}
.cil-border-inner:before {
  content: "\ea5e";
}
.cil-border-left:before {
  content: "\ea5f";
}
.cil-border-outer:before {
  content: "\ea60";
}
.cil-border-right:before {
  content: "\ea61";
}
.cil-border-style:before {
  content: "\ea62";
}
.cil-border-top:before {
  content: "\ea63";
}
.cil-border-vertical:before {
  content: "\ea64";
}
.cil-bowling:before {
  content: "\ea65";
}
.cil-braille:before {
  content: "\ea66";
}
.cil-briefcase:before {
  content: "\ea67";
}
.cil-brightness:before {
  content: "\ea68";
}
.cil-british-pound:before {
  content: "\ea69";
}
.cil-browser:before {
  content: "\ea6a";
}
.cil-brush:before {
  content: "\ea6b";
}
.cil-brush-alt:before {
  content: "\ea6c";
}
.cil-bug:before {
  content: "\ea6d";
}
.cil-building:before {
  content: "\ea6e";
}
.cil-bullhorn:before {
  content: "\ea6f";
}
.cil-burger:before {
  content: "\ea70";
}
.cil-burn:before {
  content: "\ea71";
}
.cil-bus-alt:before {
  content: "\ea72";
}
.cil-calculator:before {
  content: "\ea73";
}
.cil-calendar:before {
  content: "\ea74";
}
.cil-calendar-check:before {
  content: "\ea75";
}
.cil-camera:before {
  content: "\ea76";
}
.cil-camera-control:before {
  content: "\ea77";
}
.cil-camera-roll:before {
  content: "\ea78";
}
.cil-car-alt:before {
  content: "\ea79";
}
.cil-caret-bottom:before {
  content: "\ea7a";
}
.cil-caret-left:before {
  content: "\ea7b";
}
.cil-caret-right:before {
  content: "\ea7c";
}
.cil-caret-top:before {
  content: "\ea7d";
}
.cil-cart:before {
  content: "\ea7e";
}
.cil-cash:before {
  content: "\ea7f";
}
.cil-casino:before {
  content: "\ea80";
}
.cil-cast:before {
  content: "\ea81";
}
.cil-cat:before {
  content: "\ea82";
}
.cil-cc:before {
  content: "\ea83";
}
.cil-center-focus:before {
  content: "\ea84";
}
.cil-chart:before {
  content: "\ea85";
}
.cil-chart-line:before {
  content: "\ea86";
}
.cil-chart-pie:before {
  content: "\ea87";
}
.cil-chat-bubble:before {
  content: "\ea88";
}
.cil-check:before {
  content: "\ea89";
}
.cil-check-alt:before {
  content: "\ea8a";
}
.cil-check-circle:before {
  content: "\ea8b";
}
.cil-chevron-bottom:before {
  content: "\ea8c";
}
.cil-chevron-circle-down-alt:before {
  content: "\ea8d";
}
.cil-chevron-circle-left-alt:before {
  content: "\ea8e";
}
.cil-chevron-circle-right-alt:before {
  content: "\ea8f";
}
.cil-chevron-circle-up-alt:before {
  content: "\ea90";
}
.cil-chevron-double-down:before {
  content: "\ea91";
}
.cil-chevron-double-left:before {
  content: "\ea92";
}
.cil-chevron-double-right:before {
  content: "\ea93";
}
.cil-chevron-double-up:before {
  content: "\ea94";
}
.cil-chevron-left:before {
  content: "\ea95";
}
.cil-chevron-right:before {
  content: "\ea96";
}
.cil-chevron-top:before {
  content: "\ea97";
}
.cil-child:before {
  content: "\ea98";
}
.cil-child-friendly:before {
  content: "\ea99";
}
.cil-circle:before {
  content: "\ea9a";
}
.cil-clear-all:before {
  content: "\ea9b";
}
.cil-clipboard:before {
  content: "\ea9c";
}
.cil-clock:before {
  content: "\ea9d";
}
.cil-clone:before {
  content: "\ea9e";
}
.cil-closed-captioning:before {
  content: "\ea9f";
}
.cil-cloud:before {
  content: "\eaa0";
}
.cil-cloud-download:before {
  content: "\eaa1";
}
.cil-cloud-upload:before {
  content: "\eaa2";
}
.cil-cloudy:before {
  content: "\eaa3";
}
.cil-code:before {
  content: "\eaa4";
}
.cil-coffee:before {
  content: "\eaa5";
}
.cil-cog:before {
  content: "\eaa6";
}
.cil-color-border:before {
  content: "\eaa7";
}
.cil-color-fill:before {
  content: "\eaa8";
}
.cil-color-palette:before {
  content: "\eaa9";
}
.cil-columns:before {
  content: "\eaaa";
}
.cil-command:before {
  content: "\eaab";
}
.cil-comment-bubble:before {
  content: "\eaac";
}
.cil-comment-square:before {
  content: "\eaad";
}
.cil-compass:before {
  content: "\eaae";
}
.cil-compress:before {
  content: "\eaaf";
}
.cil-contact:before {
  content: "\eab0";
}
.cil-contrast:before {
  content: "\eab1";
}
.cil-control:before {
  content: "\eab2";
}
.cil-copy:before {
  content: "\eab3";
}
.cil-couch:before {
  content: "\eab4";
}
.cil-credit-card:before {
  content: "\eab5";
}
.cil-crop:before {
  content: "\eab6";
}
.cil-crop-rotate:before {
  content: "\eab7";
}
.cil-cursor:before {
  content: "\eab8";
}
.cil-cursor-move:before {
  content: "\eab9";
}
.cil-cut:before {
  content: "\eaba";
}
.cil-data-transfer-down:before {
  content: "\eabb";
}
.cil-data-transfer-up:before {
  content: "\eabc";
}
.cil-deaf:before {
  content: "\eabd";
}
.cil-delete:before {
  content: "\eabe";
}
.cil-description:before {
  content: "\eabf";
}
.cil-devices:before {
  content: "\eac0";
}
.cil-dialpad:before {
  content: "\eac1";
}
.cil-diamond:before {
  content: "\eac2";
}
.cil-dinner:before {
  content: "\eac3";
}
.cil-disabled:before {
  content: "\eac4";
}
.cil-dog:before {
  content: "\eac5";
}
.cil-dollar:before {
  content: "\eac6";
}
.cil-door:before {
  content: "\eac7";
}
.cil-double-quote-sans-left:before {
  content: "\eac8";
}
.cil-double-quote-sans-right:before {
  content: "\eac9";
}
.cil-drink:before {
  content: "\eaca";
}
.cil-drink-alcohol:before {
  content: "\eacb";
}
.cil-drop:before {
  content: "\eacc";
}
.cil-eco:before {
  content: "\eacd";
}
.cil-education:before {
  content: "\eace";
}
.cil-elevator:before {
  content: "\eacf";
}
.cil-envelope-closed:before {
  content: "\ead0";
}
.cil-envelope-letter:before {
  content: "\ead1";
}
.cil-envelope-open:before {
  content: "\ead2";
}
.cil-equalizer:before {
  content: "\ead3";
}
.cil-ethernet:before {
  content: "\ead4";
}
.cil-euro:before {
  content: "\ead5";
}
.cil-excerpt:before {
  content: "\ead6";
}
.cil-exit-to-app:before {
  content: "\ead7";
}
.cil-expand-down:before {
  content: "\ead8";
}
.cil-expand-left:before {
  content: "\ead9";
}
.cil-expand-right:before {
  content: "\eada";
}
.cil-expand-up:before {
  content: "\eadb";
}
.cil-exposure:before {
  content: "\eadc";
}
.cil-external-link:before {
  content: "\eadd";
}
.cil-eyedropper:before {
  content: "\eade";
}
.cil-face:before {
  content: "\eadf";
}
.cil-face-dead:before {
  content: "\eae0";
}
.cil-factory:before {
  content: "\eae1";
}
.cil-factory-slash:before {
  content: "\eae2";
}
.cil-fastfood:before {
  content: "\eae3";
}
.cil-fax:before {
  content: "\eae4";
}
.cil-featured-playlist:before {
  content: "\eae5";
}
.cil-file:before {
  content: "\eae6";
}
.cil-filter:before {
  content: "\eae7";
}
.cil-filter-frames:before {
  content: "\eae8";
}
.cil-filter-photo:before {
  content: "\eae9";
}
.cil-filter-square:before {
  content: "\eaea";
}
.cil-filter-x:before {
  content: "\eaeb";
}
.cil-find-in-page:before {
  content: "\eaec";
}
.cil-fingerprint:before {
  content: "\eaed";
}
.cil-fire:before {
  content: "\eaee";
}
.cil-flag-alt:before {
  content: "\eaef";
}
.cil-flight-takeoff:before {
  content: "\eaf0";
}
.cil-flip:before {
  content: "\eaf1";
}
.cil-flip-to-back:before {
  content: "\eaf2";
}
.cil-flip-to-front:before {
  content: "\eaf3";
}
.cil-flower:before {
  content: "\eaf4";
}
.cil-folder:before {
  content: "\eaf5";
}
.cil-folder-open:before {
  content: "\eaf6";
}
.cil-font:before {
  content: "\eaf7";
}
.cil-football:before {
  content: "\eaf8";
}
.cil-fork:before {
  content: "\eaf9";
}
.cil-fridge:before {
  content: "\eafa";
}
.cil-frown:before {
  content: "\eafb";
}
.cil-fullscreen:before {
  content: "\eafc";
}
.cil-fullscreen-exit:before {
  content: "\eafd";
}
.cil-functions:before {
  content: "\eafe";
}
.cil-functions-alt:before {
  content: "\eaff";
}
.cil-gamepad:before {
  content: "\eb00";
}
.cil-garage:before {
  content: "\eb01";
}
.cil-gauge:before {
  content: "\eb02";
}
.cil-gem:before {
  content: "\eb03";
}
.cil-gif:before {
  content: "\eb04";
}
.cil-gift:before {
  content: "\eb05";
}
.cil-globe-alt:before {
  content: "\eb06";
}
.cil-golf:before {
  content: "\eb07";
}
.cil-golf-alt:before {
  content: "\eb08";
}
.cil-gradient:before {
  content: "\eb09";
}
.cil-grain:before {
  content: "\eb0a";
}
.cil-graph:before {
  content: "\eb0b";
}
.cil-grid:before {
  content: "\eb0c";
}
.cil-grid-slash:before {
  content: "\eb0d";
}
.cil-group:before {
  content: "\eb0e";
}
.cil-hamburger-menu:before {
  content: "\eb0f";
}
.cil-hand-point-down:before {
  content: "\eb10";
}
.cil-hand-point-left:before {
  content: "\eb11";
}
.cil-hand-point-right:before {
  content: "\eb12";
}
.cil-hand-point-up:before {
  content: "\eb13";
}
.cil-happy:before {
  content: "\eb14";
}
.cil-hd:before {
  content: "\eb15";
}
.cil-hdr:before {
  content: "\eb16";
}
.cil-header:before {
  content: "\eb17";
}
.cil-headphones:before {
  content: "\eb18";
}
.cil-healing:before {
  content: "\eb19";
}
.cil-heart:before {
  content: "\eb1a";
}
.cil-highlighter:before {
  content: "\eb1b";
}
.cil-highligt:before {
  content: "\eb1c";
}
.cil-history:before {
  content: "\eb1d";
}
.cil-home:before {
  content: "\eb1e";
}
.cil-hospital:before {
  content: "\eb1f";
}
.cil-hot-tub:before {
  content: "\eb20";
}
.cil-house:before {
  content: "\eb21";
}
.cil-https:before {
  content: "\eb22";
}
.cil-image:before {
  content: "\eb23";
}
.cil-image-broken:before {
  content: "\eb24";
}
.cil-image-plus:before {
  content: "\eb25";
}
.cil-inbox:before {
  content: "\eb26";
}
.cil-indent-decrease:before {
  content: "\eb27";
}
.cil-indent-increase:before {
  content: "\eb28";
}
.cil-industry:before {
  content: "\eb29";
}
.cil-industry-slash:before {
  content: "\eb2a";
}
.cil-infinity:before {
  content: "\eb2b";
}
.cil-info:before {
  content: "\eb2c";
}
.cil-input:before {
  content: "\eb2d";
}
.cil-input-hdmi:before {
  content: "\eb2e";
}
.cil-input-power:before {
  content: "\eb2f";
}
.cil-institution:before {
  content: "\eb30";
}
.cil-italic:before {
  content: "\eb31";
}
.cil-justify-center:before {
  content: "\eb32";
}
.cil-justify-left:before {
  content: "\eb33";
}
.cil-justify-right:before {
  content: "\eb34";
}
.cil-keyboard:before {
  content: "\eb35";
}
.cil-lan:before {
  content: "\eb36";
}
.cil-language:before {
  content: "\eb37";
}
.cil-laptop:before {
  content: "\eb38";
}
.cil-layers:before {
  content: "\eb39";
}
.cil-leaf:before {
  content: "\eb3a";
}
.cil-lemon:before {
  content: "\eb3b";
}
.cil-level-down:before {
  content: "\eb3c";
}
.cil-level-up:before {
  content: "\eb3d";
}
.cil-library:before {
  content: "\eb3e";
}
.cil-library-add:before {
  content: "\eb3f";
}
.cil-library-building:before {
  content: "\eb40";
}
.cil-life-ring:before {
  content: "\eb41";
}
.cil-lightbulb:before {
  content: "\eb42";
}
.cil-line-spacing:before {
  content: "\eb43";
}
.cil-line-style:before {
  content: "\eb44";
}
.cil-line-weight:before {
  content: "\eb45";
}
.cil-link:before {
  content: "\eb46";
}
.cil-link-alt:before {
  content: "\eb47";
}
.cil-link-broken:before {
  content: "\eb48";
}
.cil-list:before {
  content: "\eb49";
}
.cil-list-filter:before {
  content: "\eb4a";
}
.cil-list-high-priority:before {
  content: "\eb4b";
}
.cil-list-low-priority:before {
  content: "\eb4c";
}
.cil-list-numbered:before {
  content: "\eb4d";
}
.cil-list-numbered-rtl:before {
  content: "\eb4e";
}
.cil-list-rich:before {
  content: "\eb4f";
}
.cil-location-pin:before {
  content: "\eb50";
}
.cil-lock-locked:before {
  content: "\eb51";
}
.cil-lock-unlocked:before {
  content: "\eb52";
}
.cil-locomotive:before {
  content: "\eb53";
}
.cil-loop:before {
  content: "\eb54";
}
.cil-loop-1:before {
  content: "\eb55";
}
.cil-loop-circular:before {
  content: "\eb56";
}
.cil-low-vision:before {
  content: "\eb57";
}
.cil-magnifying-glass:before {
  content: "\eb58";
}
.cil-map:before {
  content: "\eb59";
}
.cil-media-eject:before {
  content: "\eb5a";
}
.cil-media-pause:before {
  content: "\eb5b";
}
.cil-media-play:before {
  content: "\eb5c";
}
.cil-media-record:before {
  content: "\eb5d";
}
.cil-media-skip-backward:before {
  content: "\eb5e";
}
.cil-media-skip-forward:before {
  content: "\eb5f";
}
.cil-media-step-backward:before {
  content: "\eb60";
}
.cil-media-step-forward:before {
  content: "\eb61";
}
.cil-media-stop:before {
  content: "\eb62";
}
.cil-medical-cross:before {
  content: "\eb63";
}
.cil-meh:before {
  content: "\eb64";
}
.cil-memory:before {
  content: "\eb65";
}
.cil-menu:before {
  content: "\eb66";
}
.cil-mic:before {
  content: "\eb67";
}
.cil-microphone:before {
  content: "\eb68";
}
.cil-minus:before {
  content: "\eb69";
}
.cil-mobile:before {
  content: "\eb6a";
}
.cil-mobile-landscape:before {
  content: "\eb6b";
}
.cil-money:before {
  content: "\eb6c";
}
.cil-monitor:before {
  content: "\eb6d";
}
.cil-mood-bad:before {
  content: "\eb6e";
}
.cil-mood-good:before {
  content: "\eb6f";
}
.cil-mood-very-bad:before {
  content: "\eb70";
}
.cil-mood-very-good:before {
  content: "\eb71";
}
.cil-moon:before {
  content: "\eb72";
}
.cil-mouse:before {
  content: "\eb73";
}
.cil-mouth-slash:before {
  content: "\eb74";
}
.cil-move:before {
  content: "\eb75";
}
.cil-movie:before {
  content: "\eb76";
}
.cil-mug:before {
  content: "\eb77";
}
.cil-mug-tea:before {
  content: "\eb78";
}
.cil-music-note:before {
  content: "\eb79";
}
.cil-newspaper:before {
  content: "\eb7a";
}
.cil-note-add:before {
  content: "\eb7b";
}
.cil-notes:before {
  content: "\eb7c";
}
.cil-object-group:before {
  content: "\eb7d";
}
.cil-object-ungroup:before {
  content: "\eb7e";
}
.cil-opacity:before {
  content: "\eb7f";
}
.cil-opentype:before {
  content: "\eb80";
}
.cil-options:before {
  content: "\eb81";
}
.cil-paint:before {
  content: "\eb82";
}
.cil-paint-bucket:before {
  content: "\eb83";
}
.cil-paper-plane:before {
  content: "\eb84";
}
.cil-paperclip:before {
  content: "\eb85";
}
.cil-paragraph:before {
  content: "\eb86";
}
.cil-paw:before {
  content: "\eb87";
}
.cil-pen:before {
  content: "\eb88";
}
.cil-pen-alt:before {
  content: "\eb89";
}
.cil-pen-nib:before {
  content: "\eb8a";
}
.cil-pencil:before {
  content: "\eb8b";
}
.cil-people:before {
  content: "\eb8c";
}
.cil-phone:before {
  content: "\eb8d";
}
.cil-pin:before {
  content: "\eb8e";
}
.cil-pizza:before {
  content: "\eb8f";
}
.cil-plant:before {
  content: "\eb90";
}
.cil-playlist-add:before {
  content: "\eb91";
}
.cil-plus:before {
  content: "\eb92";
}
.cil-pool:before {
  content: "\eb93";
}
.cil-power-standby:before {
  content: "\eb94";
}
.cil-pregnant:before {
  content: "\eb95";
}
.cil-print:before {
  content: "\eb96";
}
.cil-pushchair:before {
  content: "\eb97";
}
.cil-puzzle:before {
  content: "\eb98";
}
.cil-qr-code:before {
  content: "\eb99";
}
.cil-rain:before {
  content: "\eb9a";
}
.cil-rectangle:before {
  content: "\eb9b";
}
.cil-recycle:before {
  content: "\eb9c";
}
.cil-reload:before {
  content: "\eb9d";
}
.cil-report-slash:before {
  content: "\eb9e";
}
.cil-resize-both:before {
  content: "\eb9f";
}
.cil-resize-height:before {
  content: "\eba0";
}
.cil-resize-width:before {
  content: "\eba1";
}
.cil-restaurant:before {
  content: "\eba2";
}
.cil-room:before {
  content: "\eba3";
}
.cil-router:before {
  content: "\eba4";
}
.cil-rowing:before {
  content: "\eba5";
}
.cil-rss:before {
  content: "\eba6";
}
.cil-ruble:before {
  content: "\eba7";
}
.cil-running:before {
  content: "\eba8";
}
.cil-sad:before {
  content: "\eba9";
}
.cil-satelite:before {
  content: "\ebaa";
}
.cil-save:before {
  content: "\ebab";
}
.cil-school:before {
  content: "\ebac";
}
.cil-screen-desktop:before {
  content: "\ebad";
}
.cil-screen-smartphone:before {
  content: "\ebae";
}
.cil-scrubber:before {
  content: "\ebaf";
}
.cil-search:before {
  content: "\ebb0";
}
.cil-send:before {
  content: "\ebb1";
}
.cil-settings:before {
  content: "\ebb2";
}
.cil-share:before {
  content: "\ebb3";
}
.cil-share-all:before {
  content: "\ebb4";
}
.cil-share-alt:before {
  content: "\ebb5";
}
.cil-share-boxed:before {
  content: "\ebb6";
}
.cil-shield-alt:before {
  content: "\ebb7";
}
.cil-short-text:before {
  content: "\ebb8";
}
.cil-shower:before {
  content: "\ebb9";
}
.cil-sign-language:before {
  content: "\ebba";
}
.cil-signal-cellular-0:before {
  content: "\ebbb";
}
.cil-signal-cellular-3:before {
  content: "\ebbc";
}
.cil-signal-cellular-4:before {
  content: "\ebbd";
}
.cil-sim:before {
  content: "\ebbe";
}
.cil-sitemap:before {
  content: "\ebbf";
}
.cil-smile:before {
  content: "\ebc0";
}
.cil-smile-plus:before {
  content: "\ebc1";
}
.cil-smoke:before {
  content: "\ebc2";
}
.cil-smoke-free:before {
  content: "\ebc3";
}
.cil-smoke-slash:before {
  content: "\ebc4";
}
.cil-smoking-room:before {
  content: "\ebc5";
}
.cil-snowflake:before {
  content: "\ebc6";
}
.cil-soccer:before {
  content: "\ebc7";
}
.cil-sofa:before {
  content: "\ebc8";
}
.cil-sort-alpha-down:before {
  content: "\ebc9";
}
.cil-sort-alpha-up:before {
  content: "\ebca";
}
.cil-sort-ascending:before {
  content: "\ebcb";
}
.cil-sort-descending:before {
  content: "\ebcc";
}
.cil-sort-numeric-down:before {
  content: "\ebcd";
}
.cil-sort-numeric-up:before {
  content: "\ebce";
}
.cil-spa:before {
  content: "\ebcf";
}
.cil-space-bar:before {
  content: "\ebd0";
}
.cil-speak:before {
  content: "\ebd1";
}
.cil-speaker:before {
  content: "\ebd2";
}
.cil-speech:before {
  content: "\ebd3";
}
.cil-speedometer:before {
  content: "\ebd4";
}
.cil-spreadsheet:before {
  content: "\ebd5";
}
.cil-square:before {
  content: "\ebd6";
}
.cil-star:before {
  content: "\ebd7";
}
.cil-star-half:before {
  content: "\ebd8";
}
.cil-storage:before {
  content: "\ebd9";
}
.cil-stream:before {
  content: "\ebda";
}
.cil-strikethrough:before {
  content: "\ebdb";
}
.cil-sun:before {
  content: "\ebdc";
}
.cil-swap-horizontal:before {
  content: "\ebdd";
}
.cil-swap-vertical:before {
  content: "\ebde";
}
.cil-swimming:before {
  content: "\ebdf";
}
.cil-sync:before {
  content: "\ebe0";
}
.cil-tablet:before {
  content: "\ebe1";
}
.cil-tag:before {
  content: "\ebe2";
}
.cil-tags:before {
  content: "\ebe3";
}
.cil-task:before {
  content: "\ebe4";
}
.cil-taxi:before {
  content: "\ebe5";
}
.cil-tennis:before {
  content: "\ebe6";
}
.cil-tennis-ball:before {
  content: "\ebe7";
}
.cil-terminal:before {
  content: "\ebe8";
}
.cil-terrain:before {
  content: "\ebe9";
}
.cil-text:before {
  content: "\ebea";
}
.cil-text-shapes:before {
  content: "\ebeb";
}
.cil-text-size:before {
  content: "\ebec";
}
.cil-text-square:before {
  content: "\ebed";
}
.cil-text-strike:before {
  content: "\ebee";
}
.cil-thumb-down:before {
  content: "\ebef";
}
.cil-thumb-up:before {
  content: "\ebf0";
}
.cil-toggle-off:before {
  content: "\ebf1";
}
.cil-toggle-on:before {
  content: "\ebf2";
}
.cil-toilet:before {
  content: "\ebf3";
}
.cil-touch-app:before {
  content: "\ebf4";
}
.cil-transfer:before {
  content: "\ebf5";
}
.cil-translate:before {
  content: "\ebf6";
}
.cil-trash:before {
  content: "\ebf7";
}
.cil-triangle:before {
  content: "\ebf8";
}
.cil-truck:before {
  content: "\ebf9";
}
.cil-tv:before {
  content: "\ebfa";
}
.cil-underline:before {
  content: "\ebfb";
}
.cil-usb:before {
  content: "\ebfc";
}
.cil-user:before {
  content: "\ebfd";
}
.cil-user-female:before {
  content: "\ebfe";
}
.cil-user-follow:before {
  content: "\ebff";
}
.cil-user-plus:before {
  content: "\ec00";
}
.cil-user-unfollow:before {
  content: "\ec01";
}
.cil-user-x:before {
  content: "\ec02";
}
.cil-vector:before {
  content: "\ec03";
}
.cil-vertical-align-bottom:before {
  content: "\ec04";
}
.cil-vertical-align-center:before {
  content: "\ec05";
}
.cil-vertical-align-top:before {
  content: "\ec06";
}
.cil-video:before {
  content: "\ec07";
}
.cil-videogame:before {
  content: "\ec08";
}
.cil-view-column:before {
  content: "\ec09";
}
.cil-view-module:before {
  content: "\ec0a";
}
.cil-view-quilt:before {
  content: "\ec0b";
}
.cil-view-stream:before {
  content: "\ec0c";
}
.cil-voice:before {
  content: "\ec0d";
}
.cil-voice-over-record:before {
  content: "\ec0e";
}
.cil-volume-high:before {
  content: "\ec0f";
}
.cil-volume-low:before {
  content: "\ec10";
}
.cil-volume-off:before {
  content: "\ec11";
}
.cil-walk:before {
  content: "\ec12";
}
.cil-wallet:before {
  content: "\ec13";
}
.cil-wallpaper:before {
  content: "\ec14";
}
.cil-warning:before {
  content: "\ec15";
}
.cil-watch:before {
  content: "\ec16";
}
.cil-wc:before {
  content: "\ec17";
}
.cil-weightlifitng:before {
  content: "\ec18";
}
.cil-wheelchair:before {
  content: "\ec19";
}
.cil-wifi-signal-0:before {
  content: "\ec1a";
}
.cil-wifi-signal-1:before {
  content: "\ec1b";
}
.cil-wifi-signal-2:before {
  content: "\ec1c";
}
.cil-wifi-signal-3:before {
  content: "\ec1d";
}
.cil-wifi-signal-4:before {
  content: "\ec1e";
}
.cil-wifi-signal-off:before {
  content: "\ec1f";
}
.cil-window:before {
  content: "\ec20";
}
.cil-window-maximize:before {
  content: "\ec21";
}
.cil-window-minimize:before {
  content: "\ec22";
}
.cil-window-restore:before {
  content: "\ec23";
}
.cil-wrap-text:before {
  content: "\ec24";
}
.cil-x:before {
  content: "\ec25";
}
.cil-x-circle:before {
  content: "\ec26";
}
.cil-yen:before {
  content: "\ec27";
}
.cil-zoom:before {
  content: "\ec28";
}
.cil-zoom-in:before {
  content: "\ec29";
}
.cil-zoom-out:before {
  content: "\ec2a";
}
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[2]!./node_modules/flag-icon-css/css/flag-icon.css ***!
  \**************************************************************************************************************************************************************************************************************************************/
.flag-icon-background {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}
.flag-icon {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  position: relative;
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
}
.flag-icon:before {
  content: "\00a0";
}
.flag-icon.flag-icon-squared {
  width: 1em;
}
.flag-icon-ad {
  background-image: url('ad.45026b922ec57f969a0a.svg');
}
.flag-icon-ad.flag-icon-squared {
  background-image: url('ad.94e810253dbc84702e9a.svg');
}
.flag-icon-ae {
  background-image: url('ae.2c530f6449f3e5abd04b.svg');
}
.flag-icon-ae.flag-icon-squared {
  background-image: url('ae.23c174705b39d649ba43.svg');
}
.flag-icon-af {
  background-image: url('af.458ab7e0c32d14aefe33.svg');
}
.flag-icon-af.flag-icon-squared {
  background-image: url('af.867627c537fd29812532.svg');
}
.flag-icon-ag {
  background-image: url('ag.5929ca9ff0f160f96fb5.svg');
}
.flag-icon-ag.flag-icon-squared {
  background-image: url('ag.3f18bb58815f1eb37b60.svg');
}
.flag-icon-ai {
  background-image: url('ai.c4699001b99c1638c765.svg');
}
.flag-icon-ai.flag-icon-squared {
  background-image: url('ai.546a12e334b3f4d8967c.svg');
}
.flag-icon-al {
  background-image: url('al.3dd8853b91d6f490b4c1.svg');
}
.flag-icon-al.flag-icon-squared {
  background-image: url('al.090568ab89f9b7e68f3b.svg');
}
.flag-icon-am {
  background-image: url('am.e935f82147f4d3c76c92.svg');
}
.flag-icon-am.flag-icon-squared {
  background-image: url('am.36fc7db319e532bff785.svg');
}
.flag-icon-ao {
  background-image: url('ao.ad6f3c8c3519f36b36c4.svg');
}
.flag-icon-ao.flag-icon-squared {
  background-image: url('ao.7ed590a16ff7642e7a85.svg');
}
.flag-icon-aq {
  background-image: url('aq.e3fbc5d0ce77f1c9e808.svg');
}
.flag-icon-aq.flag-icon-squared {
  background-image: url('aq.e6c275d0d4e5135fb04b.svg');
}
.flag-icon-ar {
  background-image: url('ar.2ff091f8773d0ea8640d.svg');
}
.flag-icon-ar.flag-icon-squared {
  background-image: url('ar.5a7c09af30ea06db87f1.svg');
}
.flag-icon-as {
  background-image: url('as.3b86b6876653592c0fa3.svg');
}
.flag-icon-as.flag-icon-squared {
  background-image: url('as.7ae00cb9d6bf497132c1.svg');
}
.flag-icon-at {
  background-image: url('at.1281f451a103684e9248.svg');
}
.flag-icon-at.flag-icon-squared {
  background-image: url('at.d7b3791eb6679e92a2bd.svg');
}
.flag-icon-au {
  background-image: url('au.932d918261bcbb88f0cc.svg');
}
.flag-icon-au.flag-icon-squared {
  background-image: url('au.5b98a120aeec3f5a1aeb.svg');
}
.flag-icon-aw {
  background-image: url('aw.98298192f432c6fc56cc.svg');
}
.flag-icon-aw.flag-icon-squared {
  background-image: url('aw.b478dded01c70ad2275c.svg');
}
.flag-icon-ax {
  background-image: url('ax.6651bb2513bc040f7f2b.svg');
}
.flag-icon-ax.flag-icon-squared {
  background-image: url('ax.c260e9a581b4c4415f20.svg');
}
.flag-icon-az {
  background-image: url('az.d4faca473814e47b3f03.svg');
}
.flag-icon-az.flag-icon-squared {
  background-image: url('az.84126238074d3c3c30b9.svg');
}
.flag-icon-ba {
  background-image: url('ba.7097f2f878560a3debc6.svg');
}
.flag-icon-ba.flag-icon-squared {
  background-image: url('ba.d1e732dc96724fe02492.svg');
}
.flag-icon-bb {
  background-image: url('bb.021629a6a596929b0462.svg');
}
.flag-icon-bb.flag-icon-squared {
  background-image: url('bb.17738ccf41cecf9d38ba.svg');
}
.flag-icon-bd {
  background-image: url('bd.d16830cba55e113c5888.svg');
}
.flag-icon-bd.flag-icon-squared {
  background-image: url('bd.a3ae69dedf0b3ad8fb44.svg');
}
.flag-icon-be {
  background-image: url('be.410c4acc521ec3a59836.svg');
}
.flag-icon-be.flag-icon-squared {
  background-image: url('be.914a3c37d1998aa1f6b0.svg');
}
.flag-icon-bf {
  background-image: url('bf.4275eb85c53fe2d0f6a8.svg');
}
.flag-icon-bf.flag-icon-squared {
  background-image: url('bf.6fc31e160aec39c1d496.svg');
}
.flag-icon-bg {
  background-image: url('bg.c9c13073359faec8e076.svg');
}
.flag-icon-bg.flag-icon-squared {
  background-image: url('bg.4f2a9bbb4c1ea18ea4dd.svg');
}
.flag-icon-bh {
  background-image: url('bh.ec61516daebfebe2cd49.svg');
}
.flag-icon-bh.flag-icon-squared {
  background-image: url('bh.0cfc56195412f09f4c70.svg');
}
.flag-icon-bi {
  background-image: url('bi.3fce551eeb9f82d29f76.svg');
}
.flag-icon-bi.flag-icon-squared {
  background-image: url('bi.5adb744e68bc13f75956.svg');
}
.flag-icon-bj {
  background-image: url('bj.e8591ed7d23999de96ef.svg');
}
.flag-icon-bj.flag-icon-squared {
  background-image: url('bj.fb3d1c01b8a808e6fe13.svg');
}
.flag-icon-bl {
  background-image: url('bl.6a6bc7f183b774316b63.svg');
}
.flag-icon-bl.flag-icon-squared {
  background-image: url('bl.669bbb820754f1cc0ce4.svg');
}
.flag-icon-bm {
  background-image: url('bm.e2aa572a02963b087e48.svg');
}
.flag-icon-bm.flag-icon-squared {
  background-image: url('bm.7dd1b92ad42d2f9d69dd.svg');
}
.flag-icon-bn {
  background-image: url('bn.ad7aafa9a29894397b43.svg');
}
.flag-icon-bn.flag-icon-squared {
  background-image: url('bn.5fd8c65274736a1b6af4.svg');
}
.flag-icon-bo {
  background-image: url('bo.1155c3da4861424ea8ff.svg');
}
.flag-icon-bo.flag-icon-squared {
  background-image: url('bo.ee5938f07b3324e9af6d.svg');
}
.flag-icon-bq {
  background-image: url('bq.8ff78ac28371e9069bfb.svg');
}
.flag-icon-bq.flag-icon-squared {
  background-image: url('bq.6e5b96f6104d2ff9977a.svg');
}
.flag-icon-br {
  background-image: url('br.5ec13287c2da0d77a7e7.svg');
}
.flag-icon-br.flag-icon-squared {
  background-image: url('br.b3663866f502ec386f0e.svg');
}
.flag-icon-bs {
  background-image: url('bs.5497678a4578b848e08d.svg');
}
.flag-icon-bs.flag-icon-squared {
  background-image: url('bs.a9ddb124f725485b9445.svg');
}
.flag-icon-bt {
  background-image: url('bt.eed19cbfd0dc809d6886.svg');
}
.flag-icon-bt.flag-icon-squared {
  background-image: url('bt.1372873ed65891680a2d.svg');
}
.flag-icon-bv {
  background-image: url('bv.b789f839eda2bce4b0e3.svg');
}
.flag-icon-bv.flag-icon-squared {
  background-image: url('bv.adee14dc818c2a37dbd9.svg');
}
.flag-icon-bw {
  background-image: url('bw.8a35721e3f5ff275ace8.svg');
}
.flag-icon-bw.flag-icon-squared {
  background-image: url('bw.0acc600b67ac7165e38c.svg');
}
.flag-icon-by {
  background-image: url('by.ed1fb53394827e144c8a.svg');
}
.flag-icon-by.flag-icon-squared {
  background-image: url('by.da99aaa559633b439aa3.svg');
}
.flag-icon-bz {
  background-image: url('bz.4eb2d29f4fcc586ae3e2.svg');
}
.flag-icon-bz.flag-icon-squared {
  background-image: url('bz.7826f0d58900985ad312.svg');
}
.flag-icon-ca {
  background-image: url('ca.d348137a99e6d528c5b7.svg');
}
.flag-icon-ca.flag-icon-squared {
  background-image: url('ca.102b45b24a03abdaeae6.svg');
}
.flag-icon-cc {
  background-image: url('cc.200233c98efe5d026e98.svg');
}
.flag-icon-cc.flag-icon-squared {
  background-image: url('cc.27d55bfa0a8b66542f74.svg');
}
.flag-icon-cd {
  background-image: url('cd.e5fd4d1225cc6c53b73d.svg');
}
.flag-icon-cd.flag-icon-squared {
  background-image: url('cd.d44809aaad5d32f91a56.svg');
}
.flag-icon-cf {
  background-image: url('cf.617adc02abcee400496d.svg');
}
.flag-icon-cf.flag-icon-squared {
  background-image: url('cf.eb50c5ecfa556ddba5a7.svg');
}
.flag-icon-cg {
  background-image: url('cg.440e321a39cf550b0bec.svg');
}
.flag-icon-cg.flag-icon-squared {
  background-image: url('cg.862f4608de0f8e9d213c.svg');
}
.flag-icon-ch {
  background-image: url('ch.7a52ef5e31b7f5e08a01.svg');
}
.flag-icon-ch.flag-icon-squared {
  background-image: url('ch.1113c7e9162d605ca580.svg');
}
.flag-icon-ci {
  background-image: url('ci.b1030b2b5315547c7fbe.svg');
}
.flag-icon-ci.flag-icon-squared {
  background-image: url('ci.6f06682eaf98960662af.svg');
}
.flag-icon-ck {
  background-image: url('ck.65a80cb3a138985c22dc.svg');
}
.flag-icon-ck.flag-icon-squared {
  background-image: url('ck.d76f3968d6f25ed0bc7d.svg');
}
.flag-icon-cl {
  background-image: url('cl.b9ff305a088060fd040a.svg');
}
.flag-icon-cl.flag-icon-squared {
  background-image: url('cl.303f56a616afb6bae962.svg');
}
.flag-icon-cm {
  background-image: url('cm.7578267e8568b1490427.svg');
}
.flag-icon-cm.flag-icon-squared {
  background-image: url('cm.0dcbc4f1fe098b1b8725.svg');
}
.flag-icon-cn {
  background-image: url('cn.7977e12a9afade492c93.svg');
}
.flag-icon-cn.flag-icon-squared {
  background-image: url('cn.f5a6f048eb8367343bd4.svg');
}
.flag-icon-co {
  background-image: url('co.59ec93f7d718ebed3779.svg');
}
.flag-icon-co.flag-icon-squared {
  background-image: url('co.4c87d079860a09479706.svg');
}
.flag-icon-cr {
  background-image: url('cr.6f2d7bb05d9edb1089ec.svg');
}
.flag-icon-cr.flag-icon-squared {
  background-image: url('cr.ebed46008265a3777565.svg');
}
.flag-icon-cu {
  background-image: url('cu.ff35f996902731bad287.svg');
}
.flag-icon-cu.flag-icon-squared {
  background-image: url('cu.1452c504ef675071dcdf.svg');
}
.flag-icon-cv {
  background-image: url('cv.434373304db5970887de.svg');
}
.flag-icon-cv.flag-icon-squared {
  background-image: url('cv.1ddd34244b91f9c46e1d.svg');
}
.flag-icon-cw {
  background-image: url('cw.8e4cecbf86c9e4b2df3a.svg');
}
.flag-icon-cw.flag-icon-squared {
  background-image: url('cw.3fc4503762b62953af04.svg');
}
.flag-icon-cx {
  background-image: url('cx.b9b5e6cd65826aab60c6.svg');
}
.flag-icon-cx.flag-icon-squared {
  background-image: url('cx.dda4107fd05b8081ae62.svg');
}
.flag-icon-cy {
  background-image: url('cy.70de54e68d8683969410.svg');
}
.flag-icon-cy.flag-icon-squared {
  background-image: url('cy.bdc1fde27ba14b2afa3b.svg');
}
.flag-icon-cz {
  background-image: url('cz.8ef2bc6a4d5bad23e284.svg');
}
.flag-icon-cz.flag-icon-squared {
  background-image: url('cz.1b3452b8ce83987fb494.svg');
}
.flag-icon-de {
  background-image: url('de.7e82f4c71df5fc78abbb.svg');
}
.flag-icon-de.flag-icon-squared {
  background-image: url('de.11d88d2b77e6abe5ebb1.svg');
}
.flag-icon-dj {
  background-image: url('dj.76f4cdf5eb6411038bc5.svg');
}
.flag-icon-dj.flag-icon-squared {
  background-image: url('dj.278a5a5fce9a6090ce80.svg');
}
.flag-icon-dk {
  background-image: url('dk.f4e8fc5376a202f1d771.svg');
}
.flag-icon-dk.flag-icon-squared {
  background-image: url('dk.c2e570fa503242ab4c3e.svg');
}
.flag-icon-dm {
  background-image: url('dm.466757644ba07a8bbf78.svg');
}
.flag-icon-dm.flag-icon-squared {
  background-image: url('dm.dc3455775ad035d0926c.svg');
}
.flag-icon-do {
  background-image: url('do.c05850db8e87e53a1268.svg');
}
.flag-icon-do.flag-icon-squared {
  background-image: url('do.64a9810e7d07e3af7412.svg');
}
.flag-icon-dz {
  background-image: url('dz.945a413c6ba8e484b7b1.svg');
}
.flag-icon-dz.flag-icon-squared {
  background-image: url('dz.2be2fee6433a59e75c3d.svg');
}
.flag-icon-ec {
  background-image: url('ec.3ea7f906eaf807123a28.svg');
}
.flag-icon-ec.flag-icon-squared {
  background-image: url('ec.7be6ca137c0a396154ac.svg');
}
.flag-icon-ee {
  background-image: url('ee.887a78f0eb107b3ce616.svg');
}
.flag-icon-ee.flag-icon-squared {
  background-image: url('ee.7b6b8abcf78cfa7f4a77.svg');
}
.flag-icon-eg {
  background-image: url('eg.b3580df977ae211f31d3.svg');
}
.flag-icon-eg.flag-icon-squared {
  background-image: url('eg.c6ff8d6c3057865a32f1.svg');
}
.flag-icon-eh {
  background-image: url('eh.8c8b27438e64065d8542.svg');
}
.flag-icon-eh.flag-icon-squared {
  background-image: url('eh.905fdd0842d1597c4a27.svg');
}
.flag-icon-er {
  background-image: url('er.458bc299993e856c309d.svg');
}
.flag-icon-er.flag-icon-squared {
  background-image: url('er.ff62e2720daee288818f.svg');
}
.flag-icon-es {
  background-image: url('es.1a25a96e26fcca676c08.svg');
}
.flag-icon-es.flag-icon-squared {
  background-image: url('es.b3825b28f7a64779d80d.svg');
}
.flag-icon-et {
  background-image: url('et.cbe354bb4afa8afc62da.svg');
}
.flag-icon-et.flag-icon-squared {
  background-image: url('et.061591dd14f8c02c150f.svg');
}
.flag-icon-fi {
  background-image: url('fi.3b522e7f272eee4009b2.svg');
}
.flag-icon-fi.flag-icon-squared {
  background-image: url('fi.eb793b740dd4fa0f8b63.svg');
}
.flag-icon-fj {
  background-image: url('fj.f3d86add9fe9ed672274.svg');
}
.flag-icon-fj.flag-icon-squared {
  background-image: url('fj.55dd1c6e9a323130d8e7.svg');
}
.flag-icon-fk {
  background-image: url('fk.ddc6bd174c1e6603e323.svg');
}
.flag-icon-fk.flag-icon-squared {
  background-image: url('fk.fe8e733a5a44d9626de2.svg');
}
.flag-icon-fm {
  background-image: url('fm.3bfd96ee5faa59b8017a.svg');
}
.flag-icon-fm.flag-icon-squared {
  background-image: url('fm.1579e5b6f7e79e751445.svg');
}
.flag-icon-fo {
  background-image: url('fo.f284df39e89f9ed508ad.svg');
}
.flag-icon-fo.flag-icon-squared {
  background-image: url('fo.8b4db68d6e0717fe940e.svg');
}
.flag-icon-fr {
  background-image: url('fr.81d43a151d8bc64145f2.svg');
}
.flag-icon-fr.flag-icon-squared {
  background-image: url('fr.c88df3297cffe49852ae.svg');
}
.flag-icon-ga {
  background-image: url('ga.4257c8ec8a129da794b2.svg');
}
.flag-icon-ga.flag-icon-squared {
  background-image: url('ga.dec832634c40be902627.svg');
}
.flag-icon-gb {
  background-image: url('gb.ba1c7f5df0dd4173c951.svg');
}
.flag-icon-gb.flag-icon-squared {
  background-image: url('gb.35dbacd736781608964a.svg');
}
.flag-icon-gd {
  background-image: url('gd.b446a44dff915db18869.svg');
}
.flag-icon-gd.flag-icon-squared {
  background-image: url('gd.1b313417e54a6f4446ee.svg');
}
.flag-icon-ge {
  background-image: url('ge.98cf9dc189b05e67103c.svg');
}
.flag-icon-ge.flag-icon-squared {
  background-image: url('ge.7ccc29e2355cf25d55c5.svg');
}
.flag-icon-gf {
  background-image: url('gf.695a47d62497dc584667.svg');
}
.flag-icon-gf.flag-icon-squared {
  background-image: url('gf.74219f32e778ea33b181.svg');
}
.flag-icon-gg {
  background-image: url('gg.6b23b5b1092e831766f9.svg');
}
.flag-icon-gg.flag-icon-squared {
  background-image: url('gg.30f47622e942430014e8.svg');
}
.flag-icon-gh {
  background-image: url('gh.d060e231aa94a98e78d9.svg');
}
.flag-icon-gh.flag-icon-squared {
  background-image: url('gh.286f4413bbf14d667ea8.svg');
}
.flag-icon-gi {
  background-image: url('gi.345b700f04babfed53e1.svg');
}
.flag-icon-gi.flag-icon-squared {
  background-image: url('gi.e73af10429f00dc293ea.svg');
}
.flag-icon-gl {
  background-image: url('gl.84ac5572fd0727fd850e.svg');
}
.flag-icon-gl.flag-icon-squared {
  background-image: url('gl.68756f324152d0ada90c.svg');
}
.flag-icon-gm {
  background-image: url('gm.cdfdf8bcb862134ab9fe.svg');
}
.flag-icon-gm.flag-icon-squared {
  background-image: url('gm.65c86e0a8df296521d90.svg');
}
.flag-icon-gn {
  background-image: url('gn.bf5b087387ce93eddfac.svg');
}
.flag-icon-gn.flag-icon-squared {
  background-image: url('gn.7bf7a35a82ae814ed25d.svg');
}
.flag-icon-gp {
  background-image: url('gp.092b6bf958cd4a1f76c9.svg');
}
.flag-icon-gp.flag-icon-squared {
  background-image: url('gp.30b1d26cfe9f458611e2.svg');
}
.flag-icon-gq {
  background-image: url('gq.c2cb1adba91b64af03bc.svg');
}
.flag-icon-gq.flag-icon-squared {
  background-image: url('gq.89421f59da9e40d8cfcc.svg');
}
.flag-icon-gr {
  background-image: url('gr.e2d0116790bdfda46fb4.svg');
}
.flag-icon-gr.flag-icon-squared {
  background-image: url('gr.c51a52c416ea428fe41f.svg');
}
.flag-icon-gs {
  background-image: url('gs.c19adcdd5855af626a3c.svg');
}
.flag-icon-gs.flag-icon-squared {
  background-image: url('gs.a96857cd4e8cd95734f9.svg');
}
.flag-icon-gt {
  background-image: url('gt.ccfc27d34052eec1eb6e.svg');
}
.flag-icon-gt.flag-icon-squared {
  background-image: url('gt.3d87ccc4e82ef502f1dd.svg');
}
.flag-icon-gu {
  background-image: url('gu.459831ea94ce2f15eede.svg');
}
.flag-icon-gu.flag-icon-squared {
  background-image: url('gu.35820090ead0219b998c.svg');
}
.flag-icon-gw {
  background-image: url('gw.f29eedfe431a60cae11e.svg');
}
.flag-icon-gw.flag-icon-squared {
  background-image: url('gw.f647ba54d53db3f2e3a4.svg');
}
.flag-icon-gy {
  background-image: url('gy.49a30b4ff82716f3aadd.svg');
}
.flag-icon-gy.flag-icon-squared {
  background-image: url('gy.43c003e277ed5a4d0ca0.svg');
}
.flag-icon-hk {
  background-image: url('hk.c0a93c089256c99bf337.svg');
}
.flag-icon-hk.flag-icon-squared {
  background-image: url('hk.5a1122079f786b82c2ed.svg');
}
.flag-icon-hm {
  background-image: url('hm.fdd5197f75474534c518.svg');
}
.flag-icon-hm.flag-icon-squared {
  background-image: url('hm.8b4c33d4098f83d3cddd.svg');
}
.flag-icon-hn {
  background-image: url('hn.f53ee3d65d19c9dd755e.svg');
}
.flag-icon-hn.flag-icon-squared {
  background-image: url('hn.ebef2cd564ca07f12aa1.svg');
}
.flag-icon-hr {
  background-image: url('hr.00a76e1b588a62b0fad9.svg');
}
.flag-icon-hr.flag-icon-squared {
  background-image: url('hr.f3f2e25c45a219c68654.svg');
}
.flag-icon-ht {
  background-image: url('ht.3af38bff509f443ef70e.svg');
}
.flag-icon-ht.flag-icon-squared {
  background-image: url('ht.663996cf665e8ab764d5.svg');
}
.flag-icon-hu {
  background-image: url('hu.bcbd277021f4a8f5a059.svg');
}
.flag-icon-hu.flag-icon-squared {
  background-image: url('hu.7ae2a1f04ec537fbba4b.svg');
}
.flag-icon-id {
  background-image: url('id.e2afd171e6a62816237b.svg');
}
.flag-icon-id.flag-icon-squared {
  background-image: url('id.0b7fa609d99165dc5377.svg');
}
.flag-icon-ie {
  background-image: url('ie.5ecf710f14d859cbceb6.svg');
}
.flag-icon-ie.flag-icon-squared {
  background-image: url('ie.1b0ac4e772c2e62aef2f.svg');
}
.flag-icon-il {
  background-image: url('il.4c70e23214e9da6a56eb.svg');
}
.flag-icon-il.flag-icon-squared {
  background-image: url('il.3bc4ce048568d30c327f.svg');
}
.flag-icon-im {
  background-image: url('im.b21ce587e66db16e0428.svg');
}
.flag-icon-im.flag-icon-squared {
  background-image: url('im.d637f63b68f97839a27b.svg');
}
.flag-icon-in {
  background-image: url('in.e5926cb75dcbb15638da.svg');
}
.flag-icon-in.flag-icon-squared {
  background-image: url('in.e626d1bb4e16e732e1dd.svg');
}
.flag-icon-io {
  background-image: url('io.e31ca9aa9209d9b76a0b.svg');
}
.flag-icon-io.flag-icon-squared {
  background-image: url('io.c32d7f9e59460fb90af6.svg');
}
.flag-icon-iq {
  background-image: url('iq.5cd51d2bbb7385580434.svg');
}
.flag-icon-iq.flag-icon-squared {
  background-image: url('iq.e549011efede8b5ba38b.svg');
}
.flag-icon-ir {
  background-image: url('ir.12e7432b428f8d631eb5.svg');
}
.flag-icon-ir.flag-icon-squared {
  background-image: url('ir.c945dfdfaee26ad2861c.svg');
}
.flag-icon-is {
  background-image: url('is.2dfa14d19684fbe061e4.svg');
}
.flag-icon-is.flag-icon-squared {
  background-image: url('is.1842f1a952e8f0d4ca47.svg');
}
.flag-icon-it {
  background-image: url('it.290f2fec799fabdf2a85.svg');
}
.flag-icon-it.flag-icon-squared {
  background-image: url('it.9938f4b9588502f93b20.svg');
}
.flag-icon-je {
  background-image: url('je.7a0b4850d933dbc21d75.svg');
}
.flag-icon-je.flag-icon-squared {
  background-image: url('je.e1bb30f3c6be27ba0bc4.svg');
}
.flag-icon-jm {
  background-image: url('jm.6bb96bbc99218d9f84f7.svg');
}
.flag-icon-jm.flag-icon-squared {
  background-image: url('jm.74ccffca23e5a91356de.svg');
}
.flag-icon-jo {
  background-image: url('jo.f41fe7d26b69dec06fef.svg');
}
.flag-icon-jo.flag-icon-squared {
  background-image: url('jo.e678dae866ec74e6a939.svg');
}
.flag-icon-jp {
  background-image: url('jp.19c631c1498ba5517cd5.svg');
}
.flag-icon-jp.flag-icon-squared {
  background-image: url('jp.980c12c54fe225923434.svg');
}
.flag-icon-ke {
  background-image: url('ke.74aaf58557811d8e79ab.svg');
}
.flag-icon-ke.flag-icon-squared {
  background-image: url('ke.9ea890912ffd2f80e7a3.svg');
}
.flag-icon-kg {
  background-image: url('kg.1fe994c1e99757dce023.svg');
}
.flag-icon-kg.flag-icon-squared {
  background-image: url('kg.4ad89b3a703d225e1f6d.svg');
}
.flag-icon-kh {
  background-image: url('kh.7b33804c913e2285c538.svg');
}
.flag-icon-kh.flag-icon-squared {
  background-image: url('kh.695ec7a1a39090e600d5.svg');
}
.flag-icon-ki {
  background-image: url('ki.de100d3095b62260166f.svg');
}
.flag-icon-ki.flag-icon-squared {
  background-image: url('ki.32000b051bb6bb9ee785.svg');
}
.flag-icon-km {
  background-image: url('km.93ef5e214ae093b8adc8.svg');
}
.flag-icon-km.flag-icon-squared {
  background-image: url('km.4c4fa2a75b7c9360ac5f.svg');
}
.flag-icon-kn {
  background-image: url('kn.a4e974e81853186f1522.svg');
}
.flag-icon-kn.flag-icon-squared {
  background-image: url('kn.091a5508172f8eee28f2.svg');
}
.flag-icon-kp {
  background-image: url('kp.8d10def41b377b1163c5.svg');
}
.flag-icon-kp.flag-icon-squared {
  background-image: url('kp.2e79afa21a3e610e5551.svg');
}
.flag-icon-kr {
  background-image: url('kr.939387c390531d01a687.svg');
}
.flag-icon-kr.flag-icon-squared {
  background-image: url('kr.9406f22f1237e7e4059d.svg');
}
.flag-icon-kw {
  background-image: url('kw.2dce482defe9d86d0596.svg');
}
.flag-icon-kw.flag-icon-squared {
  background-image: url('kw.a3a60802b9df1ea679ac.svg');
}
.flag-icon-ky {
  background-image: url('ky.ef8e18776eff1caf6b64.svg');
}
.flag-icon-ky.flag-icon-squared {
  background-image: url('ky.c311ddba04238d23214d.svg');
}
.flag-icon-kz {
  background-image: url('kz.7194851eb720d3fdb3ad.svg');
}
.flag-icon-kz.flag-icon-squared {
  background-image: url('kz.f528d1705766032d8237.svg');
}
.flag-icon-la {
  background-image: url('la.8d6ad26b7061bc058892.svg');
}
.flag-icon-la.flag-icon-squared {
  background-image: url('la.80cf2b55ad4d86b51967.svg');
}
.flag-icon-lb {
  background-image: url('lb.75479923a75562bb3dbe.svg');
}
.flag-icon-lb.flag-icon-squared {
  background-image: url('lb.940cc75a55e4b18f510a.svg');
}
.flag-icon-lc {
  background-image: url('lc.c6488de9494a4e151cc4.svg');
}
.flag-icon-lc.flag-icon-squared {
  background-image: url('lc.0d361ba543e6cd2404e1.svg');
}
.flag-icon-li {
  background-image: url('li.69a1d60ca3996705d91f.svg');
}
.flag-icon-li.flag-icon-squared {
  background-image: url('li.572f90277090beca0d31.svg');
}
.flag-icon-lk {
  background-image: url('lk.593078c9718a2a7a20d6.svg');
}
.flag-icon-lk.flag-icon-squared {
  background-image: url('lk.93412c6fbb52d5bb809b.svg');
}
.flag-icon-lr {
  background-image: url('lr.3a7c494b08f2d0e36a4f.svg');
}
.flag-icon-lr.flag-icon-squared {
  background-image: url('lr.74dcec3fec3f73e24a0a.svg');
}
.flag-icon-ls {
  background-image: url('ls.0de0f907e70c37b2e86e.svg');
}
.flag-icon-ls.flag-icon-squared {
  background-image: url('ls.cddead61f832a10065e9.svg');
}
.flag-icon-lt {
  background-image: url('lt.4c19d3a9f8cb00a45baa.svg');
}
.flag-icon-lt.flag-icon-squared {
  background-image: url('lt.2ea82cfcd24756f9d718.svg');
}
.flag-icon-lu {
  background-image: url('lu.adc8f77e99b53bd83b54.svg');
}
.flag-icon-lu.flag-icon-squared {
  background-image: url('lu.b843e6436ac12254b9d2.svg');
}
.flag-icon-lv {
  background-image: url('lv.9a5d132cec13c3e033f0.svg');
}
.flag-icon-lv.flag-icon-squared {
  background-image: url('lv.2ce7f836390f846b1359.svg');
}
.flag-icon-ly {
  background-image: url('ly.0ea8dfcec5cc820043a4.svg');
}
.flag-icon-ly.flag-icon-squared {
  background-image: url('ly.9d867c1b9d3b76652858.svg');
}
.flag-icon-ma {
  background-image: url('ma.363a4f79da72a6e74be0.svg');
}
.flag-icon-ma.flag-icon-squared {
  background-image: url('ma.3b79aff17ae55b760333.svg');
}
.flag-icon-mc {
  background-image: url('mc.a2634c60fa92f9ff20f0.svg');
}
.flag-icon-mc.flag-icon-squared {
  background-image: url('mc.082fc1558b4cf726b613.svg');
}
.flag-icon-md {
  background-image: url('md.4d08e48ef4cfb7c192dc.svg');
}
.flag-icon-md.flag-icon-squared {
  background-image: url('md.efdfab01385b30e73986.svg');
}
.flag-icon-me {
  background-image: url('me.0b785614513a0b99de04.svg');
}
.flag-icon-me.flag-icon-squared {
  background-image: url('me.4c8b84af010134d56b90.svg');
}
.flag-icon-mf {
  background-image: url('mf.2d96a80bd05aca4ef711.svg');
}
.flag-icon-mf.flag-icon-squared {
  background-image: url('mf.c02a78fb2738ceb5eece.svg');
}
.flag-icon-mg {
  background-image: url('mg.f9101073ea57c9f7664c.svg');
}
.flag-icon-mg.flag-icon-squared {
  background-image: url('mg.5bdc14fe1aa439d1a0b7.svg');
}
.flag-icon-mh {
  background-image: url('mh.c0b2e372c1a8cb36930e.svg');
}
.flag-icon-mh.flag-icon-squared {
  background-image: url('mh.7ec670b4d72f8a614957.svg');
}
.flag-icon-mk {
  background-image: url('mk.31ba11ec4d4cdae74ebc.svg');
}
.flag-icon-mk.flag-icon-squared {
  background-image: url('mk.c370fe88a49ab3c18701.svg');
}
.flag-icon-ml {
  background-image: url('ml.18083e46073cc9f5f58f.svg');
}
.flag-icon-ml.flag-icon-squared {
  background-image: url('ml.d11c30638f3d7b1766dc.svg');
}
.flag-icon-mm {
  background-image: url('mm.6f93c6d0ec04077b61c7.svg');
}
.flag-icon-mm.flag-icon-squared {
  background-image: url('mm.7068544f07c5b683b67a.svg');
}
.flag-icon-mn {
  background-image: url('mn.5e4557f7ed743f19592c.svg');
}
.flag-icon-mn.flag-icon-squared {
  background-image: url('mn.291814d605941d58335f.svg');
}
.flag-icon-mo {
  background-image: url('mo.ac9b1701934ac6845610.svg');
}
.flag-icon-mo.flag-icon-squared {
  background-image: url('mo.8d4848888f2e8b825545.svg');
}
.flag-icon-mp {
  background-image: url('mp.394b0c91d8212a6cb6db.svg');
}
.flag-icon-mp.flag-icon-squared {
  background-image: url('mp.b0a5ed44db6410228f23.svg');
}
.flag-icon-mq {
  background-image: url('mq.e82d6df9e99c87fb655b.svg');
}
.flag-icon-mq.flag-icon-squared {
  background-image: url('mq.09c19f656772c8f93989.svg');
}
.flag-icon-mr {
  background-image: url('mr.3642790a35ff100e55f7.svg');
}
.flag-icon-mr.flag-icon-squared {
  background-image: url('mr.7211bfd49f97a5ae1253.svg');
}
.flag-icon-ms {
  background-image: url('ms.9474f8cf128ce6813e2d.svg');
}
.flag-icon-ms.flag-icon-squared {
  background-image: url('ms.8f7c204278ae1cdcd35c.svg');
}
.flag-icon-mt {
  background-image: url('mt.c91049a111e0a4dc2611.svg');
}
.flag-icon-mt.flag-icon-squared {
  background-image: url('mt.0c23ea353ac917d9e4d4.svg');
}
.flag-icon-mu {
  background-image: url('mu.97beced41b168e88e8fb.svg');
}
.flag-icon-mu.flag-icon-squared {
  background-image: url('mu.d7d71f034d81a7105373.svg');
}
.flag-icon-mv {
  background-image: url('mv.0329f53cf8f786716fe4.svg');
}
.flag-icon-mv.flag-icon-squared {
  background-image: url('mv.91b2c27c9c550f558607.svg');
}
.flag-icon-mw {
  background-image: url('mw.b4d805efda655aef8b6e.svg');
}
.flag-icon-mw.flag-icon-squared {
  background-image: url('mw.6807c4fdd0370b23d239.svg');
}
.flag-icon-mx {
  background-image: url('mx.bc63d25be57acf721e56.svg');
}
.flag-icon-mx.flag-icon-squared {
  background-image: url('mx.05c8d69783e68aaad2f4.svg');
}
.flag-icon-my {
  background-image: url('my.0d298a9e4566332f8a84.svg');
}
.flag-icon-my.flag-icon-squared {
  background-image: url('my.848fbf91865a8d191263.svg');
}
.flag-icon-mz {
  background-image: url('mz.dac5f7ee4f2a02e79de2.svg');
}
.flag-icon-mz.flag-icon-squared {
  background-image: url('mz.dcf8977ee74002921810.svg');
}
.flag-icon-na {
  background-image: url('na.e241f81665d5aa3bcd02.svg');
}
.flag-icon-na.flag-icon-squared {
  background-image: url('na.27bc2313a9535106015c.svg');
}
.flag-icon-nc {
  background-image: url('nc.5681de45e8340741e312.svg');
}
.flag-icon-nc.flag-icon-squared {
  background-image: url('nc.046cebb8a66efa64641c.svg');
}
.flag-icon-ne {
  background-image: url('ne.6fd3ccdef1a91e8be5ae.svg');
}
.flag-icon-ne.flag-icon-squared {
  background-image: url('ne.64f5ff142997ea42d42e.svg');
}
.flag-icon-nf {
  background-image: url('nf.7a4f3d1b34fa49f5a098.svg');
}
.flag-icon-nf.flag-icon-squared {
  background-image: url('nf.7c3216dcabd2a393fa48.svg');
}
.flag-icon-ng {
  background-image: url('ng.b69ed1e58b69dcadbf0e.svg');
}
.flag-icon-ng.flag-icon-squared {
  background-image: url('ng.caaf41205a6fde2a1853.svg');
}
.flag-icon-ni {
  background-image: url('ni.d30c03773b8ce5412033.svg');
}
.flag-icon-ni.flag-icon-squared {
  background-image: url('ni.52cfeebfb0f78f395c13.svg');
}
.flag-icon-nl {
  background-image: url('nl.21eb77dcfa38c6d7bb81.svg');
}
.flag-icon-nl.flag-icon-squared {
  background-image: url('nl.f9d570ec865ab2c1e9d4.svg');
}
.flag-icon-no {
  background-image: url('no.6df96bb22557028a5f77.svg');
}
.flag-icon-no.flag-icon-squared {
  background-image: url('no.266dbd6fc3e66414aa3c.svg');
}
.flag-icon-np {
  background-image: url('np.ecc31e52fb6b958eb681.svg');
}
.flag-icon-np.flag-icon-squared {
  background-image: url('np.f7885aa646996a2aa6e0.svg');
}
.flag-icon-nr {
  background-image: url('nr.dcf2ea3a8e5dbf8a9b80.svg');
}
.flag-icon-nr.flag-icon-squared {
  background-image: url('nr.1f7cfffb6cb01e5215ad.svg');
}
.flag-icon-nu {
  background-image: url('nu.770e6779515b496ac3b6.svg');
}
.flag-icon-nu.flag-icon-squared {
  background-image: url('nu.9deebec59d90dece17fc.svg');
}
.flag-icon-nz {
  background-image: url('nz.4dedf09b8933ec6f4390.svg');
}
.flag-icon-nz.flag-icon-squared {
  background-image: url('nz.37be84f4206a0eae405b.svg');
}
.flag-icon-om {
  background-image: url('om.716feea54634c16f406a.svg');
}
.flag-icon-om.flag-icon-squared {
  background-image: url('om.09e65f88432f6b938338.svg');
}
.flag-icon-pa {
  background-image: url('pa.8788ab50de263793f74b.svg');
}
.flag-icon-pa.flag-icon-squared {
  background-image: url('pa.69b3b90501ccfc42beff.svg');
}
.flag-icon-pe {
  background-image: url('pe.9e2ec84ad461c170e9e5.svg');
}
.flag-icon-pe.flag-icon-squared {
  background-image: url('pe.83c26459858b4334c435.svg');
}
.flag-icon-pf {
  background-image: url('pf.296e94595f307817fc2b.svg');
}
.flag-icon-pf.flag-icon-squared {
  background-image: url('pf.fbd548e641a7199e969d.svg');
}
.flag-icon-pg {
  background-image: url('pg.dcb8c4ab032af81620bd.svg');
}
.flag-icon-pg.flag-icon-squared {
  background-image: url('pg.9ebf0bb36bfe656e7aba.svg');
}
.flag-icon-ph {
  background-image: url('ph.596b9b66d026fa222c2d.svg');
}
.flag-icon-ph.flag-icon-squared {
  background-image: url('ph.374bb0d60bc777e60d0f.svg');
}
.flag-icon-pk {
  background-image: url('pk.8f9276eb2e7dc3eac94d.svg');
}
.flag-icon-pk.flag-icon-squared {
  background-image: url('pk.0e17f31b0f156316ef20.svg');
}
.flag-icon-pl {
  background-image: url('pl.7ea4b5b2df865bf73b06.svg');
}
.flag-icon-pl.flag-icon-squared {
  background-image: url('pl.9400273de5d060652ce7.svg');
}
.flag-icon-pm {
  background-image: url('pm.c5a64f87b2370f6a2ae3.svg');
}
.flag-icon-pm.flag-icon-squared {
  background-image: url('pm.7582c30f04bd66a4e73c.svg');
}
.flag-icon-pn {
  background-image: url('pn.0911eddaa4bb5cbf54b8.svg');
}
.flag-icon-pn.flag-icon-squared {
  background-image: url('pn.ab7259a94ec182c05827.svg');
}
.flag-icon-pr {
  background-image: url('pr.7845ecd77b3b58e3c8a5.svg');
}
.flag-icon-pr.flag-icon-squared {
  background-image: url('pr.836bcb814711de3da206.svg');
}
.flag-icon-ps {
  background-image: url('ps.77489c5e7455703ed84f.svg');
}
.flag-icon-ps.flag-icon-squared {
  background-image: url('ps.451101efffc43d2b1ae6.svg');
}
.flag-icon-pt {
  background-image: url('pt.b89a5b80ca1a71c3b96a.svg');
}
.flag-icon-pt.flag-icon-squared {
  background-image: url('pt.5697f2973616282e4c76.svg');
}
.flag-icon-pw {
  background-image: url('pw.5f3d8d8e8473f6439a21.svg');
}
.flag-icon-pw.flag-icon-squared {
  background-image: url('pw.666cd05f03955ea01aa5.svg');
}
.flag-icon-py {
  background-image: url('py.51b0bef3321042c04717.svg');
}
.flag-icon-py.flag-icon-squared {
  background-image: url('py.b19bb0ae9ad5d553a146.svg');
}
.flag-icon-qa {
  background-image: url('qa.90240e607800ce7ed1bd.svg');
}
.flag-icon-qa.flag-icon-squared {
  background-image: url('qa.c08921ec8cdc1b1a0e8e.svg');
}
.flag-icon-re {
  background-image: url('re.f05b5f1673afe6db0760.svg');
}
.flag-icon-re.flag-icon-squared {
  background-image: url('re.7e8577e98f035e765e59.svg');
}
.flag-icon-ro {
  background-image: url('ro.ea966c3dc0adf3d08a00.svg');
}
.flag-icon-ro.flag-icon-squared {
  background-image: url('ro.51182fc9671cbaa10989.svg');
}
.flag-icon-rs {
  background-image: url('rs.51e6180ced2cf59fd51e.svg');
}
.flag-icon-rs.flag-icon-squared {
  background-image: url('rs.3b67d7bed888271edff6.svg');
}
.flag-icon-ru {
  background-image: url('ru.f1c2ba49b3ccc06ba58a.svg');
}
.flag-icon-ru.flag-icon-squared {
  background-image: url('ru.f760036294e1fff52a9a.svg');
}
.flag-icon-rw {
  background-image: url('rw.26854553b660fa5e4982.svg');
}
.flag-icon-rw.flag-icon-squared {
  background-image: url('rw.02a8a07e06e4ae9c0122.svg');
}
.flag-icon-sa {
  background-image: url('sa.b9a346574cdc8950dd34.svg');
}
.flag-icon-sa.flag-icon-squared {
  background-image: url('sa.dbdc272cb217fd407ff8.svg');
}
.flag-icon-sb {
  background-image: url('sb.2c405bb603253b6e2040.svg');
}
.flag-icon-sb.flag-icon-squared {
  background-image: url('sb.00988e025b134db97443.svg');
}
.flag-icon-sc {
  background-image: url('sc.9ba013463b785efeb6be.svg');
}
.flag-icon-sc.flag-icon-squared {
  background-image: url('sc.8357ba15f90dbaaeec2a.svg');
}
.flag-icon-sd {
  background-image: url('sd.090d0e106e0c7fd28b23.svg');
}
.flag-icon-sd.flag-icon-squared {
  background-image: url('sd.cb3da007630d3b35d1ca.svg');
}
.flag-icon-se {
  background-image: url('se.22333e71c0c3e3d8da11.svg');
}
.flag-icon-se.flag-icon-squared {
  background-image: url('se.4984ae470ed69178af1d.svg');
}
.flag-icon-sg {
  background-image: url('sg.e6a27ad3fc2dedabca8b.svg');
}
.flag-icon-sg.flag-icon-squared {
  background-image: url('sg.9751741f84e6e5263a40.svg');
}
.flag-icon-sh {
  background-image: url('sh.c5ab8c6e3ffc963e14fb.svg');
}
.flag-icon-sh.flag-icon-squared {
  background-image: url('sh.acd7b6efcbb9ea9ca7e6.svg');
}
.flag-icon-si {
  background-image: url('si.7f576d8798a8732afa46.svg');
}
.flag-icon-si.flag-icon-squared {
  background-image: url('si.d38938fcbf4ff9430856.svg');
}
.flag-icon-sj {
  background-image: url('sj.4952c9a6d03f7d9caa08.svg');
}
.flag-icon-sj.flag-icon-squared {
  background-image: url('sj.bf11a7596686324ffe17.svg');
}
.flag-icon-sk {
  background-image: url('sk.be58e557507f14689689.svg');
}
.flag-icon-sk.flag-icon-squared {
  background-image: url('sk.563985765998ec7428e9.svg');
}
.flag-icon-sl {
  background-image: url('sl.4fd17d63688a1b300bca.svg');
}
.flag-icon-sl.flag-icon-squared {
  background-image: url('sl.f1772cd4436de4f4535a.svg');
}
.flag-icon-sm {
  background-image: url('sm.7e23c95234800a7c7e1e.svg');
}
.flag-icon-sm.flag-icon-squared {
  background-image: url('sm.b57f66050d777f87e0a5.svg');
}
.flag-icon-sn {
  background-image: url('sn.1ce79520b54b680dcc8e.svg');
}
.flag-icon-sn.flag-icon-squared {
  background-image: url('sn.a03bc309423c9052891a.svg');
}
.flag-icon-so {
  background-image: url('so.d1a7d3e18523bf69f2eb.svg');
}
.flag-icon-so.flag-icon-squared {
  background-image: url('so.34b037a702aae22a7534.svg');
}
.flag-icon-sr {
  background-image: url('sr.690504905775fc1b1480.svg');
}
.flag-icon-sr.flag-icon-squared {
  background-image: url('sr.4ee190817d3b7c7862ca.svg');
}
.flag-icon-ss {
  background-image: url('ss.e4bf9bb42bd5d25190fe.svg');
}
.flag-icon-ss.flag-icon-squared {
  background-image: url('ss.2ad5a2bbc138690704ad.svg');
}
.flag-icon-st {
  background-image: url('st.72697a87cfc011963be4.svg');
}
.flag-icon-st.flag-icon-squared {
  background-image: url('st.342a4d3c791c89ec3103.svg');
}
.flag-icon-sv {
  background-image: url('sv.3bb132d79aef68d7fe41.svg');
}
.flag-icon-sv.flag-icon-squared {
  background-image: url('sv.f4b3314001ea3a7cf1d0.svg');
}
.flag-icon-sx {
  background-image: url('sx.aafb13f61b6688f41a14.svg');
}
.flag-icon-sx.flag-icon-squared {
  background-image: url('sx.ce647f25cbbcdb00ed0d.svg');
}
.flag-icon-sy {
  background-image: url('sy.6529aa17e46f775a3931.svg');
}
.flag-icon-sy.flag-icon-squared {
  background-image: url('sy.9d13beb594ee208a6864.svg');
}
.flag-icon-sz {
  background-image: url('sz.d62f5eab640be40355e8.svg');
}
.flag-icon-sz.flag-icon-squared {
  background-image: url('sz.5d494168348fddfa3aeb.svg');
}
.flag-icon-tc {
  background-image: url('tc.f4f865830e706c26ef44.svg');
}
.flag-icon-tc.flag-icon-squared {
  background-image: url('tc.0d16f864a483488586df.svg');
}
.flag-icon-td {
  background-image: url('td.230eddd3b5d97166b70e.svg');
}
.flag-icon-td.flag-icon-squared {
  background-image: url('td.ad4b4469031fb72bd98f.svg');
}
.flag-icon-tf {
  background-image: url('tf.cf4959c4339d5b123093.svg');
}
.flag-icon-tf.flag-icon-squared {
  background-image: url('tf.aaea08de295f296f0bba.svg');
}
.flag-icon-tg {
  background-image: url('tg.f97c4ebe662df8683fde.svg');
}
.flag-icon-tg.flag-icon-squared {
  background-image: url('tg.4abbb52870d11bce293d.svg');
}
.flag-icon-th {
  background-image: url('th.b6ade2beba225ed5f2b5.svg');
}
.flag-icon-th.flag-icon-squared {
  background-image: url('th.2ca3db46e2b26412705d.svg');
}
.flag-icon-tj {
  background-image: url('tj.b32f7c017787f0d8579b.svg');
}
.flag-icon-tj.flag-icon-squared {
  background-image: url('tj.e97716cafb6e3b770d5b.svg');
}
.flag-icon-tk {
  background-image: url('tk.54b9e3f941cb3083e2a3.svg');
}
.flag-icon-tk.flag-icon-squared {
  background-image: url('tk.e3d850fb9644bf50d891.svg');
}
.flag-icon-tl {
  background-image: url('tl.503b7926732b784efbed.svg');
}
.flag-icon-tl.flag-icon-squared {
  background-image: url('tl.c7a146dc0d916983f8dd.svg');
}
.flag-icon-tm {
  background-image: url('tm.6de6696b70775fd30f3b.svg');
}
.flag-icon-tm.flag-icon-squared {
  background-image: url('tm.442986488503d5356e80.svg');
}
.flag-icon-tn {
  background-image: url('tn.6cd8c9a453cd0fc5e761.svg');
}
.flag-icon-tn.flag-icon-squared {
  background-image: url('tn.8b09464a7524dff3fa47.svg');
}
.flag-icon-to {
  background-image: url('to.65dda6ec6f9719bbd784.svg');
}
.flag-icon-to.flag-icon-squared {
  background-image: url('to.aaa24511e1160314531a.svg');
}
.flag-icon-tr {
  background-image: url('tr.2880fc8e0e28f4a11a8d.svg');
}
.flag-icon-tr.flag-icon-squared {
  background-image: url('tr.c3d773f3ebbea061e963.svg');
}
.flag-icon-tt {
  background-image: url('tt.b9a6939e2ab09927d190.svg');
}
.flag-icon-tt.flag-icon-squared {
  background-image: url('tt.4252a958aa98bb3ef5f7.svg');
}
.flag-icon-tv {
  background-image: url('tv.99618ffc3d126b12802f.svg');
}
.flag-icon-tv.flag-icon-squared {
  background-image: url('tv.c87adc622981a557f7a5.svg');
}
.flag-icon-tw {
  background-image: url('tw.83324ef79fd96b77a609.svg');
}
.flag-icon-tw.flag-icon-squared {
  background-image: url('tw.a72a85cb06aca393b4c6.svg');
}
.flag-icon-tz {
  background-image: url('tz.2218434d34c055a29fdf.svg');
}
.flag-icon-tz.flag-icon-squared {
  background-image: url('tz.20af2614818e325d953d.svg');
}
.flag-icon-ua {
  background-image: url('ua.67a46bf793e26237ace0.svg');
}
.flag-icon-ua.flag-icon-squared {
  background-image: url('ua.2dd397cb920452449aca.svg');
}
.flag-icon-ug {
  background-image: url('ug.83f4207b3f42150d667b.svg');
}
.flag-icon-ug.flag-icon-squared {
  background-image: url('ug.983dd591c56af488af96.svg');
}
.flag-icon-um {
  background-image: url('um.0f5d59d436cb8d91444b.svg');
}
.flag-icon-um.flag-icon-squared {
  background-image: url('um.c3e06b4d7dfec14653ce.svg');
}
.flag-icon-us {
  background-image: url('us.3cc0d38b3e8d93132c90.svg');
}
.flag-icon-us.flag-icon-squared {
  background-image: url('us.c95087e1a852cf730acc.svg');
}
.flag-icon-uy {
  background-image: url('uy.4c3f85c5401c3a3875ee.svg');
}
.flag-icon-uy.flag-icon-squared {
  background-image: url('uy.091841c8018480091fae.svg');
}
.flag-icon-uz {
  background-image: url('uz.6e16292aee6b5262a693.svg');
}
.flag-icon-uz.flag-icon-squared {
  background-image: url('uz.ba79d2974850ade2d036.svg');
}
.flag-icon-va {
  background-image: url('va.6d6a5ae672030a7e351f.svg');
}
.flag-icon-va.flag-icon-squared {
  background-image: url('va.555e01bd279c6bbc28c4.svg');
}
.flag-icon-vc {
  background-image: url('vc.e19034240ae39be40a4c.svg');
}
.flag-icon-vc.flag-icon-squared {
  background-image: url('vc.2614017538c99c0f2ea7.svg');
}
.flag-icon-ve {
  background-image: url('ve.b8bb0477d02228d82ffb.svg');
}
.flag-icon-ve.flag-icon-squared {
  background-image: url('ve.125ee9246eea1cb1b662.svg');
}
.flag-icon-vg {
  background-image: url('vg.7bc5c83294392b4386af.svg');
}
.flag-icon-vg.flag-icon-squared {
  background-image: url('vg.76c17dc332a16129789a.svg');
}
.flag-icon-vi {
  background-image: url('vi.bad0002a368d6bca0956.svg');
}
.flag-icon-vi.flag-icon-squared {
  background-image: url('vi.1ae3b627580608e32c99.svg');
}
.flag-icon-vn {
  background-image: url('vn.2301f6f466f2e744e48a.svg');
}
.flag-icon-vn.flag-icon-squared {
  background-image: url('vn.25540177a9e64be64b71.svg');
}
.flag-icon-vu {
  background-image: url('vu.25acc79729c1d8a104b6.svg');
}
.flag-icon-vu.flag-icon-squared {
  background-image: url('vu.87d69c1826cf7245c2d8.svg');
}
.flag-icon-wf {
  background-image: url('wf.e9a373c6bcbf8ea5021f.svg');
}
.flag-icon-wf.flag-icon-squared {
  background-image: url('wf.55c63880d97978216450.svg');
}
.flag-icon-ws {
  background-image: url('ws.a6817aa95b8cdf652ba6.svg');
}
.flag-icon-ws.flag-icon-squared {
  background-image: url('ws.81cbff3db85ab05a4ac8.svg');
}
.flag-icon-ye {
  background-image: url('ye.f62afcddf3ac8b1dfd3e.svg');
}
.flag-icon-ye.flag-icon-squared {
  background-image: url('ye.2289a38ef27f51f7e88a.svg');
}
.flag-icon-yt {
  background-image: url('yt.5c77b04743aada260f6b.svg');
}
.flag-icon-yt.flag-icon-squared {
  background-image: url('yt.bf9663aea16580e485d0.svg');
}
.flag-icon-za {
  background-image: url('za.83099da26ad6804ed7a5.svg');
}
.flag-icon-za.flag-icon-squared {
  background-image: url('za.3c71a2a22271a414dd3d.svg');
}
.flag-icon-zm {
  background-image: url('zm.79a41234f024ca5ab0a9.svg');
}
.flag-icon-zm.flag-icon-squared {
  background-image: url('zm.a303f51a36999e85ed3b.svg');
}
.flag-icon-zw {
  background-image: url('zw.f5ceeff7d65d0b5e33b7.svg');
}
.flag-icon-zw.flag-icon-squared {
  background-image: url('zw.2840e62aaca37d36b327.svg');
}
.flag-icon-es-ca {
  background-image: url('es-ca.2b2d073d58c4747e322e.svg');
}
.flag-icon-es-ca.flag-icon-squared {
  background-image: url('es-ca.a03129ff81e9f5633e75.svg');
}
.flag-icon-es-ga {
  background-image: url('es-ga.23a30d01d4c6338e7f5c.svg');
}
.flag-icon-es-ga.flag-icon-squared {
  background-image: url('es-ga.405f191245cb5d0085d7.svg');
}
.flag-icon-eu {
  background-image: url('eu.db2e50ad0bf5ecccc0ca.svg');
}
.flag-icon-eu.flag-icon-squared {
  background-image: url('eu.1019087b8a58ac24c4ea.svg');
}
.flag-icon-gb-eng {
  background-image: url('gb-eng.1fa89df2764a3f107c34.svg');
}
.flag-icon-gb-eng.flag-icon-squared {
  background-image: url('gb-eng.ebbd69accf16823ad684.svg');
}
.flag-icon-gb-nir {
  background-image: url('gb-nir.8ad03581959bce8c3da0.svg');
}
.flag-icon-gb-nir.flag-icon-squared {
  background-image: url('gb-nir.f9fcc604971f0e53b88d.svg');
}
.flag-icon-gb-sct {
  background-image: url('gb-sct.4743f1b0f3ffe8d16ba5.svg');
}
.flag-icon-gb-sct.flag-icon-squared {
  background-image: url('gb-sct.bea4865f11865ef56465.svg');
}
.flag-icon-gb-wls {
  background-image: url('gb-wls.a69b6bb076d89808e064.svg');
}
.flag-icon-gb-wls.flag-icon-squared {
  background-image: url('gb-wls.77f90d8e620af4d9604d.svg');
}
.flag-icon-un {
  background-image: url('un.4c0a34afb0c02cd13b3d.svg');
}
.flag-icon-un.flag-icon-squared {
  background-image: url('un.19c1ad320e901a90c0e3.svg');
}
.flag-icon-xk {
  background-image: url('xk.12d1409638442c96c8b9.svg');
}
.flag-icon-xk.flag-icon-squared {
  background-image: url('xk.48269c0b55c2532ccb76.svg');
}

/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[2]!./node_modules/font-awesome/css/font-awesome.css ***!
  \****************************************************************************************************************************************************************************************************************************************/
/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@font-face {
  font-family: 'FontAwesome';
  src: url('fontawesome-webfont.8b43027f47b20503057d.eot?v=4.7.0');
  src: url('fontawesome-webfont.8b43027f47b20503057d.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('fontawesome-webfont.20fd1704ea223900efa9.woff2?v=4.7.0') format('woff2'), url('fontawesome-webfont.f691f37e57f04c152e23.woff?v=4.7.0') format('woff'), url('fontawesome-webfont.1e59d2330b4c6deb84b3.ttf?v=4.7.0') format('truetype'), url('fontawesome-webfont.c1e38fd9e0e74ba58f7a.svg?v=4.7.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}
.fa-2x {
  font-size: 2em;
}
.fa-3x {
  font-size: 3em;
}
.fa-4x {
  font-size: 4em;
}
.fa-5x {
  font-size: 5em;
}
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}
.fa-ul > li {
  position: relative;
}
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}
.fa-li.fa-lg {
  left: -1.85714286em;
}
.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eeeeee;
  border-radius: .1em;
}
.fa-pull-left {
  float: left;
}
.fa-pull-right {
  float: right;
}
.fa.fa-pull-left {
  margin-right: .3em;
}
.fa.fa-pull-right {
  margin-left: .3em;
}
/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.fa.pull-left {
  margin-right: .3em;
}
.fa.pull-right {
  margin-left: .3em;
}
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}
@-webkit-keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  transform: rotate(90deg);
}
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  transform: rotate(180deg);
}
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  transform: rotate(270deg);
}
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  transform: scale(-1, 1);
}
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(1, -1);
}
:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  filter: none;
}
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}
.fa-stack-1x {
  line-height: inherit;
}
.fa-stack-2x {
  font-size: 2em;
}
.fa-inverse {
  color: #ffffff;
}
/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "\f000";
}
.fa-music:before {
  content: "\f001";
}
.fa-search:before {
  content: "\f002";
}
.fa-envelope-o:before {
  content: "\f003";
}
.fa-heart:before {
  content: "\f004";
}
.fa-star:before {
  content: "\f005";
}
.fa-star-o:before {
  content: "\f006";
}
.fa-user:before {
  content: "\f007";
}
.fa-film:before {
  content: "\f008";
}
.fa-th-large:before {
  content: "\f009";
}
.fa-th:before {
  content: "\f00a";
}
.fa-th-list:before {
  content: "\f00b";
}
.fa-check:before {
  content: "\f00c";
}
.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "\f00d";
}
.fa-search-plus:before {
  content: "\f00e";
}
.fa-search-minus:before {
  content: "\f010";
}
.fa-power-off:before {
  content: "\f011";
}
.fa-signal:before {
  content: "\f012";
}
.fa-gear:before,
.fa-cog:before {
  content: "\f013";
}
.fa-trash-o:before {
  content: "\f014";
}
.fa-home:before {
  content: "\f015";
}
.fa-file-o:before {
  content: "\f016";
}
.fa-clock-o:before {
  content: "\f017";
}
.fa-road:before {
  content: "\f018";
}
.fa-download:before {
  content: "\f019";
}
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}
.fa-inbox:before {
  content: "\f01c";
}
.fa-play-circle-o:before {
  content: "\f01d";
}
.fa-rotate-right:before,
.fa-repeat:before {
  content: "\f01e";
}
.fa-refresh:before {
  content: "\f021";
}
.fa-list-alt:before {
  content: "\f022";
}
.fa-lock:before {
  content: "\f023";
}
.fa-flag:before {
  content: "\f024";
}
.fa-headphones:before {
  content: "\f025";
}
.fa-volume-off:before {
  content: "\f026";
}
.fa-volume-down:before {
  content: "\f027";
}
.fa-volume-up:before {
  content: "\f028";
}
.fa-qrcode:before {
  content: "\f029";
}
.fa-barcode:before {
  content: "\f02a";
}
.fa-tag:before {
  content: "\f02b";
}
.fa-tags:before {
  content: "\f02c";
}
.fa-book:before {
  content: "\f02d";
}
.fa-bookmark:before {
  content: "\f02e";
}
.fa-print:before {
  content: "\f02f";
}
.fa-camera:before {
  content: "\f030";
}
.fa-font:before {
  content: "\f031";
}
.fa-bold:before {
  content: "\f032";
}
.fa-italic:before {
  content: "\f033";
}
.fa-text-height:before {
  content: "\f034";
}
.fa-text-width:before {
  content: "\f035";
}
.fa-align-left:before {
  content: "\f036";
}
.fa-align-center:before {
  content: "\f037";
}
.fa-align-right:before {
  content: "\f038";
}
.fa-align-justify:before {
  content: "\f039";
}
.fa-list:before {
  content: "\f03a";
}
.fa-dedent:before,
.fa-outdent:before {
  content: "\f03b";
}
.fa-indent:before {
  content: "\f03c";
}
.fa-video-camera:before {
  content: "\f03d";
}
.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "\f03e";
}
.fa-pencil:before {
  content: "\f040";
}
.fa-map-marker:before {
  content: "\f041";
}
.fa-adjust:before {
  content: "\f042";
}
.fa-tint:before {
  content: "\f043";
}
.fa-edit:before,
.fa-pencil-square-o:before {
  content: "\f044";
}
.fa-share-square-o:before {
  content: "\f045";
}
.fa-check-square-o:before {
  content: "\f046";
}
.fa-arrows:before {
  content: "\f047";
}
.fa-step-backward:before {
  content: "\f048";
}
.fa-fast-backward:before {
  content: "\f049";
}
.fa-backward:before {
  content: "\f04a";
}
.fa-play:before {
  content: "\f04b";
}
.fa-pause:before {
  content: "\f04c";
}
.fa-stop:before {
  content: "\f04d";
}
.fa-forward:before {
  content: "\f04e";
}
.fa-fast-forward:before {
  content: "\f050";
}
.fa-step-forward:before {
  content: "\f051";
}
.fa-eject:before {
  content: "\f052";
}
.fa-chevron-left:before {
  content: "\f053";
}
.fa-chevron-right:before {
  content: "\f054";
}
.fa-plus-circle:before {
  content: "\f055";
}
.fa-minus-circle:before {
  content: "\f056";
}
.fa-times-circle:before {
  content: "\f057";
}
.fa-check-circle:before {
  content: "\f058";
}
.fa-question-circle:before {
  content: "\f059";
}
.fa-info-circle:before {
  content: "\f05a";
}
.fa-crosshairs:before {
  content: "\f05b";
}
.fa-times-circle-o:before {
  content: "\f05c";
}
.fa-check-circle-o:before {
  content: "\f05d";
}
.fa-ban:before {
  content: "\f05e";
}
.fa-arrow-left:before {
  content: "\f060";
}
.fa-arrow-right:before {
  content: "\f061";
}
.fa-arrow-up:before {
  content: "\f062";
}
.fa-arrow-down:before {
  content: "\f063";
}
.fa-mail-forward:before,
.fa-share:before {
  content: "\f064";
}
.fa-expand:before {
  content: "\f065";
}
.fa-compress:before {
  content: "\f066";
}
.fa-plus:before {
  content: "\f067";
}
.fa-minus:before {
  content: "\f068";
}
.fa-asterisk:before {
  content: "\f069";
}
.fa-exclamation-circle:before {
  content: "\f06a";
}
.fa-gift:before {
  content: "\f06b";
}
.fa-leaf:before {
  content: "\f06c";
}
.fa-fire:before {
  content: "\f06d";
}
.fa-eye:before {
  content: "\f06e";
}
.fa-eye-slash:before {
  content: "\f070";
}
.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "\f071";
}
.fa-plane:before {
  content: "\f072";
}
.fa-calendar:before {
  content: "\f073";
}
.fa-random:before {
  content: "\f074";
}
.fa-comment:before {
  content: "\f075";
}
.fa-magnet:before {
  content: "\f076";
}
.fa-chevron-up:before {
  content: "\f077";
}
.fa-chevron-down:before {
  content: "\f078";
}
.fa-retweet:before {
  content: "\f079";
}
.fa-shopping-cart:before {
  content: "\f07a";
}
.fa-folder:before {
  content: "\f07b";
}
.fa-folder-open:before {
  content: "\f07c";
}
.fa-arrows-v:before {
  content: "\f07d";
}
.fa-arrows-h:before {
  content: "\f07e";
}
.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "\f080";
}
.fa-twitter-square:before {
  content: "\f081";
}
.fa-facebook-square:before {
  content: "\f082";
}
.fa-camera-retro:before {
  content: "\f083";
}
.fa-key:before {
  content: "\f084";
}
.fa-gears:before,
.fa-cogs:before {
  content: "\f085";
}
.fa-comments:before {
  content: "\f086";
}
.fa-thumbs-o-up:before {
  content: "\f087";
}
.fa-thumbs-o-down:before {
  content: "\f088";
}
.fa-star-half:before {
  content: "\f089";
}
.fa-heart-o:before {
  content: "\f08a";
}
.fa-sign-out:before {
  content: "\f08b";
}
.fa-linkedin-square:before {
  content: "\f08c";
}
.fa-thumb-tack:before {
  content: "\f08d";
}
.fa-external-link:before {
  content: "\f08e";
}
.fa-sign-in:before {
  content: "\f090";
}
.fa-trophy:before {
  content: "\f091";
}
.fa-github-square:before {
  content: "\f092";
}
.fa-upload:before {
  content: "\f093";
}
.fa-lemon-o:before {
  content: "\f094";
}
.fa-phone:before {
  content: "\f095";
}
.fa-square-o:before {
  content: "\f096";
}
.fa-bookmark-o:before {
  content: "\f097";
}
.fa-phone-square:before {
  content: "\f098";
}
.fa-twitter:before {
  content: "\f099";
}
.fa-facebook-f:before,
.fa-facebook:before {
  content: "\f09a";
}
.fa-github:before {
  content: "\f09b";
}
.fa-unlock:before {
  content: "\f09c";
}
.fa-credit-card:before {
  content: "\f09d";
}
.fa-feed:before,
.fa-rss:before {
  content: "\f09e";
}
.fa-hdd-o:before {
  content: "\f0a0";
}
.fa-bullhorn:before {
  content: "\f0a1";
}
.fa-bell:before {
  content: "\f0f3";
}
.fa-certificate:before {
  content: "\f0a3";
}
.fa-hand-o-right:before {
  content: "\f0a4";
}
.fa-hand-o-left:before {
  content: "\f0a5";
}
.fa-hand-o-up:before {
  content: "\f0a6";
}
.fa-hand-o-down:before {
  content: "\f0a7";
}
.fa-arrow-circle-left:before {
  content: "\f0a8";
}
.fa-arrow-circle-right:before {
  content: "\f0a9";
}
.fa-arrow-circle-up:before {
  content: "\f0aa";
}
.fa-arrow-circle-down:before {
  content: "\f0ab";
}
.fa-globe:before {
  content: "\f0ac";
}
.fa-wrench:before {
  content: "\f0ad";
}
.fa-tasks:before {
  content: "\f0ae";
}
.fa-filter:before {
  content: "\f0b0";
}
.fa-briefcase:before {
  content: "\f0b1";
}
.fa-arrows-alt:before {
  content: "\f0b2";
}
.fa-group:before,
.fa-users:before {
  content: "\f0c0";
}
.fa-chain:before,
.fa-link:before {
  content: "\f0c1";
}
.fa-cloud:before {
  content: "\f0c2";
}
.fa-flask:before {
  content: "\f0c3";
}
.fa-cut:before,
.fa-scissors:before {
  content: "\f0c4";
}
.fa-copy:before,
.fa-files-o:before {
  content: "\f0c5";
}
.fa-paperclip:before {
  content: "\f0c6";
}
.fa-save:before,
.fa-floppy-o:before {
  content: "\f0c7";
}
.fa-square:before {
  content: "\f0c8";
}
.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "\f0c9";
}
.fa-list-ul:before {
  content: "\f0ca";
}
.fa-list-ol:before {
  content: "\f0cb";
}
.fa-strikethrough:before {
  content: "\f0cc";
}
.fa-underline:before {
  content: "\f0cd";
}
.fa-table:before {
  content: "\f0ce";
}
.fa-magic:before {
  content: "\f0d0";
}
.fa-truck:before {
  content: "\f0d1";
}
.fa-pinterest:before {
  content: "\f0d2";
}
.fa-pinterest-square:before {
  content: "\f0d3";
}
.fa-google-plus-square:before {
  content: "\f0d4";
}
.fa-google-plus:before {
  content: "\f0d5";
}
.fa-money:before {
  content: "\f0d6";
}
.fa-caret-down:before {
  content: "\f0d7";
}
.fa-caret-up:before {
  content: "\f0d8";
}
.fa-caret-left:before {
  content: "\f0d9";
}
.fa-caret-right:before {
  content: "\f0da";
}
.fa-columns:before {
  content: "\f0db";
}
.fa-unsorted:before,
.fa-sort:before {
  content: "\f0dc";
}
.fa-sort-down:before,
.fa-sort-desc:before {
  content: "\f0dd";
}
.fa-sort-up:before,
.fa-sort-asc:before {
  content: "\f0de";
}
.fa-envelope:before {
  content: "\f0e0";
}
.fa-linkedin:before {
  content: "\f0e1";
}
.fa-rotate-left:before,
.fa-undo:before {
  content: "\f0e2";
}
.fa-legal:before,
.fa-gavel:before {
  content: "\f0e3";
}
.fa-dashboard:before,
.fa-tachometer:before {
  content: "\f0e4";
}
.fa-comment-o:before {
  content: "\f0e5";
}
.fa-comments-o:before {
  content: "\f0e6";
}
.fa-flash:before,
.fa-bolt:before {
  content: "\f0e7";
}
.fa-sitemap:before {
  content: "\f0e8";
}
.fa-umbrella:before {
  content: "\f0e9";
}
.fa-paste:before,
.fa-clipboard:before {
  content: "\f0ea";
}
.fa-lightbulb-o:before {
  content: "\f0eb";
}
.fa-exchange:before {
  content: "\f0ec";
}
.fa-cloud-download:before {
  content: "\f0ed";
}
.fa-cloud-upload:before {
  content: "\f0ee";
}
.fa-user-md:before {
  content: "\f0f0";
}
.fa-stethoscope:before {
  content: "\f0f1";
}
.fa-suitcase:before {
  content: "\f0f2";
}
.fa-bell-o:before {
  content: "\f0a2";
}
.fa-coffee:before {
  content: "\f0f4";
}
.fa-cutlery:before {
  content: "\f0f5";
}
.fa-file-text-o:before {
  content: "\f0f6";
}
.fa-building-o:before {
  content: "\f0f7";
}
.fa-hospital-o:before {
  content: "\f0f8";
}
.fa-ambulance:before {
  content: "\f0f9";
}
.fa-medkit:before {
  content: "\f0fa";
}
.fa-fighter-jet:before {
  content: "\f0fb";
}
.fa-beer:before {
  content: "\f0fc";
}
.fa-h-square:before {
  content: "\f0fd";
}
.fa-plus-square:before {
  content: "\f0fe";
}
.fa-angle-double-left:before {
  content: "\f100";
}
.fa-angle-double-right:before {
  content: "\f101";
}
.fa-angle-double-up:before {
  content: "\f102";
}
.fa-angle-double-down:before {
  content: "\f103";
}
.fa-angle-left:before {
  content: "\f104";
}
.fa-angle-right:before {
  content: "\f105";
}
.fa-angle-up:before {
  content: "\f106";
}
.fa-angle-down:before {
  content: "\f107";
}
.fa-desktop:before {
  content: "\f108";
}
.fa-laptop:before {
  content: "\f109";
}
.fa-tablet:before {
  content: "\f10a";
}
.fa-mobile-phone:before,
.fa-mobile:before {
  content: "\f10b";
}
.fa-circle-o:before {
  content: "\f10c";
}
.fa-quote-left:before {
  content: "\f10d";
}
.fa-quote-right:before {
  content: "\f10e";
}
.fa-spinner:before {
  content: "\f110";
}
.fa-circle:before {
  content: "\f111";
}
.fa-mail-reply:before,
.fa-reply:before {
  content: "\f112";
}
.fa-github-alt:before {
  content: "\f113";
}
.fa-folder-o:before {
  content: "\f114";
}
.fa-folder-open-o:before {
  content: "\f115";
}
.fa-smile-o:before {
  content: "\f118";
}
.fa-frown-o:before {
  content: "\f119";
}
.fa-meh-o:before {
  content: "\f11a";
}
.fa-gamepad:before {
  content: "\f11b";
}
.fa-keyboard-o:before {
  content: "\f11c";
}
.fa-flag-o:before {
  content: "\f11d";
}
.fa-flag-checkered:before {
  content: "\f11e";
}
.fa-terminal:before {
  content: "\f120";
}
.fa-code:before {
  content: "\f121";
}
.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "\f122";
}
.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "\f123";
}
.fa-location-arrow:before {
  content: "\f124";
}
.fa-crop:before {
  content: "\f125";
}
.fa-code-fork:before {
  content: "\f126";
}
.fa-unlink:before,
.fa-chain-broken:before {
  content: "\f127";
}
.fa-question:before {
  content: "\f128";
}
.fa-info:before {
  content: "\f129";
}
.fa-exclamation:before {
  content: "\f12a";
}
.fa-superscript:before {
  content: "\f12b";
}
.fa-subscript:before {
  content: "\f12c";
}
.fa-eraser:before {
  content: "\f12d";
}
.fa-puzzle-piece:before {
  content: "\f12e";
}
.fa-microphone:before {
  content: "\f130";
}
.fa-microphone-slash:before {
  content: "\f131";
}
.fa-shield:before {
  content: "\f132";
}
.fa-calendar-o:before {
  content: "\f133";
}
.fa-fire-extinguisher:before {
  content: "\f134";
}
.fa-rocket:before {
  content: "\f135";
}
.fa-maxcdn:before {
  content: "\f136";
}
.fa-chevron-circle-left:before {
  content: "\f137";
}
.fa-chevron-circle-right:before {
  content: "\f138";
}
.fa-chevron-circle-up:before {
  content: "\f139";
}
.fa-chevron-circle-down:before {
  content: "\f13a";
}
.fa-html5:before {
  content: "\f13b";
}
.fa-css3:before {
  content: "\f13c";
}
.fa-anchor:before {
  content: "\f13d";
}
.fa-unlock-alt:before {
  content: "\f13e";
}
.fa-bullseye:before {
  content: "\f140";
}
.fa-ellipsis-h:before {
  content: "\f141";
}
.fa-ellipsis-v:before {
  content: "\f142";
}
.fa-rss-square:before {
  content: "\f143";
}
.fa-play-circle:before {
  content: "\f144";
}
.fa-ticket:before {
  content: "\f145";
}
.fa-minus-square:before {
  content: "\f146";
}
.fa-minus-square-o:before {
  content: "\f147";
}
.fa-level-up:before {
  content: "\f148";
}
.fa-level-down:before {
  content: "\f149";
}
.fa-check-square:before {
  content: "\f14a";
}
.fa-pencil-square:before {
  content: "\f14b";
}
.fa-external-link-square:before {
  content: "\f14c";
}
.fa-share-square:before {
  content: "\f14d";
}
.fa-compass:before {
  content: "\f14e";
}
.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "\f150";
}
.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "\f151";
}
.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "\f152";
}
.fa-euro:before,
.fa-eur:before {
  content: "\f153";
}
.fa-gbp:before {
  content: "\f154";
}
.fa-dollar:before,
.fa-usd:before {
  content: "\f155";
}
.fa-rupee:before,
.fa-inr:before {
  content: "\f156";
}
.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "\f157";
}
.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "\f158";
}
.fa-won:before,
.fa-krw:before {
  content: "\f159";
}
.fa-bitcoin:before,
.fa-btc:before {
  content: "\f15a";
}
.fa-file:before {
  content: "\f15b";
}
.fa-file-text:before {
  content: "\f15c";
}
.fa-sort-alpha-asc:before {
  content: "\f15d";
}
.fa-sort-alpha-desc:before {
  content: "\f15e";
}
.fa-sort-amount-asc:before {
  content: "\f160";
}
.fa-sort-amount-desc:before {
  content: "\f161";
}
.fa-sort-numeric-asc:before {
  content: "\f162";
}
.fa-sort-numeric-desc:before {
  content: "\f163";
}
.fa-thumbs-up:before {
  content: "\f164";
}
.fa-thumbs-down:before {
  content: "\f165";
}
.fa-youtube-square:before {
  content: "\f166";
}
.fa-youtube:before {
  content: "\f167";
}
.fa-xing:before {
  content: "\f168";
}
.fa-xing-square:before {
  content: "\f169";
}
.fa-youtube-play:before {
  content: "\f16a";
}
.fa-dropbox:before {
  content: "\f16b";
}
.fa-stack-overflow:before {
  content: "\f16c";
}
.fa-instagram:before {
  content: "\f16d";
}
.fa-flickr:before {
  content: "\f16e";
}
.fa-adn:before {
  content: "\f170";
}
.fa-bitbucket:before {
  content: "\f171";
}
.fa-bitbucket-square:before {
  content: "\f172";
}
.fa-tumblr:before {
  content: "\f173";
}
.fa-tumblr-square:before {
  content: "\f174";
}
.fa-long-arrow-down:before {
  content: "\f175";
}
.fa-long-arrow-up:before {
  content: "\f176";
}
.fa-long-arrow-left:before {
  content: "\f177";
}
.fa-long-arrow-right:before {
  content: "\f178";
}
.fa-apple:before {
  content: "\f179";
}
.fa-windows:before {
  content: "\f17a";
}
.fa-android:before {
  content: "\f17b";
}
.fa-linux:before {
  content: "\f17c";
}
.fa-dribbble:before {
  content: "\f17d";
}
.fa-skype:before {
  content: "\f17e";
}
.fa-foursquare:before {
  content: "\f180";
}
.fa-trello:before {
  content: "\f181";
}
.fa-female:before {
  content: "\f182";
}
.fa-male:before {
  content: "\f183";
}
.fa-gittip:before,
.fa-gratipay:before {
  content: "\f184";
}
.fa-sun-o:before {
  content: "\f185";
}
.fa-moon-o:before {
  content: "\f186";
}
.fa-archive:before {
  content: "\f187";
}
.fa-bug:before {
  content: "\f188";
}
.fa-vk:before {
  content: "\f189";
}
.fa-weibo:before {
  content: "\f18a";
}
.fa-renren:before {
  content: "\f18b";
}
.fa-pagelines:before {
  content: "\f18c";
}
.fa-stack-exchange:before {
  content: "\f18d";
}
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}
.fa-arrow-circle-o-left:before {
  content: "\f190";
}
.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "\f191";
}
.fa-dot-circle-o:before {
  content: "\f192";
}
.fa-wheelchair:before {
  content: "\f193";
}
.fa-vimeo-square:before {
  content: "\f194";
}
.fa-turkish-lira:before,
.fa-try:before {
  content: "\f195";
}
.fa-plus-square-o:before {
  content: "\f196";
}
.fa-space-shuttle:before {
  content: "\f197";
}
.fa-slack:before {
  content: "\f198";
}
.fa-envelope-square:before {
  content: "\f199";
}
.fa-wordpress:before {
  content: "\f19a";
}
.fa-openid:before {
  content: "\f19b";
}
.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "\f19c";
}
.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "\f19d";
}
.fa-yahoo:before {
  content: "\f19e";
}
.fa-google:before {
  content: "\f1a0";
}
.fa-reddit:before {
  content: "\f1a1";
}
.fa-reddit-square:before {
  content: "\f1a2";
}
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}
.fa-stumbleupon:before {
  content: "\f1a4";
}
.fa-delicious:before {
  content: "\f1a5";
}
.fa-digg:before {
  content: "\f1a6";
}
.fa-pied-piper-pp:before {
  content: "\f1a7";
}
.fa-pied-piper-alt:before {
  content: "\f1a8";
}
.fa-drupal:before {
  content: "\f1a9";
}
.fa-joomla:before {
  content: "\f1aa";
}
.fa-language:before {
  content: "\f1ab";
}
.fa-fax:before {
  content: "\f1ac";
}
.fa-building:before {
  content: "\f1ad";
}
.fa-child:before {
  content: "\f1ae";
}
.fa-paw:before {
  content: "\f1b0";
}
.fa-spoon:before {
  content: "\f1b1";
}
.fa-cube:before {
  content: "\f1b2";
}
.fa-cubes:before {
  content: "\f1b3";
}
.fa-behance:before {
  content: "\f1b4";
}
.fa-behance-square:before {
  content: "\f1b5";
}
.fa-steam:before {
  content: "\f1b6";
}
.fa-steam-square:before {
  content: "\f1b7";
}
.fa-recycle:before {
  content: "\f1b8";
}
.fa-automobile:before,
.fa-car:before {
  content: "\f1b9";
}
.fa-cab:before,
.fa-taxi:before {
  content: "\f1ba";
}
.fa-tree:before {
  content: "\f1bb";
}
.fa-spotify:before {
  content: "\f1bc";
}
.fa-deviantart:before {
  content: "\f1bd";
}
.fa-soundcloud:before {
  content: "\f1be";
}
.fa-database:before {
  content: "\f1c0";
}
.fa-file-pdf-o:before {
  content: "\f1c1";
}
.fa-file-word-o:before {
  content: "\f1c2";
}
.fa-file-excel-o:before {
  content: "\f1c3";
}
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}
.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "\f1c5";
}
.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "\f1c6";
}
.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "\f1c7";
}
.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "\f1c8";
}
.fa-file-code-o:before {
  content: "\f1c9";
}
.fa-vine:before {
  content: "\f1ca";
}
.fa-codepen:before {
  content: "\f1cb";
}
.fa-jsfiddle:before {
  content: "\f1cc";
}
.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "\f1cd";
}
.fa-circle-o-notch:before {
  content: "\f1ce";
}
.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: "\f1d0";
}
.fa-ge:before,
.fa-empire:before {
  content: "\f1d1";
}
.fa-git-square:before {
  content: "\f1d2";
}
.fa-git:before {
  content: "\f1d3";
}
.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "\f1d4";
}
.fa-tencent-weibo:before {
  content: "\f1d5";
}
.fa-qq:before {
  content: "\f1d6";
}
.fa-wechat:before,
.fa-weixin:before {
  content: "\f1d7";
}
.fa-send:before,
.fa-paper-plane:before {
  content: "\f1d8";
}
.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "\f1d9";
}
.fa-history:before {
  content: "\f1da";
}
.fa-circle-thin:before {
  content: "\f1db";
}
.fa-header:before {
  content: "\f1dc";
}
.fa-paragraph:before {
  content: "\f1dd";
}
.fa-sliders:before {
  content: "\f1de";
}
.fa-share-alt:before {
  content: "\f1e0";
}
.fa-share-alt-square:before {
  content: "\f1e1";
}
.fa-bomb:before {
  content: "\f1e2";
}
.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "\f1e3";
}
.fa-tty:before {
  content: "\f1e4";
}
.fa-binoculars:before {
  content: "\f1e5";
}
.fa-plug:before {
  content: "\f1e6";
}
.fa-slideshare:before {
  content: "\f1e7";
}
.fa-twitch:before {
  content: "\f1e8";
}
.fa-yelp:before {
  content: "\f1e9";
}
.fa-newspaper-o:before {
  content: "\f1ea";
}
.fa-wifi:before {
  content: "\f1eb";
}
.fa-calculator:before {
  content: "\f1ec";
}
.fa-paypal:before {
  content: "\f1ed";
}
.fa-google-wallet:before {
  content: "\f1ee";
}
.fa-cc-visa:before {
  content: "\f1f0";
}
.fa-cc-mastercard:before {
  content: "\f1f1";
}
.fa-cc-discover:before {
  content: "\f1f2";
}
.fa-cc-amex:before {
  content: "\f1f3";
}
.fa-cc-paypal:before {
  content: "\f1f4";
}
.fa-cc-stripe:before {
  content: "\f1f5";
}
.fa-bell-slash:before {
  content: "\f1f6";
}
.fa-bell-slash-o:before {
  content: "\f1f7";
}
.fa-trash:before {
  content: "\f1f8";
}
.fa-copyright:before {
  content: "\f1f9";
}
.fa-at:before {
  content: "\f1fa";
}
.fa-eyedropper:before {
  content: "\f1fb";
}
.fa-paint-brush:before {
  content: "\f1fc";
}
.fa-birthday-cake:before {
  content: "\f1fd";
}
.fa-area-chart:before {
  content: "\f1fe";
}
.fa-pie-chart:before {
  content: "\f200";
}
.fa-line-chart:before {
  content: "\f201";
}
.fa-lastfm:before {
  content: "\f202";
}
.fa-lastfm-square:before {
  content: "\f203";
}
.fa-toggle-off:before {
  content: "\f204";
}
.fa-toggle-on:before {
  content: "\f205";
}
.fa-bicycle:before {
  content: "\f206";
}
.fa-bus:before {
  content: "\f207";
}
.fa-ioxhost:before {
  content: "\f208";
}
.fa-angellist:before {
  content: "\f209";
}
.fa-cc:before {
  content: "\f20a";
}
.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "\f20b";
}
.fa-meanpath:before {
  content: "\f20c";
}
.fa-buysellads:before {
  content: "\f20d";
}
.fa-connectdevelop:before {
  content: "\f20e";
}
.fa-dashcube:before {
  content: "\f210";
}
.fa-forumbee:before {
  content: "\f211";
}
.fa-leanpub:before {
  content: "\f212";
}
.fa-sellsy:before {
  content: "\f213";
}
.fa-shirtsinbulk:before {
  content: "\f214";
}
.fa-simplybuilt:before {
  content: "\f215";
}
.fa-skyatlas:before {
  content: "\f216";
}
.fa-cart-plus:before {
  content: "\f217";
}
.fa-cart-arrow-down:before {
  content: "\f218";
}
.fa-diamond:before {
  content: "\f219";
}
.fa-ship:before {
  content: "\f21a";
}
.fa-user-secret:before {
  content: "\f21b";
}
.fa-motorcycle:before {
  content: "\f21c";
}
.fa-street-view:before {
  content: "\f21d";
}
.fa-heartbeat:before {
  content: "\f21e";
}
.fa-venus:before {
  content: "\f221";
}
.fa-mars:before {
  content: "\f222";
}
.fa-mercury:before {
  content: "\f223";
}
.fa-intersex:before,
.fa-transgender:before {
  content: "\f224";
}
.fa-transgender-alt:before {
  content: "\f225";
}
.fa-venus-double:before {
  content: "\f226";
}
.fa-mars-double:before {
  content: "\f227";
}
.fa-venus-mars:before {
  content: "\f228";
}
.fa-mars-stroke:before {
  content: "\f229";
}
.fa-mars-stroke-v:before {
  content: "\f22a";
}
.fa-mars-stroke-h:before {
  content: "\f22b";
}
.fa-neuter:before {
  content: "\f22c";
}
.fa-genderless:before {
  content: "\f22d";
}
.fa-facebook-official:before {
  content: "\f230";
}
.fa-pinterest-p:before {
  content: "\f231";
}
.fa-whatsapp:before {
  content: "\f232";
}
.fa-server:before {
  content: "\f233";
}
.fa-user-plus:before {
  content: "\f234";
}
.fa-user-times:before {
  content: "\f235";
}
.fa-hotel:before,
.fa-bed:before {
  content: "\f236";
}
.fa-viacoin:before {
  content: "\f237";
}
.fa-train:before {
  content: "\f238";
}
.fa-subway:before {
  content: "\f239";
}
.fa-medium:before {
  content: "\f23a";
}
.fa-yc:before,
.fa-y-combinator:before {
  content: "\f23b";
}
.fa-optin-monster:before {
  content: "\f23c";
}
.fa-opencart:before {
  content: "\f23d";
}
.fa-expeditedssl:before {
  content: "\f23e";
}
.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: "\f240";
}
.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "\f241";
}
.fa-battery-2:before,
.fa-battery-half:before {
  content: "\f242";
}
.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "\f243";
}
.fa-battery-0:before,
.fa-battery-empty:before {
  content: "\f244";
}
.fa-mouse-pointer:before {
  content: "\f245";
}
.fa-i-cursor:before {
  content: "\f246";
}
.fa-object-group:before {
  content: "\f247";
}
.fa-object-ungroup:before {
  content: "\f248";
}
.fa-sticky-note:before {
  content: "\f249";
}
.fa-sticky-note-o:before {
  content: "\f24a";
}
.fa-cc-jcb:before {
  content: "\f24b";
}
.fa-cc-diners-club:before {
  content: "\f24c";
}
.fa-clone:before {
  content: "\f24d";
}
.fa-balance-scale:before {
  content: "\f24e";
}
.fa-hourglass-o:before {
  content: "\f250";
}
.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "\f251";
}
.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "\f252";
}
.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "\f253";
}
.fa-hourglass:before {
  content: "\f254";
}
.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "\f255";
}
.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "\f256";
}
.fa-hand-scissors-o:before {
  content: "\f257";
}
.fa-hand-lizard-o:before {
  content: "\f258";
}
.fa-hand-spock-o:before {
  content: "\f259";
}
.fa-hand-pointer-o:before {
  content: "\f25a";
}
.fa-hand-peace-o:before {
  content: "\f25b";
}
.fa-trademark:before {
  content: "\f25c";
}
.fa-registered:before {
  content: "\f25d";
}
.fa-creative-commons:before {
  content: "\f25e";
}
.fa-gg:before {
  content: "\f260";
}
.fa-gg-circle:before {
  content: "\f261";
}
.fa-tripadvisor:before {
  content: "\f262";
}
.fa-odnoklassniki:before {
  content: "\f263";
}
.fa-odnoklassniki-square:before {
  content: "\f264";
}
.fa-get-pocket:before {
  content: "\f265";
}
.fa-wikipedia-w:before {
  content: "\f266";
}
.fa-safari:before {
  content: "\f267";
}
.fa-chrome:before {
  content: "\f268";
}
.fa-firefox:before {
  content: "\f269";
}
.fa-opera:before {
  content: "\f26a";
}
.fa-internet-explorer:before {
  content: "\f26b";
}
.fa-tv:before,
.fa-television:before {
  content: "\f26c";
}
.fa-contao:before {
  content: "\f26d";
}
.fa-500px:before {
  content: "\f26e";
}
.fa-amazon:before {
  content: "\f270";
}
.fa-calendar-plus-o:before {
  content: "\f271";
}
.fa-calendar-minus-o:before {
  content: "\f272";
}
.fa-calendar-times-o:before {
  content: "\f273";
}
.fa-calendar-check-o:before {
  content: "\f274";
}
.fa-industry:before {
  content: "\f275";
}
.fa-map-pin:before {
  content: "\f276";
}
.fa-map-signs:before {
  content: "\f277";
}
.fa-map-o:before {
  content: "\f278";
}
.fa-map:before {
  content: "\f279";
}
.fa-commenting:before {
  content: "\f27a";
}
.fa-commenting-o:before {
  content: "\f27b";
}
.fa-houzz:before {
  content: "\f27c";
}
.fa-vimeo:before {
  content: "\f27d";
}
.fa-black-tie:before {
  content: "\f27e";
}
.fa-fonticons:before {
  content: "\f280";
}
.fa-reddit-alien:before {
  content: "\f281";
}
.fa-edge:before {
  content: "\f282";
}
.fa-credit-card-alt:before {
  content: "\f283";
}
.fa-codiepie:before {
  content: "\f284";
}
.fa-modx:before {
  content: "\f285";
}
.fa-fort-awesome:before {
  content: "\f286";
}
.fa-usb:before {
  content: "\f287";
}
.fa-product-hunt:before {
  content: "\f288";
}
.fa-mixcloud:before {
  content: "\f289";
}
.fa-scribd:before {
  content: "\f28a";
}
.fa-pause-circle:before {
  content: "\f28b";
}
.fa-pause-circle-o:before {
  content: "\f28c";
}
.fa-stop-circle:before {
  content: "\f28d";
}
.fa-stop-circle-o:before {
  content: "\f28e";
}
.fa-shopping-bag:before {
  content: "\f290";
}
.fa-shopping-basket:before {
  content: "\f291";
}
.fa-hashtag:before {
  content: "\f292";
}
.fa-bluetooth:before {
  content: "\f293";
}
.fa-bluetooth-b:before {
  content: "\f294";
}
.fa-percent:before {
  content: "\f295";
}
.fa-gitlab:before {
  content: "\f296";
}
.fa-wpbeginner:before {
  content: "\f297";
}
.fa-wpforms:before {
  content: "\f298";
}
.fa-envira:before {
  content: "\f299";
}
.fa-universal-access:before {
  content: "\f29a";
}
.fa-wheelchair-alt:before {
  content: "\f29b";
}
.fa-question-circle-o:before {
  content: "\f29c";
}
.fa-blind:before {
  content: "\f29d";
}
.fa-audio-description:before {
  content: "\f29e";
}
.fa-volume-control-phone:before {
  content: "\f2a0";
}
.fa-braille:before {
  content: "\f2a1";
}
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}
.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}
.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: "\f2a4";
}
.fa-glide:before {
  content: "\f2a5";
}
.fa-glide-g:before {
  content: "\f2a6";
}
.fa-signing:before,
.fa-sign-language:before {
  content: "\f2a7";
}
.fa-low-vision:before {
  content: "\f2a8";
}
.fa-viadeo:before {
  content: "\f2a9";
}
.fa-viadeo-square:before {
  content: "\f2aa";
}
.fa-snapchat:before {
  content: "\f2ab";
}
.fa-snapchat-ghost:before {
  content: "\f2ac";
}
.fa-snapchat-square:before {
  content: "\f2ad";
}
.fa-pied-piper:before {
  content: "\f2ae";
}
.fa-first-order:before {
  content: "\f2b0";
}
.fa-yoast:before {
  content: "\f2b1";
}
.fa-themeisle:before {
  content: "\f2b2";
}
.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "\f2b3";
}
.fa-fa:before,
.fa-font-awesome:before {
  content: "\f2b4";
}
.fa-handshake-o:before {
  content: "\f2b5";
}
.fa-envelope-open:before {
  content: "\f2b6";
}
.fa-envelope-open-o:before {
  content: "\f2b7";
}
.fa-linode:before {
  content: "\f2b8";
}
.fa-address-book:before {
  content: "\f2b9";
}
.fa-address-book-o:before {
  content: "\f2ba";
}
.fa-vcard:before,
.fa-address-card:before {
  content: "\f2bb";
}
.fa-vcard-o:before,
.fa-address-card-o:before {
  content: "\f2bc";
}
.fa-user-circle:before {
  content: "\f2bd";
}
.fa-user-circle-o:before {
  content: "\f2be";
}
.fa-user-o:before {
  content: "\f2c0";
}
.fa-id-badge:before {
  content: "\f2c1";
}
.fa-drivers-license:before,
.fa-id-card:before {
  content: "\f2c2";
}
.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "\f2c3";
}
.fa-quora:before {
  content: "\f2c4";
}
.fa-free-code-camp:before {
  content: "\f2c5";
}
.fa-telegram:before {
  content: "\f2c6";
}
.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: "\f2c7";
}
.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}
.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "\f2c9";
}
.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "\f2ca";
}
.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "\f2cb";
}
.fa-shower:before {
  content: "\f2cc";
}
.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: "\f2cd";
}
.fa-podcast:before {
  content: "\f2ce";
}
.fa-window-maximize:before {
  content: "\f2d0";
}
.fa-window-minimize:before {
  content: "\f2d1";
}
.fa-window-restore:before {
  content: "\f2d2";
}
.fa-times-rectangle:before,
.fa-window-close:before {
  content: "\f2d3";
}
.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "\f2d4";
}
.fa-bandcamp:before {
  content: "\f2d5";
}
.fa-grav:before {
  content: "\f2d6";
}
.fa-etsy:before {
  content: "\f2d7";
}
.fa-imdb:before {
  content: "\f2d8";
}
.fa-ravelry:before {
  content: "\f2d9";
}
.fa-eercast:before {
  content: "\f2da";
}
.fa-microchip:before {
  content: "\f2db";
}
.fa-snowflake-o:before {
  content: "\f2dc";
}
.fa-superpowers:before {
  content: "\f2dd";
}
.fa-wpexplorer:before {
  content: "\f2de";
}
.fa-meetup:before {
  content: "\f2e0";
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].rules[0].oneOf[1].use[2]!./node_modules/simple-line-icons/dist/styles/simple-line-icons.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/*
* Font Face
*/
@font-face {
  font-family: 'simple-line-icons';
  src: url('Simple-Line-Icons.85e8c542d5e137beecf9.eot?v=2.4.0');
  src: url('Simple-Line-Icons.85e8c542d5e137beecf9.eot?v=2.4.0#iefix') format('embedded-opentype'), url('Simple-Line-Icons.3826fa1cb2348dd93948.woff2?v=2.4.0') format('woff2'), url('Simple-Line-Icons.3ec13a24af3fdda11107.ttf?v=2.4.0') format('truetype'), url('Simple-Line-Icons.5c9febce52054ae0b96d.woff?v=2.4.0') format('woff'), url('Simple-Line-Icons.f1515a459c8850890812.svg?v=2.4.0#simple-line-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}
/*
 Use the following code if you want to have a class per icon.
 Instead of a list of all class selectors, you can use the generic [class*="icon-"] selector,
 but it's slower.
*/
.icon-user,
.icon-people,
.icon-user-female,
.icon-user-follow,
.icon-user-following,
.icon-user-unfollow,
.icon-login,
.icon-logout,
.icon-emotsmile,
.icon-phone,
.icon-call-end,
.icon-call-in,
.icon-call-out,
.icon-map,
.icon-location-pin,
.icon-direction,
.icon-directions,
.icon-compass,
.icon-layers,
.icon-menu,
.icon-list,
.icon-options-vertical,
.icon-options,
.icon-arrow-down,
.icon-arrow-left,
.icon-arrow-right,
.icon-arrow-up,
.icon-arrow-up-circle,
.icon-arrow-left-circle,
.icon-arrow-right-circle,
.icon-arrow-down-circle,
.icon-check,
.icon-clock,
.icon-plus,
.icon-minus,
.icon-close,
.icon-event,
.icon-exclamation,
.icon-organization,
.icon-trophy,
.icon-screen-smartphone,
.icon-screen-desktop,
.icon-plane,
.icon-notebook,
.icon-mustache,
.icon-mouse,
.icon-magnet,
.icon-energy,
.icon-disc,
.icon-cursor,
.icon-cursor-move,
.icon-crop,
.icon-chemistry,
.icon-speedometer,
.icon-shield,
.icon-screen-tablet,
.icon-magic-wand,
.icon-hourglass,
.icon-graduation,
.icon-ghost,
.icon-game-controller,
.icon-fire,
.icon-eyeglass,
.icon-envelope-open,
.icon-envelope-letter,
.icon-bell,
.icon-badge,
.icon-anchor,
.icon-wallet,
.icon-vector,
.icon-speech,
.icon-puzzle,
.icon-printer,
.icon-present,
.icon-playlist,
.icon-pin,
.icon-picture,
.icon-handbag,
.icon-globe-alt,
.icon-globe,
.icon-folder-alt,
.icon-folder,
.icon-film,
.icon-feed,
.icon-drop,
.icon-drawer,
.icon-docs,
.icon-doc,
.icon-diamond,
.icon-cup,
.icon-calculator,
.icon-bubbles,
.icon-briefcase,
.icon-book-open,
.icon-basket-loaded,
.icon-basket,
.icon-bag,
.icon-action-undo,
.icon-action-redo,
.icon-wrench,
.icon-umbrella,
.icon-trash,
.icon-tag,
.icon-support,
.icon-frame,
.icon-size-fullscreen,
.icon-size-actual,
.icon-shuffle,
.icon-share-alt,
.icon-share,
.icon-rocket,
.icon-question,
.icon-pie-chart,
.icon-pencil,
.icon-note,
.icon-loop,
.icon-home,
.icon-grid,
.icon-graph,
.icon-microphone,
.icon-music-tone-alt,
.icon-music-tone,
.icon-earphones-alt,
.icon-earphones,
.icon-equalizer,
.icon-like,
.icon-dislike,
.icon-control-start,
.icon-control-rewind,
.icon-control-play,
.icon-control-pause,
.icon-control-forward,
.icon-control-end,
.icon-volume-1,
.icon-volume-2,
.icon-volume-off,
.icon-calendar,
.icon-bulb,
.icon-chart,
.icon-ban,
.icon-bubble,
.icon-camrecorder,
.icon-camera,
.icon-cloud-download,
.icon-cloud-upload,
.icon-envelope,
.icon-eye,
.icon-flag,
.icon-heart,
.icon-info,
.icon-key,
.icon-link,
.icon-lock,
.icon-lock-open,
.icon-magnifier,
.icon-magnifier-add,
.icon-magnifier-remove,
.icon-paper-clip,
.icon-paper-plane,
.icon-power,
.icon-refresh,
.icon-reload,
.icon-settings,
.icon-star,
.icon-symbol-female,
.icon-symbol-male,
.icon-target,
.icon-credit-card,
.icon-paypal,
.icon-social-tumblr,
.icon-social-twitter,
.icon-social-facebook,
.icon-social-instagram,
.icon-social-linkedin,
.icon-social-pinterest,
.icon-social-github,
.icon-social-google,
.icon-social-reddit,
.icon-social-skype,
.icon-social-dribbble,
.icon-social-behance,
.icon-social-foursqare,
.icon-social-soundcloud,
.icon-social-spotify,
.icon-social-stumbleupon,
.icon-social-youtube,
.icon-social-dropbox,
.icon-social-vkontakte,
.icon-social-steam {
  font-family: 'simple-line-icons';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-feature-settings: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-user:before {
  content: "\e005";
}
.icon-people:before {
  content: "\e001";
}
.icon-user-female:before {
  content: "\e000";
}
.icon-user-follow:before {
  content: "\e002";
}
.icon-user-following:before {
  content: "\e003";
}
.icon-user-unfollow:before {
  content: "\e004";
}
.icon-login:before {
  content: "\e066";
}
.icon-logout:before {
  content: "\e065";
}
.icon-emotsmile:before {
  content: "\e021";
}
.icon-phone:before {
  content: "\e600";
}
.icon-call-end:before {
  content: "\e048";
}
.icon-call-in:before {
  content: "\e047";
}
.icon-call-out:before {
  content: "\e046";
}
.icon-map:before {
  content: "\e033";
}
.icon-location-pin:before {
  content: "\e096";
}
.icon-direction:before {
  content: "\e042";
}
.icon-directions:before {
  content: "\e041";
}
.icon-compass:before {
  content: "\e045";
}
.icon-layers:before {
  content: "\e034";
}
.icon-menu:before {
  content: "\e601";
}
.icon-list:before {
  content: "\e067";
}
.icon-options-vertical:before {
  content: "\e602";
}
.icon-options:before {
  content: "\e603";
}
.icon-arrow-down:before {
  content: "\e604";
}
.icon-arrow-left:before {
  content: "\e605";
}
.icon-arrow-right:before {
  content: "\e606";
}
.icon-arrow-up:before {
  content: "\e607";
}
.icon-arrow-up-circle:before {
  content: "\e078";
}
.icon-arrow-left-circle:before {
  content: "\e07a";
}
.icon-arrow-right-circle:before {
  content: "\e079";
}
.icon-arrow-down-circle:before {
  content: "\e07b";
}
.icon-check:before {
  content: "\e080";
}
.icon-clock:before {
  content: "\e081";
}
.icon-plus:before {
  content: "\e095";
}
.icon-minus:before {
  content: "\e615";
}
.icon-close:before {
  content: "\e082";
}
.icon-event:before {
  content: "\e619";
}
.icon-exclamation:before {
  content: "\e617";
}
.icon-organization:before {
  content: "\e616";
}
.icon-trophy:before {
  content: "\e006";
}
.icon-screen-smartphone:before {
  content: "\e010";
}
.icon-screen-desktop:before {
  content: "\e011";
}
.icon-plane:before {
  content: "\e012";
}
.icon-notebook:before {
  content: "\e013";
}
.icon-mustache:before {
  content: "\e014";
}
.icon-mouse:before {
  content: "\e015";
}
.icon-magnet:before {
  content: "\e016";
}
.icon-energy:before {
  content: "\e020";
}
.icon-disc:before {
  content: "\e022";
}
.icon-cursor:before {
  content: "\e06e";
}
.icon-cursor-move:before {
  content: "\e023";
}
.icon-crop:before {
  content: "\e024";
}
.icon-chemistry:before {
  content: "\e026";
}
.icon-speedometer:before {
  content: "\e007";
}
.icon-shield:before {
  content: "\e00e";
}
.icon-screen-tablet:before {
  content: "\e00f";
}
.icon-magic-wand:before {
  content: "\e017";
}
.icon-hourglass:before {
  content: "\e018";
}
.icon-graduation:before {
  content: "\e019";
}
.icon-ghost:before {
  content: "\e01a";
}
.icon-game-controller:before {
  content: "\e01b";
}
.icon-fire:before {
  content: "\e01c";
}
.icon-eyeglass:before {
  content: "\e01d";
}
.icon-envelope-open:before {
  content: "\e01e";
}
.icon-envelope-letter:before {
  content: "\e01f";
}
.icon-bell:before {
  content: "\e027";
}
.icon-badge:before {
  content: "\e028";
}
.icon-anchor:before {
  content: "\e029";
}
.icon-wallet:before {
  content: "\e02a";
}
.icon-vector:before {
  content: "\e02b";
}
.icon-speech:before {
  content: "\e02c";
}
.icon-puzzle:before {
  content: "\e02d";
}
.icon-printer:before {
  content: "\e02e";
}
.icon-present:before {
  content: "\e02f";
}
.icon-playlist:before {
  content: "\e030";
}
.icon-pin:before {
  content: "\e031";
}
.icon-picture:before {
  content: "\e032";
}
.icon-handbag:before {
  content: "\e035";
}
.icon-globe-alt:before {
  content: "\e036";
}
.icon-globe:before {
  content: "\e037";
}
.icon-folder-alt:before {
  content: "\e039";
}
.icon-folder:before {
  content: "\e089";
}
.icon-film:before {
  content: "\e03a";
}
.icon-feed:before {
  content: "\e03b";
}
.icon-drop:before {
  content: "\e03e";
}
.icon-drawer:before {
  content: "\e03f";
}
.icon-docs:before {
  content: "\e040";
}
.icon-doc:before {
  content: "\e085";
}
.icon-diamond:before {
  content: "\e043";
}
.icon-cup:before {
  content: "\e044";
}
.icon-calculator:before {
  content: "\e049";
}
.icon-bubbles:before {
  content: "\e04a";
}
.icon-briefcase:before {
  content: "\e04b";
}
.icon-book-open:before {
  content: "\e04c";
}
.icon-basket-loaded:before {
  content: "\e04d";
}
.icon-basket:before {
  content: "\e04e";
}
.icon-bag:before {
  content: "\e04f";
}
.icon-action-undo:before {
  content: "\e050";
}
.icon-action-redo:before {
  content: "\e051";
}
.icon-wrench:before {
  content: "\e052";
}
.icon-umbrella:before {
  content: "\e053";
}
.icon-trash:before {
  content: "\e054";
}
.icon-tag:before {
  content: "\e055";
}
.icon-support:before {
  content: "\e056";
}
.icon-frame:before {
  content: "\e038";
}
.icon-size-fullscreen:before {
  content: "\e057";
}
.icon-size-actual:before {
  content: "\e058";
}
.icon-shuffle:before {
  content: "\e059";
}
.icon-share-alt:before {
  content: "\e05a";
}
.icon-share:before {
  content: "\e05b";
}
.icon-rocket:before {
  content: "\e05c";
}
.icon-question:before {
  content: "\e05d";
}
.icon-pie-chart:before {
  content: "\e05e";
}
.icon-pencil:before {
  content: "\e05f";
}
.icon-note:before {
  content: "\e060";
}
.icon-loop:before {
  content: "\e064";
}
.icon-home:before {
  content: "\e069";
}
.icon-grid:before {
  content: "\e06a";
}
.icon-graph:before {
  content: "\e06b";
}
.icon-microphone:before {
  content: "\e063";
}
.icon-music-tone-alt:before {
  content: "\e061";
}
.icon-music-tone:before {
  content: "\e062";
}
.icon-earphones-alt:before {
  content: "\e03c";
}
.icon-earphones:before {
  content: "\e03d";
}
.icon-equalizer:before {
  content: "\e06c";
}
.icon-like:before {
  content: "\e068";
}
.icon-dislike:before {
  content: "\e06d";
}
.icon-control-start:before {
  content: "\e06f";
}
.icon-control-rewind:before {
  content: "\e070";
}
.icon-control-play:before {
  content: "\e071";
}
.icon-control-pause:before {
  content: "\e072";
}
.icon-control-forward:before {
  content: "\e073";
}
.icon-control-end:before {
  content: "\e074";
}
.icon-volume-1:before {
  content: "\e09f";
}
.icon-volume-2:before {
  content: "\e0a0";
}
.icon-volume-off:before {
  content: "\e0a1";
}
.icon-calendar:before {
  content: "\e075";
}
.icon-bulb:before {
  content: "\e076";
}
.icon-chart:before {
  content: "\e077";
}
.icon-ban:before {
  content: "\e07c";
}
.icon-bubble:before {
  content: "\e07d";
}
.icon-camrecorder:before {
  content: "\e07e";
}
.icon-camera:before {
  content: "\e07f";
}
.icon-cloud-download:before {
  content: "\e083";
}
.icon-cloud-upload:before {
  content: "\e084";
}
.icon-envelope:before {
  content: "\e086";
}
.icon-eye:before {
  content: "\e087";
}
.icon-flag:before {
  content: "\e088";
}
.icon-heart:before {
  content: "\e08a";
}
.icon-info:before {
  content: "\e08b";
}
.icon-key:before {
  content: "\e08c";
}
.icon-link:before {
  content: "\e08d";
}
.icon-lock:before {
  content: "\e08e";
}
.icon-lock-open:before {
  content: "\e08f";
}
.icon-magnifier:before {
  content: "\e090";
}
.icon-magnifier-add:before {
  content: "\e091";
}
.icon-magnifier-remove:before {
  content: "\e092";
}
.icon-paper-clip:before {
  content: "\e093";
}
.icon-paper-plane:before {
  content: "\e094";
}
.icon-power:before {
  content: "\e097";
}
.icon-refresh:before {
  content: "\e098";
}
.icon-reload:before {
  content: "\e099";
}
.icon-settings:before {
  content: "\e09a";
}
.icon-star:before {
  content: "\e09b";
}
.icon-symbol-female:before {
  content: "\e09c";
}
.icon-symbol-male:before {
  content: "\e09d";
}
.icon-target:before {
  content: "\e09e";
}
.icon-credit-card:before {
  content: "\e025";
}
.icon-paypal:before {
  content: "\e608";
}
.icon-social-tumblr:before {
  content: "\e00a";
}
.icon-social-twitter:before {
  content: "\e009";
}
.icon-social-facebook:before {
  content: "\e00b";
}
.icon-social-instagram:before {
  content: "\e609";
}
.icon-social-linkedin:before {
  content: "\e60a";
}
.icon-social-pinterest:before {
  content: "\e60b";
}
.icon-social-github:before {
  content: "\e60c";
}
.icon-social-google:before {
  content: "\e60d";
}
.icon-social-reddit:before {
  content: "\e60e";
}
.icon-social-skype:before {
  content: "\e60f";
}
.icon-social-dribbble:before {
  content: "\e00d";
}
.icon-social-behance:before {
  content: "\e610";
}
.icon-social-foursqare:before {
  content: "\e611";
}
.icon-social-soundcloud:before {
  content: "\e612";
}
.icon-social-spotify:before {
  content: "\e613";
}
.icon-social-stumbleupon:before {
  content: "\e614";
}
.icon-social-youtube:before {
  content: "\e008";
}
.icon-social-dropbox:before {
  content: "\e00c";
}
.icon-social-vkontakte:before {
  content: "\e618";
}
.icon-social-steam:before {
  content: "\e620";
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].rules[0].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].rules[0].oneOf[1].use[2]!./node_modules/resolve-url-loader/index.js??ruleSet[1].rules[4].rules[1].use[0]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].rules[1].use[1]!./src/scss/style.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/*!
 * CoreUI - Open Source Dashboard UI Kit
 * @version v2.1.16
 * @link https://coreui.io
 * Copyright (c) 2018 creativeLabs Łukasz Holeczek
 * Licensed under MIT (https://coreui.io/license)
 */
/*!
 * Bootstrap v4.3.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
:root {
  --blue: #20a8d8;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #f86c6b;
  --orange: #f8cb00;
  --yellow: #ffc107;
  --green: #4dbd74;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #73818f;
  --gray-dark: #2f353a;
  --light-blue: #63c2de;
  --primary: #20a8d8;
  --secondary: #c8ced3;
  --success: #4dbd74;
  --info: #63c2de;
  --warning: #ffc107;
  --danger: #f86c6b;
  --light: #f0f3f5;
  --dark: #2f353a;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #23282c;
  text-align: left;
  background-color: #e4e5e6;
}
[tabindex="-1"]:focus {
  outline: 0 !important;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: #20a8d8;
  text-decoration: none;
  background-color: transparent;
}
a:hover {
  color: #167495;
  text-decoration: underline;
}
a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
  outline: 0;
}
pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}
pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
}
figure {
  margin: 0 0 1rem;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg {
  overflow: hidden;
  vertical-align: middle;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #73818f;
  text-align: left;
  caption-side: bottom;
}
th {
  text-align: inherit;
}
label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
button {
  border-radius: 0;
}
button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
select {
  word-wrap: normal;
}
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type=radio],
input[type=checkbox] {
  box-sizing: border-box;
  padding: 0;
}
input[type=date],
input[type=time],
input[type=datetime-local],
input[type=month] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}
[type=search] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
  cursor: pointer;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}
h1, .h1 {
  font-size: 2.1875rem;
}
h2, .h2 {
  font-size: 1.75rem;
}
h3, .h3 {
  font-size: 1.53125rem;
}
h4, .h4 {
  font-size: 1.3125rem;
}
h5, .h5 {
  font-size: 1.09375rem;
}
h6, .h6 {
  font-size: 0.875rem;
}
.lead {
  font-size: 1.09375rem;
  font-weight: 300;
}
.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}
.display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}
hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
small,
.small {
  font-size: 80%;
  font-weight: 400;
}
mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
}
.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
.blockquote {
  margin-bottom: 1rem;
  font-size: 1.09375rem;
}
.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #73818f;
}
.blockquote-footer::before {
  content: "— ";
}
.img-fluid {
  max-width: 100%;
  height: auto;
}
.img-thumbnail {
  padding: 0.25rem;
  background-color: #e4e5e6;
  border: 1px solid #c8ced3;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}
.figure {
  display: inline-block;
}
.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}
.figure-caption {
  font-size: 90%;
  color: #73818f;
}
code {
  font-size: 87.5%;
  color: #e83e8c;
  word-break: break-word;
}
a > code {
  color: inherit;
}
kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #23282c;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}
pre {
  display: block;
  font-size: 87.5%;
  color: #23282c;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}
.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}
.col-xl,
.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,
.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,
.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,
.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,
.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}
.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}
.col-1 {
  flex: 0 0 8.3333333333%;
  max-width: 8.3333333333%;
}
.col-2 {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}
.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}
.col-4 {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}
.col-5 {
  flex: 0 0 41.6666666667%;
  max-width: 41.6666666667%;
}
.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-7 {
  flex: 0 0 58.3333333333%;
  max-width: 58.3333333333%;
}
.col-8 {
  flex: 0 0 66.6666666667%;
  max-width: 66.6666666667%;
}
.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}
.col-10 {
  flex: 0 0 83.3333333333%;
  max-width: 83.3333333333%;
}
.col-11 {
  flex: 0 0 91.6666666667%;
  max-width: 91.6666666667%;
}
.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.order-first {
  order: -1;
}
.order-last {
  order: 13;
}
.order-0 {
  order: 0;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.order-3 {
  order: 3;
}
.order-4 {
  order: 4;
}
.order-5 {
  order: 5;
}
.order-6 {
  order: 6;
}
.order-7 {
  order: 7;
}
.order-8 {
  order: 8;
}
.order-9 {
  order: 9;
}
.order-10 {
  order: 10;
}
.order-11 {
  order: 11;
}
.order-12 {
  order: 12;
}
.offset-1 {
  margin-left: 8.3333333333%;
}
.offset-2 {
  margin-left: 16.6666666667%;
}
.offset-3 {
  margin-left: 25%;
}
.offset-4 {
  margin-left: 33.3333333333%;
}
.offset-5 {
  margin-left: 41.6666666667%;
}
.offset-6 {
  margin-left: 50%;
}
.offset-7 {
  margin-left: 58.3333333333%;
}
.offset-8 {
  margin-left: 66.6666666667%;
}
.offset-9 {
  margin-left: 75%;
}
.offset-10 {
  margin-left: 83.3333333333%;
}
.offset-11 {
  margin-left: 91.6666666667%;
}
@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-sm-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }

  .col-sm-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .col-sm-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }

  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }

  .col-sm-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }

  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }

  .col-sm-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }

  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-sm-first {
    order: -1;
  }

  .order-sm-last {
    order: 13;
  }

  .order-sm-0 {
    order: 0;
  }

  .order-sm-1 {
    order: 1;
  }

  .order-sm-2 {
    order: 2;
  }

  .order-sm-3 {
    order: 3;
  }

  .order-sm-4 {
    order: 4;
  }

  .order-sm-5 {
    order: 5;
  }

  .order-sm-6 {
    order: 6;
  }

  .order-sm-7 {
    order: 7;
  }

  .order-sm-8 {
    order: 8;
  }

  .order-sm-9 {
    order: 9;
  }

  .order-sm-10 {
    order: 10;
  }

  .order-sm-11 {
    order: 11;
  }

  .order-sm-12 {
    order: 12;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.3333333333%;
  }

  .offset-sm-2 {
    margin-left: 16.6666666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.3333333333%;
  }

  .offset-sm-5 {
    margin-left: 41.6666666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.3333333333%;
  }

  .offset-sm-8 {
    margin-left: 66.6666666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.3333333333%;
  }

  .offset-sm-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-md-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }

  .col-md-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-md-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .col-md-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }

  .col-md-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }

  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-md-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }

  .col-md-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }

  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-md-first {
    order: -1;
  }

  .order-md-last {
    order: 13;
  }

  .order-md-0 {
    order: 0;
  }

  .order-md-1 {
    order: 1;
  }

  .order-md-2 {
    order: 2;
  }

  .order-md-3 {
    order: 3;
  }

  .order-md-4 {
    order: 4;
  }

  .order-md-5 {
    order: 5;
  }

  .order-md-6 {
    order: 6;
  }

  .order-md-7 {
    order: 7;
  }

  .order-md-8 {
    order: 8;
  }

  .order-md-9 {
    order: 9;
  }

  .order-md-10 {
    order: 10;
  }

  .order-md-11 {
    order: 11;
  }

  .order-md-12 {
    order: 12;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.3333333333%;
  }

  .offset-md-2 {
    margin-left: 16.6666666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.3333333333%;
  }

  .offset-md-5 {
    margin-left: 41.6666666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.3333333333%;
  }

  .offset-md-8 {
    margin-left: 66.6666666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.3333333333%;
  }

  .offset-md-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-lg-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }

  .col-lg-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .col-lg-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }

  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }

  .col-lg-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }

  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }

  .col-lg-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }

  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-lg-first {
    order: -1;
  }

  .order-lg-last {
    order: 13;
  }

  .order-lg-0 {
    order: 0;
  }

  .order-lg-1 {
    order: 1;
  }

  .order-lg-2 {
    order: 2;
  }

  .order-lg-3 {
    order: 3;
  }

  .order-lg-4 {
    order: 4;
  }

  .order-lg-5 {
    order: 5;
  }

  .order-lg-6 {
    order: 6;
  }

  .order-lg-7 {
    order: 7;
  }

  .order-lg-8 {
    order: 8;
  }

  .order-lg-9 {
    order: 9;
  }

  .order-lg-10 {
    order: 10;
  }

  .order-lg-11 {
    order: 11;
  }

  .order-lg-12 {
    order: 12;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.3333333333%;
  }

  .offset-lg-2 {
    margin-left: 16.6666666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.3333333333%;
  }

  .offset-lg-5 {
    margin-left: 41.6666666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.3333333333%;
  }

  .offset-lg-8 {
    margin-left: 66.6666666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.3333333333%;
  }

  .offset-lg-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-xl-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }

  .col-xl-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .col-xl-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }

  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }

  .col-xl-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }

  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }

  .col-xl-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }

  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-xl-first {
    order: -1;
  }

  .order-xl-last {
    order: 13;
  }

  .order-xl-0 {
    order: 0;
  }

  .order-xl-1 {
    order: 1;
  }

  .order-xl-2 {
    order: 2;
  }

  .order-xl-3 {
    order: 3;
  }

  .order-xl-4 {
    order: 4;
  }

  .order-xl-5 {
    order: 5;
  }

  .order-xl-6 {
    order: 6;
  }

  .order-xl-7 {
    order: 7;
  }

  .order-xl-8 {
    order: 8;
  }

  .order-xl-9 {
    order: 9;
  }

  .order-xl-10 {
    order: 10;
  }

  .order-xl-11 {
    order: 11;
  }

  .order-xl-12 {
    order: 12;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.3333333333%;
  }

  .offset-xl-2 {
    margin-left: 16.6666666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.3333333333%;
  }

  .offset-xl-5 {
    margin-left: 41.6666666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.3333333333%;
  }

  .offset-xl-8 {
    margin-left: 66.6666666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.3333333333%;
  }

  .offset-xl-11 {
    margin-left: 91.6666666667%;
  }
}
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #23282c;
}
.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #c8ced3;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #c8ced3;
}
.table tbody + tbody {
  border-top: 2px solid #c8ced3;
}
.table-sm th,
.table-sm td {
  padding: 0.3rem;
}
.table-bordered {
  border: 1px solid #c8ced3;
}
.table-bordered th,
.table-bordered td {
  border: 1px solid #c8ced3;
}
.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}
.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}
.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}
.table-hover tbody tr:hover {
  color: #23282c;
  background-color: rgba(0, 0, 0, 0.075);
}
.table-primary,
.table-primary > th,
.table-primary > td {
  background-color: #c1e7f4;
}
.table-primary th,
.table-primary td,
.table-primary thead th,
.table-primary tbody + tbody {
  border-color: #8bd2eb;
}
.table-hover .table-primary:hover {
  background-color: #abdff0;
}
.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #abdff0;
}
.table-secondary,
.table-secondary > th,
.table-secondary > td {
  background-color: #f0f1f3;
}
.table-secondary th,
.table-secondary td,
.table-secondary thead th,
.table-secondary tbody + tbody {
  border-color: #e2e6e8;
}
.table-hover .table-secondary:hover {
  background-color: #e2e4e8;
}
.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #e2e4e8;
}
.table-success,
.table-success > th,
.table-success > td {
  background-color: #cdedd8;
}
.table-success th,
.table-success td,
.table-success thead th,
.table-success tbody + tbody {
  border-color: #a2ddb7;
}
.table-hover .table-success:hover {
  background-color: #bae6c9;
}
.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #bae6c9;
}
.table-info,
.table-info > th,
.table-info > td {
  background-color: #d3eef6;
}
.table-info th,
.table-info td,
.table-info thead th,
.table-info tbody + tbody {
  border-color: #aedfee;
}
.table-hover .table-info:hover {
  background-color: #bee6f2;
}
.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #bee6f2;
}
.table-warning,
.table-warning > th,
.table-warning > td {
  background-color: #ffeeba;
}
.table-warning th,
.table-warning td,
.table-warning thead th,
.table-warning tbody + tbody {
  border-color: #ffdf7e;
}
.table-hover .table-warning:hover {
  background-color: #ffe8a1;
}
.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #ffe8a1;
}
.table-danger,
.table-danger > th,
.table-danger > td {
  background-color: #fdd6d6;
}
.table-danger th,
.table-danger td,
.table-danger thead th,
.table-danger tbody + tbody {
  border-color: #fbb3b2;
}
.table-hover .table-danger:hover {
  background-color: #fcbebe;
}
.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #fcbebe;
}
.table-light,
.table-light > th,
.table-light > td {
  background-color: #fbfcfc;
}
.table-light th,
.table-light td,
.table-light thead th,
.table-light tbody + tbody {
  border-color: #f7f9fa;
}
.table-hover .table-light:hover {
  background-color: #ecf1f1;
}
.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #ecf1f1;
}
.table-dark,
.table-dark > th,
.table-dark > td {
  background-color: #c5c6c8;
}
.table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
  border-color: #939699;
}
.table-hover .table-dark:hover {
  background-color: #b8b9bc;
}
.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #b8b9bc;
}
.table-active,
.table-active > th,
.table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}
.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}
.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}
.table .thead-dark th {
  color: #fff;
  background-color: #2f353a;
  border-color: #40484f;
}
.table .thead-light th {
  color: #5c6873;
  background-color: #e4e7ea;
  border-color: #c8ced3;
}
.table-dark {
  color: #fff;
  background-color: #2f353a;
}
.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #40484f;
}
.table-dark.table-bordered {
  border: 0;
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}
.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}
@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.table-responsive > .table-bordered {
  border: 0;
}
.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #5c6873;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #e4e7ea;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}
.form-control:focus {
  color: #5c6873;
  background-color: #fff;
  border-color: #8ad4ee;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.form-control::-moz-placeholder {
  color: #73818f;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #73818f;
  opacity: 1;
}
.form-control::placeholder {
  color: #73818f;
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: #e4e7ea;
  opacity: 1;
}
select.form-control:focus::-ms-value {
  color: #5c6873;
  background-color: #fff;
}
.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}
.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}
.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.09375rem;
  line-height: 1.5;
}
.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.765625rem;
  line-height: 1.5;
}
.form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  color: #23282c;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}
.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
select.form-control[size], select.form-control[multiple] {
  height: auto;
}
textarea.form-control {
  height: auto;
}
.form-group {
  margin-bottom: 1rem;
}
.form-text {
  display: block;
  margin-top: 0.25rem;
}
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
.form-row > .col,
.form-row > [class*=col-] {
  padding-right: 5px;
  padding-left: 5px;
}
.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}
.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}
.form-check-input:disabled ~ .form-check-label {
  color: #73818f;
}
.form-check-label {
  margin-bottom: 0;
}
.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}
.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #4dbd74;
}
.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(77, 189, 116, 0.9);
  border-radius: 0.25rem;
}
.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #4dbd74;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%234dbd74' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #4dbd74;
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.25);
}
.was-validated .form-control:valid ~ .valid-feedback,
.was-validated .form-control:valid ~ .valid-tooltip, .form-control.is-valid ~ .valid-feedback,
.form-control.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .custom-select:valid, .custom-select.is-valid {
  border-color: #4dbd74;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%232f353a' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%234dbd74' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
  border-color: #4dbd74;
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.25);
}
.was-validated .custom-select:valid ~ .valid-feedback,
.was-validated .custom-select:valid ~ .valid-tooltip, .custom-select.is-valid ~ .valid-feedback,
.custom-select.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .form-control-file:valid ~ .valid-feedback,
.was-validated .form-control-file:valid ~ .valid-tooltip, .form-control-file.is-valid ~ .valid-feedback,
.form-control-file.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #4dbd74;
}
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {
  color: #4dbd74;
}
.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #4dbd74;
}
.was-validated .custom-control-input:valid ~ .valid-feedback,
.was-validated .custom-control-input:valid ~ .valid-tooltip, .custom-control-input.is-valid ~ .valid-feedback,
.custom-control-input.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #72cb91;
  background-color: #72cb91;
}
.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.25);
}
.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #4dbd74;
}
.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #4dbd74;
}
.was-validated .custom-file-input:valid ~ .valid-feedback,
.was-validated .custom-file-input:valid ~ .valid-tooltip, .custom-file-input.is-valid ~ .valid-feedback,
.custom-file-input.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #4dbd74;
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.25);
}
.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #f86c6b;
}
.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(248, 108, 107, 0.9);
  border-radius: 0.25rem;
}
.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #f86c6b;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f86c6b' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23f86c6b' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #f86c6b;
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.25);
}
.was-validated .form-control:invalid ~ .invalid-feedback,
.was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .custom-select:invalid, .custom-select.is-invalid {
  border-color: #f86c6b;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%232f353a' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f86c6b' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23f86c6b' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
  border-color: #f86c6b;
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.25);
}
.was-validated .custom-select:invalid ~ .invalid-feedback,
.was-validated .custom-select:invalid ~ .invalid-tooltip, .custom-select.is-invalid ~ .invalid-feedback,
.custom-select.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .form-control-file:invalid ~ .invalid-feedback,
.was-validated .form-control-file:invalid ~ .invalid-tooltip, .form-control-file.is-invalid ~ .invalid-feedback,
.form-control-file.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #f86c6b;
}
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {
  color: #f86c6b;
}
.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #f86c6b;
}
.was-validated .custom-control-input:invalid ~ .invalid-feedback,
.was-validated .custom-control-input:invalid ~ .invalid-tooltip, .custom-control-input.is-invalid ~ .invalid-feedback,
.custom-control-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #fa9c9c;
  background-color: #fa9c9c;
}
.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.25);
}
.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #f86c6b;
}
.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #f86c6b;
}
.was-validated .custom-file-input:invalid ~ .invalid-feedback,
.was-validated .custom-file-input:invalid ~ .invalid-tooltip, .custom-file-input.is-invalid ~ .invalid-feedback,
.custom-file-input.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #f86c6b;
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.25);
}
.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}
.form-inline .form-check {
  width: 100%;
}
@media (min-width: 576px) {
  .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .input-group,
.form-inline .custom-select {
    width: auto;
  }
  .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}
.btn {
  display: inline-block;
  font-weight: 400;
  color: #23282c;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: #23282c;
  text-decoration: none;
}
.btn:focus, .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.btn.disabled, .btn:disabled {
  opacity: 0.65;
}
a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}
.btn-primary {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-primary:hover {
  color: #fff;
  background-color: #1b8eb7;
  border-color: #1985ac;
}
.btn-primary:focus, .btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(65, 181, 222, 0.5);
}
.btn-primary.disabled, .btn-primary:disabled {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #1985ac;
  border-color: #187da0;
}
.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(65, 181, 222, 0.5);
}
.btn-secondary {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-secondary:hover {
  color: #23282c;
  background-color: #b3bbc2;
  border-color: #acb5bc;
}
.btn-secondary:focus, .btn-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(175, 181, 186, 0.5);
}
.btn-secondary.disabled, .btn-secondary:disabled {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
  color: #23282c;
  background-color: #acb5bc;
  border-color: #a5aeb7;
}
.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(175, 181, 186, 0.5);
}
.btn-success {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-success:hover {
  color: #fff;
  background-color: #3ea662;
  border-color: #3a9d5d;
}
.btn-success:focus, .btn-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 199, 137, 0.5);
}
.btn-success.disabled, .btn-success:disabled {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active, .show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #3a9d5d;
  border-color: #379457;
}
.btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(104, 199, 137, 0.5);
}
.btn-info {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-info:hover {
  color: #23282c;
  background-color: #43b6d7;
  border-color: #39b2d5;
}
.btn-info:focus, .btn-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 171, 195, 0.5);
}
.btn-info.disabled, .btn-info:disabled {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active, .show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #39b2d5;
  border-color: #2eadd3;
}
.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 171, 195, 0.5);
}
.btn-warning {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-warning:hover {
  color: #23282c;
  background-color: #e0a800;
  border-color: #d39e00;
}
.btn-warning:focus, .btn-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 13, 0.5);
}
.btn-warning.disabled, .btn-warning:disabled {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active, .show > .btn-warning.dropdown-toggle {
  color: #23282c;
  background-color: #d39e00;
  border-color: #c69500;
}
.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 13, 0.5);
}
.btn-danger {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-danger:hover {
  color: #fff;
  background-color: #f64846;
  border-color: #f63c3a;
}
.btn-danger:focus, .btn-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 130, 129, 0.5);
}
.btn-danger.disabled, .btn-danger:disabled {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active, .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #f63c3a;
  border-color: #f5302e;
}
.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 130, 129, 0.5);
}
.btn-light {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-light:hover {
  color: #23282c;
  background-color: #d9e1e6;
  border-color: #d1dbe1;
}
.btn-light:focus, .btn-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(209, 213, 215, 0.5);
}
.btn-light.disabled, .btn-light:disabled {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle {
  color: #23282c;
  background-color: #d1dbe1;
  border-color: #cad4dc;
}
.btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(209, 213, 215, 0.5);
}
.btn-dark {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-dark:hover {
  color: #fff;
  background-color: #1e2225;
  border-color: #181b1e;
}
.btn-dark:focus, .btn-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(78, 83, 88, 0.5);
}
.btn-dark.disabled, .btn-dark:disabled {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active, .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #181b1e;
  border-color: #121517;
}
.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(78, 83, 88, 0.5);
}
.btn-outline-primary {
  color: #20a8d8;
  border-color: #20a8d8;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-outline-primary:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.5);
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #20a8d8;
  background-color: transparent;
}
.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.5);
}
.btn-outline-secondary {
  color: #c8ced3;
  border-color: #c8ced3;
}
.btn-outline-secondary:hover {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-outline-secondary:focus, .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(200, 206, 211, 0.5);
}
.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
  color: #c8ced3;
  background-color: transparent;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(200, 206, 211, 0.5);
}
.btn-outline-success {
  color: #4dbd74;
  border-color: #4dbd74;
}
.btn-outline-success:hover {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-outline-success:focus, .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.5);
}
.btn-outline-success.disabled, .btn-outline-success:disabled {
  color: #4dbd74;
  background-color: transparent;
}
.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.5);
}
.btn-outline-info {
  color: #63c2de;
  border-color: #63c2de;
}
.btn-outline-info:hover {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-outline-info:focus, .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 194, 222, 0.5);
}
.btn-outline-info.disabled, .btn-outline-info:disabled {
  color: #63c2de;
  background-color: transparent;
}
.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 194, 222, 0.5);
}
.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:hover {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:focus, .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.btn-outline-warning.disabled, .btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}
.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.btn-outline-danger {
  color: #f86c6b;
  border-color: #f86c6b;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-outline-danger:focus, .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.5);
}
.btn-outline-danger.disabled, .btn-outline-danger:disabled {
  color: #f86c6b;
  background-color: transparent;
}
.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.5);
}
.btn-outline-light {
  color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-outline-light:hover {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-outline-light:focus, .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 243, 245, 0.5);
}
.btn-outline-light.disabled, .btn-outline-light:disabled {
  color: #f0f3f5;
  background-color: transparent;
}
.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 243, 245, 0.5);
}
.btn-outline-dark {
  color: #2f353a;
  border-color: #2f353a;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-outline-dark:focus, .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(47, 53, 58, 0.5);
}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #2f353a;
  background-color: transparent;
}
.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(47, 53, 58, 0.5);
}
.btn-link {
  font-weight: 400;
  color: #20a8d8;
  text-decoration: none;
}
.btn-link:hover {
  color: #167495;
  text-decoration: underline;
}
.btn-link:focus, .btn-link.focus {
  text-decoration: underline;
  box-shadow: none;
}
.btn-link:disabled, .btn-link.disabled {
  color: #73818f;
  pointer-events: none;
}
.btn-lg, .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.btn-sm, .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 0.5rem;
}
input[type=submit].btn-block,
input[type=reset].btn-block,
input[type=button].btn-block {
  width: 100%;
}
.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}
.collapse:not(.show) {
  display: none;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}
.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #23282c;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #c8ced3;
  border-radius: 0.25rem;
}
.dropdown-menu-left {
  right: auto;
  left: 0;
}
.dropdown-menu-right {
  right: 0;
  left: auto;
}
@media (min-width: 576px) {
  .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropright .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropright .dropdown-toggle::after {
  vertical-align: 0;
}
.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropleft .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropleft .dropdown-toggle::after {
  display: none;
}
.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}
.dropdown-menu[x-placement^=top], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left] {
  right: auto;
  bottom: auto;
}
.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e4e7ea;
}
.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #23282c;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:first-child {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.dropdown-item:last-child {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.dropdown-item:hover, .dropdown-item:focus {
  color: #181b1e;
  text-decoration: none;
  background-color: #f0f3f5;
}
.dropdown-item.active, .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #20a8d8;
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: #73818f;
  pointer-events: none;
  background-color: transparent;
}
.dropdown-menu.show {
  display: block;
}
.dropdown-header {
  display: block;
  padding: 0 1.5rem;
  margin-bottom: 0;
  font-size: 0.765625rem;
  color: #73818f;
  white-space: nowrap;
}
.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #23282c;
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover {
  z-index: 1;
}
.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}
.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}
.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}
.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}
.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}
.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}
.btn-group-toggle > .btn input[type=radio],
.btn-group-toggle > .btn input[type=checkbox],
.btn-group-toggle > .btn-group > .btn input[type=radio],
.btn-group-toggle > .btn-group > .btn input[type=checkbox] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-control-plaintext,
.input-group > .custom-select,
.input-group > .custom-file {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}
.input-group > .form-control + .form-control,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .custom-file,
.input-group > .form-control-plaintext + .form-control,
.input-group > .form-control-plaintext + .custom-select,
.input-group > .form-control-plaintext + .custom-file,
.input-group > .custom-select + .form-control,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .custom-file,
.input-group > .custom-file + .form-control,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .custom-file {
  margin-left: -1px;
}
.input-group > .form-control:focus,
.input-group > .custom-select:focus,
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}
.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}
.input-group > .form-control:not(:last-child),
.input-group > .custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .form-control:not(:first-child),
.input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .custom-file {
  display: flex;
  align-items: center;
}
.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group-prepend,
.input-group-append {
  display: flex;
}
.input-group-prepend .btn,
.input-group-append .btn {
  position: relative;
  z-index: 2;
}
.input-group-prepend .btn:focus,
.input-group-append .btn:focus {
  z-index: 3;
}
.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .input-group-text,
.input-group-append .input-group-text + .btn {
  margin-left: -1px;
}
.input-group-prepend {
  margin-right: -1px;
}
.input-group-append {
  margin-left: -1px;
}
.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #5c6873;
  text-align: center;
  white-space: nowrap;
  background-color: #f0f3f5;
  border: 1px solid #e4e7ea;
  border-radius: 0.25rem;
}
.input-group-text input[type=radio],
.input-group-text input[type=checkbox] {
  margin-top: 0;
}
.input-group-lg > .form-control:not(textarea),
.input-group-lg > .custom-select {
  height: calc(1.5em + 1rem + 2px);
}
.input-group-lg > .form-control,
.input-group-lg > .custom-select,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.input-group-sm > .form-control:not(textarea),
.input-group-sm > .custom-select {
  height: calc(1.5em + 0.5rem + 2px);
}
.input-group-sm > .form-control,
.input-group-sm > .custom-select,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.input-group-lg > .custom-select,
.input-group-sm > .custom-select {
  padding-right: 1.75rem;
}
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.custom-control {
  position: relative;
  display: block;
  min-height: 1.3125rem;
  padding-left: 1.5rem;
}
.custom-control-inline {
  display: inline-flex;
  margin-right: 1rem;
}
.custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #20a8d8;
  background-color: #20a8d8;
}
.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #8ad4ee;
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #b6e4f4;
  border-color: #b6e4f4;
}
.custom-control-input:disabled ~ .custom-control-label {
  color: #73818f;
}
.custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e4e7ea;
}
.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}
.custom-control-label::before {
  position: absolute;
  top: 0.15625rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  border: #8f9ba6 solid 1px;
}
.custom-control-label::after {
  position: absolute;
  top: 0.15625rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: no-repeat 50%/50% 50%;
}
.custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #20a8d8;
  background-color: #20a8d8;
}
.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}
.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(32, 168, 216, 0.5);
}
.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: rgba(32, 168, 216, 0.5);
}
.custom-radio .custom-control-label::before {
  border-radius: 50%;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(32, 168, 216, 0.5);
}
.custom-switch {
  padding-left: 2.25rem;
}
.custom-switch .custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}
.custom-switch .custom-control-label::after {
  top: calc(0.15625rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #8f9ba6;
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}
.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff;
  transform: translateX(0.75rem);
}
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(32, 168, 216, 0.5);
}
.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #5c6873;
  vertical-align: middle;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%232f353a' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  background-color: #fff;
  border: 1px solid #e4e7ea;
  border-radius: 0.25rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-select:focus {
  border-color: #8ad4ee;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-select:focus::-ms-value {
  color: #5c6873;
  background-color: #fff;
}
.custom-select[multiple], .custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}
.custom-select:disabled {
  color: #73818f;
  background-color: #e4e7ea;
}
.custom-select::-ms-expand {
  display: none;
}
.custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.765625rem;
}
.custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.09375rem;
}
.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin-bottom: 0;
}
.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin: 0;
  opacity: 0;
}
.custom-file-input:focus ~ .custom-file-label {
  border-color: #8ad4ee;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e4e7ea;
}
.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}
.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}
.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  color: #5c6873;
  background-color: #fff;
  border: 1px solid #e4e7ea;
  border-radius: 0.25rem;
}
.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.75rem);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #5c6873;
  content: "Browse";
  background-color: #f0f3f5;
  border-left: inherit;
  border-radius: 0 0.25rem 0.25rem 0;
}
.custom-range {
  width: 100%;
  height: calc(1rem + 0.4rem);
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.custom-range:focus {
  outline: none;
}
.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #e4e5e6, 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #e4e5e6, 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #e4e5e6, 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.custom-range::-moz-focus-outer {
  border: 0;
}
.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #20a8d8;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.custom-range::-webkit-slider-thumb:active {
  background-color: #b6e4f4;
}
.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #c8ced3;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #20a8d8;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.custom-range::-moz-range-thumb:active {
  background-color: #b6e4f4;
}
.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #c8ced3;
  border-color: transparent;
  border-radius: 1rem;
}
.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #20a8d8;
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}
.custom-range::-ms-thumb:active {
  background-color: #b6e4f4;
}
.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}
.custom-range::-ms-fill-lower {
  background-color: #c8ced3;
  border-radius: 1rem;
}
.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #c8ced3;
  border-radius: 1rem;
}
.custom-range:disabled::-webkit-slider-thumb {
  background-color: #8f9ba6;
}
.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}
.custom-range:disabled::-moz-range-thumb {
  background-color: #8f9ba6;
}
.custom-range:disabled::-moz-range-track {
  cursor: default;
}
.custom-range:disabled::-ms-thumb {
  background-color: #8f9ba6;
}
.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
.custom-file-label,
.custom-select {
    transition: none;
  }
}
.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.nav-link:hover, .nav-link:focus {
  text-decoration: none;
}
.nav-link.disabled {
  color: #73818f;
  pointer-events: none;
  cursor: default;
}
.nav-tabs {
  border-bottom: 1px solid #c8ced3;
}
.nav-tabs .nav-item {
  margin-bottom: -1px;
}
.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e4e7ea #e4e7ea #c8ced3;
}
.nav-tabs .nav-link.disabled {
  color: #73818f;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #5c6873;
  background-color: #e4e5e6;
  border-color: #c8ced3 #c8ced3 #e4e5e6;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav-pills .nav-link {
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #20a8d8;
}
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}
.navbar > .container,
.navbar > .container-fluid {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  display: inline-block;
  padding-top: 0.3359375rem;
  padding-bottom: 0.3359375rem;
  margin-right: 1rem;
  font-size: 1.09375rem;
  line-height: inherit;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}
.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}
.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}
.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.09375rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.navbar-toggler:hover, .navbar-toggler:focus {
  text-decoration: none;
}
.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}
@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
.navbar-expand-sm > .container-fluid {
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
.navbar-expand-md > .container-fluid {
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
.navbar-expand-lg > .container-fluid {
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
.navbar-expand-xl > .container-fluid {
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}
.navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid {
  padding-right: 0;
  padding-left: 0;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand > .container,
.navbar-expand > .container-fluid {
  flex-wrap: nowrap;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}
.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-dark .navbar-text a {
  color: #fff;
}
.navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus {
  color: #fff;
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid #c8ced3;
  border-radius: 0.25rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group:first-child .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.card > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}
.card-title {
  margin-bottom: 0.75rem;
}
.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}
.card-text:last-child {
  margin-bottom: 0;
}
.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1.25rem;
}
.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: #f0f3f5;
  border-bottom: 1px solid #c8ced3;
}
.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}
.card-header + .list-group .list-group-item:first-child {
  border-top: 0;
}
.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: #f0f3f5;
  border-top: 1px solid #c8ced3;
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}
.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}
.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}
.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}
.card-img {
  width: 100%;
  border-radius: calc(0.25rem - 1px);
}
.card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.card-deck {
  display: flex;
  flex-direction: column;
}
.card-deck .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-deck {
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .card-deck .card {
    display: flex;
    flex: 1 0 0%;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}
.card-group {
  display: flex;
  flex-direction: column;
}
.card-group > .card {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .card-group {
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}
.card-columns .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 576px) {
  .card-columns {
    -moz-column-count: 3;
         column-count: 3;
    grid-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}
.accordion > .card {
  overflow: hidden;
}
.accordion > .card:not(:first-of-type) .card-header:first-child {
  border-radius: 0;
}
.accordion > .card:not(:first-of-type):not(:last-of-type) {
  border-bottom: 0;
  border-radius: 0;
}
.accordion > .card:first-of-type {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion > .card:last-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion > .card .card-header {
  margin-bottom: -1px;
}
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  list-style: none;
  background-color: #fff;
  border-radius: 0;
}
.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #73818f;
  content: "/";
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}
.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}
.breadcrumb-item.active {
  color: #73818f;
}
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}
.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #20a8d8;
  background-color: #fff;
  border: 1px solid #c8ced3;
}
.page-link:hover {
  z-index: 2;
  color: #167495;
  text-decoration: none;
  background-color: #e4e7ea;
  border-color: #c8ced3;
}
.page-link:focus {
  z-index: 2;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.25);
}
.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.page-item.active .page-link {
  z-index: 1;
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.page-item.disabled .page-link {
  color: #73818f;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #c8ced3;
}
.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.09375rem;
  line-height: 1.5;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }
}
a.badge:hover, a.badge:focus {
  text-decoration: none;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}
.badge-primary {
  color: #fff;
  background-color: #20a8d8;
}
a.badge-primary:hover, a.badge-primary:focus {
  color: #fff;
  background-color: #1985ac;
}
a.badge-primary:focus, a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.5);
}
.badge-secondary {
  color: #23282c;
  background-color: #c8ced3;
}
a.badge-secondary:hover, a.badge-secondary:focus {
  color: #23282c;
  background-color: #acb5bc;
}
a.badge-secondary:focus, a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(200, 206, 211, 0.5);
}
.badge-success {
  color: #fff;
  background-color: #4dbd74;
}
a.badge-success:hover, a.badge-success:focus {
  color: #fff;
  background-color: #3a9d5d;
}
a.badge-success:focus, a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.5);
}
.badge-info {
  color: #23282c;
  background-color: #63c2de;
}
a.badge-info:hover, a.badge-info:focus {
  color: #23282c;
  background-color: #39b2d5;
}
a.badge-info:focus, a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(99, 194, 222, 0.5);
}
.badge-warning {
  color: #23282c;
  background-color: #ffc107;
}
a.badge-warning:hover, a.badge-warning:focus {
  color: #23282c;
  background-color: #d39e00;
}
a.badge-warning:focus, a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.badge-danger {
  color: #fff;
  background-color: #f86c6b;
}
a.badge-danger:hover, a.badge-danger:focus {
  color: #fff;
  background-color: #f63c3a;
}
a.badge-danger:focus, a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.5);
}
.badge-light {
  color: #23282c;
  background-color: #f0f3f5;
}
a.badge-light:hover, a.badge-light:focus {
  color: #23282c;
  background-color: #d1dbe1;
}
a.badge-light:focus, a.badge-light.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(240, 243, 245, 0.5);
}
.badge-dark {
  color: #fff;
  background-color: #2f353a;
}
a.badge-dark:hover, a.badge-dark:focus {
  color: #fff;
  background-color: #181b1e;
}
a.badge-dark:focus, a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(47, 53, 58, 0.5);
}
.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e4e7ea;
  border-radius: 0.3rem;
}
@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}
.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.alert-heading {
  color: inherit;
}
.alert-link {
  font-weight: 700;
}
.alert-dismissible {
  padding-right: 3.8125rem;
}
.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}
.alert-primary {
  color: #115770;
  background-color: #d2eef7;
  border-color: #c1e7f4;
}
.alert-primary hr {
  border-top-color: #abdff0;
}
.alert-primary .alert-link {
  color: #0a3544;
}
.alert-secondary {
  color: #686b6e;
  background-color: #f4f5f6;
  border-color: #f0f1f3;
}
.alert-secondary hr {
  border-top-color: #e2e4e8;
}
.alert-secondary .alert-link {
  color: #4f5254;
}
.alert-success {
  color: #28623c;
  background-color: #dbf2e3;
  border-color: #cdedd8;
}
.alert-success hr {
  border-top-color: #bae6c9;
}
.alert-success .alert-link {
  color: #193e26;
}
.alert-info {
  color: #336573;
  background-color: #e0f3f8;
  border-color: #d3eef6;
}
.alert-info hr {
  border-top-color: #bee6f2;
}
.alert-info .alert-link {
  color: #234650;
}
.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}
.alert-warning hr {
  border-top-color: #ffe8a1;
}
.alert-warning .alert-link {
  color: #533f03;
}
.alert-danger {
  color: #813838;
  background-color: #fee2e1;
  border-color: #fdd6d6;
}
.alert-danger hr {
  border-top-color: #fcbebe;
}
.alert-danger .alert-link {
  color: #5d2929;
}
.alert-light {
  color: #7d7e7f;
  background-color: #fcfdfd;
  border-color: #fbfcfc;
}
.alert-light hr {
  border-top-color: #ecf1f1;
}
.alert-light .alert-link {
  color: #646565;
}
.alert-dark {
  color: #181c1e;
  background-color: #d5d7d8;
  border-color: #c5c6c8;
}
.alert-dark hr {
  border-top-color: #b8b9bc;
}
.alert-dark .alert-link {
  color: #010202;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.65625rem;
  background-color: #f0f3f5;
  border-radius: 0.25rem;
}
.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #20a8d8;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}
.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}
.progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
          animation: progress-bar-stripes 1s linear infinite;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
            animation: none;
  }
}
.media {
  display: flex;
  align-items: flex-start;
}
.media-body {
  flex: 1;
}
.list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
}
.list-group-item-action {
  width: 100%;
  color: #5c6873;
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: #5c6873;
  text-decoration: none;
  background-color: #f0f3f5;
}
.list-group-item-action:active {
  color: #23282c;
  background-color: #e4e7ea;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: #73818f;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal .list-group-item {
  margin-right: -1px;
  margin-bottom: 0;
}
.list-group-horizontal .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}
.list-group-horizontal .list-group-item:last-child {
  margin-right: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}
@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .list-group-horizontal-sm .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .list-group-horizontal-md .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .list-group-horizontal-lg .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl .list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .list-group-horizontal-xl .list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}
.list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.list-group-flush .list-group-item:last-child {
  margin-bottom: -1px;
}
.list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}
.list-group-flush:last-child .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom: 0;
}
.list-group-item-primary {
  color: #115770;
  background-color: #c1e7f4;
}
.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #115770;
  background-color: #abdff0;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #115770;
  border-color: #115770;
}
.list-group-item-secondary {
  color: #686b6e;
  background-color: #f0f1f3;
}
.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #686b6e;
  background-color: #e2e4e8;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #686b6e;
  border-color: #686b6e;
}
.list-group-item-success {
  color: #28623c;
  background-color: #cdedd8;
}
.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #28623c;
  background-color: #bae6c9;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #28623c;
  border-color: #28623c;
}
.list-group-item-info {
  color: #336573;
  background-color: #d3eef6;
}
.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #336573;
  background-color: #bee6f2;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #336573;
  border-color: #336573;
}
.list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}
.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #856404;
  background-color: #ffe8a1;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}
.list-group-item-danger {
  color: #813838;
  background-color: #fdd6d6;
}
.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #813838;
  background-color: #fcbebe;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #813838;
  border-color: #813838;
}
.list-group-item-light {
  color: #7d7e7f;
  background-color: #fbfcfc;
}
.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #7d7e7f;
  background-color: #ecf1f1;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #7d7e7f;
  border-color: #7d7e7f;
}
.list-group-item-dark {
  color: #181c1e;
  background-color: #c5c6c8;
}
.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #181c1e;
  background-color: #b8b9bc;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #181c1e;
  border-color: #181c1e;
}
.close {
  float: right;
  font-size: 1.3125rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
.close:hover {
  color: #000;
  text-decoration: none;
}
.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {
  opacity: 0.75;
}
button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
a.close.disabled {
  pointer-events: none;
}
.toast {
  max-width: 350px;
  overflow: hidden;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
}
.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.toast.showing {
  opacity: 1;
}
.toast.show {
  display: block;
  opacity: 1;
}
.toast.hide {
  display: none;
}
.toast-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #73818f;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.toast-body {
  padding: 0.75rem;
}
.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}
.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #c8ced3;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}
.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #c8ced3;
  border-bottom-right-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.modal-footer > :not(:first-child) {
  margin-left: 0.25rem;
}
.modal-footer > :not(:last-child) {
  margin-right: 0.25rem;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }

  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
  }

  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.bs-tooltip-top, .bs-tooltip-auto[x-placement^=top] {
  padding: 0.4rem 0;
}
.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^=top] .arrow {
  bottom: 0;
}
.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=top] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}
.bs-tooltip-right, .bs-tooltip-auto[x-placement^=right] {
  padding: 0 0.4rem;
}
.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^=right] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=right] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}
.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^=bottom] {
  padding: 0.4rem 0;
}
.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^=bottom] .arrow {
  top: 0;
}
.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}
.bs-tooltip-left, .bs-tooltip-auto[x-placement^=left] {
  padding: 0 0.4rem;
}
.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^=left] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=left] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}
.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}
.popover .arrow::before, .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.bs-popover-top, .bs-popover-auto[x-placement^=top] {
  margin-bottom: 0.5rem;
}
.bs-popover-top > .arrow, .bs-popover-auto[x-placement^=top] > .arrow {
  bottom: calc((0.5rem + 1px) * -1);
}
.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^=top] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^=top] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.bs-popover-right, .bs-popover-auto[x-placement^=right] {
  margin-left: 0.5rem;
}
.bs-popover-right > .arrow, .bs-popover-auto[x-placement^=right] > .arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^=right] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^=right] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.bs-popover-bottom, .bs-popover-auto[x-placement^=bottom] {
  margin-top: 0.5rem;
}
.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^=bottom] > .arrow {
  top: calc((0.5rem + 1px) * -1);
}
.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^=bottom] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^=bottom] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}
.bs-popover-left, .bs-popover-auto[x-placement^=left] {
  margin-right: 0.5rem;
}
.bs-popover-left > .arrow, .bs-popover-auto[x-placement^=left] > .arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}
.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^=left] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^=left] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.popover-header:empty {
  display: none;
}
.popover-body {
  padding: 0.5rem 0.75rem;
  color: #23282c;
}
.carousel {
  position: relative;
}
.carousel.pointer-event {
  touch-action: pan-y;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}
.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}
.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}
.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  transform: translateX(100%);
}
.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  transform: translateX(-100%);
}
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: 0s 0.6s opacity;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
    transition: none;
  }
}
.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}
.carousel-control-prev {
  left: 0;
}
.carousel-control-next {
  right: 0;
}
.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat 50%/100% 100%;
}
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e");
}
.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e");
}
.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}
.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}
@-webkit-keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
          animation: spinner-border 0.75s linear infinite;
}
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}
@-webkit-keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow 0.75s linear infinite;
          animation: spinner-grow 0.75s linear infinite;
}
.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}
.align-baseline {
  vertical-align: baseline !important;
}
.align-top {
  vertical-align: top !important;
}
.align-middle {
  vertical-align: middle !important;
}
.align-bottom {
  vertical-align: bottom !important;
}
.align-text-bottom {
  vertical-align: text-bottom !important;
}
.align-text-top {
  vertical-align: text-top !important;
}
.bg-primary {
  background-color: #20a8d8 !important;
}
a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #1985ac !important;
}
.bg-secondary {
  background-color: #c8ced3 !important;
}
a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #acb5bc !important;
}
.bg-success {
  background-color: #4dbd74 !important;
}
a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #3a9d5d !important;
}
.bg-info {
  background-color: #63c2de !important;
}
a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #39b2d5 !important;
}
.bg-warning {
  background-color: #ffc107 !important;
}
a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #d39e00 !important;
}
.bg-danger {
  background-color: #f86c6b !important;
}
a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #f63c3a !important;
}
.bg-light {
  background-color: #f0f3f5 !important;
}
a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #d1dbe1 !important;
}
.bg-dark {
  background-color: #2f353a !important;
}
a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #181b1e !important;
}
.bg-white {
  background-color: #fff !important;
}
.bg-transparent {
  background-color: transparent !important;
}
.border {
  border: 1px solid #c8ced3 !important;
}
.border-top {
  border-top: 1px solid #c8ced3 !important;
}
.border-right {
  border-right: 1px solid #c8ced3 !important;
}
.border-bottom {
  border-bottom: 1px solid #c8ced3 !important;
}
.border-left {
  border-left: 1px solid #c8ced3 !important;
}
.border-0 {
  border: 0 !important;
}
.border-top-0 {
  border-top: 0 !important;
}
.border-right-0 {
  border-right: 0 !important;
}
.border-bottom-0 {
  border-bottom: 0 !important;
}
.border-left-0 {
  border-left: 0 !important;
}
.border-primary {
  border-color: #20a8d8 !important;
}
.border-secondary {
  border-color: #c8ced3 !important;
}
.border-success {
  border-color: #4dbd74 !important;
}
.border-info {
  border-color: #63c2de !important;
}
.border-warning {
  border-color: #ffc107 !important;
}
.border-danger {
  border-color: #f86c6b !important;
}
.border-light {
  border-color: #f0f3f5 !important;
}
.border-dark {
  border-color: #2f353a !important;
}
.border-white {
  border-color: #fff !important;
}
.rounded-sm {
  border-radius: 0.2rem !important;
}
.rounded {
  border-radius: 0.25rem !important;
}
.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}
.rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}
.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}
.rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}
.rounded-lg {
  border-radius: 0.3rem !important;
}
.rounded-circle {
  border-radius: 50% !important;
}
.rounded-pill {
  border-radius: 50rem !important;
}
.rounded-0 {
  border-radius: 0 !important;
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}
.d-none {
  display: none !important;
}
.d-inline {
  display: inline !important;
}
.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-table {
  display: table !important;
}
.d-table-row {
  display: table-row !important;
}
.d-table-cell {
  display: table-cell !important;
}
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}
@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }

  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: flex !important;
  }

  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}
.embed-responsive::before {
  display: block;
  content: "";
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.embed-responsive-21by9::before {
  padding-top: 42.8571428571%;
}
.embed-responsive-16by9::before {
  padding-top: 56.25%;
}
.embed-responsive-4by3::before {
  padding-top: 75%;
}
.embed-responsive-1by1::before {
  padding-top: 100%;
}
.embed-responsive-21by9::before {
  padding-top: 42.8571428571%;
}
.embed-responsive-16by9::before {
  padding-top: 56.25%;
}
.embed-responsive-4by3::before {
  padding-top: 75%;
}
.embed-responsive-1by1::before {
  padding-top: 100%;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.flex-fill {
  flex: 1 1 auto !important;
}
.flex-grow-0 {
  flex-grow: 0 !important;
}
.flex-grow-1 {
  flex-grow: 1 !important;
}
.flex-shrink-0 {
  flex-shrink: 0 !important;
}
.flex-shrink-1 {
  flex-shrink: 1 !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
.align-content-start {
  align-content: flex-start !important;
}
.align-content-end {
  align-content: flex-end !important;
}
.align-content-center {
  align-content: center !important;
}
.align-content-between {
  align-content: space-between !important;
}
.align-content-around {
  align-content: space-around !important;
}
.align-content-stretch {
  align-content: stretch !important;
}
.align-self-auto {
  align-self: auto !important;
}
.align-self-start {
  align-self: flex-start !important;
}
.align-self-end {
  align-self: flex-end !important;
}
.align-self-center {
  align-self: center !important;
}
.align-self-baseline {
  align-self: baseline !important;
}
.align-self-stretch {
  align-self: stretch !important;
}
@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }

  .flex-sm-column {
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-sm-fill {
    flex: 1 1 auto !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    justify-content: center !important;
  }

  .justify-content-sm-between {
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    justify-content: space-around !important;
  }

  .align-items-sm-start {
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    align-items: center !important;
  }

  .align-items-sm-baseline {
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    align-items: stretch !important;
  }

  .align-content-sm-start {
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    align-content: center !important;
  }

  .align-content-sm-between {
    align-content: space-between !important;
  }

  .align-content-sm-around {
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    align-self: auto !important;
  }

  .align-self-sm-start {
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    align-self: center !important;
  }

  .align-self-sm-baseline {
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }

  .flex-md-column {
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-md-fill {
    flex: 1 1 auto !important;
  }

  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-md-start {
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    justify-content: center !important;
  }

  .justify-content-md-between {
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    justify-content: space-around !important;
  }

  .align-items-md-start {
    align-items: flex-start !important;
  }

  .align-items-md-end {
    align-items: flex-end !important;
  }

  .align-items-md-center {
    align-items: center !important;
  }

  .align-items-md-baseline {
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    align-items: stretch !important;
  }

  .align-content-md-start {
    align-content: flex-start !important;
  }

  .align-content-md-end {
    align-content: flex-end !important;
  }

  .align-content-md-center {
    align-content: center !important;
  }

  .align-content-md-between {
    align-content: space-between !important;
  }

  .align-content-md-around {
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    align-content: stretch !important;
  }

  .align-self-md-auto {
    align-self: auto !important;
  }

  .align-self-md-start {
    align-self: flex-start !important;
  }

  .align-self-md-end {
    align-self: flex-end !important;
  }

  .align-self-md-center {
    align-self: center !important;
  }

  .align-self-md-baseline {
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }

  .flex-lg-column {
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-lg-fill {
    flex: 1 1 auto !important;
  }

  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    justify-content: center !important;
  }

  .justify-content-lg-between {
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    justify-content: space-around !important;
  }

  .align-items-lg-start {
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    align-items: center !important;
  }

  .align-items-lg-baseline {
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    align-items: stretch !important;
  }

  .align-content-lg-start {
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    align-content: center !important;
  }

  .align-content-lg-between {
    align-content: space-between !important;
  }

  .align-content-lg-around {
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    align-self: auto !important;
  }

  .align-self-lg-start {
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    align-self: center !important;
  }

  .align-self-lg-baseline {
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }

  .flex-xl-column {
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .flex-xl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    justify-content: center !important;
  }

  .justify-content-xl-between {
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    justify-content: space-around !important;
  }

  .align-items-xl-start {
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    align-items: center !important;
  }

  .align-items-xl-baseline {
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    align-items: stretch !important;
  }

  .align-content-xl-start {
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    align-content: center !important;
  }

  .align-content-xl-between {
    align-content: space-between !important;
  }

  .align-content-xl-around {
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    align-self: auto !important;
  }

  .align-self-xl-start {
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    align-self: center !important;
  }

  .align-self-xl-baseline {
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
.float-left {
  float: left !important;
}
.float-right {
  float: right !important;
}
.float-none {
  float: none !important;
}
@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }

  .float-sm-right {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }

  .float-md-right {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }
}
@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }

  .float-lg-right {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }

  .float-xl-right {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }
}
.overflow-auto {
  overflow: auto !important;
}
.overflow-hidden {
  overflow: hidden !important;
}
.position-static {
  position: static !important;
}
.position-relative {
  position: relative !important;
}
.position-absolute {
  position: absolute !important;
}
.position-fixed {
  position: fixed !important;
}
.position-sticky {
  position: sticky !important;
}
.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}
@supports (position: sticky) {
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
.shadow-none {
  box-shadow: none !important;
}
.w-25 {
  width: 25% !important;
}
.w-50 {
  width: 50% !important;
}
.w-75 {
  width: 75% !important;
}
.w-100 {
  width: 100% !important;
}
.w-auto {
  width: auto !important;
}
.h-25 {
  height: 25% !important;
}
.h-50 {
  height: 50% !important;
}
.h-75 {
  height: 75% !important;
}
.h-100 {
  height: 100% !important;
}
.h-auto {
  height: auto !important;
}
.mw-100 {
  max-width: 100% !important;
}
.mh-100 {
  max-height: 100% !important;
}
.min-vw-100 {
  min-width: 100vw !important;
}
.min-vh-100 {
  min-height: 100vh !important;
}
.vw-100 {
  width: 100vw !important;
}
.vh-100 {
  height: 100vh !important;
}
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0);
}
.m-0 {
  margin: 0 !important;
}
.mt-0,
.my-0 {
  margin-top: 0 !important;
}
.mr-0,
.mx-0 {
  margin-right: 0 !important;
}
.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}
.ml-0,
.mx-0 {
  margin-left: 0 !important;
}
.m-1 {
  margin: 0.25rem !important;
}
.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}
.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}
.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}
.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}
.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}
.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}
.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}
.m-3 {
  margin: 1rem !important;
}
.mt-3,
.my-3 {
  margin-top: 1rem !important;
}
.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}
.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}
.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}
.m-4 {
  margin: 1.5rem !important;
}
.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}
.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}
.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}
.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}
.m-5 {
  margin: 3rem !important;
}
.mt-5,
.my-5 {
  margin-top: 3rem !important;
}
.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}
.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}
.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}
.p-0 {
  padding: 0 !important;
}
.pt-0,
.py-0 {
  padding-top: 0 !important;
}
.pr-0,
.px-0 {
  padding-right: 0 !important;
}
.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}
.pl-0,
.px-0 {
  padding-left: 0 !important;
}
.p-1 {
  padding: 0.25rem !important;
}
.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}
.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}
.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}
.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}
.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}
.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}
.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}
.p-3 {
  padding: 1rem !important;
}
.pt-3,
.py-3 {
  padding-top: 1rem !important;
}
.pr-3,
.px-3 {
  padding-right: 1rem !important;
}
.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}
.pl-3,
.px-3 {
  padding-left: 1rem !important;
}
.p-4 {
  padding: 1.5rem !important;
}
.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}
.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}
.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}
.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}
.p-5 {
  padding: 3rem !important;
}
.pt-5,
.py-5 {
  padding-top: 3rem !important;
}
.pr-5,
.px-5 {
  padding-right: 3rem !important;
}
.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}
.pl-5,
.px-5 {
  padding-left: 3rem !important;
}
.m-n1 {
  margin: -0.25rem !important;
}
.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}
.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}
.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}
.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}
.m-n2 {
  margin: -0.5rem !important;
}
.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}
.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}
.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}
.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}
.m-n3 {
  margin: -1rem !important;
}
.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}
.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}
.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}
.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}
.m-n4 {
  margin: -1.5rem !important;
}
.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}
.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}
.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}
.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}
.m-n5 {
  margin: -3rem !important;
}
.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}
.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}
.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}
.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}
.m-auto {
  margin: auto !important;
}
.mt-auto,
.my-auto {
  margin-top: auto !important;
}
.mr-auto,
.mx-auto {
  margin-right: auto !important;
}
.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}
.ml-auto,
.mx-auto {
  margin-left: auto !important;
}
@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }

  .mt-sm-0,
.my-sm-0 {
    margin-top: 0 !important;
  }

  .mr-sm-0,
.mx-sm-0 {
    margin-right: 0 !important;
  }

  .mb-sm-0,
.my-sm-0 {
    margin-bottom: 0 !important;
  }

  .ml-sm-0,
.mx-sm-0 {
    margin-left: 0 !important;
  }

  .m-sm-1 {
    margin: 0.25rem !important;
  }

  .mt-sm-1,
.my-sm-1 {
    margin-top: 0.25rem !important;
  }

  .mr-sm-1,
.mx-sm-1 {
    margin-right: 0.25rem !important;
  }

  .mb-sm-1,
.my-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-sm-1,
.mx-sm-1 {
    margin-left: 0.25rem !important;
  }

  .m-sm-2 {
    margin: 0.5rem !important;
  }

  .mt-sm-2,
.my-sm-2 {
    margin-top: 0.5rem !important;
  }

  .mr-sm-2,
.mx-sm-2 {
    margin-right: 0.5rem !important;
  }

  .mb-sm-2,
.my-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-sm-2,
.mx-sm-2 {
    margin-left: 0.5rem !important;
  }

  .m-sm-3 {
    margin: 1rem !important;
  }

  .mt-sm-3,
.my-sm-3 {
    margin-top: 1rem !important;
  }

  .mr-sm-3,
.mx-sm-3 {
    margin-right: 1rem !important;
  }

  .mb-sm-3,
.my-sm-3 {
    margin-bottom: 1rem !important;
  }

  .ml-sm-3,
.mx-sm-3 {
    margin-left: 1rem !important;
  }

  .m-sm-4 {
    margin: 1.5rem !important;
  }

  .mt-sm-4,
.my-sm-4 {
    margin-top: 1.5rem !important;
  }

  .mr-sm-4,
.mx-sm-4 {
    margin-right: 1.5rem !important;
  }

  .mb-sm-4,
.my-sm-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-sm-4,
.mx-sm-4 {
    margin-left: 1.5rem !important;
  }

  .m-sm-5 {
    margin: 3rem !important;
  }

  .mt-sm-5,
.my-sm-5 {
    margin-top: 3rem !important;
  }

  .mr-sm-5,
.mx-sm-5 {
    margin-right: 3rem !important;
  }

  .mb-sm-5,
.my-sm-5 {
    margin-bottom: 3rem !important;
  }

  .ml-sm-5,
.mx-sm-5 {
    margin-left: 3rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .pt-sm-0,
.py-sm-0 {
    padding-top: 0 !important;
  }

  .pr-sm-0,
.px-sm-0 {
    padding-right: 0 !important;
  }

  .pb-sm-0,
.py-sm-0 {
    padding-bottom: 0 !important;
  }

  .pl-sm-0,
.px-sm-0 {
    padding-left: 0 !important;
  }

  .p-sm-1 {
    padding: 0.25rem !important;
  }

  .pt-sm-1,
.py-sm-1 {
    padding-top: 0.25rem !important;
  }

  .pr-sm-1,
.px-sm-1 {
    padding-right: 0.25rem !important;
  }

  .pb-sm-1,
.py-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-sm-1,
.px-sm-1 {
    padding-left: 0.25rem !important;
  }

  .p-sm-2 {
    padding: 0.5rem !important;
  }

  .pt-sm-2,
.py-sm-2 {
    padding-top: 0.5rem !important;
  }

  .pr-sm-2,
.px-sm-2 {
    padding-right: 0.5rem !important;
  }

  .pb-sm-2,
.py-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-sm-2,
.px-sm-2 {
    padding-left: 0.5rem !important;
  }

  .p-sm-3 {
    padding: 1rem !important;
  }

  .pt-sm-3,
.py-sm-3 {
    padding-top: 1rem !important;
  }

  .pr-sm-3,
.px-sm-3 {
    padding-right: 1rem !important;
  }

  .pb-sm-3,
.py-sm-3 {
    padding-bottom: 1rem !important;
  }

  .pl-sm-3,
.px-sm-3 {
    padding-left: 1rem !important;
  }

  .p-sm-4 {
    padding: 1.5rem !important;
  }

  .pt-sm-4,
.py-sm-4 {
    padding-top: 1.5rem !important;
  }

  .pr-sm-4,
.px-sm-4 {
    padding-right: 1.5rem !important;
  }

  .pb-sm-4,
.py-sm-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-sm-4,
.px-sm-4 {
    padding-left: 1.5rem !important;
  }

  .p-sm-5 {
    padding: 3rem !important;
  }

  .pt-sm-5,
.py-sm-5 {
    padding-top: 3rem !important;
  }

  .pr-sm-5,
.px-sm-5 {
    padding-right: 3rem !important;
  }

  .pb-sm-5,
.py-sm-5 {
    padding-bottom: 3rem !important;
  }

  .pl-sm-5,
.px-sm-5 {
    padding-left: 3rem !important;
  }

  .m-sm-n1 {
    margin: -0.25rem !important;
  }

  .mt-sm-n1,
.my-sm-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-sm-n1,
.mx-sm-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-sm-n1,
.my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-sm-n1,
.mx-sm-n1 {
    margin-left: -0.25rem !important;
  }

  .m-sm-n2 {
    margin: -0.5rem !important;
  }

  .mt-sm-n2,
.my-sm-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-sm-n2,
.mx-sm-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-sm-n2,
.my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-sm-n2,
.mx-sm-n2 {
    margin-left: -0.5rem !important;
  }

  .m-sm-n3 {
    margin: -1rem !important;
  }

  .mt-sm-n3,
.my-sm-n3 {
    margin-top: -1rem !important;
  }

  .mr-sm-n3,
.mx-sm-n3 {
    margin-right: -1rem !important;
  }

  .mb-sm-n3,
.my-sm-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-sm-n3,
.mx-sm-n3 {
    margin-left: -1rem !important;
  }

  .m-sm-n4 {
    margin: -1.5rem !important;
  }

  .mt-sm-n4,
.my-sm-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-sm-n4,
.mx-sm-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-sm-n4,
.my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-sm-n4,
.mx-sm-n4 {
    margin-left: -1.5rem !important;
  }

  .m-sm-n5 {
    margin: -3rem !important;
  }

  .mt-sm-n5,
.my-sm-n5 {
    margin-top: -3rem !important;
  }

  .mr-sm-n5,
.mx-sm-n5 {
    margin-right: -3rem !important;
  }

  .mb-sm-n5,
.my-sm-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-sm-n5,
.mx-sm-n5 {
    margin-left: -3rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mt-sm-auto,
.my-sm-auto {
    margin-top: auto !important;
  }

  .mr-sm-auto,
.mx-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-auto,
.my-sm-auto {
    margin-bottom: auto !important;
  }

  .ml-sm-auto,
.mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }

  .mt-md-0,
.my-md-0 {
    margin-top: 0 !important;
  }

  .mr-md-0,
.mx-md-0 {
    margin-right: 0 !important;
  }

  .mb-md-0,
.my-md-0 {
    margin-bottom: 0 !important;
  }

  .ml-md-0,
.mx-md-0 {
    margin-left: 0 !important;
  }

  .m-md-1 {
    margin: 0.25rem !important;
  }

  .mt-md-1,
.my-md-1 {
    margin-top: 0.25rem !important;
  }

  .mr-md-1,
.mx-md-1 {
    margin-right: 0.25rem !important;
  }

  .mb-md-1,
.my-md-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-md-1,
.mx-md-1 {
    margin-left: 0.25rem !important;
  }

  .m-md-2 {
    margin: 0.5rem !important;
  }

  .mt-md-2,
.my-md-2 {
    margin-top: 0.5rem !important;
  }

  .mr-md-2,
.mx-md-2 {
    margin-right: 0.5rem !important;
  }

  .mb-md-2,
.my-md-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-md-2,
.mx-md-2 {
    margin-left: 0.5rem !important;
  }

  .m-md-3 {
    margin: 1rem !important;
  }

  .mt-md-3,
.my-md-3 {
    margin-top: 1rem !important;
  }

  .mr-md-3,
.mx-md-3 {
    margin-right: 1rem !important;
  }

  .mb-md-3,
.my-md-3 {
    margin-bottom: 1rem !important;
  }

  .ml-md-3,
.mx-md-3 {
    margin-left: 1rem !important;
  }

  .m-md-4 {
    margin: 1.5rem !important;
  }

  .mt-md-4,
.my-md-4 {
    margin-top: 1.5rem !important;
  }

  .mr-md-4,
.mx-md-4 {
    margin-right: 1.5rem !important;
  }

  .mb-md-4,
.my-md-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-md-4,
.mx-md-4 {
    margin-left: 1.5rem !important;
  }

  .m-md-5 {
    margin: 3rem !important;
  }

  .mt-md-5,
.my-md-5 {
    margin-top: 3rem !important;
  }

  .mr-md-5,
.mx-md-5 {
    margin-right: 3rem !important;
  }

  .mb-md-5,
.my-md-5 {
    margin-bottom: 3rem !important;
  }

  .ml-md-5,
.mx-md-5 {
    margin-left: 3rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .pt-md-0,
.py-md-0 {
    padding-top: 0 !important;
  }

  .pr-md-0,
.px-md-0 {
    padding-right: 0 !important;
  }

  .pb-md-0,
.py-md-0 {
    padding-bottom: 0 !important;
  }

  .pl-md-0,
.px-md-0 {
    padding-left: 0 !important;
  }

  .p-md-1 {
    padding: 0.25rem !important;
  }

  .pt-md-1,
.py-md-1 {
    padding-top: 0.25rem !important;
  }

  .pr-md-1,
.px-md-1 {
    padding-right: 0.25rem !important;
  }

  .pb-md-1,
.py-md-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-md-1,
.px-md-1 {
    padding-left: 0.25rem !important;
  }

  .p-md-2 {
    padding: 0.5rem !important;
  }

  .pt-md-2,
.py-md-2 {
    padding-top: 0.5rem !important;
  }

  .pr-md-2,
.px-md-2 {
    padding-right: 0.5rem !important;
  }

  .pb-md-2,
.py-md-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-md-2,
.px-md-2 {
    padding-left: 0.5rem !important;
  }

  .p-md-3 {
    padding: 1rem !important;
  }

  .pt-md-3,
.py-md-3 {
    padding-top: 1rem !important;
  }

  .pr-md-3,
.px-md-3 {
    padding-right: 1rem !important;
  }

  .pb-md-3,
.py-md-3 {
    padding-bottom: 1rem !important;
  }

  .pl-md-3,
.px-md-3 {
    padding-left: 1rem !important;
  }

  .p-md-4 {
    padding: 1.5rem !important;
  }

  .pt-md-4,
.py-md-4 {
    padding-top: 1.5rem !important;
  }

  .pr-md-4,
.px-md-4 {
    padding-right: 1.5rem !important;
  }

  .pb-md-4,
.py-md-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-md-4,
.px-md-4 {
    padding-left: 1.5rem !important;
  }

  .p-md-5 {
    padding: 3rem !important;
  }

  .pt-md-5,
.py-md-5 {
    padding-top: 3rem !important;
  }

  .pr-md-5,
.px-md-5 {
    padding-right: 3rem !important;
  }

  .pb-md-5,
.py-md-5 {
    padding-bottom: 3rem !important;
  }

  .pl-md-5,
.px-md-5 {
    padding-left: 3rem !important;
  }

  .m-md-n1 {
    margin: -0.25rem !important;
  }

  .mt-md-n1,
.my-md-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-md-n1,
.mx-md-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-md-n1,
.my-md-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-md-n1,
.mx-md-n1 {
    margin-left: -0.25rem !important;
  }

  .m-md-n2 {
    margin: -0.5rem !important;
  }

  .mt-md-n2,
.my-md-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-md-n2,
.mx-md-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-md-n2,
.my-md-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-md-n2,
.mx-md-n2 {
    margin-left: -0.5rem !important;
  }

  .m-md-n3 {
    margin: -1rem !important;
  }

  .mt-md-n3,
.my-md-n3 {
    margin-top: -1rem !important;
  }

  .mr-md-n3,
.mx-md-n3 {
    margin-right: -1rem !important;
  }

  .mb-md-n3,
.my-md-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-md-n3,
.mx-md-n3 {
    margin-left: -1rem !important;
  }

  .m-md-n4 {
    margin: -1.5rem !important;
  }

  .mt-md-n4,
.my-md-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-md-n4,
.mx-md-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-md-n4,
.my-md-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-md-n4,
.mx-md-n4 {
    margin-left: -1.5rem !important;
  }

  .m-md-n5 {
    margin: -3rem !important;
  }

  .mt-md-n5,
.my-md-n5 {
    margin-top: -3rem !important;
  }

  .mr-md-n5,
.mx-md-n5 {
    margin-right: -3rem !important;
  }

  .mb-md-n5,
.my-md-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-md-n5,
.mx-md-n5 {
    margin-left: -3rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mt-md-auto,
.my-md-auto {
    margin-top: auto !important;
  }

  .mr-md-auto,
.mx-md-auto {
    margin-right: auto !important;
  }

  .mb-md-auto,
.my-md-auto {
    margin-bottom: auto !important;
  }

  .ml-md-auto,
.mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }

  .mt-lg-0,
.my-lg-0 {
    margin-top: 0 !important;
  }

  .mr-lg-0,
.mx-lg-0 {
    margin-right: 0 !important;
  }

  .mb-lg-0,
.my-lg-0 {
    margin-bottom: 0 !important;
  }

  .ml-lg-0,
.mx-lg-0 {
    margin-left: 0 !important;
  }

  .m-lg-1 {
    margin: 0.25rem !important;
  }

  .mt-lg-1,
.my-lg-1 {
    margin-top: 0.25rem !important;
  }

  .mr-lg-1,
.mx-lg-1 {
    margin-right: 0.25rem !important;
  }

  .mb-lg-1,
.my-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-lg-1,
.mx-lg-1 {
    margin-left: 0.25rem !important;
  }

  .m-lg-2 {
    margin: 0.5rem !important;
  }

  .mt-lg-2,
.my-lg-2 {
    margin-top: 0.5rem !important;
  }

  .mr-lg-2,
.mx-lg-2 {
    margin-right: 0.5rem !important;
  }

  .mb-lg-2,
.my-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-lg-2,
.mx-lg-2 {
    margin-left: 0.5rem !important;
  }

  .m-lg-3 {
    margin: 1rem !important;
  }

  .mt-lg-3,
.my-lg-3 {
    margin-top: 1rem !important;
  }

  .mr-lg-3,
.mx-lg-3 {
    margin-right: 1rem !important;
  }

  .mb-lg-3,
.my-lg-3 {
    margin-bottom: 1rem !important;
  }

  .ml-lg-3,
.mx-lg-3 {
    margin-left: 1rem !important;
  }

  .m-lg-4 {
    margin: 1.5rem !important;
  }

  .mt-lg-4,
.my-lg-4 {
    margin-top: 1.5rem !important;
  }

  .mr-lg-4,
.mx-lg-4 {
    margin-right: 1.5rem !important;
  }

  .mb-lg-4,
.my-lg-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-lg-4,
.mx-lg-4 {
    margin-left: 1.5rem !important;
  }

  .m-lg-5 {
    margin: 3rem !important;
  }

  .mt-lg-5,
.my-lg-5 {
    margin-top: 3rem !important;
  }

  .mr-lg-5,
.mx-lg-5 {
    margin-right: 3rem !important;
  }

  .mb-lg-5,
.my-lg-5 {
    margin-bottom: 3rem !important;
  }

  .ml-lg-5,
.mx-lg-5 {
    margin-left: 3rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .pt-lg-0,
.py-lg-0 {
    padding-top: 0 !important;
  }

  .pr-lg-0,
.px-lg-0 {
    padding-right: 0 !important;
  }

  .pb-lg-0,
.py-lg-0 {
    padding-bottom: 0 !important;
  }

  .pl-lg-0,
.px-lg-0 {
    padding-left: 0 !important;
  }

  .p-lg-1 {
    padding: 0.25rem !important;
  }

  .pt-lg-1,
.py-lg-1 {
    padding-top: 0.25rem !important;
  }

  .pr-lg-1,
.px-lg-1 {
    padding-right: 0.25rem !important;
  }

  .pb-lg-1,
.py-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-lg-1,
.px-lg-1 {
    padding-left: 0.25rem !important;
  }

  .p-lg-2 {
    padding: 0.5rem !important;
  }

  .pt-lg-2,
.py-lg-2 {
    padding-top: 0.5rem !important;
  }

  .pr-lg-2,
.px-lg-2 {
    padding-right: 0.5rem !important;
  }

  .pb-lg-2,
.py-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-lg-2,
.px-lg-2 {
    padding-left: 0.5rem !important;
  }

  .p-lg-3 {
    padding: 1rem !important;
  }

  .pt-lg-3,
.py-lg-3 {
    padding-top: 1rem !important;
  }

  .pr-lg-3,
.px-lg-3 {
    padding-right: 1rem !important;
  }

  .pb-lg-3,
.py-lg-3 {
    padding-bottom: 1rem !important;
  }

  .pl-lg-3,
.px-lg-3 {
    padding-left: 1rem !important;
  }

  .p-lg-4 {
    padding: 1.5rem !important;
  }

  .pt-lg-4,
.py-lg-4 {
    padding-top: 1.5rem !important;
  }

  .pr-lg-4,
.px-lg-4 {
    padding-right: 1.5rem !important;
  }

  .pb-lg-4,
.py-lg-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-lg-4,
.px-lg-4 {
    padding-left: 1.5rem !important;
  }

  .p-lg-5 {
    padding: 3rem !important;
  }

  .pt-lg-5,
.py-lg-5 {
    padding-top: 3rem !important;
  }

  .pr-lg-5,
.px-lg-5 {
    padding-right: 3rem !important;
  }

  .pb-lg-5,
.py-lg-5 {
    padding-bottom: 3rem !important;
  }

  .pl-lg-5,
.px-lg-5 {
    padding-left: 3rem !important;
  }

  .m-lg-n1 {
    margin: -0.25rem !important;
  }

  .mt-lg-n1,
.my-lg-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-lg-n1,
.mx-lg-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-lg-n1,
.my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-lg-n1,
.mx-lg-n1 {
    margin-left: -0.25rem !important;
  }

  .m-lg-n2 {
    margin: -0.5rem !important;
  }

  .mt-lg-n2,
.my-lg-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-lg-n2,
.mx-lg-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-lg-n2,
.my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-lg-n2,
.mx-lg-n2 {
    margin-left: -0.5rem !important;
  }

  .m-lg-n3 {
    margin: -1rem !important;
  }

  .mt-lg-n3,
.my-lg-n3 {
    margin-top: -1rem !important;
  }

  .mr-lg-n3,
.mx-lg-n3 {
    margin-right: -1rem !important;
  }

  .mb-lg-n3,
.my-lg-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-lg-n3,
.mx-lg-n3 {
    margin-left: -1rem !important;
  }

  .m-lg-n4 {
    margin: -1.5rem !important;
  }

  .mt-lg-n4,
.my-lg-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-lg-n4,
.mx-lg-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-lg-n4,
.my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-lg-n4,
.mx-lg-n4 {
    margin-left: -1.5rem !important;
  }

  .m-lg-n5 {
    margin: -3rem !important;
  }

  .mt-lg-n5,
.my-lg-n5 {
    margin-top: -3rem !important;
  }

  .mr-lg-n5,
.mx-lg-n5 {
    margin-right: -3rem !important;
  }

  .mb-lg-n5,
.my-lg-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-lg-n5,
.mx-lg-n5 {
    margin-left: -3rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mt-lg-auto,
.my-lg-auto {
    margin-top: auto !important;
  }

  .mr-lg-auto,
.mx-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-auto,
.my-lg-auto {
    margin-bottom: auto !important;
  }

  .ml-lg-auto,
.mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }

  .mt-xl-0,
.my-xl-0 {
    margin-top: 0 !important;
  }

  .mr-xl-0,
.mx-xl-0 {
    margin-right: 0 !important;
  }

  .mb-xl-0,
.my-xl-0 {
    margin-bottom: 0 !important;
  }

  .ml-xl-0,
.mx-xl-0 {
    margin-left: 0 !important;
  }

  .m-xl-1 {
    margin: 0.25rem !important;
  }

  .mt-xl-1,
.my-xl-1 {
    margin-top: 0.25rem !important;
  }

  .mr-xl-1,
.mx-xl-1 {
    margin-right: 0.25rem !important;
  }

  .mb-xl-1,
.my-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-xl-1,
.mx-xl-1 {
    margin-left: 0.25rem !important;
  }

  .m-xl-2 {
    margin: 0.5rem !important;
  }

  .mt-xl-2,
.my-xl-2 {
    margin-top: 0.5rem !important;
  }

  .mr-xl-2,
.mx-xl-2 {
    margin-right: 0.5rem !important;
  }

  .mb-xl-2,
.my-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-xl-2,
.mx-xl-2 {
    margin-left: 0.5rem !important;
  }

  .m-xl-3 {
    margin: 1rem !important;
  }

  .mt-xl-3,
.my-xl-3 {
    margin-top: 1rem !important;
  }

  .mr-xl-3,
.mx-xl-3 {
    margin-right: 1rem !important;
  }

  .mb-xl-3,
.my-xl-3 {
    margin-bottom: 1rem !important;
  }

  .ml-xl-3,
.mx-xl-3 {
    margin-left: 1rem !important;
  }

  .m-xl-4 {
    margin: 1.5rem !important;
  }

  .mt-xl-4,
.my-xl-4 {
    margin-top: 1.5rem !important;
  }

  .mr-xl-4,
.mx-xl-4 {
    margin-right: 1.5rem !important;
  }

  .mb-xl-4,
.my-xl-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-xl-4,
.mx-xl-4 {
    margin-left: 1.5rem !important;
  }

  .m-xl-5 {
    margin: 3rem !important;
  }

  .mt-xl-5,
.my-xl-5 {
    margin-top: 3rem !important;
  }

  .mr-xl-5,
.mx-xl-5 {
    margin-right: 3rem !important;
  }

  .mb-xl-5,
.my-xl-5 {
    margin-bottom: 3rem !important;
  }

  .ml-xl-5,
.mx-xl-5 {
    margin-left: 3rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .pt-xl-0,
.py-xl-0 {
    padding-top: 0 !important;
  }

  .pr-xl-0,
.px-xl-0 {
    padding-right: 0 !important;
  }

  .pb-xl-0,
.py-xl-0 {
    padding-bottom: 0 !important;
  }

  .pl-xl-0,
.px-xl-0 {
    padding-left: 0 !important;
  }

  .p-xl-1 {
    padding: 0.25rem !important;
  }

  .pt-xl-1,
.py-xl-1 {
    padding-top: 0.25rem !important;
  }

  .pr-xl-1,
.px-xl-1 {
    padding-right: 0.25rem !important;
  }

  .pb-xl-1,
.py-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-xl-1,
.px-xl-1 {
    padding-left: 0.25rem !important;
  }

  .p-xl-2 {
    padding: 0.5rem !important;
  }

  .pt-xl-2,
.py-xl-2 {
    padding-top: 0.5rem !important;
  }

  .pr-xl-2,
.px-xl-2 {
    padding-right: 0.5rem !important;
  }

  .pb-xl-2,
.py-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-xl-2,
.px-xl-2 {
    padding-left: 0.5rem !important;
  }

  .p-xl-3 {
    padding: 1rem !important;
  }

  .pt-xl-3,
.py-xl-3 {
    padding-top: 1rem !important;
  }

  .pr-xl-3,
.px-xl-3 {
    padding-right: 1rem !important;
  }

  .pb-xl-3,
.py-xl-3 {
    padding-bottom: 1rem !important;
  }

  .pl-xl-3,
.px-xl-3 {
    padding-left: 1rem !important;
  }

  .p-xl-4 {
    padding: 1.5rem !important;
  }

  .pt-xl-4,
.py-xl-4 {
    padding-top: 1.5rem !important;
  }

  .pr-xl-4,
.px-xl-4 {
    padding-right: 1.5rem !important;
  }

  .pb-xl-4,
.py-xl-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-xl-4,
.px-xl-4 {
    padding-left: 1.5rem !important;
  }

  .p-xl-5 {
    padding: 3rem !important;
  }

  .pt-xl-5,
.py-xl-5 {
    padding-top: 3rem !important;
  }

  .pr-xl-5,
.px-xl-5 {
    padding-right: 3rem !important;
  }

  .pb-xl-5,
.py-xl-5 {
    padding-bottom: 3rem !important;
  }

  .pl-xl-5,
.px-xl-5 {
    padding-left: 3rem !important;
  }

  .m-xl-n1 {
    margin: -0.25rem !important;
  }

  .mt-xl-n1,
.my-xl-n1 {
    margin-top: -0.25rem !important;
  }

  .mr-xl-n1,
.mx-xl-n1 {
    margin-right: -0.25rem !important;
  }

  .mb-xl-n1,
.my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }

  .ml-xl-n1,
.mx-xl-n1 {
    margin-left: -0.25rem !important;
  }

  .m-xl-n2 {
    margin: -0.5rem !important;
  }

  .mt-xl-n2,
.my-xl-n2 {
    margin-top: -0.5rem !important;
  }

  .mr-xl-n2,
.mx-xl-n2 {
    margin-right: -0.5rem !important;
  }

  .mb-xl-n2,
.my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }

  .ml-xl-n2,
.mx-xl-n2 {
    margin-left: -0.5rem !important;
  }

  .m-xl-n3 {
    margin: -1rem !important;
  }

  .mt-xl-n3,
.my-xl-n3 {
    margin-top: -1rem !important;
  }

  .mr-xl-n3,
.mx-xl-n3 {
    margin-right: -1rem !important;
  }

  .mb-xl-n3,
.my-xl-n3 {
    margin-bottom: -1rem !important;
  }

  .ml-xl-n3,
.mx-xl-n3 {
    margin-left: -1rem !important;
  }

  .m-xl-n4 {
    margin: -1.5rem !important;
  }

  .mt-xl-n4,
.my-xl-n4 {
    margin-top: -1.5rem !important;
  }

  .mr-xl-n4,
.mx-xl-n4 {
    margin-right: -1.5rem !important;
  }

  .mb-xl-n4,
.my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }

  .ml-xl-n4,
.mx-xl-n4 {
    margin-left: -1.5rem !important;
  }

  .m-xl-n5 {
    margin: -3rem !important;
  }

  .mt-xl-n5,
.my-xl-n5 {
    margin-top: -3rem !important;
  }

  .mr-xl-n5,
.mx-xl-n5 {
    margin-right: -3rem !important;
  }

  .mb-xl-n5,
.my-xl-n5 {
    margin-bottom: -3rem !important;
  }

  .ml-xl-n5,
.mx-xl-n5 {
    margin-left: -3rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mt-xl-auto,
.my-xl-auto {
    margin-top: auto !important;
  }

  .mr-xl-auto,
.mx-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-auto,
.my-xl-auto {
    margin-bottom: auto !important;
  }

  .ml-xl-auto,
.mx-xl-auto {
    margin-left: auto !important;
  }
}
.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}
.text-justify {
  text-align: justify !important;
}
.text-wrap {
  white-space: normal !important;
}
.text-nowrap {
  white-space: nowrap !important;
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-left {
  text-align: left !important;
}
.text-right {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }

  .text-sm-right {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }

  .text-md-right {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }

  .text-lg-right {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }

  .text-xl-right {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.font-weight-light {
  font-weight: 300 !important;
}
.font-weight-lighter {
  font-weight: lighter !important;
}
.font-weight-normal {
  font-weight: 400 !important;
}
.font-weight-bold {
  font-weight: 700 !important;
}
.font-weight-bolder {
  font-weight: bolder !important;
}
.font-italic {
  font-style: italic !important;
}
.text-white {
  color: #fff !important;
}
.text-primary {
  color: #20a8d8 !important;
}
a.text-primary:hover, a.text-primary:focus {
  color: #167495 !important;
}
.text-secondary {
  color: #c8ced3 !important;
}
a.text-secondary:hover, a.text-secondary:focus {
  color: #9ea8b1 !important;
}
.text-success {
  color: #4dbd74 !important;
}
a.text-success:hover, a.text-success:focus {
  color: #338a52 !important;
}
.text-info {
  color: #63c2de !important;
}
a.text-info:hover, a.text-info:focus {
  color: #2ba6ca !important;
}
.text-warning {
  color: #ffc107 !important;
}
a.text-warning:hover, a.text-warning:focus {
  color: #ba8b00 !important;
}
.text-danger {
  color: #f86c6b !important;
}
a.text-danger:hover, a.text-danger:focus {
  color: #f52322 !important;
}
.text-light {
  color: #f0f3f5 !important;
}
a.text-light:hover, a.text-light:focus {
  color: #c2ced6 !important;
}
.text-dark {
  color: #2f353a !important;
}
a.text-dark:hover, a.text-dark:focus {
  color: #0d0e10 !important;
}
.text-body {
  color: #23282c !important;
}
.text-muted {
  color: #73818f !important;
}
.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}
.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.text-decoration-none {
  text-decoration: none !important;
}
.text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}
.text-reset {
  color: inherit !important;
}
.visible {
  visibility: visible !important;
}
.invisible {
  visibility: hidden !important;
}
@media print {
  *,
*::before,
*::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  a:not(.btn) {
    text-decoration: underline;
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
blockquote {
    border: 1px solid #8f9ba6;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
img {
    page-break-inside: avoid;
  }

  p,
h2,
h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
h3 {
    page-break-after: avoid;
  }

  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }

  .container {
    min-width: 992px !important;
  }

  .navbar {
    display: none;
  }

  .badge {
    border: 1px solid #000;
  }

  .table {
    border-collapse: collapse !important;
  }
  .table td,
.table th {
    background-color: #fff !important;
  }

  .table-bordered th,
.table-bordered td {
    border: 1px solid #c8ced3 !important;
  }

  .table-dark {
    color: inherit;
  }
  .table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
    border-color: #c8ced3;
  }

  .table .thead-dark th {
    color: inherit;
    border-color: #c8ced3;
  }
}
.animated {
  -webkit-animation-duration: 1s;
          animation-duration: 1s;
}
.animated.infinite {
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
}
.animated.hinge {
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fadeIn {
  -webkit-animation-name: fadeIn;
          animation-name: fadeIn;
}
.ps {
  overflow: hidden !important;
  touch-action: auto;
  -ms-overflow-style: none;
  overflow-anchor: none;
}
.ps__rail-x {
  position: absolute;
  bottom: 0;
  display: none;
  height: 15px;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
}
.ps__rail-y {
  position: absolute;
  right: 0;
  display: none;
  width: 15px;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
}
.ps--active-x > .ps__rail-x,
.ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}
.ps:hover > .ps__rail-x,
.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-x,
.ps--focus > .ps__rail-y,
.ps--scrolling-x > .ps__rail-x,
.ps--scrolling-y > .ps__rail-y {
  opacity: 0.6;
}
.ps__rail-x:hover,
.ps__rail-y:hover,
.ps__rail-x:focus,
.ps__rail-y:focus {
  background-color: #eee;
  opacity: 0.9;
}
/*
 * Scrollbar thumb styles
 */
.ps__thumb-x {
  position: absolute;
  bottom: 2px;
  height: 6px;
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color 0.2s linear, height 0.2s ease-in-out;
}
.ps__thumb-y {
  position: absolute;
  right: 2px;
  width: 6px;
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color 0.2s linear, width 0.2s ease-in-out;
}
.ps__rail-x:hover > .ps__thumb-x,
.ps__rail-x:focus > .ps__thumb-x {
  height: 11px;
  background-color: #999;
}
.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y {
  width: 11px;
  background-color: #999;
}
@supports (-ms-overflow-style: none) {
  .ps {
    overflow: auto !important;
  }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .ps {
    overflow: auto !important;
  }
}
.aside-menu {
  z-index: 1019;
  width: 250px;
  color: #2f353a;
  background: #fff;
  border-left: 1px solid #c8ced3;
}
.aside-menu .nav-tabs {
  border-color: #c8ced3;
}
.aside-menu .nav-tabs .nav-link {
  padding: 0.75rem 1rem;
  color: #23282c;
  border-top: 0;
  border-radius: 0;
}
.aside-menu .nav-tabs .nav-link.active {
  color: #20a8d8;
  border-right-color: #c8ced3;
  border-left-color: #c8ced3;
}
.aside-menu .nav-tabs .nav-item:first-child .nav-link {
  border-left: 0;
}
.aside-menu .tab-content {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  border: 0;
  border-top: 1px solid #c8ced3;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
.aside-menu .tab-content::-webkit-scrollbar {
  width: 10px;
  margin-left: -10px;
  -webkit-appearance: none;
          appearance: none;
}
.aside-menu .tab-content::-webkit-scrollbar-track {
  background-color: white;
  border-right: 1px solid #f2f2f2;
  border-left: 1px solid #f2f2f2;
}
.aside-menu .tab-content::-webkit-scrollbar-thumb {
  height: 50px;
  background-color: #e6e6e6;
  background-clip: content-box;
  border-color: transparent;
  border-style: solid;
  border-width: 1px 2px;
}
.aside-menu .tab-content .tab-pane {
  padding: 0;
}
.avatar {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 36px;
}
.avatar .avatar-status {
  position: absolute;
  right: 0;
  bottom: 0;
  display: block;
  width: 10px;
  height: 10px;
  border: 1px solid #fff;
  border-radius: 50em;
}
.avatar > img {
  vertical-align: baseline;
  vertical-align: initial;
}
.avatar-lg {
  position: relative;
  display: inline-block;
  width: 72px;
  height: 72px;
}
.avatar-lg .avatar-status {
  position: absolute;
  right: 0;
  bottom: 0;
  display: block;
  width: 12px;
  height: 12px;
  border: 1px solid #fff;
  border-radius: 50em;
}
.avatar-sm {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
}
.avatar-sm .avatar-status {
  position: absolute;
  right: 0;
  bottom: 0;
  display: block;
  width: 8px;
  height: 8px;
  border: 1px solid #fff;
  border-radius: 50em;
}
.avatar-xs {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
}
.avatar-xs .avatar-status {
  position: absolute;
  right: 0;
  bottom: 0;
  display: block;
  width: 8px;
  height: 8px;
  border: 1px solid #fff;
  border-radius: 50em;
}
.avatars-stack .avatar {
  margin-right: -18px;
  transition: margin-right 0.25s;
}
.avatars-stack .avatar:hover {
  margin-right: 0;
}
.avatars-stack .avatar-lg {
  margin-right: -36px;
}
.avatars-stack .avatar-sm {
  margin-right: -12px;
}
.avatars-stack .avatar-xs {
  margin-right: -10px;
}
.badge-pill {
  border-radius: 10rem;
}
.breadcrumb-menu {
  margin-left: auto;
}
.breadcrumb-menu::before {
  display: none;
}
.breadcrumb-menu .btn-group {
  vertical-align: top;
}
.breadcrumb-menu .btn {
  padding: 0 0.75rem;
  color: #73818f;
  vertical-align: top;
  border: 0;
}
.breadcrumb-menu .btn:hover, .breadcrumb-menu .btn.active {
  color: #23282c;
  background: transparent;
}
.breadcrumb-menu .open .btn {
  color: #23282c;
  background: transparent;
}
.breadcrumb-menu .dropdown-menu {
  min-width: 180px;
  line-height: 1.5;
}
*[dir=rtl] .breadcrumb-menu {
  margin-right: auto;
  margin-left: 0;
  margin-left: initial;
}
.breadcrumb {
  position: relative;
  border-radius: 0;
  border-bottom: 1px solid #c8ced3;
}
*[dir=rtl] .breadcrumb-item::before {
  padding-right: 0;
  padding-left: 0.5rem;
}
*[dir=rtl] .breadcrumb-item {
  padding-right: 0.5rem;
  padding-left: 0;
}
.brand-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-bottom: 1.5rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid #c8ced3;
  border-radius: 0.25rem;
}
.brand-card-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 6rem;
  border-radius: 0.25rem 0.25rem 0 0;
}
.brand-card-header i {
  font-size: 2rem;
  color: #fff;
}
.brand-card-header .chart-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.brand-card-body {
  display: flex;
  flex-direction: row;
  padding: 0.75rem 0;
  text-align: center;
}
.brand-card-body > * {
  flex: 1;
  padding: 0.1875rem 0;
}
.brand-card-body > *:not(:last-child) {
  border-right: 1px solid #c8ced3;
}
*[dir=rtl] .brand-card-body > *:not(:last-child) {
  border-right: 0;
  border-left: 1px solid #c8ced3;
}
.btn-brand {
  border: 0;
}
.btn-brand i {
  display: inline-block;
  width: 2.0625rem;
  margin: -0.375rem -0.75rem;
  line-height: 2.0625rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
}
.btn-brand i + span {
  margin-left: 1.5rem;
}
.btn-brand.btn-lg i, .btn-group-lg > .btn-brand.btn i {
  width: 2.640625rem;
  margin: -0.5rem -1rem;
  line-height: 2.640625rem;
  border-radius: 0.3rem;
}
.btn-brand.btn-lg i + span, .btn-group-lg > .btn-brand.btn i + span {
  margin-left: 2rem;
}
.btn-brand.btn-sm i, .btn-group-sm > .btn-brand.btn i {
  width: 1.6484375rem;
  margin: -0.25rem -0.5rem;
  line-height: 1.6484375rem;
  border-radius: 0.2rem;
}
.btn-brand.btn-sm i + span, .btn-group-sm > .btn-brand.btn i + span {
  margin-left: 1rem;
}
.btn-brand.btn-square i {
  border-radius: 0;
}
.btn-facebook {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}
.btn-facebook:hover {
  color: #fff;
  background-color: #30497c;
  border-color: #2d4373;
}
.btn-facebook:focus, .btn-facebook.focus {
  box-shadow: 0 0 0 0.2rem rgba(88, 114, 167, 0.5);
}
.btn-facebook.disabled, .btn-facebook:disabled {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}
.btn-facebook:not(:disabled):not(.disabled):active, .btn-facebook:not(:disabled):not(.disabled).active, .show > .btn-facebook.dropdown-toggle {
  color: #fff;
  background-color: #2d4373;
  border-color: #293e6a;
}
.btn-facebook:not(:disabled):not(.disabled):active:focus, .btn-facebook:not(:disabled):not(.disabled).active:focus, .show > .btn-facebook.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(88, 114, 167, 0.5);
}
.btn-twitter {
  color: #fff;
  background-color: #00aced;
  border-color: #00aced;
}
.btn-twitter:hover {
  color: #fff;
  background-color: #0090c7;
  border-color: #0087ba;
}
.btn-twitter:focus, .btn-twitter.focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 184, 240, 0.5);
}
.btn-twitter.disabled, .btn-twitter:disabled {
  color: #fff;
  background-color: #00aced;
  border-color: #00aced;
}
.btn-twitter:not(:disabled):not(.disabled):active, .btn-twitter:not(:disabled):not(.disabled).active, .show > .btn-twitter.dropdown-toggle {
  color: #fff;
  background-color: #0087ba;
  border-color: #007ead;
}
.btn-twitter:not(:disabled):not(.disabled):active:focus, .btn-twitter:not(:disabled):not(.disabled).active:focus, .show > .btn-twitter.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 184, 240, 0.5);
}
.btn-linkedin {
  color: #fff;
  background-color: #4875b4;
  border-color: #4875b4;
}
.btn-linkedin:hover {
  color: #fff;
  background-color: #3d6399;
  border-color: #395d90;
}
.btn-linkedin:focus, .btn-linkedin.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 138, 191, 0.5);
}
.btn-linkedin.disabled, .btn-linkedin:disabled {
  color: #fff;
  background-color: #4875b4;
  border-color: #4875b4;
}
.btn-linkedin:not(:disabled):not(.disabled):active, .btn-linkedin:not(:disabled):not(.disabled).active, .show > .btn-linkedin.dropdown-toggle {
  color: #fff;
  background-color: #395d90;
  border-color: #365786;
}
.btn-linkedin:not(:disabled):not(.disabled):active:focus, .btn-linkedin:not(:disabled):not(.disabled).active:focus, .show > .btn-linkedin.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 138, 191, 0.5);
}
.btn-google-plus {
  color: #fff;
  background-color: #d34836;
  border-color: #d34836;
}
.btn-google-plus:hover {
  color: #fff;
  background-color: #ba3929;
  border-color: #b03626;
}
.btn-google-plus:focus, .btn-google-plus.focus {
  box-shadow: 0 0 0 0.2rem rgba(218, 99, 84, 0.5);
}
.btn-google-plus.disabled, .btn-google-plus:disabled {
  color: #fff;
  background-color: #d34836;
  border-color: #d34836;
}
.btn-google-plus:not(:disabled):not(.disabled):active, .btn-google-plus:not(:disabled):not(.disabled).active, .show > .btn-google-plus.dropdown-toggle {
  color: #fff;
  background-color: #b03626;
  border-color: #a53324;
}
.btn-google-plus:not(:disabled):not(.disabled):active:focus, .btn-google-plus:not(:disabled):not(.disabled).active:focus, .show > .btn-google-plus.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(218, 99, 84, 0.5);
}
.btn-flickr {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}
.btn-flickr:hover {
  color: #fff;
  background-color: #d90070;
  border-color: #cc006a;
}
.btn-flickr:focus, .btn-flickr.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 38, 150, 0.5);
}
.btn-flickr.disabled, .btn-flickr:disabled {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}
.btn-flickr:not(:disabled):not(.disabled):active, .btn-flickr:not(:disabled):not(.disabled).active, .show > .btn-flickr.dropdown-toggle {
  color: #fff;
  background-color: #cc006a;
  border-color: #bf0063;
}
.btn-flickr:not(:disabled):not(.disabled):active:focus, .btn-flickr:not(:disabled):not(.disabled).active:focus, .show > .btn-flickr.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 38, 150, 0.5);
}
.btn-tumblr {
  color: #fff;
  background-color: #32506d;
  border-color: #32506d;
}
.btn-tumblr:hover {
  color: #fff;
  background-color: #263d53;
  border-color: #22364a;
}
.btn-tumblr:focus, .btn-tumblr.focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 106, 131, 0.5);
}
.btn-tumblr.disabled, .btn-tumblr:disabled {
  color: #fff;
  background-color: #32506d;
  border-color: #32506d;
}
.btn-tumblr:not(:disabled):not(.disabled):active, .btn-tumblr:not(:disabled):not(.disabled).active, .show > .btn-tumblr.dropdown-toggle {
  color: #fff;
  background-color: #22364a;
  border-color: #1e3041;
}
.btn-tumblr:not(:disabled):not(.disabled):active:focus, .btn-tumblr:not(:disabled):not(.disabled).active:focus, .show > .btn-tumblr.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 106, 131, 0.5);
}
.btn-xing {
  color: #fff;
  background-color: #026466;
  border-color: #026466;
}
.btn-xing:hover {
  color: #fff;
  background-color: #013f40;
  border-color: #013334;
}
.btn-xing:focus, .btn-xing.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 123, 125, 0.5);
}
.btn-xing.disabled, .btn-xing:disabled {
  color: #fff;
  background-color: #026466;
  border-color: #026466;
}
.btn-xing:not(:disabled):not(.disabled):active, .btn-xing:not(:disabled):not(.disabled).active, .show > .btn-xing.dropdown-toggle {
  color: #fff;
  background-color: #013334;
  border-color: #012727;
}
.btn-xing:not(:disabled):not(.disabled):active:focus, .btn-xing:not(:disabled):not(.disabled).active:focus, .show > .btn-xing.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 123, 125, 0.5);
}
.btn-github {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}
.btn-github:hover {
  color: #fff;
  background-color: #3570aa;
  border-color: #3269a0;
}
.btn-github:focus, .btn-github.focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 150, 205, 0.5);
}
.btn-github.disabled, .btn-github:disabled {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}
.btn-github:not(:disabled):not(.disabled):active, .btn-github:not(:disabled):not(.disabled).active, .show > .btn-github.dropdown-toggle {
  color: #fff;
  background-color: #3269a0;
  border-color: #2f6397;
}
.btn-github:not(:disabled):not(.disabled):active:focus, .btn-github:not(:disabled):not(.disabled).active:focus, .show > .btn-github.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 150, 205, 0.5);
}
.btn-html5 {
  color: #fff;
  background-color: #e34f26;
  border-color: #e34f26;
}
.btn-html5:hover {
  color: #fff;
  background-color: #c9401a;
  border-color: #be3c18;
}
.btn-html5:focus, .btn-html5.focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 105, 71, 0.5);
}
.btn-html5.disabled, .btn-html5:disabled {
  color: #fff;
  background-color: #e34f26;
  border-color: #e34f26;
}
.btn-html5:not(:disabled):not(.disabled):active, .btn-html5:not(:disabled):not(.disabled).active, .show > .btn-html5.dropdown-toggle {
  color: #fff;
  background-color: #be3c18;
  border-color: #b23917;
}
.btn-html5:not(:disabled):not(.disabled):active:focus, .btn-html5:not(:disabled):not(.disabled).active:focus, .show > .btn-html5.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 105, 71, 0.5);
}
.btn-openid {
  color: #23282c;
  background-color: #f78c40;
  border-color: #f78c40;
}
.btn-openid:hover {
  color: #fff;
  background-color: #f5761b;
  border-color: #f56f0f;
}
.btn-openid:focus, .btn-openid.focus {
  box-shadow: 0 0 0 0.2rem rgba(215, 125, 61, 0.5);
}
.btn-openid.disabled, .btn-openid:disabled {
  color: #23282c;
  background-color: #f78c40;
  border-color: #f78c40;
}
.btn-openid:not(:disabled):not(.disabled):active, .btn-openid:not(:disabled):not(.disabled).active, .show > .btn-openid.dropdown-toggle {
  color: #fff;
  background-color: #f56f0f;
  border-color: #ed680a;
}
.btn-openid:not(:disabled):not(.disabled):active:focus, .btn-openid:not(:disabled):not(.disabled).active:focus, .show > .btn-openid.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(215, 125, 61, 0.5);
}
.btn-stack-overflow {
  color: #fff;
  background-color: #fe7a15;
  border-color: #fe7a15;
}
.btn-stack-overflow:hover {
  color: #fff;
  background-color: #ec6701;
  border-color: #df6101;
}
.btn-stack-overflow:focus, .btn-stack-overflow.focus {
  box-shadow: 0 0 0 0.2rem rgba(254, 142, 56, 0.5);
}
.btn-stack-overflow.disabled, .btn-stack-overflow:disabled {
  color: #fff;
  background-color: #fe7a15;
  border-color: #fe7a15;
}
.btn-stack-overflow:not(:disabled):not(.disabled):active, .btn-stack-overflow:not(:disabled):not(.disabled).active, .show > .btn-stack-overflow.dropdown-toggle {
  color: #fff;
  background-color: #df6101;
  border-color: #d25c01;
}
.btn-stack-overflow:not(:disabled):not(.disabled):active:focus, .btn-stack-overflow:not(:disabled):not(.disabled).active:focus, .show > .btn-stack-overflow.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(254, 142, 56, 0.5);
}
.btn-youtube {
  color: #fff;
  background-color: #b00;
  border-color: #b00;
}
.btn-youtube:hover {
  color: #fff;
  background-color: #950000;
  border-color: #880000;
}
.btn-youtube:focus, .btn-youtube.focus {
  box-shadow: 0 0 0 0.2rem rgba(197, 38, 38, 0.5);
}
.btn-youtube.disabled, .btn-youtube:disabled {
  color: #fff;
  background-color: #b00;
  border-color: #b00;
}
.btn-youtube:not(:disabled):not(.disabled):active, .btn-youtube:not(:disabled):not(.disabled).active, .show > .btn-youtube.dropdown-toggle {
  color: #fff;
  background-color: #880000;
  border-color: #7b0000;
}
.btn-youtube:not(:disabled):not(.disabled):active:focus, .btn-youtube:not(:disabled):not(.disabled).active:focus, .show > .btn-youtube.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(197, 38, 38, 0.5);
}
.btn-css3 {
  color: #fff;
  background-color: #0170ba;
  border-color: #0170ba;
}
.btn-css3:hover {
  color: #fff;
  background-color: #015994;
  border-color: #015187;
}
.btn-css3:focus, .btn-css3.focus {
  box-shadow: 0 0 0 0.2rem rgba(39, 133, 196, 0.5);
}
.btn-css3.disabled, .btn-css3:disabled {
  color: #fff;
  background-color: #0170ba;
  border-color: #0170ba;
}
.btn-css3:not(:disabled):not(.disabled):active, .btn-css3:not(:disabled):not(.disabled).active, .show > .btn-css3.dropdown-toggle {
  color: #fff;
  background-color: #015187;
  border-color: #014a7b;
}
.btn-css3:not(:disabled):not(.disabled):active:focus, .btn-css3:not(:disabled):not(.disabled).active:focus, .show > .btn-css3.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(39, 133, 196, 0.5);
}
.btn-dribbble {
  color: #fff;
  background-color: #ea4c89;
  border-color: #ea4c89;
}
.btn-dribbble:hover {
  color: #fff;
  background-color: #e62a72;
  border-color: #e51e6b;
}
.btn-dribbble:focus, .btn-dribbble.focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 103, 155, 0.5);
}
.btn-dribbble.disabled, .btn-dribbble:disabled {
  color: #fff;
  background-color: #ea4c89;
  border-color: #ea4c89;
}
.btn-dribbble:not(:disabled):not(.disabled):active, .btn-dribbble:not(:disabled):not(.disabled).active, .show > .btn-dribbble.dropdown-toggle {
  color: #fff;
  background-color: #e51e6b;
  border-color: #dc1a65;
}
.btn-dribbble:not(:disabled):not(.disabled):active:focus, .btn-dribbble:not(:disabled):not(.disabled).active:focus, .show > .btn-dribbble.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 103, 155, 0.5);
}
.btn-instagram {
  color: #fff;
  background-color: #517fa4;
  border-color: #517fa4;
}
.btn-instagram:hover {
  color: #fff;
  background-color: #446b8a;
  border-color: #406582;
}
.btn-instagram:focus, .btn-instagram.focus {
  box-shadow: 0 0 0 0.2rem rgba(107, 146, 178, 0.5);
}
.btn-instagram.disabled, .btn-instagram:disabled {
  color: #fff;
  background-color: #517fa4;
  border-color: #517fa4;
}
.btn-instagram:not(:disabled):not(.disabled):active, .btn-instagram:not(:disabled):not(.disabled).active, .show > .btn-instagram.dropdown-toggle {
  color: #fff;
  background-color: #406582;
  border-color: #3c5e79;
}
.btn-instagram:not(:disabled):not(.disabled):active:focus, .btn-instagram:not(:disabled):not(.disabled).active:focus, .show > .btn-instagram.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(107, 146, 178, 0.5);
}
.btn-pinterest {
  color: #fff;
  background-color: #cb2027;
  border-color: #cb2027;
}
.btn-pinterest:hover {
  color: #fff;
  background-color: #aa1b21;
  border-color: #9f191f;
}
.btn-pinterest:focus, .btn-pinterest.focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 65, 71, 0.5);
}
.btn-pinterest.disabled, .btn-pinterest:disabled {
  color: #fff;
  background-color: #cb2027;
  border-color: #cb2027;
}
.btn-pinterest:not(:disabled):not(.disabled):active, .btn-pinterest:not(:disabled):not(.disabled).active, .show > .btn-pinterest.dropdown-toggle {
  color: #fff;
  background-color: #9f191f;
  border-color: #94171c;
}
.btn-pinterest:not(:disabled):not(.disabled):active:focus, .btn-pinterest:not(:disabled):not(.disabled).active:focus, .show > .btn-pinterest.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 65, 71, 0.5);
}
.btn-vk {
  color: #fff;
  background-color: #45668e;
  border-color: #45668e;
}
.btn-vk:hover {
  color: #fff;
  background-color: #385474;
  border-color: #344d6c;
}
.btn-vk:focus, .btn-vk.focus {
  box-shadow: 0 0 0 0.2rem rgba(97, 125, 159, 0.5);
}
.btn-vk.disabled, .btn-vk:disabled {
  color: #fff;
  background-color: #45668e;
  border-color: #45668e;
}
.btn-vk:not(:disabled):not(.disabled):active, .btn-vk:not(:disabled):not(.disabled).active, .show > .btn-vk.dropdown-toggle {
  color: #fff;
  background-color: #344d6c;
  border-color: #304763;
}
.btn-vk:not(:disabled):not(.disabled):active:focus, .btn-vk:not(:disabled):not(.disabled).active:focus, .show > .btn-vk.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(97, 125, 159, 0.5);
}
.btn-yahoo {
  color: #fff;
  background-color: #400191;
  border-color: #400191;
}
.btn-yahoo:hover {
  color: #fff;
  background-color: #2f016b;
  border-color: #2a015e;
}
.btn-yahoo:focus, .btn-yahoo.focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 39, 162, 0.5);
}
.btn-yahoo.disabled, .btn-yahoo:disabled {
  color: #fff;
  background-color: #400191;
  border-color: #400191;
}
.btn-yahoo:not(:disabled):not(.disabled):active, .btn-yahoo:not(:disabled):not(.disabled).active, .show > .btn-yahoo.dropdown-toggle {
  color: #fff;
  background-color: #2a015e;
  border-color: #240152;
}
.btn-yahoo:not(:disabled):not(.disabled):active:focus, .btn-yahoo:not(:disabled):not(.disabled).active:focus, .show > .btn-yahoo.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 39, 162, 0.5);
}
.btn-behance {
  color: #fff;
  background-color: #1769ff;
  border-color: #1769ff;
}
.btn-behance:hover {
  color: #fff;
  background-color: #0055f0;
  border-color: #0050e3;
}
.btn-behance:focus, .btn-behance.focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 128, 255, 0.5);
}
.btn-behance.disabled, .btn-behance:disabled {
  color: #fff;
  background-color: #1769ff;
  border-color: #1769ff;
}
.btn-behance:not(:disabled):not(.disabled):active, .btn-behance:not(:disabled):not(.disabled).active, .show > .btn-behance.dropdown-toggle {
  color: #fff;
  background-color: #0050e3;
  border-color: #004cd6;
}
.btn-behance:not(:disabled):not(.disabled):active:focus, .btn-behance:not(:disabled):not(.disabled).active:focus, .show > .btn-behance.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 128, 255, 0.5);
}
.btn-dropbox {
  color: #fff;
  background-color: #007ee5;
  border-color: #007ee5;
}
.btn-dropbox:hover {
  color: #fff;
  background-color: #0069bf;
  border-color: #0062b2;
}
.btn-dropbox:focus, .btn-dropbox.focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 145, 233, 0.5);
}
.btn-dropbox.disabled, .btn-dropbox:disabled {
  color: #fff;
  background-color: #007ee5;
  border-color: #007ee5;
}
.btn-dropbox:not(:disabled):not(.disabled):active, .btn-dropbox:not(:disabled):not(.disabled).active, .show > .btn-dropbox.dropdown-toggle {
  color: #fff;
  background-color: #0062b2;
  border-color: #005ba5;
}
.btn-dropbox:not(:disabled):not(.disabled):active:focus, .btn-dropbox:not(:disabled):not(.disabled).active:focus, .show > .btn-dropbox.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 145, 233, 0.5);
}
.btn-reddit {
  color: #fff;
  background-color: #ff4500;
  border-color: #ff4500;
}
.btn-reddit:hover {
  color: #fff;
  background-color: #d93b00;
  border-color: #cc3700;
}
.btn-reddit:focus, .btn-reddit.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 97, 38, 0.5);
}
.btn-reddit.disabled, .btn-reddit:disabled {
  color: #fff;
  background-color: #ff4500;
  border-color: #ff4500;
}
.btn-reddit:not(:disabled):not(.disabled):active, .btn-reddit:not(:disabled):not(.disabled).active, .show > .btn-reddit.dropdown-toggle {
  color: #fff;
  background-color: #cc3700;
  border-color: #bf3400;
}
.btn-reddit:not(:disabled):not(.disabled):active:focus, .btn-reddit:not(:disabled):not(.disabled).active:focus, .show > .btn-reddit.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 97, 38, 0.5);
}
.btn-spotify {
  color: #fff;
  background-color: #7ab800;
  border-color: #7ab800;
}
.btn-spotify:hover {
  color: #fff;
  background-color: #619200;
  border-color: #588500;
}
.btn-spotify:focus, .btn-spotify.focus {
  box-shadow: 0 0 0 0.2rem rgba(142, 195, 38, 0.5);
}
.btn-spotify.disabled, .btn-spotify:disabled {
  color: #fff;
  background-color: #7ab800;
  border-color: #7ab800;
}
.btn-spotify:not(:disabled):not(.disabled):active, .btn-spotify:not(:disabled):not(.disabled).active, .show > .btn-spotify.dropdown-toggle {
  color: #fff;
  background-color: #588500;
  border-color: #507800;
}
.btn-spotify:not(:disabled):not(.disabled):active:focus, .btn-spotify:not(:disabled):not(.disabled).active:focus, .show > .btn-spotify.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(142, 195, 38, 0.5);
}
.btn-vine {
  color: #fff;
  background-color: #00bf8f;
  border-color: #00bf8f;
}
.btn-vine:hover {
  color: #fff;
  background-color: #009972;
  border-color: #008c69;
}
.btn-vine:focus, .btn-vine.focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 201, 160, 0.5);
}
.btn-vine.disabled, .btn-vine:disabled {
  color: #fff;
  background-color: #00bf8f;
  border-color: #00bf8f;
}
.btn-vine:not(:disabled):not(.disabled):active, .btn-vine:not(:disabled):not(.disabled).active, .show > .btn-vine.dropdown-toggle {
  color: #fff;
  background-color: #008c69;
  border-color: #007f5f;
}
.btn-vine:not(:disabled):not(.disabled):active:focus, .btn-vine:not(:disabled):not(.disabled).active:focus, .show > .btn-vine.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 201, 160, 0.5);
}
.btn-foursquare {
  color: #fff;
  background-color: #1073af;
  border-color: #1073af;
}
.btn-foursquare:hover {
  color: #fff;
  background-color: #0d5c8c;
  border-color: #0c5480;
}
.btn-foursquare:focus, .btn-foursquare.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 136, 187, 0.5);
}
.btn-foursquare.disabled, .btn-foursquare:disabled {
  color: #fff;
  background-color: #1073af;
  border-color: #1073af;
}
.btn-foursquare:not(:disabled):not(.disabled):active, .btn-foursquare:not(:disabled):not(.disabled).active, .show > .btn-foursquare.dropdown-toggle {
  color: #fff;
  background-color: #0c5480;
  border-color: #0b4d75;
}
.btn-foursquare:not(:disabled):not(.disabled):active:focus, .btn-foursquare:not(:disabled):not(.disabled).active:focus, .show > .btn-foursquare.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 136, 187, 0.5);
}
.btn-vimeo {
  color: #23282c;
  background-color: #aad450;
  border-color: #aad450;
}
.btn-vimeo:hover {
  color: #23282c;
  background-color: #9bcc32;
  border-color: #93c130;
}
.btn-vimeo:focus, .btn-vimeo.focus {
  box-shadow: 0 0 0 0.2rem rgba(150, 186, 75, 0.5);
}
.btn-vimeo.disabled, .btn-vimeo:disabled {
  color: #23282c;
  background-color: #aad450;
  border-color: #aad450;
}
.btn-vimeo:not(:disabled):not(.disabled):active, .btn-vimeo:not(:disabled):not(.disabled).active, .show > .btn-vimeo.dropdown-toggle {
  color: #23282c;
  background-color: #93c130;
  border-color: #8bb72d;
}
.btn-vimeo:not(:disabled):not(.disabled):active:focus, .btn-vimeo:not(:disabled):not(.disabled).active:focus, .show > .btn-vimeo.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(150, 186, 75, 0.5);
}
*[dir=rtl] .btn-group > .btn:not(:first-child),
*[dir=rtl] .btn-group > .btn-group:not(:first-child) {
  margin-right: -1px;
}
*[dir=rtl] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
*[dir=rtl] .btn-group > .btn-group:not(:last-child) > .btn {
  border-radius: 0.25rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
*[dir=rtl] .btn-group > .btn:not(:first-child),
*[dir=rtl] .btn-group > .btn-group:not(:first-child) > .btn {
  border-radius: 0.25rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
*[dir=rtl] .btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-transparent {
  color: #fff;
  background-color: transparent;
  border-color: transparent;
}
.btn [class^=icon-],
.btn [class*=" icon-"] {
  display: inline-block;
  margin-top: -2px;
  vertical-align: middle;
}
.btn-pill {
  border-radius: 50em;
}
.btn-square {
  border-radius: 0;
}
.btn-ghost-primary {
  color: #20a8d8;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-primary:hover {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-ghost-primary:focus, .btn-ghost-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.5);
}
.btn-ghost-primary.disabled, .btn-ghost-primary:disabled {
  color: #20a8d8;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-primary:not(:disabled):not(.disabled):active, .btn-ghost-primary:not(:disabled):not(.disabled).active, .show > .btn-ghost-primary.dropdown-toggle {
  color: #fff;
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.btn-ghost-primary:not(:disabled):not(.disabled):active:focus, .btn-ghost-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 168, 216, 0.5);
}
.btn-ghost-secondary {
  color: #c8ced3;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-secondary:hover {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-ghost-secondary:focus, .btn-ghost-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(200, 206, 211, 0.5);
}
.btn-ghost-secondary.disabled, .btn-ghost-secondary:disabled {
  color: #c8ced3;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-secondary:not(:disabled):not(.disabled):active, .btn-ghost-secondary:not(:disabled):not(.disabled).active, .show > .btn-ghost-secondary.dropdown-toggle {
  color: #23282c;
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.btn-ghost-secondary:not(:disabled):not(.disabled):active:focus, .btn-ghost-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(200, 206, 211, 0.5);
}
.btn-ghost-success {
  color: #4dbd74;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-success:hover {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-ghost-success:focus, .btn-ghost-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.5);
}
.btn-ghost-success.disabled, .btn-ghost-success:disabled {
  color: #4dbd74;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-success:not(:disabled):not(.disabled):active, .btn-ghost-success:not(:disabled):not(.disabled).active, .show > .btn-ghost-success.dropdown-toggle {
  color: #fff;
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.btn-ghost-success:not(:disabled):not(.disabled):active:focus, .btn-ghost-success:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 189, 116, 0.5);
}
.btn-ghost-info {
  color: #63c2de;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-info:hover {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-ghost-info:focus, .btn-ghost-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 194, 222, 0.5);
}
.btn-ghost-info.disabled, .btn-ghost-info:disabled {
  color: #63c2de;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-info:not(:disabled):not(.disabled):active, .btn-ghost-info:not(:disabled):not(.disabled).active, .show > .btn-ghost-info.dropdown-toggle {
  color: #23282c;
  background-color: #63c2de;
  border-color: #63c2de;
}
.btn-ghost-info:not(:disabled):not(.disabled):active:focus, .btn-ghost-info:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 194, 222, 0.5);
}
.btn-ghost-warning {
  color: #ffc107;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-warning:hover {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-ghost-warning:focus, .btn-ghost-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.btn-ghost-warning.disabled, .btn-ghost-warning:disabled {
  color: #ffc107;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-warning:not(:disabled):not(.disabled):active, .btn-ghost-warning:not(:disabled):not(.disabled).active, .show > .btn-ghost-warning.dropdown-toggle {
  color: #23282c;
  background-color: #ffc107;
  border-color: #ffc107;
}
.btn-ghost-warning:not(:disabled):not(.disabled):active:focus, .btn-ghost-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}
.btn-ghost-danger {
  color: #f86c6b;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-danger:hover {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-ghost-danger:focus, .btn-ghost-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.5);
}
.btn-ghost-danger.disabled, .btn-ghost-danger:disabled {
  color: #f86c6b;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-danger:not(:disabled):not(.disabled):active, .btn-ghost-danger:not(:disabled):not(.disabled).active, .show > .btn-ghost-danger.dropdown-toggle {
  color: #fff;
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.btn-ghost-danger:not(:disabled):not(.disabled):active:focus, .btn-ghost-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 108, 107, 0.5);
}
.btn-ghost-light {
  color: #f0f3f5;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-light:hover {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-ghost-light:focus, .btn-ghost-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 243, 245, 0.5);
}
.btn-ghost-light.disabled, .btn-ghost-light:disabled {
  color: #f0f3f5;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-light:not(:disabled):not(.disabled):active, .btn-ghost-light:not(:disabled):not(.disabled).active, .show > .btn-ghost-light.dropdown-toggle {
  color: #23282c;
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.btn-ghost-light:not(:disabled):not(.disabled):active:focus, .btn-ghost-light:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 243, 245, 0.5);
}
.btn-ghost-dark {
  color: #2f353a;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}
.btn-ghost-dark:hover {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-ghost-dark:focus, .btn-ghost-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(47, 53, 58, 0.5);
}
.btn-ghost-dark.disabled, .btn-ghost-dark:disabled {
  color: #2f353a;
  background-color: transparent;
  border-color: transparent;
}
.btn-ghost-dark:not(:disabled):not(.disabled):active, .btn-ghost-dark:not(:disabled):not(.disabled).active, .show > .btn-ghost-dark.dropdown-toggle {
  color: #fff;
  background-color: #2f353a;
  border-color: #2f353a;
}
.btn-ghost-dark:not(:disabled):not(.disabled):active:focus, .btn-ghost-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-ghost-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(47, 53, 58, 0.5);
}
.callout {
  position: relative;
  padding: 0 1rem;
  margin: 1rem 0;
  border-left: 4px solid #c8ced3;
  border-radius: 0.25rem;
}
.callout .chart-wrapper {
  position: absolute;
  top: 10px;
  left: 50%;
  float: right;
  width: 50%;
}
.callout-bordered {
  border: 1px solid #c8ced3;
  border-left-width: 4px;
}
.callout code {
  border-radius: 0.25rem;
}
.callout h4 {
  margin-top: 0;
  margin-bottom: 0.25rem;
}
.callout p:last-child {
  margin-bottom: 0;
}
.callout + .callout {
  margin-top: -0.25rem;
}
.callout-primary {
  border-left-color: #20a8d8;
}
.callout-primary h4 {
  color: #20a8d8;
}
.callout-secondary {
  border-left-color: #c8ced3;
}
.callout-secondary h4 {
  color: #c8ced3;
}
.callout-success {
  border-left-color: #4dbd74;
}
.callout-success h4 {
  color: #4dbd74;
}
.callout-info {
  border-left-color: #63c2de;
}
.callout-info h4 {
  color: #63c2de;
}
.callout-warning {
  border-left-color: #ffc107;
}
.callout-warning h4 {
  color: #ffc107;
}
.callout-danger {
  border-left-color: #f86c6b;
}
.callout-danger h4 {
  color: #f86c6b;
}
.callout-light {
  border-left-color: #f0f3f5;
}
.callout-light h4 {
  color: #f0f3f5;
}
.callout-dark {
  border-left-color: #2f353a;
}
.callout-dark h4 {
  color: #2f353a;
}
*[dir=rtl] .callout {
  border-right: 4px solid #c8ced3;
  border-left: 0;
}
*[dir=rtl] .callout.callout-primary {
  border-right-color: #20a8d8;
}
*[dir=rtl] .callout.callout-secondary {
  border-right-color: #c8ced3;
}
*[dir=rtl] .callout.callout-success {
  border-right-color: #4dbd74;
}
*[dir=rtl] .callout.callout-info {
  border-right-color: #63c2de;
}
*[dir=rtl] .callout.callout-warning {
  border-right-color: #ffc107;
}
*[dir=rtl] .callout.callout-danger {
  border-right-color: #f86c6b;
}
*[dir=rtl] .callout.callout-light {
  border-right-color: #f0f3f5;
}
*[dir=rtl] .callout.callout-dark {
  border-right-color: #2f353a;
}
*[dir=rtl] .callout .chart-wrapper {
  left: 0;
  float: left;
}
.card {
  margin-bottom: 1.5rem;
}
.card.bg-primary {
  border-color: #187da0;
}
.card.bg-primary .card-header {
  background-color: #1e9ecb;
  border-color: #187da0;
}
.card.bg-secondary {
  border-color: #a5aeb7;
}
.card.bg-secondary .card-header {
  background-color: #c0c6cc;
  border-color: #a5aeb7;
}
.card.bg-success {
  border-color: #379457;
}
.card.bg-success .card-header {
  background-color: #44b76c;
  border-color: #379457;
}
.card.bg-info {
  border-color: #2eadd3;
}
.card.bg-info .card-header {
  background-color: #56bddb;
  border-color: #2eadd3;
}
.card.bg-warning {
  border-color: #c69500;
}
.card.bg-warning .card-header {
  background-color: #f7b900;
  border-color: #c69500;
}
.card.bg-danger {
  border-color: #f5302e;
}
.card.bg-danger .card-header {
  background-color: #f75d5c;
  border-color: #f5302e;
}
.card.bg-light {
  border-color: #cad4dc;
}
.card.bg-light .card-header {
  background-color: #e7ecef;
  border-color: #cad4dc;
}
.card.bg-dark {
  border-color: #121517;
}
.card.bg-dark .card-header {
  background-color: #282d32;
  border-color: #121517;
}
.card.drag,
.card .drag {
  cursor: move;
}
.card-placeholder {
  background: rgba(0, 0, 0, 0.025);
  border: 1px dashed #c8ced3;
}
.card-header > i {
  margin-right: 0.5rem;
}
.card-header .nav-tabs {
  margin-top: -0.75rem;
  margin-bottom: -0.75rem;
  border-bottom: 0;
}
.card-header .nav-tabs .nav-item {
  border-top: 0;
}
.card-header .nav-tabs .nav-link {
  padding: 0.75rem 0.625rem;
  color: #73818f;
  border-top: 0;
}
.card-header .nav-tabs .nav-link.active {
  color: #23282c;
  background: #fff;
}
*[dir=rtl] .card-header > i {
  margin-right: 0;
  margin-left: 0.5rem;
}
.card-header-icon-bg {
  display: inline-block;
  width: 2.8125rem;
  padding: 0.75rem 0;
  margin: -0.75rem 1.25rem -0.75rem -1.25rem;
  line-height: inherit;
  color: #23282c;
  text-align: center;
  background: transparent;
  border-right: 1px solid #c8ced3;
}
.card-header-actions {
  display: inline-block;
  float: right;
  margin-right: -0.25rem;
}
*[dir=rtl] .card-header-actions {
  float: left;
  margin-right: auto;
  margin-left: -0.25rem;
}
.card-header-action {
  padding: 0 0.25rem;
  color: #73818f;
}
.card-header-action:hover {
  color: #23282c;
  text-decoration: none;
}
.card-accent-primary {
  border-top-color: #20a8d8;
  border-top-width: 2px;
}
.card-accent-secondary {
  border-top-color: #c8ced3;
  border-top-width: 2px;
}
.card-accent-success {
  border-top-color: #4dbd74;
  border-top-width: 2px;
}
.card-accent-info {
  border-top-color: #63c2de;
  border-top-width: 2px;
}
.card-accent-warning {
  border-top-color: #ffc107;
  border-top-width: 2px;
}
.card-accent-danger {
  border-top-color: #f86c6b;
  border-top-width: 2px;
}
.card-accent-light {
  border-top-color: #f0f3f5;
  border-top-width: 2px;
}
.card-accent-dark {
  border-top-color: #2f353a;
  border-top-width: 2px;
}
.card-full {
  margin-top: -1rem;
  margin-right: -15px;
  margin-left: -15px;
  border: 0;
  border-bottom: 1px solid #c8ced3;
}
@media (min-width: 576px) {
  .card-columns.cols-2 {
    -moz-column-count: 2;
         column-count: 2;
  }
}
.chart-wrapper canvas {
  width: 100%;
}
base-chart.chart {
  display: block;
}
canvas {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.chartjs-tooltip {
  position: absolute;
  z-index: 1021;
  display: flex;
  flex-direction: column;
  padding: 0.25rem 0.5rem;
  color: #fff;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: all 0.25s ease;
  transform: translate(-50%, 0);
  border-radius: 0.25rem;
}
.chartjs-tooltip .tooltip-header {
  margin-bottom: 0.5rem;
}
.chartjs-tooltip .tooltip-header-item {
  font-size: 0.765625rem;
  font-weight: 700;
}
.chartjs-tooltip .tooltip-body-item {
  display: flex;
  align-items: center;
  font-size: 0.765625rem;
  white-space: nowrap;
}
.chartjs-tooltip .tooltip-body-item-color {
  display: inline-block;
  width: 0.875rem;
  height: 0.875rem;
  margin-right: 0.875rem;
}
.chartjs-tooltip .tooltip-body-item-value {
  padding-left: 1rem;
  margin-left: auto;
  font-weight: 700;
}
.dropdown-item {
  position: relative;
  padding: 10px 20px;
  border-bottom: 1px solid #c8ced3;
}
.dropdown-item:last-child {
  border-bottom: 0;
}
.dropdown-item i {
  display: inline-block;
  width: 20px;
  margin-right: 10px;
  margin-left: -10px;
  color: #c8ced3;
  text-align: center;
}
.dropdown-item .badge {
  position: absolute;
  right: 10px;
  margin-top: 2px;
}
.dropdown-header {
  padding: 8px 20px;
  background: #e4e7ea;
  border-bottom: 1px solid #c8ced3;
}
.dropdown-header .btn {
  margin-top: -7px;
  color: #73818f;
}
.dropdown-header .btn:hover {
  color: #23282c;
}
.dropdown-header .btn.pull-right {
  margin-right: -20px;
}
.dropdown-menu-lg {
  width: 250px;
}
.app-header .navbar-nav .dropdown-menu {
  position: absolute;
}
.app-header .navbar-nav .dropdown-menu-right {
  right: 0;
  left: auto;
}
.app-header .navbar-nav .dropdown-menu-left {
  right: auto;
  left: 0;
}
*[dir=rtl] .dropdown-toggle::before {
  margin-right: 0;
  margin-left: 0.255em;
}
*[dir=rtl] .dropdown-toggle::after {
  margin-right: 0.255em;
  margin-left: 0;
}
.app-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 1rem;
  color: #23282c;
  background: #f0f3f5;
  border-top: 1px solid #c8ced3;
}
.row.row-equal {
  padding-right: 7.5px;
  padding-left: 7.5px;
  margin-right: -15px;
  margin-left: -15px;
}
.row.row-equal [class*=col-] {
  padding-right: 7.5px;
  padding-left: 7.5px;
}
.main .container-fluid {
  padding: 0 30px;
}
.app-header {
  position: relative;
  flex-direction: row;
  height: 55px;
  padding: 0;
  margin: 0;
  background-color: #fff;
  border-bottom: 1px solid #c8ced3;
}
.app-header .navbar-brand {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 155px;
  height: 55px;
  padding: 0;
  margin-right: 0;
  background-color: transparent;
}
.app-header .navbar-brand .navbar-brand-minimized {
  display: none;
}
.app-header .navbar-toggler {
  min-width: 50px;
  padding: 0.25rem 0;
}
.app-header .navbar-toggler:hover .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%232f353a' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}
.app-header .navbar-toggler-icon {
  height: 23px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%2373818f' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}
.app-header .navbar-nav {
  flex-direction: row;
  align-items: center;
}
.app-header .nav-item {
  position: relative;
  min-width: 50px;
  margin: 0;
  text-align: center;
}
.app-header .nav-item button {
  margin: 0 auto;
}
.app-header .nav-item .nav-link {
  padding-top: 0;
  padding-bottom: 0;
  background: 0;
  border: 0;
}
.app-header .nav-item .nav-link .badge {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -16px;
  margin-left: 0;
}
.app-header .nav-item .nav-link > .img-avatar, .app-header .nav-item .avatar.nav-link > img {
  height: 35px;
  margin: 0 10px;
}
.app-header .dropdown-menu {
  padding-bottom: 0;
  line-height: 1.5;
}
.app-header .dropdown-item {
  min-width: 180px;
}
.navbar-nav .nav-link {
  color: #73818f;
}
.navbar-nav .nav-link:hover, .navbar-nav .nav-link:focus {
  color: #2f353a;
}
.navbar-nav .open > .nav-link, .navbar-nav .open > .nav-link:hover, .navbar-nav .open > .nav-link:focus,
.navbar-nav .active > .nav-link,
.navbar-nav .active > .nav-link:hover,
.navbar-nav .active > .nav-link:focus,
.navbar-nav .nav-link.open,
.navbar-nav .nav-link.open:hover,
.navbar-nav .nav-link.open:focus,
.navbar-nav .nav-link.active,
.navbar-nav .nav-link.active:hover,
.navbar-nav .nav-link.active:focus {
  color: #2f353a;
}
.navbar-divider {
  background-color: rgba(0, 0, 0, 0.075);
}
@media (min-width: 992px) {
  .brand-minimized .app-header .navbar-brand {
    width: 50px;
    background-color: transparent;
  }
  .brand-minimized .app-header .navbar-brand .navbar-brand-full {
    display: none;
  }
  .brand-minimized .app-header .navbar-brand .navbar-brand-minimized {
    display: block;
  }
}
.input-group-prepend,
.input-group-append {
  white-space: nowrap;
  vertical-align: middle;
}
*[dir=rtl] .input-group > .form-control,
*[dir=rtl] .input-group > .custom-select {
  border-radius: 0.25rem;
}
*[dir=rtl] .input-group > .form-control:not(:last-child),
*[dir=rtl] .input-group > .custom-select:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
*[dir=rtl] .input-group > .form-control:not(:first-child),
*[dir=rtl] .input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
*[dir=rtl] .input-group-prepend {
  margin-left: -1px;
}
*[dir=rtl] .input-group-append {
  margin-right: -1px;
}
*[dir=rtl] .input-group > .input-group-prepend > .btn,
*[dir=rtl] .input-group > .input-group-prepend > .input-group-text,
*[dir=rtl] .input-group > .input-group-append:not(:last-child) > .btn,
*[dir=rtl] .input-group > .input-group-append:not(:last-child) > .input-group-text,
*[dir=rtl] .input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
*[dir=rtl] .input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-radius: 0.25rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
*[dir=rtl] .input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
*[dir=rtl] .input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
*[dir=rtl] .input-group > .input-group-append > .btn,
*[dir=rtl] .input-group > .input-group-append > .input-group-text,
*[dir=rtl] .input-group > .input-group-prepend:not(:first-child) > .btn,
*[dir=rtl] .input-group > .input-group-prepend:not(:first-child) > .input-group-text,
*[dir=rtl] .input-group > .input-group-prepend:first-child > .btn:not(:first-child),
*[dir=rtl] .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-radius: 0.25rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
*[dir=rtl] .input-group > .input-group-prepend:first-child > .btn:not(:first-child),
*[dir=rtl] .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.img-avatar, .avatar > img,
.img-circle {
  max-width: 100%;
  height: auto;
  border-radius: 50em;
}
.list-group-accent .list-group-item {
  margin-bottom: 1px;
  border-top: 0;
  border-right: 0;
  border-bottom: 0;
  border-radius: 0;
}
.list-group-accent .list-group-item.list-group-item-divider {
  position: relative;
}
.list-group-accent .list-group-item.list-group-item-divider::before {
  position: absolute;
  bottom: -1px;
  left: 5%;
  width: 90%;
  height: 1px;
  content: "";
  background-color: #e4e7ea;
}
.list-group-item-accent-primary {
  border-left: 4px solid #20a8d8;
}
.list-group-item-accent-secondary {
  border-left: 4px solid #c8ced3;
}
.list-group-item-accent-success {
  border-left: 4px solid #4dbd74;
}
.list-group-item-accent-info {
  border-left: 4px solid #63c2de;
}
.list-group-item-accent-warning {
  border-left: 4px solid #ffc107;
}
.list-group-item-accent-danger {
  border-left: 4px solid #f86c6b;
}
.list-group-item-accent-light {
  border-left: 4px solid #f0f3f5;
}
.list-group-item-accent-dark {
  border-left: 4px solid #2f353a;
}
.modal-primary .modal-content {
  border-color: #20a8d8;
}
.modal-primary .modal-header {
  color: #fff;
  background-color: #20a8d8;
}
.modal-secondary .modal-content {
  border-color: #c8ced3;
}
.modal-secondary .modal-header {
  color: #fff;
  background-color: #c8ced3;
}
.modal-success .modal-content {
  border-color: #4dbd74;
}
.modal-success .modal-header {
  color: #fff;
  background-color: #4dbd74;
}
.modal-info .modal-content {
  border-color: #63c2de;
}
.modal-info .modal-header {
  color: #fff;
  background-color: #63c2de;
}
.modal-warning .modal-content {
  border-color: #ffc107;
}
.modal-warning .modal-header {
  color: #fff;
  background-color: #ffc107;
}
.modal-danger .modal-content {
  border-color: #f86c6b;
}
.modal-danger .modal-header {
  color: #fff;
  background-color: #f86c6b;
}
.modal-light .modal-content {
  border-color: #f0f3f5;
}
.modal-light .modal-header {
  color: #fff;
  background-color: #f0f3f5;
}
.modal-dark .modal-content {
  border-color: #2f353a;
}
.modal-dark .modal-header {
  color: #fff;
  background-color: #2f353a;
}
.nav-tabs .nav-link {
  color: #73818f;
}
.nav-tabs .nav-link:hover {
  cursor: pointer;
}
.nav-tabs .nav-link.active {
  color: #2f353a;
  background: #fff;
  border-color: #c8ced3;
  border-bottom-color: #fff;
}
.nav-tabs .nav-link.active:focus {
  background: #fff;
  border-color: #c8ced3;
  border-bottom-color: #fff;
}
.tab-content {
  margin-top: -1px;
  background: #fff;
  border: 1px solid #c8ced3;
}
.tab-content .tab-pane {
  padding: 1rem;
}
.card-block .tab-content {
  margin-top: 0;
  border: 0;
}
.nav-fill .nav-link {
  background-color: #fff;
  border-color: #c8ced3;
}
.nav-fill .nav-link + .nav-link {
  margin-left: -1px;
}
.nav-fill .nav-link.active {
  margin-top: -1px;
  border-top: 2px solid #20a8d8;
}
*[dir=rtl] .nav {
  padding-right: 0;
}
.progress-xs {
  height: 4px;
}
.progress-sm {
  height: 8px;
}
.progress-white {
  background-color: rgba(255, 255, 255, 0.2);
}
.progress-white .progress-bar {
  background-color: #fff;
}
.progress-group {
  display: flex;
  flex-flow: row wrap;
  margin-bottom: 1rem;
}
.progress-group-prepend {
  flex: 0 0 100px;
  align-self: center;
}
.progress-group-icon {
  margin: 0 1rem 0 0.25rem;
  font-size: 1.09375rem;
}
.progress-group-text {
  font-size: 0.765625rem;
  color: #73818f;
}
.progress-group-header {
  display: flex;
  flex-basis: 100%;
  align-items: flex-end;
  margin-bottom: 0.25rem;
}
.progress-group-bars {
  flex-grow: 1;
  align-self: center;
}
.progress-group-bars .progress:not(:last-child) {
  margin-bottom: 2px;
}
.progress-group-header + .progress-group-bars {
  flex-basis: 100%;
}
.sidebar {
  display: flex;
  flex-direction: column;
  padding: 0;
  color: #fff;
  background: #2f353a;
}
.sidebar .sidebar-close {
  position: absolute;
  right: 0;
  display: none;
  padding: 0 1rem;
  font-size: 24px;
  font-weight: 800;
  line-height: 55px;
  color: #fff;
  background: 0;
  border: 0;
  opacity: 0.8;
}
.sidebar .sidebar-close:hover {
  opacity: 1;
}
.sidebar .sidebar-header {
  flex: 0 0 auto;
  padding: 0.75rem 1rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
}
.sidebar .sidebar-form .form-control {
  color: #fff;
  background: #181b1e;
  border: 0;
}
.sidebar .sidebar-form .form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .sidebar-form .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .sidebar-form .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .sidebar-scroll {
  position: relative;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  width: 200px;
}
.sidebar .sidebar-nav {
  position: relative;
  flex: 1;
  width: 200px;
}
.sidebar > .sidebar-nav {
  overflow-x: hidden;
  overflow-y: auto;
}
.sidebar .nav {
  width: 200px;
  flex-direction: column;
  min-height: 100%;
  padding: 0;
}
.sidebar .nav-title {
  padding: 0.75rem 1rem;
  font-size: 80%;
  font-weight: 700;
  color: #e4e7ea;
  text-transform: uppercase;
}
.sidebar .nav-divider {
  height: 10px;
}
.sidebar .nav-item {
  position: relative;
  margin: 0;
  transition: background 0.3s ease-in-out;
}
.sidebar .nav-dropdown-items {
  max-height: 0;
  padding: 0;
  margin: 0;
  overflow-y: hidden;
  transition: max-height 0.3s ease-in-out;
}
.sidebar .nav-dropdown-items .nav-item {
  padding: 0;
  list-style: none;
}
.sidebar .nav-link {
  display: block;
  padding: 0.75rem 1rem;
  color: #fff;
  text-decoration: none;
  background: transparent;
}
.sidebar .nav-link .nav-icon {
  display: inline-block;
  width: 1.09375rem;
  margin: 0 0.5rem 0 0;
  font-size: 0.875rem;
  color: #73818f;
  text-align: center;
}
.sidebar .nav-link .badge {
  float: right;
  margin-top: 2px;
}
.sidebar .nav-link.active {
  color: #fff;
  background: #3a4248;
}
.sidebar .nav-link.active .nav-icon {
  color: #20a8d8;
}
.sidebar .nav-link:hover {
  color: #fff;
  background: #20a8d8;
}
.sidebar .nav-link:hover .nav-icon {
  color: #fff;
}
.sidebar .nav-link:hover.nav-dropdown-toggle::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}
.sidebar .nav-link.disabled {
  color: #b3b3b3;
  cursor: default;
  background: transparent;
}
.sidebar .nav-link.disabled .nav-icon {
  color: #73818f;
}
.sidebar .nav-link.disabled:hover {
  color: #b3b3b3;
}
.sidebar .nav-link.disabled:hover .nav-icon {
  color: #73818f;
}
.sidebar .nav-link.disabled:hover.nav-dropdown-toggle::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}
.sidebar .nav-link.nav-link-primary {
  background: #20a8d8;
}
.sidebar .nav-link.nav-link-primary .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-primary:hover {
  background: #1d97c2;
}
.sidebar .nav-link.nav-link-primary:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-secondary {
  background: #c8ced3;
}
.sidebar .nav-link.nav-link-secondary .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-secondary:hover {
  background: #bac1c8;
}
.sidebar .nav-link.nav-link-secondary:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-success {
  background: #4dbd74;
}
.sidebar .nav-link.nav-link-success .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-success:hover {
  background: #41af67;
}
.sidebar .nav-link.nav-link-success:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-info {
  background: #63c2de;
}
.sidebar .nav-link.nav-link-info .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-info:hover {
  background: #4ebada;
}
.sidebar .nav-link.nav-link-info:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-warning {
  background: #ffc107;
}
.sidebar .nav-link.nav-link-warning .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-warning:hover {
  background: #edb100;
}
.sidebar .nav-link.nav-link-warning:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-danger {
  background: #f86c6b;
}
.sidebar .nav-link.nav-link-danger .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-danger:hover {
  background: #f75453;
}
.sidebar .nav-link.nav-link-danger:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-light {
  background: #f0f3f5;
}
.sidebar .nav-link.nav-link-light .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-light:hover {
  background: #e1e7eb;
}
.sidebar .nav-link.nav-link-light:hover i {
  color: #fff;
}
.sidebar .nav-link.nav-link-dark {
  background: #2f353a;
}
.sidebar .nav-link.nav-link-dark .nav-icon {
  color: rgba(255, 255, 255, 0.7);
}
.sidebar .nav-link.nav-link-dark:hover {
  background: #24282c;
}
.sidebar .nav-link.nav-link-dark:hover i {
  color: #fff;
}
.sidebar .nav-dropdown-toggle {
  position: relative;
}
.sidebar .nav-dropdown-toggle::before {
  position: absolute;
  top: 50%;
  right: 1rem;
  display: block;
  width: 8px;
  height: 8px;
  padding: 0;
  margin-top: -4px;
  content: "";
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%2373818f' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s;
}
.sidebar .nav-dropdown-toggle .badge {
  margin-right: 16px;
}
.sidebar .nav-dropdown.open {
  background: rgba(0, 0, 0, 0.2);
}
.sidebar .nav-dropdown.open > .nav-dropdown-items {
  max-height: 1500px;
}
.sidebar .nav-dropdown.open .nav-link {
  color: #fff;
  border-left: 0;
}
.sidebar .nav-dropdown.open .nav-link.disabled {
  color: #b3b3b3;
  background: transparent;
}
.sidebar .nav-dropdown.open .nav-link.disabled:hover {
  color: #b3b3b3;
}
.sidebar .nav-dropdown.open .nav-link.disabled:hover .nav-icon {
  color: #73818f;
}
.sidebar .nav-dropdown.open > .nav-dropdown-toggle::before {
  transform: rotate(-90deg);
}
.sidebar .nav-dropdown.open .nav-dropdown.open {
  border-left: 0;
}
.sidebar .nav-label {
  display: block;
  padding: 0.09375rem 1rem;
  color: #e4e7ea;
}
.sidebar .nav-label:hover {
  color: #fff;
  text-decoration: none;
}
.sidebar .nav-label .nav-icon {
  width: 20px;
  margin: -3px 0.5rem 0 0;
  font-size: 10px;
  color: #73818f;
  text-align: center;
  vertical-align: middle;
}
.sidebar .progress {
  background-color: #515c64 !important;
}
.sidebar .sidebar-footer {
  flex: 0 0 auto;
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.2);
}
.sidebar .sidebar-minimizer {
  position: relative;
  flex: 0 0 50px;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.2);
  border: 0;
}
.sidebar .sidebar-minimizer::before {
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 50px;
  content: "";
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%2373818f' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12.5px;
  transition: 0.3s;
}
.sidebar .sidebar-minimizer:focus, .sidebar .sidebar-minimizer.focus {
  outline: 0;
}
.sidebar .sidebar-minimizer:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
.sidebar .sidebar-minimizer:hover::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}
@media (min-width: 992px) {
  .sidebar-compact .sidebar .sidebar-nav {
    width: 150px;
  }
  .sidebar-compact .sidebar .nav {
    width: 150px;
  }
  .sidebar-compact .sidebar .d-compact-none {
    display: none;
  }
  .sidebar-compact .sidebar .nav-title {
    text-align: center;
  }
  .sidebar-compact .sidebar .nav-item {
    width: 150px;
    border-left: 0;
  }
  .sidebar-compact .sidebar .nav-link {
    text-align: center;
  }
  .sidebar-compact .sidebar .nav-link .nav-icon {
    display: block;
    width: 100%;
    margin: 0.25rem 0;
    font-size: 24px;
  }
  .sidebar-compact .sidebar .nav-link .badge {
    position: absolute;
    top: 18px;
    right: 10px;
  }
  .sidebar-compact .sidebar .nav-link.nav-dropdown-toggle::before {
    top: 30px;
  }

  .sidebar-minimized .sidebar {
    z-index: 1019;
  }
  .sidebar-minimized .sidebar .sidebar-scroll {
    overflow: visible;
    width: 50px;
  }
  .sidebar-minimized .sidebar .sidebar-nav {
    overflow: visible;
    width: 50px;
  }
  .sidebar-minimized .sidebar .nav {
    width: 50px;
  }
  .sidebar-minimized .sidebar .d-minimized-none,
.sidebar-minimized .sidebar .nav-divider,
.sidebar-minimized .sidebar .nav-label,
.sidebar-minimized .sidebar .nav-title,
.sidebar-minimized .sidebar .sidebar-footer,
.sidebar-minimized .sidebar .sidebar-form,
.sidebar-minimized .sidebar .sidebar-header {
    display: none;
  }
  .sidebar-minimized .sidebar .sidebar-minimizer {
    position: fixed;
    bottom: 0;
    width: 50px;
    height: 50px;
    background-color: #24282c;
  }
  .sidebar-minimized .sidebar .sidebar-nav {
    padding-bottom: 50px;
  }
  .sidebar-minimized .sidebar .sidebar-minimizer::before {
    width: 100%;
    transform: rotate(-180deg);
  }
  .sidebar-minimized .sidebar .nav-item {
    width: 50px;
    overflow: hidden;
  }
  .sidebar-minimized .sidebar .nav-item:hover {
    width: 250px;
    overflow: visible;
  }
  .sidebar-minimized .sidebar .nav-item:hover > .nav-link {
    background: #20a8d8;
  }
  .sidebar-minimized .sidebar .nav-item:hover > .nav-link .nav-icon {
    color: #fff;
  }
  .sidebar-minimized .sidebar .nav-item:hover .nav-link.disabled,
.sidebar-minimized .sidebar .nav-item:hover .nav-link :disabled {
    background: #2f353a;
  }
  .sidebar-minimized .sidebar .nav-item:hover .nav-link.disabled .nav-icon,
.sidebar-minimized .sidebar .nav-item:hover .nav-link :disabled .nav-icon {
    color: #73818f;
  }
  .sidebar-minimized .sidebar section :not(.nav-dropdown-items) > .nav-item:last-child::after {
    display: block;
    margin-bottom: 50px;
    content: "";
  }
  .sidebar-minimized .sidebar .nav-link {
    position: relative;
    padding-left: 0;
    margin: 0;
    white-space: nowrap;
    border-left: 0;
  }
  .sidebar-minimized .sidebar .nav-link .nav-icon {
    display: block;
    float: left;
    width: 50px;
    font-size: 18px;
  }
  .sidebar-minimized .sidebar .nav-link .badge {
    position: absolute;
    right: 15px;
    display: none;
  }
  .sidebar-minimized .sidebar .nav-link:hover {
    width: 250px;
    background: #20a8d8;
  }
  .sidebar-minimized .sidebar .nav-link:hover .badge {
    display: inline;
  }
  .sidebar-minimized .sidebar .nav-link.nav-dropdown-toggle::before {
    display: none;
  }
  .sidebar-minimized .sidebar .nav-dropdown-items .nav-item {
    width: 200px;
  }
  .sidebar-minimized .sidebar .nav-dropdown-items .nav-item .nav-link {
    width: 200px;
  }
  .sidebar-minimized .sidebar .nav > .nav-dropdown > .nav-dropdown-items {
    display: none;
    max-height: 1000px;
    background: #2f353a;
  }
  .sidebar-minimized .sidebar .nav > .nav-dropdown:hover {
    background: #20a8d8;
  }
  .sidebar-minimized .sidebar .nav > .nav-dropdown:hover > .nav-dropdown-items {
    position: absolute;
    left: 50px;
    display: inline;
  }

  *[dir=rtl] .sidebar-minimized .sidebar .nav {
    list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav .divider {
    height: 0;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .sidebar-minimizer::before {
    width: 100%;
    transform: rotate(0deg);
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav-link {
    padding-right: 0;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav-link .nav-icon {
    float: right;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav-link .badge {
    right: auto;
    left: 15px;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav-link:hover .badge {
    display: inline;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav > .nav-dropdown > .nav-dropdown-items {
    display: none;
    max-height: 1000px;
    background: #2f353a;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav > .nav-dropdown:hover {
    background: #20a8d8;
  }
  *[dir=rtl] .sidebar-minimized .sidebar .nav > .nav-dropdown:hover > .nav-dropdown-items {
    position: absolute;
    left: 0;
    display: inline;
  }
}
*[dir=rtl] .sidebar .nav-dropdown-toggle::before {
  position: absolute;
  right: auto;
  left: 1rem;
  transform: rotate(180deg);
}
*[dir=rtl] .sidebar .nav-dropdown.open > .nav-dropdown-toggle::before {
  transform: rotate(270deg);
}
*[dir=rtl] .sidebar .nav-link .nav-icon {
  margin: 0 0 0 0.5rem;
}
*[dir=rtl] .sidebar .nav-link .badge {
  float: left;
  margin-top: 2px;
}
*[dir=rtl] .sidebar .nav-link.nav-dropdown-toggle .badge {
  margin-right: auto;
  margin-left: 16px;
}
*[dir=rtl] .sidebar .sidebar-minimizer::before {
  right: auto;
  left: 0;
  transform: rotate(180deg);
}
*[dir=rtl] .sidebar-toggler {
  margin-right: 0 !important;
}
.switch {
  display: inline-block;
  width: 40px;
  height: 26px;
}
.switch-input {
  display: none;
}
.switch-slider {
  position: relative;
  display: block;
  height: inherit;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #c8ced3;
  transition: 0.15s ease-out;
  border-radius: 0.25rem;
}
.switch-slider::before {
  position: absolute;
  top: 2px;
  left: 2px;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #fff;
  border: 1px solid #c8ced3;
  transition: 0.15s ease-out;
  border-radius: 0.125rem;
}
.switch-input:checked ~ .switch-slider::before {
  transform: translateX(14px);
}
.switch-input:disabled ~ .switch-slider {
  cursor: not-allowed;
  opacity: 0.5;
}
.switch-lg {
  width: 48px;
  height: 30px;
}
.switch-lg .switch-slider {
  font-size: 12px;
}
.switch-lg .switch-slider::before {
  width: 24px;
  height: 24px;
}
.switch-lg .switch-slider::after {
  font-size: 12px;
}
.switch-lg .switch-input:checked ~ .switch-slider::before {
  transform: translateX(18px);
}
.switch-sm {
  width: 32px;
  height: 22px;
}
.switch-sm .switch-slider {
  font-size: 8px;
}
.switch-sm .switch-slider::before {
  width: 16px;
  height: 16px;
}
.switch-sm .switch-slider::after {
  font-size: 8px;
}
.switch-sm .switch-input:checked ~ .switch-slider::before {
  transform: translateX(10px);
}
.switch-label {
  width: 48px;
}
.switch-label .switch-slider::before {
  z-index: 2;
}
.switch-label .switch-slider::after {
  position: absolute;
  top: 50%;
  right: 1px;
  z-index: 1;
  width: 50%;
  margin-top: -0.5em;
  font-size: 10px;
  font-weight: 600;
  line-height: 1;
  color: #c8ced3;
  text-align: center;
  text-transform: uppercase;
  content: attr(data-unchecked);
  transition: inherit;
}
.switch-label .switch-input:checked ~ .switch-slider::before {
  transform: translateX(22px);
}
.switch-label .switch-input:checked ~ .switch-slider::after {
  left: 1px;
  color: #fff;
  content: attr(data-checked);
}
.switch-label.switch-lg {
  width: 56px;
  height: 30px;
}
.switch-label.switch-lg .switch-slider {
  font-size: 12px;
}
.switch-label.switch-lg .switch-slider::before {
  width: 24px;
  height: 24px;
}
.switch-label.switch-lg .switch-slider::after {
  font-size: 12px;
}
.switch-label.switch-lg .switch-input:checked ~ .switch-slider::before {
  transform: translateX(26px);
}
.switch-label.switch-sm {
  width: 40px;
  height: 22px;
}
.switch-label.switch-sm .switch-slider {
  font-size: 8px;
}
.switch-label.switch-sm .switch-slider::before {
  width: 16px;
  height: 16px;
}
.switch-label.switch-sm .switch-slider::after {
  font-size: 8px;
}
.switch-label.switch-sm .switch-input:checked ~ .switch-slider::before {
  transform: translateX(18px);
}
.switch-3d .switch-slider {
  background-color: #f0f3f5;
  border-radius: 50em;
}
.switch-3d .switch-slider::before {
  top: -1px;
  left: -1px;
  width: 26px;
  height: 26px;
  border: 0;
  border-radius: 50em;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
.switch-3d.switch-lg {
  width: 48px;
  height: 30px;
}
.switch-3d.switch-lg .switch-slider::before {
  width: 30px;
  height: 30px;
}
.switch-3d.switch-lg .switch-input:checked ~ .switch-slider::before {
  transform: translateX(18px);
}
.switch-3d.switch-sm {
  width: 32px;
  height: 22px;
}
.switch-3d.switch-sm .switch-slider::before {
  width: 22px;
  height: 22px;
}
.switch-3d.switch-sm .switch-input:checked ~ .switch-slider::before {
  transform: translateX(10px);
}
.switch-primary .switch-input:checked + .switch-slider {
  background-color: #20a8d8;
  border-color: #1985ac;
}
.switch-primary .switch-input:checked + .switch-slider::before {
  border-color: #1985ac;
}
.switch-outline-primary .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #20a8d8;
}
.switch-outline-primary .switch-input:checked + .switch-slider::before {
  border-color: #20a8d8;
}
.switch-outline-primary .switch-input:checked + .switch-slider::after {
  color: #20a8d8;
}
.switch-outline-primary-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #20a8d8;
}
.switch-outline-primary-alt .switch-input:checked + .switch-slider::before {
  background-color: #20a8d8;
  border-color: #20a8d8;
}
.switch-outline-primary-alt .switch-input:checked + .switch-slider::after {
  color: #20a8d8;
}
.switch-secondary .switch-input:checked + .switch-slider {
  background-color: #c8ced3;
  border-color: #acb5bc;
}
.switch-secondary .switch-input:checked + .switch-slider::before {
  border-color: #acb5bc;
}
.switch-outline-secondary .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #c8ced3;
}
.switch-outline-secondary .switch-input:checked + .switch-slider::before {
  border-color: #c8ced3;
}
.switch-outline-secondary .switch-input:checked + .switch-slider::after {
  color: #c8ced3;
}
.switch-outline-secondary-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #c8ced3;
}
.switch-outline-secondary-alt .switch-input:checked + .switch-slider::before {
  background-color: #c8ced3;
  border-color: #c8ced3;
}
.switch-outline-secondary-alt .switch-input:checked + .switch-slider::after {
  color: #c8ced3;
}
.switch-success .switch-input:checked + .switch-slider {
  background-color: #4dbd74;
  border-color: #3a9d5d;
}
.switch-success .switch-input:checked + .switch-slider::before {
  border-color: #3a9d5d;
}
.switch-outline-success .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #4dbd74;
}
.switch-outline-success .switch-input:checked + .switch-slider::before {
  border-color: #4dbd74;
}
.switch-outline-success .switch-input:checked + .switch-slider::after {
  color: #4dbd74;
}
.switch-outline-success-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #4dbd74;
}
.switch-outline-success-alt .switch-input:checked + .switch-slider::before {
  background-color: #4dbd74;
  border-color: #4dbd74;
}
.switch-outline-success-alt .switch-input:checked + .switch-slider::after {
  color: #4dbd74;
}
.switch-info .switch-input:checked + .switch-slider {
  background-color: #63c2de;
  border-color: #39b2d5;
}
.switch-info .switch-input:checked + .switch-slider::before {
  border-color: #39b2d5;
}
.switch-outline-info .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #63c2de;
}
.switch-outline-info .switch-input:checked + .switch-slider::before {
  border-color: #63c2de;
}
.switch-outline-info .switch-input:checked + .switch-slider::after {
  color: #63c2de;
}
.switch-outline-info-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #63c2de;
}
.switch-outline-info-alt .switch-input:checked + .switch-slider::before {
  background-color: #63c2de;
  border-color: #63c2de;
}
.switch-outline-info-alt .switch-input:checked + .switch-slider::after {
  color: #63c2de;
}
.switch-warning .switch-input:checked + .switch-slider {
  background-color: #ffc107;
  border-color: #d39e00;
}
.switch-warning .switch-input:checked + .switch-slider::before {
  border-color: #d39e00;
}
.switch-outline-warning .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #ffc107;
}
.switch-outline-warning .switch-input:checked + .switch-slider::before {
  border-color: #ffc107;
}
.switch-outline-warning .switch-input:checked + .switch-slider::after {
  color: #ffc107;
}
.switch-outline-warning-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #ffc107;
}
.switch-outline-warning-alt .switch-input:checked + .switch-slider::before {
  background-color: #ffc107;
  border-color: #ffc107;
}
.switch-outline-warning-alt .switch-input:checked + .switch-slider::after {
  color: #ffc107;
}
.switch-danger .switch-input:checked + .switch-slider {
  background-color: #f86c6b;
  border-color: #f63c3a;
}
.switch-danger .switch-input:checked + .switch-slider::before {
  border-color: #f63c3a;
}
.switch-outline-danger .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #f86c6b;
}
.switch-outline-danger .switch-input:checked + .switch-slider::before {
  border-color: #f86c6b;
}
.switch-outline-danger .switch-input:checked + .switch-slider::after {
  color: #f86c6b;
}
.switch-outline-danger-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #f86c6b;
}
.switch-outline-danger-alt .switch-input:checked + .switch-slider::before {
  background-color: #f86c6b;
  border-color: #f86c6b;
}
.switch-outline-danger-alt .switch-input:checked + .switch-slider::after {
  color: #f86c6b;
}
.switch-light .switch-input:checked + .switch-slider {
  background-color: #f0f3f5;
  border-color: #d1dbe1;
}
.switch-light .switch-input:checked + .switch-slider::before {
  border-color: #d1dbe1;
}
.switch-outline-light .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #f0f3f5;
}
.switch-outline-light .switch-input:checked + .switch-slider::before {
  border-color: #f0f3f5;
}
.switch-outline-light .switch-input:checked + .switch-slider::after {
  color: #f0f3f5;
}
.switch-outline-light-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #f0f3f5;
}
.switch-outline-light-alt .switch-input:checked + .switch-slider::before {
  background-color: #f0f3f5;
  border-color: #f0f3f5;
}
.switch-outline-light-alt .switch-input:checked + .switch-slider::after {
  color: #f0f3f5;
}
.switch-dark .switch-input:checked + .switch-slider {
  background-color: #2f353a;
  border-color: #181b1e;
}
.switch-dark .switch-input:checked + .switch-slider::before {
  border-color: #181b1e;
}
.switch-outline-dark .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #2f353a;
}
.switch-outline-dark .switch-input:checked + .switch-slider::before {
  border-color: #2f353a;
}
.switch-outline-dark .switch-input:checked + .switch-slider::after {
  color: #2f353a;
}
.switch-outline-dark-alt .switch-input:checked + .switch-slider {
  background-color: #fff;
  border-color: #2f353a;
}
.switch-outline-dark-alt .switch-input:checked + .switch-slider::before {
  background-color: #2f353a;
  border-color: #2f353a;
}
.switch-outline-dark-alt .switch-input:checked + .switch-slider::after {
  color: #2f353a;
}
.switch-pill .switch-slider {
  border-radius: 50em;
}
.switch-pill .switch-slider::before {
  border-radius: 50em;
}
.table-outline {
  border: 1px solid #c8ced3;
}
.table-outline td {
  vertical-align: middle;
}
.table-align-middle td {
  vertical-align: middle;
}
.table-clear td {
  border: 0;
}
@media all and (-ms-high-contrast: none) {
  html {
    display: flex;
    flex-direction: column;
  }
}
.app,
app-dashboard,
app-root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.app-header {
  flex: 0 0 55px;
}
.app-footer {
  flex: 0 0 50px;
}
.app-body {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  overflow-x: hidden;
}
.app-body .main {
  flex: 1;
  min-width: 0;
}
.app-body .sidebar {
  flex: 0 0 200px;
  order: -1;
}
.app-body .aside-menu {
  flex: 0 0 250px;
}
html:not([dir=rtl]) .sidebar {
  margin-left: -200px;
}
html:not([dir=rtl]) .aside-menu {
  right: 0;
  margin-right: -250px;
}
html[dir=rtl] .sidebar {
  margin-right: -200px;
}
html[dir=rtl] .aside-menu {
  left: 0;
  margin-left: -250px;
}
@media (min-width: 992px) {
  .header-fixed .app-header {
    position: fixed;
    z-index: 1020;
    width: 100%;
  }
  .header-fixed .app-body {
    margin-top: 55px;
  }

  .sidebar-fixed .sidebar {
    position: fixed;
    z-index: 1019;
    width: 200px;
    height: 100vh;
  }

  .sidebar-fixed .app-header + .app-body .sidebar {
    height: calc(100vh - 55px);
  }

  .sidebar-compact .sidebar {
    flex: 0 0 150px;
  }
  .sidebar-compact.sidebar-fixed .sidebar {
    width: 150px;
  }
  .sidebar-compact .sidebar-minimizer {
    display: none;
  }

  .sidebar-minimized .sidebar {
    flex: 0 0 50px;
  }
  .sidebar-minimized.sidebar-fixed .sidebar {
    width: 50px;
  }

  .sidebar-off-canvas .sidebar {
    position: fixed;
    z-index: 1019;
    height: 100%;
  }

  .sidebar-off-canvas .app-header + .app-body .sidebar {
    height: calc(100vh - 55px);
  }

  html:not([dir=rtl]) .sidebar-compact .sidebar {
    margin-left: -150px;
  }
  html:not([dir=rtl]) .sidebar-minimized .sidebar {
    margin-left: -50px;
  }

  html[dir=rtl] .sidebar-compact .sidebar {
    margin-right: -150px;
  }
  html[dir=rtl] .sidebar-minimized .sidebar {
    margin-right: -50px;
  }

  .aside-menu-fixed .aside-menu {
    position: fixed;
    height: 100%;
  }
  .aside-menu-fixed .aside-menu .tab-content {
    height: calc(100vh - 2.375rem - 55px);
  }

  .aside-menu-fixed .app-header + .app-body .aside-menu {
    height: calc(100vh - 55px);
  }

  .aside-menu-off-canvas .aside-menu {
    position: fixed;
    z-index: 1019;
    height: 100%;
  }

  .aside-menu-off-canvas .app-header + .app-body .aside-menu {
    height: calc(100vh - 55px);
  }

  html:not([dir=rtl]) .aside-menu-fixed .aside-menu,
html:not([dir=rtl]) .aside-menu-off-canvas .aside-menu {
    right: 0;
  }

  html[dir=rtl] .aside-menu-fixed .aside-menu,
html[dir=rtl] .aside-menu-off-canvas .aside-menu {
    left: 0;
  }
}
.breadcrumb-fixed .main {
  padding-top: 3.875rem;
}
.breadcrumb-fixed .breadcrumb {
  position: fixed;
  top: 55px;
  right: 0;
  left: 0;
  z-index: 1017;
}
html:not([dir=rtl]) .sidebar-show .sidebar,
html:not([dir=rtl]) .sidebar-show .sidebar {
  margin-left: 0;
}
html:not([dir=rtl]) .aside-menu-show .aside-menu,
html:not([dir=rtl]) .aside-menu-show .aside-menu {
  margin-right: 0;
}
html[dir=rtl] .sidebar-show .sidebar,
html[dir=rtl] .sidebar-show .sidebar {
  margin-right: 0;
}
html[dir=rtl] .aside-menu-show .aside-menu,
html[dir=rtl] .aside-menu-show .aside-menu {
  margin-left: 0;
}
@-webkit-keyframes opacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes opacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@media (max-width: 575.98px) {
  .sidebar-show .main,
.aside-menu-show .main {
    position: relative;
  }
  .sidebar-show .main::before,
.aside-menu-show .main::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1018;
    width: 100%;
    height: 100%;
    content: "";
    background: rgba(0, 0, 0, 0.7);
    -webkit-animation: opacity 0.25s;
            animation: opacity 0.25s;
  }
}
@media (min-width: 576px) {
  html:not([dir=rtl]) .sidebar-sm-show .sidebar,
html:not([dir=rtl]) .sidebar-show .sidebar {
    margin-left: 0;
  }
  html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .app-footer {
    margin-left: 200px;
  }
  html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-compact .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-left: 150px;
  }
}
@media (min-width: 576px) and (max-width: 991.98px) {
  html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 200px;
  }
}
@media (min-width: 576px) and (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-sm-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 50px;
  }
}
@media (min-width: 576px) {
  html:not([dir=rtl]) .sidebar-sm-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed .breadcrumb {
    left: 200px;
  }
  html:not([dir=rtl]) .sidebar-sm-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    left: 150px;
  }
  html:not([dir=rtl]) .sidebar-sm-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    left: 50px;
  }
}
@media (min-width: 576px) {
  html:not([dir=rtl]) .aside-menu-show .aside-menu,
html:not([dir=rtl]) .aside-menu-sm-show .aside-menu {
    margin-right: 0;
  }
  html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .app-footer,
html:not([dir=rtl]) .aside-menu-sm-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-sm-show.aside-menu-fixed .app-footer {
    margin-right: 250px;
  }
  html:not([dir=rtl]) .aside-menu-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .aside-menu-sm-show.breadcrumb-fixed .breadcrumb {
    right: 250px;
  }
}
@media (min-width: 576px) {
  html[dir=rtl] .sidebar-sm-show .sidebar,
html[dir=rtl] .sidebar-show .sidebar {
    margin-right: 0;
  }
  html[dir=rtl] .sidebar-sm-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-sm-show.sidebar-fixed .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-show.sidebar-fixed .app-footer {
    margin-right: 200px;
  }
  html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-compact .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-right: 150px;
  }
}
@media (min-width: 576px) and (max-width: 991.98px) {
  html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 200px;
  }
}
@media (min-width: 576px) and (min-width: 992px) {
  html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-sm-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 50px;
  }
}
@media (min-width: 576px) {
  html[dir=rtl] .sidebar-sm-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed .breadcrumb {
    right: 200px;
  }
  html[dir=rtl] .sidebar-sm-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    right: 150px;
  }
  html[dir=rtl] .sidebar-sm-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    right: 50px;
  }
}
@media (min-width: 576px) {
  html[dir=rtl] .aside-menu-show .aside-menu,
html[dir=rtl] .aside-menu-sm-show .aside-menu {
    margin-left: 0;
  }
  html[dir=rtl] .aside-menu-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-show.aside-menu-fixed .app-footer,
html[dir=rtl] .aside-menu-sm-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-sm-show.aside-menu-fixed .app-footer {
    margin-left: 250px;
  }
  html[dir=rtl] .aside-menu-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .aside-menu-sm-show.breadcrumb-fixed .breadcrumb {
    left: 250px;
  }
}
@media (min-width: 576px) {
  @-webkit-keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
@media (min-width: 768px) {
  html:not([dir=rtl]) .sidebar-md-show .sidebar,
html:not([dir=rtl]) .sidebar-show .sidebar {
    margin-left: 0;
  }
  html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .app-footer {
    margin-left: 200px;
  }
  html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-compact .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-left: 150px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 200px;
  }
}
@media (min-width: 768px) and (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-md-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 50px;
  }
}
@media (min-width: 768px) {
  html:not([dir=rtl]) .sidebar-md-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed .breadcrumb {
    left: 200px;
  }
  html:not([dir=rtl]) .sidebar-md-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    left: 150px;
  }
  html:not([dir=rtl]) .sidebar-md-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    left: 50px;
  }
}
@media (min-width: 768px) {
  html:not([dir=rtl]) .aside-menu-show .aside-menu,
html:not([dir=rtl]) .aside-menu-md-show .aside-menu {
    margin-right: 0;
  }
  html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .app-footer,
html:not([dir=rtl]) .aside-menu-md-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-md-show.aside-menu-fixed .app-footer {
    margin-right: 250px;
  }
  html:not([dir=rtl]) .aside-menu-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .aside-menu-md-show.breadcrumb-fixed .breadcrumb {
    right: 250px;
  }
}
@media (min-width: 768px) {
  html[dir=rtl] .sidebar-md-show .sidebar,
html[dir=rtl] .sidebar-show .sidebar {
    margin-right: 0;
  }
  html[dir=rtl] .sidebar-md-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-md-show.sidebar-fixed .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-show.sidebar-fixed .app-footer {
    margin-right: 200px;
  }
  html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-compact .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-right: 150px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 200px;
  }
}
@media (min-width: 768px) and (min-width: 992px) {
  html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-md-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 50px;
  }
}
@media (min-width: 768px) {
  html[dir=rtl] .sidebar-md-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed .breadcrumb {
    right: 200px;
  }
  html[dir=rtl] .sidebar-md-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    right: 150px;
  }
  html[dir=rtl] .sidebar-md-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    right: 50px;
  }
}
@media (min-width: 768px) {
  html[dir=rtl] .aside-menu-show .aside-menu,
html[dir=rtl] .aside-menu-md-show .aside-menu {
    margin-left: 0;
  }
  html[dir=rtl] .aside-menu-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-show.aside-menu-fixed .app-footer,
html[dir=rtl] .aside-menu-md-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-md-show.aside-menu-fixed .app-footer {
    margin-left: 250px;
  }
  html[dir=rtl] .aside-menu-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .aside-menu-md-show.breadcrumb-fixed .breadcrumb {
    left: 250px;
  }
}
@media (min-width: 768px) {
  @-webkit-keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
@media (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-lg-show .sidebar,
html:not([dir=rtl]) .sidebar-show .sidebar {
    margin-left: 0;
  }
  html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .app-footer {
    margin-left: 200px;
  }
  html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-compact .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-left: 150px;
  }
}
@media (min-width: 992px) and (max-width: 991.98px) {
  html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 200px;
  }
}
@media (min-width: 992px) and (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-lg-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 50px;
  }
}
@media (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-lg-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed .breadcrumb {
    left: 200px;
  }
  html:not([dir=rtl]) .sidebar-lg-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    left: 150px;
  }
  html:not([dir=rtl]) .sidebar-lg-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    left: 50px;
  }
}
@media (min-width: 992px) {
  html:not([dir=rtl]) .aside-menu-show .aside-menu,
html:not([dir=rtl]) .aside-menu-lg-show .aside-menu {
    margin-right: 0;
  }
  html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .app-footer,
html:not([dir=rtl]) .aside-menu-lg-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-lg-show.aside-menu-fixed .app-footer {
    margin-right: 250px;
  }
  html:not([dir=rtl]) .aside-menu-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .aside-menu-lg-show.breadcrumb-fixed .breadcrumb {
    right: 250px;
  }
}
@media (min-width: 992px) {
  html[dir=rtl] .sidebar-lg-show .sidebar,
html[dir=rtl] .sidebar-show .sidebar {
    margin-right: 0;
  }
  html[dir=rtl] .sidebar-lg-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-lg-show.sidebar-fixed .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-show.sidebar-fixed .app-footer {
    margin-right: 200px;
  }
  html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-compact .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-right: 150px;
  }
}
@media (min-width: 992px) and (max-width: 991.98px) {
  html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 200px;
  }
}
@media (min-width: 992px) and (min-width: 992px) {
  html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-lg-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 50px;
  }
}
@media (min-width: 992px) {
  html[dir=rtl] .sidebar-lg-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed .breadcrumb {
    right: 200px;
  }
  html[dir=rtl] .sidebar-lg-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    right: 150px;
  }
  html[dir=rtl] .sidebar-lg-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    right: 50px;
  }
}
@media (min-width: 992px) {
  html[dir=rtl] .aside-menu-show .aside-menu,
html[dir=rtl] .aside-menu-lg-show .aside-menu {
    margin-left: 0;
  }
  html[dir=rtl] .aside-menu-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-show.aside-menu-fixed .app-footer,
html[dir=rtl] .aside-menu-lg-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-lg-show.aside-menu-fixed .app-footer {
    margin-left: 250px;
  }
  html[dir=rtl] .aside-menu-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .aside-menu-lg-show.breadcrumb-fixed .breadcrumb {
    left: 250px;
  }
}
@media (min-width: 992px) {
  @-webkit-keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
@media (min-width: 1200px) {
  html:not([dir=rtl]) .sidebar-xl-show .sidebar,
html:not([dir=rtl]) .sidebar-show .sidebar {
    margin-left: 0;
  }
  html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed .app-footer {
    margin-left: 200px;
  }
  html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-compact .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-left: 150px;
  }
}
@media (min-width: 1200px) and (max-width: 991.98px) {
  html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 200px;
  }
}
@media (min-width: 1200px) and (min-width: 992px) {
  html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-xl-show.sidebar-fixed.sidebar-minimized .app-footer,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html:not([dir=rtl]) .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-left: 50px;
  }
}
@media (min-width: 1200px) {
  html:not([dir=rtl]) .sidebar-xl-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed .breadcrumb {
    left: 200px;
  }
  html:not([dir=rtl]) .sidebar-xl-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    left: 150px;
  }
  html:not([dir=rtl]) .sidebar-xl-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html:not([dir=rtl]) .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    left: 50px;
  }
}
@media (min-width: 1200px) {
  html:not([dir=rtl]) .aside-menu-show .aside-menu,
html:not([dir=rtl]) .aside-menu-xl-show .aside-menu {
    margin-right: 0;
  }
  html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-show.aside-menu-fixed .app-footer,
html:not([dir=rtl]) .aside-menu-xl-show.aside-menu-fixed .main,
html:not([dir=rtl]) .aside-menu-xl-show.aside-menu-fixed .app-footer {
    margin-right: 250px;
  }
  html:not([dir=rtl]) .aside-menu-show.breadcrumb-fixed .breadcrumb,
html:not([dir=rtl]) .aside-menu-xl-show.breadcrumb-fixed .breadcrumb {
    right: 250px;
  }
}
@media (min-width: 1200px) {
  html[dir=rtl] .sidebar-xl-show .sidebar,
html[dir=rtl] .sidebar-show .sidebar {
    margin-right: 0;
  }
  html[dir=rtl] .sidebar-xl-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-xl-show.sidebar-fixed .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed .main,
html[dir=rtl] .sidebar-show.sidebar-fixed .app-footer {
    margin-right: 200px;
  }
  html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-compact .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-compact .app-footer {
    margin-right: 150px;
  }
}
@media (min-width: 1200px) and (max-width: 991.98px) {
  html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 200px;
  }
}
@media (min-width: 1200px) and (min-width: 992px) {
  html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-xl-show.sidebar-fixed.sidebar-minimized .app-footer,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .main,
html[dir=rtl] .sidebar-show.sidebar-fixed.sidebar-minimized .app-footer {
    margin-right: 50px;
  }
}
@media (min-width: 1200px) {
  html[dir=rtl] .sidebar-xl-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed .breadcrumb {
    right: 200px;
  }
  html[dir=rtl] .sidebar-xl-show.breadcrumb-fixed.sidebar-compact .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-compact .breadcrumb {
    right: 150px;
  }
  html[dir=rtl] .sidebar-xl-show.breadcrumb-fixed.sidebar-minimized .breadcrumb,
html[dir=rtl] .sidebar-show.breadcrumb-fixed.sidebar-minimized .breadcrumb {
    right: 50px;
  }
}
@media (min-width: 1200px) {
  html[dir=rtl] .aside-menu-show .aside-menu,
html[dir=rtl] .aside-menu-xl-show .aside-menu {
    margin-left: 0;
  }
  html[dir=rtl] .aside-menu-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-show.aside-menu-fixed .app-footer,
html[dir=rtl] .aside-menu-xl-show.aside-menu-fixed .main,
html[dir=rtl] .aside-menu-xl-show.aside-menu-fixed .app-footer {
    margin-left: 250px;
  }
  html[dir=rtl] .aside-menu-show.breadcrumb-fixed .breadcrumb,
html[dir=rtl] .aside-menu-xl-show.breadcrumb-fixed .breadcrumb {
    left: 250px;
  }
}
@media (min-width: 1200px) {
  @-webkit-keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
.footer-fixed .app-footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1020;
  height: 50px;
}
.footer-fixed .app-body {
  margin-bottom: 50px;
}
.app-header,
.app-footer,
.sidebar,
.main,
.aside-menu {
  transition: margin-left 0.25s, margin-right 0.25s, width 0.25s, flex 0.25s;
}
.sidebar-nav {
  transition: width 0.25s;
}
.breadcrumb {
  transition: left 0.25s, right 0.25s, width 0.25s;
}
@media (max-width: 991.98px) {
  .app-header {
    position: fixed;
    z-index: 1020;
    width: 100%;
    text-align: center;
    background-color: #fff;
  }
  .app-header .navbar-toggler {
    color: #fff;
  }
  .app-header .navbar-brand {
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -77.5px;
  }

  .app-body {
    margin-top: 55px;
  }

  .sidebar {
    position: fixed;
    z-index: 1019;
    width: 200px;
    height: calc(100vh - 55px);
  }

  .sidebar-minimizer {
    display: none;
  }

  .aside-menu {
    position: fixed;
    height: 100%;
  }
}
hr.transparent {
  border-top: 1px solid transparent;
}
.bg-primary,
.bg-success,
.bg-info,
.bg-warning,
.bg-danger,
.bg-dark {
  color: #fff;
}
.bg-facebook {
  background-color: #3b5998 !important;
}
a.bg-facebook:hover, a.bg-facebook:focus,
button.bg-facebook:hover,
button.bg-facebook:focus {
  background-color: #2d4373 !important;
}
.bg-twitter {
  background-color: #00aced !important;
}
a.bg-twitter:hover, a.bg-twitter:focus,
button.bg-twitter:hover,
button.bg-twitter:focus {
  background-color: #0087ba !important;
}
.bg-linkedin {
  background-color: #4875b4 !important;
}
a.bg-linkedin:hover, a.bg-linkedin:focus,
button.bg-linkedin:hover,
button.bg-linkedin:focus {
  background-color: #395d90 !important;
}
.bg-google-plus {
  background-color: #d34836 !important;
}
a.bg-google-plus:hover, a.bg-google-plus:focus,
button.bg-google-plus:hover,
button.bg-google-plus:focus {
  background-color: #b03626 !important;
}
.bg-flickr {
  background-color: #ff0084 !important;
}
a.bg-flickr:hover, a.bg-flickr:focus,
button.bg-flickr:hover,
button.bg-flickr:focus {
  background-color: #cc006a !important;
}
.bg-tumblr {
  background-color: #32506d !important;
}
a.bg-tumblr:hover, a.bg-tumblr:focus,
button.bg-tumblr:hover,
button.bg-tumblr:focus {
  background-color: #22364a !important;
}
.bg-xing {
  background-color: #026466 !important;
}
a.bg-xing:hover, a.bg-xing:focus,
button.bg-xing:hover,
button.bg-xing:focus {
  background-color: #013334 !important;
}
.bg-github {
  background-color: #4183c4 !important;
}
a.bg-github:hover, a.bg-github:focus,
button.bg-github:hover,
button.bg-github:focus {
  background-color: #3269a0 !important;
}
.bg-html5 {
  background-color: #e34f26 !important;
}
a.bg-html5:hover, a.bg-html5:focus,
button.bg-html5:hover,
button.bg-html5:focus {
  background-color: #be3c18 !important;
}
.bg-openid {
  background-color: #f78c40 !important;
}
a.bg-openid:hover, a.bg-openid:focus,
button.bg-openid:hover,
button.bg-openid:focus {
  background-color: #f56f0f !important;
}
.bg-stack-overflow {
  background-color: #fe7a15 !important;
}
a.bg-stack-overflow:hover, a.bg-stack-overflow:focus,
button.bg-stack-overflow:hover,
button.bg-stack-overflow:focus {
  background-color: #df6101 !important;
}
.bg-youtube {
  background-color: #b00 !important;
}
a.bg-youtube:hover, a.bg-youtube:focus,
button.bg-youtube:hover,
button.bg-youtube:focus {
  background-color: #880000 !important;
}
.bg-css3 {
  background-color: #0170ba !important;
}
a.bg-css3:hover, a.bg-css3:focus,
button.bg-css3:hover,
button.bg-css3:focus {
  background-color: #015187 !important;
}
.bg-dribbble {
  background-color: #ea4c89 !important;
}
a.bg-dribbble:hover, a.bg-dribbble:focus,
button.bg-dribbble:hover,
button.bg-dribbble:focus {
  background-color: #e51e6b !important;
}
.bg-instagram {
  background-color: #517fa4 !important;
}
a.bg-instagram:hover, a.bg-instagram:focus,
button.bg-instagram:hover,
button.bg-instagram:focus {
  background-color: #406582 !important;
}
.bg-pinterest {
  background-color: #cb2027 !important;
}
a.bg-pinterest:hover, a.bg-pinterest:focus,
button.bg-pinterest:hover,
button.bg-pinterest:focus {
  background-color: #9f191f !important;
}
.bg-vk {
  background-color: #45668e !important;
}
a.bg-vk:hover, a.bg-vk:focus,
button.bg-vk:hover,
button.bg-vk:focus {
  background-color: #344d6c !important;
}
.bg-yahoo {
  background-color: #400191 !important;
}
a.bg-yahoo:hover, a.bg-yahoo:focus,
button.bg-yahoo:hover,
button.bg-yahoo:focus {
  background-color: #2a015e !important;
}
.bg-behance {
  background-color: #1769ff !important;
}
a.bg-behance:hover, a.bg-behance:focus,
button.bg-behance:hover,
button.bg-behance:focus {
  background-color: #0050e3 !important;
}
.bg-dropbox {
  background-color: #007ee5 !important;
}
a.bg-dropbox:hover, a.bg-dropbox:focus,
button.bg-dropbox:hover,
button.bg-dropbox:focus {
  background-color: #0062b2 !important;
}
.bg-reddit {
  background-color: #ff4500 !important;
}
a.bg-reddit:hover, a.bg-reddit:focus,
button.bg-reddit:hover,
button.bg-reddit:focus {
  background-color: #cc3700 !important;
}
.bg-spotify {
  background-color: #7ab800 !important;
}
a.bg-spotify:hover, a.bg-spotify:focus,
button.bg-spotify:hover,
button.bg-spotify:focus {
  background-color: #588500 !important;
}
.bg-vine {
  background-color: #00bf8f !important;
}
a.bg-vine:hover, a.bg-vine:focus,
button.bg-vine:hover,
button.bg-vine:focus {
  background-color: #008c69 !important;
}
.bg-foursquare {
  background-color: #1073af !important;
}
a.bg-foursquare:hover, a.bg-foursquare:focus,
button.bg-foursquare:hover,
button.bg-foursquare:focus {
  background-color: #0c5480 !important;
}
.bg-vimeo {
  background-color: #aad450 !important;
}
a.bg-vimeo:hover, a.bg-vimeo:focus,
button.bg-vimeo:hover,
button.bg-vimeo:focus {
  background-color: #93c130 !important;
}
.bg-blue {
  background-color: #20a8d8 !important;
}
a.bg-blue:hover, a.bg-blue:focus,
button.bg-blue:hover,
button.bg-blue:focus {
  background-color: #1985ac !important;
}
.bg-indigo {
  background-color: #6610f2 !important;
}
a.bg-indigo:hover, a.bg-indigo:focus,
button.bg-indigo:hover,
button.bg-indigo:focus {
  background-color: #510bc4 !important;
}
.bg-purple {
  background-color: #6f42c1 !important;
}
a.bg-purple:hover, a.bg-purple:focus,
button.bg-purple:hover,
button.bg-purple:focus {
  background-color: #59339d !important;
}
.bg-pink {
  background-color: #e83e8c !important;
}
a.bg-pink:hover, a.bg-pink:focus,
button.bg-pink:hover,
button.bg-pink:focus {
  background-color: #d91a72 !important;
}
.bg-red {
  background-color: #f86c6b !important;
}
a.bg-red:hover, a.bg-red:focus,
button.bg-red:hover,
button.bg-red:focus {
  background-color: #f63c3a !important;
}
.bg-orange {
  background-color: #f8cb00 !important;
}
a.bg-orange:hover, a.bg-orange:focus,
button.bg-orange:hover,
button.bg-orange:focus {
  background-color: #c5a100 !important;
}
.bg-yellow {
  background-color: #ffc107 !important;
}
a.bg-yellow:hover, a.bg-yellow:focus,
button.bg-yellow:hover,
button.bg-yellow:focus {
  background-color: #d39e00 !important;
}
.bg-green {
  background-color: #4dbd74 !important;
}
a.bg-green:hover, a.bg-green:focus,
button.bg-green:hover,
button.bg-green:focus {
  background-color: #3a9d5d !important;
}
.bg-teal {
  background-color: #20c997 !important;
}
a.bg-teal:hover, a.bg-teal:focus,
button.bg-teal:hover,
button.bg-teal:focus {
  background-color: #199d76 !important;
}
.bg-cyan {
  background-color: #17a2b8 !important;
}
a.bg-cyan:hover, a.bg-cyan:focus,
button.bg-cyan:hover,
button.bg-cyan:focus {
  background-color: #117a8b !important;
}
.bg-white {
  background-color: #fff !important;
}
a.bg-white:hover, a.bg-white:focus,
button.bg-white:hover,
button.bg-white:focus {
  background-color: #e6e6e6 !important;
}
.bg-gray {
  background-color: #73818f !important;
}
a.bg-gray:hover, a.bg-gray:focus,
button.bg-gray:hover,
button.bg-gray:focus {
  background-color: #5c6873 !important;
}
.bg-gray-dark {
  background-color: #2f353a !important;
}
a.bg-gray-dark:hover, a.bg-gray-dark:focus,
button.bg-gray-dark:hover,
button.bg-gray-dark:focus {
  background-color: #181b1e !important;
}
.bg-light-blue {
  background-color: #63c2de !important;
}
a.bg-light-blue:hover, a.bg-light-blue:focus,
button.bg-light-blue:hover,
button.bg-light-blue:focus {
  background-color: #39b2d5 !important;
}
.bg-gray-100 {
  background-color: #f0f3f5 !important;
}
a.bg-gray-100:hover, a.bg-gray-100:focus,
button.bg-gray-100:hover,
button.bg-gray-100:focus {
  background-color: #d1dbe1 !important;
}
.bg-gray-200 {
  background-color: #e4e7ea !important;
}
a.bg-gray-200:hover, a.bg-gray-200:focus,
button.bg-gray-200:hover,
button.bg-gray-200:focus {
  background-color: #c7ced4 !important;
}
.bg-gray-300 {
  background-color: #c8ced3 !important;
}
a.bg-gray-300:hover, a.bg-gray-300:focus,
button.bg-gray-300:hover,
button.bg-gray-300:focus {
  background-color: #acb5bc !important;
}
.bg-gray-400 {
  background-color: #acb4bc !important;
}
a.bg-gray-400:hover, a.bg-gray-400:focus,
button.bg-gray-400:hover,
button.bg-gray-400:focus {
  background-color: #909ba5 !important;
}
.bg-gray-500 {
  background-color: #8f9ba6 !important;
}
a.bg-gray-500:hover, a.bg-gray-500:focus,
button.bg-gray-500:hover,
button.bg-gray-500:focus {
  background-color: #73828f !important;
}
.bg-gray-600 {
  background-color: #73818f !important;
}
a.bg-gray-600:hover, a.bg-gray-600:focus,
button.bg-gray-600:hover,
button.bg-gray-600:focus {
  background-color: #5c6873 !important;
}
.bg-gray-700 {
  background-color: #5c6873 !important;
}
a.bg-gray-700:hover, a.bg-gray-700:focus,
button.bg-gray-700:hover,
button.bg-gray-700:focus {
  background-color: #454e57 !important;
}
.bg-gray-800 {
  background-color: #2f353a !important;
}
a.bg-gray-800:hover, a.bg-gray-800:focus,
button.bg-gray-800:hover,
button.bg-gray-800:focus {
  background-color: #181b1e !important;
}
.bg-gray-900 {
  background-color: #23282c !important;
}
a.bg-gray-900:hover, a.bg-gray-900:focus,
button.bg-gray-900:hover,
button.bg-gray-900:focus {
  background-color: #0c0e10 !important;
}
.bg-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}
.b-a-0 {
  border: 0 !important;
}
.b-t-0 {
  border-top: 0 !important;
}
.b-r-0 {
  border-right: 0 !important;
}
.b-b-0 {
  border-bottom: 0 !important;
}
.b-l-0 {
  border-left: 0 !important;
}
.b-a-1 {
  border: 1px solid #c8ced3;
}
.b-t-1 {
  border-top: 1px solid #c8ced3;
}
.b-r-1 {
  border-right: 1px solid #c8ced3;
}
.b-b-1 {
  border-bottom: 1px solid #c8ced3;
}
.b-l-1 {
  border-left: 1px solid #c8ced3;
}
.b-a-2 {
  border: 2px solid #c8ced3;
}
.b-t-2 {
  border-top: 2px solid #c8ced3;
}
.b-r-2 {
  border-right: 2px solid #c8ced3;
}
.b-b-2 {
  border-bottom: 2px solid #c8ced3;
}
.b-l-2 {
  border-left: 2px solid #c8ced3;
}
@media (max-width: 575.98px) {
  .d-down-none {
    display: none !important;
  }
}
@media (max-width: 767.98px) {
  .d-sm-down-none {
    display: none !important;
  }
}
@media (max-width: 991.98px) {
  .d-md-down-none {
    display: none !important;
  }
}
@media (max-width: 1199.98px) {
  .d-lg-down-none {
    display: none !important;
  }
}
.d-xl-down-none {
  display: none !important;
}
body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}
.font-xs {
  font-size: 0.75rem !important;
}
.font-sm {
  font-size: 0.85rem !important;
}
.font-lg {
  font-size: 1rem !important;
}
.font-xl {
  font-size: 1.25rem !important;
}
.font-2xl {
  font-size: 1.5rem !important;
}
.font-3xl {
  font-size: 1.75rem !important;
}
.font-4xl {
  font-size: 2rem !important;
}
.font-5xl {
  font-size: 2.5rem !important;
}
.text-value {
  font-size: 1.3125rem;
  font-weight: 600;
}
.text-value-sm {
  font-size: 1.09375rem;
  font-weight: 600;
}
.text-value-lg {
  font-size: 1.53125rem;
  font-weight: 600;
}
.text-white .text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
}
*[dir=rtl] {
  direction: rtl;
  unicode-bidi: embed;
}
*[dir=rtl] body {
  text-align: right;
}
*[dir=rtl] .dropdown-item {
  text-align: right;
}
*[dir=rtl] .dropdown-item i {
  margin-right: -10px;
  margin-left: 10px;
}
*[dir=rtl] .dropdown-item .badge {
  right: auto;
  left: 10px;
}
*[dir=rtl] .float-left {
  float: right !important;
}
*[dir=rtl] .float-right {
  float: left !important;
}
*[dir=rtl] .mr-0,
*[dir=rtl] .mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
*[dir=rtl] .ml-0,
*[dir=rtl] .mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-1,
*[dir=rtl] .mx-1 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}
*[dir=rtl] .ml-1,
*[dir=rtl] .mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-2,
*[dir=rtl] .mx-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}
*[dir=rtl] .ml-2,
*[dir=rtl] .mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-3,
*[dir=rtl] .mx-3 {
  margin-right: 0 !important;
  margin-left: 1rem !important;
}
*[dir=rtl] .ml-3,
*[dir=rtl] .mx-3 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-4,
*[dir=rtl] .mx-4 {
  margin-right: 0 !important;
  margin-left: 1.5rem !important;
}
*[dir=rtl] .ml-4,
*[dir=rtl] .mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-5,
*[dir=rtl] .mx-5 {
  margin-right: 0 !important;
  margin-left: 3rem !important;
}
*[dir=rtl] .ml-5,
*[dir=rtl] .mx-5 {
  margin-right: 3rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .pr-0,
*[dir=rtl] .px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pl-0,
*[dir=rtl] .px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pr-1,
*[dir=rtl] .px-1 {
  padding-right: 0 !important;
  padding-left: 0.25rem !important;
}
*[dir=rtl] .pl-1,
*[dir=rtl] .px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pr-2,
*[dir=rtl] .px-2 {
  padding-right: 0 !important;
  padding-left: 0.5rem !important;
}
*[dir=rtl] .pl-2,
*[dir=rtl] .px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pr-3,
*[dir=rtl] .px-3 {
  padding-right: 0 !important;
  padding-left: 1rem !important;
}
*[dir=rtl] .pl-3,
*[dir=rtl] .px-3 {
  padding-right: 1rem !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pr-4,
*[dir=rtl] .px-4 {
  padding-right: 0 !important;
  padding-left: 1.5rem !important;
}
*[dir=rtl] .pl-4,
*[dir=rtl] .px-4 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important;
}
*[dir=rtl] .pr-5,
*[dir=rtl] .px-5 {
  padding-right: 0 !important;
  padding-left: 3rem !important;
}
*[dir=rtl] .pl-5,
*[dir=rtl] .px-5 {
  padding-right: 3rem !important;
  padding-left: 0 !important;
}
*[dir=rtl] .mr-n1,
*[dir=rtl] .mx-n1 {
  margin-right: 0 !important;
  margin-left: -0.25rem !important;
}
*[dir=rtl] .ml-n1,
*[dir=rtl] .mx-n1 {
  margin-right: -0.25rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-n2,
*[dir=rtl] .mx-n2 {
  margin-right: 0 !important;
  margin-left: -0.5rem !important;
}
*[dir=rtl] .ml-n2,
*[dir=rtl] .mx-n2 {
  margin-right: -0.5rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-n3,
*[dir=rtl] .mx-n3 {
  margin-right: 0 !important;
  margin-left: -1rem !important;
}
*[dir=rtl] .ml-n3,
*[dir=rtl] .mx-n3 {
  margin-right: -1rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-n4,
*[dir=rtl] .mx-n4 {
  margin-right: 0 !important;
  margin-left: -1.5rem !important;
}
*[dir=rtl] .ml-n4,
*[dir=rtl] .mx-n4 {
  margin-right: -1.5rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-n5,
*[dir=rtl] .mx-n5 {
  margin-right: 0 !important;
  margin-left: -3rem !important;
}
*[dir=rtl] .ml-n5,
*[dir=rtl] .mx-n5 {
  margin-right: -3rem !important;
  margin-left: 0 !important;
}
*[dir=rtl] .mr-auto,
*[dir=rtl] .mx-auto {
  margin-left: auto !important;
}
*[dir=rtl] .ml-auto,
*[dir=rtl] .mx-auto {
  margin-right: auto !important;
}
@media (min-width: 576px) {
  *[dir=rtl] .mr-sm-0,
*[dir=rtl] .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .ml-sm-0,
*[dir=rtl] .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-1,
*[dir=rtl] .mx-sm-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ml-sm-1,
*[dir=rtl] .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-2,
*[dir=rtl] .mx-sm-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ml-sm-2,
*[dir=rtl] .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-3,
*[dir=rtl] .mx-sm-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ml-sm-3,
*[dir=rtl] .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-4,
*[dir=rtl] .mx-sm-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ml-sm-4,
*[dir=rtl] .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-5,
*[dir=rtl] .mx-sm-5 {
    margin-right: 0 !important;
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ml-sm-5,
*[dir=rtl] .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-0,
*[dir=rtl] .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pl-sm-0,
*[dir=rtl] .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-1,
*[dir=rtl] .px-sm-1 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .pl-sm-1,
*[dir=rtl] .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-2,
*[dir=rtl] .px-sm-2 {
    padding-right: 0 !important;
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .pl-sm-2,
*[dir=rtl] .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-3,
*[dir=rtl] .px-sm-3 {
    padding-right: 0 !important;
    padding-left: 1rem !important;
  }
  *[dir=rtl] .pl-sm-3,
*[dir=rtl] .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-4,
*[dir=rtl] .px-sm-4 {
    padding-right: 0 !important;
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .pl-sm-4,
*[dir=rtl] .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-sm-5,
*[dir=rtl] .px-sm-5 {
    padding-right: 0 !important;
    padding-left: 3rem !important;
  }
  *[dir=rtl] .pl-sm-5,
*[dir=rtl] .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-n1,
*[dir=rtl] .mx-sm-n1 {
    margin-right: 0 !important;
    margin-left: -0.25rem !important;
  }
  *[dir=rtl] .ml-sm-n1,
*[dir=rtl] .mx-sm-n1 {
    margin-right: -0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-n2,
*[dir=rtl] .mx-sm-n2 {
    margin-right: 0 !important;
    margin-left: -0.5rem !important;
  }
  *[dir=rtl] .ml-sm-n2,
*[dir=rtl] .mx-sm-n2 {
    margin-right: -0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-n3,
*[dir=rtl] .mx-sm-n3 {
    margin-right: 0 !important;
    margin-left: -1rem !important;
  }
  *[dir=rtl] .ml-sm-n3,
*[dir=rtl] .mx-sm-n3 {
    margin-right: -1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-n4,
*[dir=rtl] .mx-sm-n4 {
    margin-right: 0 !important;
    margin-left: -1.5rem !important;
  }
  *[dir=rtl] .ml-sm-n4,
*[dir=rtl] .mx-sm-n4 {
    margin-right: -1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-n5,
*[dir=rtl] .mx-sm-n5 {
    margin-right: 0 !important;
    margin-left: -3rem !important;
  }
  *[dir=rtl] .ml-sm-n5,
*[dir=rtl] .mx-sm-n5 {
    margin-right: -3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-sm-auto,
*[dir=rtl] .mx-sm-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ml-sm-auto,
*[dir=rtl] .mx-sm-auto {
    margin-right: auto !important;
  }
}
@media (min-width: 768px) {
  *[dir=rtl] .mr-md-0,
*[dir=rtl] .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .ml-md-0,
*[dir=rtl] .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-1,
*[dir=rtl] .mx-md-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ml-md-1,
*[dir=rtl] .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-2,
*[dir=rtl] .mx-md-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ml-md-2,
*[dir=rtl] .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-3,
*[dir=rtl] .mx-md-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ml-md-3,
*[dir=rtl] .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-4,
*[dir=rtl] .mx-md-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ml-md-4,
*[dir=rtl] .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-5,
*[dir=rtl] .mx-md-5 {
    margin-right: 0 !important;
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ml-md-5,
*[dir=rtl] .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .pr-md-0,
*[dir=rtl] .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pl-md-0,
*[dir=rtl] .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-md-1,
*[dir=rtl] .px-md-1 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .pl-md-1,
*[dir=rtl] .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-md-2,
*[dir=rtl] .px-md-2 {
    padding-right: 0 !important;
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .pl-md-2,
*[dir=rtl] .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-md-3,
*[dir=rtl] .px-md-3 {
    padding-right: 0 !important;
    padding-left: 1rem !important;
  }
  *[dir=rtl] .pl-md-3,
*[dir=rtl] .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-md-4,
*[dir=rtl] .px-md-4 {
    padding-right: 0 !important;
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .pl-md-4,
*[dir=rtl] .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-md-5,
*[dir=rtl] .px-md-5 {
    padding-right: 0 !important;
    padding-left: 3rem !important;
  }
  *[dir=rtl] .pl-md-5,
*[dir=rtl] .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .mr-md-n1,
*[dir=rtl] .mx-md-n1 {
    margin-right: 0 !important;
    margin-left: -0.25rem !important;
  }
  *[dir=rtl] .ml-md-n1,
*[dir=rtl] .mx-md-n1 {
    margin-right: -0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-n2,
*[dir=rtl] .mx-md-n2 {
    margin-right: 0 !important;
    margin-left: -0.5rem !important;
  }
  *[dir=rtl] .ml-md-n2,
*[dir=rtl] .mx-md-n2 {
    margin-right: -0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-n3,
*[dir=rtl] .mx-md-n3 {
    margin-right: 0 !important;
    margin-left: -1rem !important;
  }
  *[dir=rtl] .ml-md-n3,
*[dir=rtl] .mx-md-n3 {
    margin-right: -1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-n4,
*[dir=rtl] .mx-md-n4 {
    margin-right: 0 !important;
    margin-left: -1.5rem !important;
  }
  *[dir=rtl] .ml-md-n4,
*[dir=rtl] .mx-md-n4 {
    margin-right: -1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-n5,
*[dir=rtl] .mx-md-n5 {
    margin-right: 0 !important;
    margin-left: -3rem !important;
  }
  *[dir=rtl] .ml-md-n5,
*[dir=rtl] .mx-md-n5 {
    margin-right: -3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-md-auto,
*[dir=rtl] .mx-md-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ml-md-auto,
*[dir=rtl] .mx-md-auto {
    margin-right: auto !important;
  }
}
@media (min-width: 992px) {
  *[dir=rtl] .mr-lg-0,
*[dir=rtl] .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .ml-lg-0,
*[dir=rtl] .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-1,
*[dir=rtl] .mx-lg-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ml-lg-1,
*[dir=rtl] .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-2,
*[dir=rtl] .mx-lg-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ml-lg-2,
*[dir=rtl] .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-3,
*[dir=rtl] .mx-lg-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ml-lg-3,
*[dir=rtl] .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-4,
*[dir=rtl] .mx-lg-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ml-lg-4,
*[dir=rtl] .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-5,
*[dir=rtl] .mx-lg-5 {
    margin-right: 0 !important;
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ml-lg-5,
*[dir=rtl] .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-0,
*[dir=rtl] .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pl-lg-0,
*[dir=rtl] .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-1,
*[dir=rtl] .px-lg-1 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .pl-lg-1,
*[dir=rtl] .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-2,
*[dir=rtl] .px-lg-2 {
    padding-right: 0 !important;
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .pl-lg-2,
*[dir=rtl] .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-3,
*[dir=rtl] .px-lg-3 {
    padding-right: 0 !important;
    padding-left: 1rem !important;
  }
  *[dir=rtl] .pl-lg-3,
*[dir=rtl] .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-4,
*[dir=rtl] .px-lg-4 {
    padding-right: 0 !important;
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .pl-lg-4,
*[dir=rtl] .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-lg-5,
*[dir=rtl] .px-lg-5 {
    padding-right: 0 !important;
    padding-left: 3rem !important;
  }
  *[dir=rtl] .pl-lg-5,
*[dir=rtl] .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-n1,
*[dir=rtl] .mx-lg-n1 {
    margin-right: 0 !important;
    margin-left: -0.25rem !important;
  }
  *[dir=rtl] .ml-lg-n1,
*[dir=rtl] .mx-lg-n1 {
    margin-right: -0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-n2,
*[dir=rtl] .mx-lg-n2 {
    margin-right: 0 !important;
    margin-left: -0.5rem !important;
  }
  *[dir=rtl] .ml-lg-n2,
*[dir=rtl] .mx-lg-n2 {
    margin-right: -0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-n3,
*[dir=rtl] .mx-lg-n3 {
    margin-right: 0 !important;
    margin-left: -1rem !important;
  }
  *[dir=rtl] .ml-lg-n3,
*[dir=rtl] .mx-lg-n3 {
    margin-right: -1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-n4,
*[dir=rtl] .mx-lg-n4 {
    margin-right: 0 !important;
    margin-left: -1.5rem !important;
  }
  *[dir=rtl] .ml-lg-n4,
*[dir=rtl] .mx-lg-n4 {
    margin-right: -1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-n5,
*[dir=rtl] .mx-lg-n5 {
    margin-right: 0 !important;
    margin-left: -3rem !important;
  }
  *[dir=rtl] .ml-lg-n5,
*[dir=rtl] .mx-lg-n5 {
    margin-right: -3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-lg-auto,
*[dir=rtl] .mx-lg-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ml-lg-auto,
*[dir=rtl] .mx-lg-auto {
    margin-right: auto !important;
  }
}
@media (min-width: 1200px) {
  *[dir=rtl] .mr-xl-0,
*[dir=rtl] .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .ml-xl-0,
*[dir=rtl] .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-1,
*[dir=rtl] .mx-xl-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ml-xl-1,
*[dir=rtl] .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-2,
*[dir=rtl] .mx-xl-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ml-xl-2,
*[dir=rtl] .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-3,
*[dir=rtl] .mx-xl-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ml-xl-3,
*[dir=rtl] .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-4,
*[dir=rtl] .mx-xl-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ml-xl-4,
*[dir=rtl] .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-5,
*[dir=rtl] .mx-xl-5 {
    margin-right: 0 !important;
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ml-xl-5,
*[dir=rtl] .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-0,
*[dir=rtl] .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pl-xl-0,
*[dir=rtl] .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-1,
*[dir=rtl] .px-xl-1 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .pl-xl-1,
*[dir=rtl] .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-2,
*[dir=rtl] .px-xl-2 {
    padding-right: 0 !important;
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .pl-xl-2,
*[dir=rtl] .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-3,
*[dir=rtl] .px-xl-3 {
    padding-right: 0 !important;
    padding-left: 1rem !important;
  }
  *[dir=rtl] .pl-xl-3,
*[dir=rtl] .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-4,
*[dir=rtl] .px-xl-4 {
    padding-right: 0 !important;
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .pl-xl-4,
*[dir=rtl] .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .pr-xl-5,
*[dir=rtl] .px-xl-5 {
    padding-right: 0 !important;
    padding-left: 3rem !important;
  }
  *[dir=rtl] .pl-xl-5,
*[dir=rtl] .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-n1,
*[dir=rtl] .mx-xl-n1 {
    margin-right: 0 !important;
    margin-left: -0.25rem !important;
  }
  *[dir=rtl] .ml-xl-n1,
*[dir=rtl] .mx-xl-n1 {
    margin-right: -0.25rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-n2,
*[dir=rtl] .mx-xl-n2 {
    margin-right: 0 !important;
    margin-left: -0.5rem !important;
  }
  *[dir=rtl] .ml-xl-n2,
*[dir=rtl] .mx-xl-n2 {
    margin-right: -0.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-n3,
*[dir=rtl] .mx-xl-n3 {
    margin-right: 0 !important;
    margin-left: -1rem !important;
  }
  *[dir=rtl] .ml-xl-n3,
*[dir=rtl] .mx-xl-n3 {
    margin-right: -1rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-n4,
*[dir=rtl] .mx-xl-n4 {
    margin-right: 0 !important;
    margin-left: -1.5rem !important;
  }
  *[dir=rtl] .ml-xl-n4,
*[dir=rtl] .mx-xl-n4 {
    margin-right: -1.5rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-n5,
*[dir=rtl] .mx-xl-n5 {
    margin-right: 0 !important;
    margin-left: -3rem !important;
  }
  *[dir=rtl] .ml-xl-n5,
*[dir=rtl] .mx-xl-n5 {
    margin-right: -3rem !important;
    margin-left: 0 !important;
  }
  *[dir=rtl] .mr-xl-auto,
*[dir=rtl] .mx-xl-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ml-xl-auto,
*[dir=rtl] .mx-xl-auto {
    margin-right: auto !important;
  }
}
.ie-custom-properties {
  blue: #20a8d8;
  indigo: #6610f2;
  purple: #6f42c1;
  pink: #e83e8c;
  red: #f86c6b;
  orange: #f8cb00;
  yellow: #ffc107;
  green: #4dbd74;
  teal: #20c997;
  cyan: #17a2b8;
  white: #fff;
  gray: #73818f;
  gray-dark: #2f353a;
  light-blue: #63c2de;
  primary: #20a8d8;
  secondary: #c8ced3;
  success: #4dbd74;
  info: #63c2de;
  warning: #ffc107;
  danger: #f86c6b;
  light: #f0f3f5;
  dark: #2f353a;
  breakpoint-xs: 0;
  breakpoint-sm: 576px;
  breakpoint-md: 768px;
  breakpoint-lg: 992px;
  breakpoint-xl: 1200px;
}
.was-validated .form-control:valid,
.form-control.is-valid {
  background-position: right calc(0.375em + 0.1875rem) center;
}
.was-validated .form-control:invalid,
.form-control.is-invalid {
  background-position: right calc(0.375em + 0.1875rem) center;
}
.auth-login-sign .card.bg-primary {
  border-color: #568d2c;
}
.auth-login-sign .bg-primary {
  background-color: #000 !important;
}
.auth-login-sign .btn {
  border-radius: 5px;
  border: 1px solid #568d2c;
  padding: 0.5rem 1.75rem;
}
.auth-login-sign .btn-primary:not(:disabled):not(.disabled):active, .auth-login-sign .btn-primary:not(:disabled):not(.disabled).active, .auth-login-sign .show > .btn-primary.dropdown-toggle,
.auth-login-sign .btn-primary:hover, .auth-login-sign .btn-primary:focus, .auth-login-sign .btn-success:not(:disabled):not(.disabled):active, .auth-login-sign .btn-success:not(:disabled):not(.disabled).active,
.auth-login-sign .show > .btn-success.dropdown-toggle, .auth-login-sign .btn-success:hover {
  color: #fff;
  background-color: #568d2c;
  border-color: #568d2c;
}
.auth-login-sign .btn-primary, .auth-login-sign .btn-success {
  color: #fff;
  background-color: #568d2c;
  border-color: #568d2c;
  padding: 0.5rem 1.75rem;
  line-height: 2;
  width: 100%;
}
.auth-login-sign .btn-link {
  border-color: transparent;
  color: #568d2c;
}
.auth-login-sign .btn-link:hover {
  color: #568d2c;
  text-decoration: none;
}
.auth-login-sign .btn-primary:not(:disabled):not(.disabled):active:focus,
.auth-login-sign .btn-primary:not(:disabled):not(.disabled).active:focus,
.auth-login-sign .show > .btn-primary.dropdown-toggle:focus,
.auth-login-sign .btn-success:not(:disabled):not(.disabled):active:focus,
.auth-login-sign .btn-success:not(:disabled):not(.disabled).active:focus,
.auth-login-sign .show > .btn-success.dropdown-toggle:focus {
  box-shadow: none;
}
.auth-login-sign .btn-primary:focus, .auth-login-sign .btn-primary.focus,
.auth-login-sign .btn-success:focus, .auth-login-sign .btn-success.focus {
  box-shadow: none;
}
.auth-login-sign .form-control:focus {
  box-shadow: none;
  border-color: #568d2c;
}
.auth-login-sign .input-group:focus .input-group-text,
.auth-login-sign .input-group:hover .input-group-text {
  color: #568d2c;
}
.auth-login-sign .input-group-text i,
.auth-login-sign .input-group-text {
  font-weight: bold;
}
a, a:focus {
  color: #568d2c;
}
a:hover {
  color: #568d2c;
  text-decoration: none;
}
.sidebar .nav-link.active .nav-icon {
  color: #568d2c;
  font-weight: 500;
}
.sidebar .nav-link:hover, .sidebar .nav-link.active:hover .nav-icon {
  color: #fff;
  background: #568d2c;
}
.sidebar .nav-link.active {
  border-bottom: 2px solid #568d2c;
}
.sidebar .nav-link .nav-icon {
  color: #fff;
  font-weight: 500;
}
.page-link, .page-link:hover {
  color: #568d2c;
  font-weight: 500;
}
.page-item.active .page-link {
  background-color: #568d2c;
  border-color: #568d2c;
}
.page-link:focus {
  box-shadow: 0 0 0 0.2rem rgba(86, 141, 44, 0.25);
}
.pagination {
  float: right;
}
.btn-primary {
  color: #fff;
  background-color: #568d2c;
  border-color: #568d2c;
  padding: 0.1rem 0.75rem;
  line-height: 2;
}
.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle, .btn-primary:hover {
  color: #fff;
  background-color: #568d2c;
  border-color: #568d2c;
}
.btn-primary:focus, .btn-primary.focus, .btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(86, 141, 44, 0.25);
}
.form-control:focus {
  border-color: #568d2c;
  box-shadow: 0 0 0 0.2rem rgba(86, 141, 44, 0.25);
}
.table-search {
  float: right;
  width: 30%;
  display: inline-block;
  margin: auto;
}
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}
.slider:before {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  left: 4px;
  bottom: 5px;
  background-color: white;
  transition: 0.4s;
}
input:checked + .slider {
  background-color: #568d2c;
}
input:focus + .slider {
  box-shadow: 0 0 1px #568d2c;
}
input:checked + .slider:before {
  transform: translateX(18px);
}
/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}
.slider.round:before {
  border-radius: 50%;
}
.table th, .table td {
  vertical-align: middle;
}
.badge {
  padding: 7px;
}
.modal-primary .modal-header {
  background-color: #568d2c;
  padding: 0.35rem 0.35rem;
}
.modal-primary .modal-content {
  border-color: #568d2c;
}
.btn-secondary, .btn-danger {
  padding: 0.1rem 0.75rem;
  line-height: 2;
}
.modal-dialog.modal-primary .modal-footer button, .modal-dialog.modal-danger .modal-footer button {
  width: 35%;
  margin: auto;
}
.modal-danger p {
  font-size: 18px;
  text-align: center;
  margin-bottom: 0;
}
.edit-slider input:checked + .slider {
  background-color: #39b2d5;
}
.view-slider input:checked + .slider {
  background-color: #3a9d5d;
}
.delete-slider input:checked + .slider {
  background-color: #f63c3a;
}
.action-slider input:checked + .slider {
  background-color: #ff5722;
}
#select1 {
  width: 25%;
  display: inline-block;
}
app-forgot-password, app-reset-password {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
label {
  font-weight: 500;
}
.spinner-top {
  background: #000000ba;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 10000;
}
.spinner-border {
  position: absolute;
  top: 35%;
  width: 100px;
  height: 100px;
}
.spin-load {
  position: absolute;
  top: 35%;
  width: 100px;
  height: 100px;
}
body {
  position: relative;
}
/* based on angular-toastr css https://github.com/Foxandxss/angular-toastr/blob/cb508fe6801d6b288d3afc525bb40fee1b101650/dist/angular-toastr.css */
/* position */
.toast-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.toast-top-center {
  top: 0;
  right: 0;
  width: 100%;
}
.toast-bottom-center {
  bottom: 0;
  right: 0;
  width: 100%;
}
.toast-top-full-width {
  top: 0;
  right: 0;
  width: 100%;
}
.toast-bottom-full-width {
  bottom: 0;
  right: 0;
  width: 100%;
}
.toast-top-left {
  top: 12px;
  left: 12px;
}
.toast-top-right {
  top: 12px;
  right: 12px;
}
.toast-bottom-right {
  right: 12px;
  bottom: 12px;
}
.toast-bottom-left {
  bottom: 12px;
  left: 12px;
}
/* toast styles */
.toast-title {
  font-weight: bold;
}
.toast-message {
  word-wrap: break-word;
}
.toast-message a,
.toast-message label {
  color: #FFFFFF;
}
.toast-message a:hover {
  color: #CCCCCC;
  text-decoration: none;
}
.toast-close-button {
  position: relative;
  right: -0.3em;
  top: -0.3em;
  float: right;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 1px 0 #ffffff;
  /* opacity: 0.8; */
}
.toast-close-button:hover,
.toast-close-button:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.4;
}
/*Additional properties for button version
 iOS requires the button element instead of an anchor tag.
 If you want the anchor version, it requires `href="#"`.*/
button.toast-close-button {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
}
.toast-container {
  pointer-events: none;
  position: fixed;
  z-index: 999999;
}
.toast-container * {
  box-sizing: border-box;
}
.toast-container .ngx-toastr {
  position: relative;
  overflow: hidden;
  margin: 0 0 6px;
  padding: 15px 15px 15px 50px;
  width: 300px;
  border-radius: 3px 3px 3px 3px;
  background-position: 15px center;
  background-repeat: no-repeat;
  background-size: 24px;
  box-shadow: 0 0 12px #999999;
  color: #FFFFFF;
}
.toast-container .ngx-toastr:hover {
  box-shadow: 0 0 12px #000000;
  opacity: 1;
  cursor: pointer;
}
/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/info-circle.svg */
.toast-info {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOS4wNDMgOCA4IDExOS4wODMgOCAyNTZjMCAxMzYuOTk3IDExMS4wNDMgMjQ4IDI0OCAyNDhzMjQ4LTExMS4wMDMgMjQ4LTI0OEM1MDQgMTE5LjA4MyAzOTIuOTU3IDggMjU2IDh6bTAgMTEwYzIzLjE5NiAwIDQyIDE4LjgwNCA0MiA0MnMtMTguODA0IDQyLTQyIDQyLTQyLTE4LjgwNC00Mi00MiAxOC44MDQtNDIgNDItNDJ6bTU2IDI1NGMwIDYuNjI3LTUuMzczIDEyLTEyIDEyaC04OGMtNi42MjcgMC0xMi01LjM3My0xMi0xMnYtMjRjMC02LjYyNyA1LjM3My0xMiAxMi0xMmgxMnYtNjRoLTEyYy02LjYyNyAwLTEyLTUuMzczLTEyLTEydi0yNGMwLTYuNjI3IDUuMzczLTEyIDEyLTEyaDY0YzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MTAwaDEyYzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MjR6Jy8+PC9zdmc+");
}
/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/times-circle.svg */
.toast-error {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOSA4IDggMTE5IDggMjU2czExMSAyNDggMjQ4IDI0OCAyNDgtMTExIDI0OC0yNDhTMzkzIDggMjU2IDh6bTEyMS42IDMxMy4xYzQuNyA0LjcgNC43IDEyLjMgMCAxN0wzMzggMzc3LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwyNTYgMzEybC02NS4xIDY1LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwxMzQuNCAzMzhjLTQuNy00LjctNC43LTEyLjMgMC0xN2w2NS42LTY1LTY1LjYtNjUuMWMtNC43LTQuNy00LjctMTIuMyAwLTE3bDM5LjYtMzkuNmM0LjctNC43IDEyLjMtNC43IDE3IDBsNjUgNjUuNyA2NS4xLTY1LjZjNC43LTQuNyAxMi4zLTQuNyAxNyAwbDM5LjYgMzkuNmM0LjcgNC43IDQuNyAxMi4zIDAgMTdMMzEyIDI1Nmw2NS42IDY1LjF6Jy8+PC9zdmc+");
}
/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/check.svg */
.toast-success {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTE3My44OTggNDM5LjQwNGwtMTY2LjQtMTY2LjRjLTkuOTk3LTkuOTk3LTkuOTk3LTI2LjIwNiAwLTM2LjIwNGwzNi4yMDMtMzYuMjA0YzkuOTk3LTkuOTk4IDI2LjIwNy05Ljk5OCAzNi4yMDQgMEwxOTIgMzEyLjY5IDQzMi4wOTUgNzIuNTk2YzkuOTk3LTkuOTk3IDI2LjIwNy05Ljk5NyAzNi4yMDQgMGwzNi4yMDMgMzYuMjA0YzkuOTk3IDkuOTk3IDkuOTk3IDI2LjIwNiAwIDM2LjIwNGwtMjk0LjQgMjk0LjQwMWMtOS45OTggOS45OTctMjYuMjA3IDkuOTk3LTM2LjIwNC0uMDAxeicvPjwvc3ZnPg==");
}
/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/exclamation-triangle.svg */
.toast-warning {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NzYgNTEyJyB3aWR0aD0nNTc2JyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTU2OS41MTcgNDQwLjAxM0M1ODcuOTc1IDQ3Mi4wMDcgNTY0LjgwNiA1MTIgNTI3Ljk0IDUxMkg0OC4wNTRjLTM2LjkzNyAwLTU5Ljk5OS00MC4wNTUtNDEuNTc3LTcxLjk4N0wyNDYuNDIzIDIzLjk4NWMxOC40NjctMzIuMDA5IDY0LjcyLTMxLjk1MSA4My4xNTQgMGwyMzkuOTQgNDE2LjAyOHpNMjg4IDM1NGMtMjUuNDA1IDAtNDYgMjAuNTk1LTQ2IDQ2czIwLjU5NSA0NiA0NiA0NiA0Ni0yMC41OTUgNDYtNDYtMjAuNTk1LTQ2LTQ2LTQ2em0tNDMuNjczLTE2NS4zNDZsNy40MTggMTM2Yy4zNDcgNi4zNjQgNS42MDkgMTEuMzQ2IDExLjk4MiAxMS4zNDZoNDguNTQ2YzYuMzczIDAgMTEuNjM1LTQuOTgyIDExLjk4Mi0xMS4zNDZsNy40MTgtMTM2Yy4zNzUtNi44NzQtNS4wOTgtMTIuNjU0LTExLjk4Mi0xMi42NTRoLTYzLjM4M2MtNi44ODQgMC0xMi4zNTYgNS43OC0xMS45ODEgMTIuNjU0eicvPjwvc3ZnPg==");
}
.toast-container.toast-top-center .ngx-toastr,
.toast-container.toast-bottom-center .ngx-toastr {
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}
.toast-container.toast-top-full-width .ngx-toastr,
.toast-container.toast-bottom-full-width .ngx-toastr {
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}
.ngx-toastr {
  background-color: #030303;
  pointer-events: auto;
}
.toast-success {
  background-color: #51A351;
}
.toast-error {
  background-color: #BD362F;
}
.toast-info {
  background-color: #2F96B4;
}
.toast-warning {
  background-color: #F89406;
}
.toast-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 4px;
  background-color: #000000;
  opacity: 0.4;
}
/* Responsive Design */
@media all and (max-width: 240px) {
  .toast-container .ngx-toastr.div {
    padding: 8px 8px 8px 50px;
    width: 11em;
  }

  .toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em;
  }
}
@media all and (min-width: 241px) and (max-width: 480px) {
  .toast-container .ngx-toastr.div {
    padding: 8px 8px 8px 50px;
    width: 18em;
  }

  .toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em;
  }
}
@media all and (min-width: 481px) and (max-width: 768px) {
  .toast-container .ngx-toastr.div {
    padding: 15px 15px 15px 50px;
    width: 25em;
  }
}
