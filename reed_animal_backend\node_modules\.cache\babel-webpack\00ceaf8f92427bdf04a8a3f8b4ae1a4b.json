{"ast": null, "code": "import apply from './_apply.js';\nimport createCtor from './_createCtor.js';\nimport root from './_root.js';\n/** Used to compose bitmasks for function metadata. */\n\nvar WRAP_BIND_FLAG = 1;\n/**\n * Creates a function that wraps `func` to invoke it with the `this` binding\n * of `thisArg` and `partials` prepended to the arguments it receives.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} partials The arguments to prepend to those provided to\n *  the new function.\n * @returns {Function} Returns the new wrapped function.\n */\n\nfunction createPartial(func, bitmask, thisArg, partials) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n      Ctor = createCtor(func);\n\n  function wrapper() {\n    var argsIndex = -1,\n        argsLength = arguments.length,\n        leftIndex = -1,\n        leftLength = partials.length,\n        args = Array(leftLength + argsLength),\n        fn = this && this !== root && this instanceof wrapper ? Ctor : func;\n\n    while (++leftIndex < leftLength) {\n      args[leftIndex] = partials[leftIndex];\n    }\n\n    while (argsLength--) {\n      args[leftIndex++] = arguments[++argsIndex];\n    }\n\n    return apply(fn, isBind ? thisArg : this, args);\n  }\n\n  return wrapper;\n}\n\nexport default createPartial;", "map": null, "metadata": {}, "sourceType": "module"}