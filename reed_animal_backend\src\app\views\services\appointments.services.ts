import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class AppointmentService extends Api {

    //Get All Doctor 
    GetDoctorlist(params: any): Observable<any> {
        const param = params;
        return this.http.get(`${this.config.APIUrl}/Employee`, { params: param });
    }

    //Get Doctor details in Appointment collection
    GetDoctorDetails(id, data: any, start: any, end: any, params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/bookedAppointment/${id}?search=${data}&start=${start}&end=${end}&token=${localStorage.auth_token}`, { params });
    }

    //Delete booked appointment by using appointmnet id 
    DeleteBooked(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/DeleteBookedAppointment/${id}?token=${localStorage.auth_token}`);
    }

    //Get All Doctor Type 
    GetDoctorsList(param: any): Observable<any> {
        const params = param;
        return this.http.get(`${this.config.APIUrl}/Employee`, { params: params });
    }

    //Get the doctor schedule by using doctor id
    GetDoctor(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/app_schedule/${id}?token=${localStorage.auth_token}`);
    }

    //Update the doctor schedule by using doctor id
    UpdateDoctor(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/app_schedule/${id}?token=${localStorage.auth_token}`, data);
    }

    //Get the doctor schedule by using doctor id
    GetAllappointment(data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/allappointment?token=${localStorage.auth_token}`, { params: data });
    }

    //get Past visits by using user id
    GetPastVisit(id: any, data: any): Observable<any> {
        // console.log('res',id)
        return this.http.get(`${this.config.APIUrl3}/v1/PastVisit/${id}?token=${localStorage.auth_token}`, { params: data });
    }
         // https://reedapp.net:3000/api/v1/cms/appointmentDetail/65f1465a20ab5cb675e86e7d

         
    appointmentDetail(id: any, ): Observable<any> {
         // console.log('res',id)
         return this.http.get(`${this.config.APIUrl}/appointmentDetail/${id}?token=${localStorage.auth_token}`);
        }

    getappointment(id:any, data:any):Observable<any>{
        return this.http.get(`${this.config.APIUrl4}/getAppointment/${id}?token=${localStorage.auth_token}`, { params: data })    

    }

    update_appointment(id:any, data:any):Observable<any>{

        return this.http.put(`${this.config.APIUrl4}/appointment/${id}?token=${localStorage.auth_token}`, data);

    }

    update_appointment_Reson(id:any, data:any,param:any):Observable<any>{

        return this.http.put(`${this.config.APIUrl4}/appointment/${id}?token=${localStorage.auth_token}`, data,param);

    }

    getReson(data:any):Observable<any>{
        return this.http.get(`${this.config.APIUrl}/treatment/?token=${localStorage.auth_token}`, { params: data })    
}

getbreed(data:any):Observable<any>{
    return this.http.get(`${this.config.APIUrl}/breeding/?token=${localStorage.auth_token}`, { params: data })  

}
getDoctor(data:any):Observable<any>{
    return this.http.get(`${this.config.APIUrl}/Doctor?search=${data}` ) 
    
}
// uploadPetImage(data:any):Observable<any>{
//     return this.http.post(`${this.config.APIUrl4}/pet/PetImage`, data,{
//         headers:{
//             "Content-Type": "multipart/form-data",
//             "Accept": "application/json"
//         }
//     });
   
// }

uploadFile(data: File): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('file', data);
    return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
        reportProgress: true,
        responseType: 'json'
    });
}

backendappointment(data:any): Observable<any> {
    return this.http.post(`${this.config.APIUrl4}/addCustomAppointment`, data );
}

GetUserSearch(data: any): Observable<any> {

    return this.http.post(`${this.config.APIUrl4}/user/getUsersByEmail?token=${localStorage.auth_token}`, data )    
   
}

emailSearchData(data:any): Observable<any> {
    return this.http.post(`${this.config.APIUrl4}/user/findUserByEmailId`, data );
}

Cancleappointment(id:any,status:any): Observable<any> {
    return this.http.put(`${this.config.APIUrl4}/cancelAppointment/${id}`,status );

}


}