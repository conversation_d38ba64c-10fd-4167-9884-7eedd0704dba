<div class="row" (keydown)="handleKeyboardEvent($event)">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Orders
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body pb-3">
                                <div class="text-value">Today Order's</div>
                                <div>{{TodayOrder}}</div>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white badge-danger">
                            <div class="card-body pb-3">
                                <div class="text-value">Total Order's</div>
                                <div>{{TotalOrder}}</div>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white badge-success">
                            <div class="card-body pb-3">
                                <div class="text-value">Today Payment</div>
                                <div>$ {{TodayAmount.toFixed(2)}}</div>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white badge-dark">
                            <div class="card-body pb-3">
                                <div class="text-value">Total Payment</div>
                                <div>$ {{TotalAmount.toFixed(2)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 my-3">
                        <div class="row">
                            <div class="col-md-6">
                            </div>
                            <div class="col-md-2">
                                <!-- <label>From Date</label>
                <input type="text" placeholder="From Date" class="form-control" bsDatepicker
                  [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY'}" [(ngModel)]="from_date"
                  (ngModelChange)="page=1;GetOrderLists();"> -->
                            </div>

                            <div class="col-md-2">
                                <!-- <label>To Date</label>
                <input type="text" placeholder="To Date" class="form-control" bsDatepicker
                  [bsConfig]="{ isAnimated: true, dateInputFormat: 'MM-DD-YYYY'}" [(ngModel)]="to_date"
                  (ngModelChange)="page=1;GetOrderLists();"> -->
                            </div>
                            <div class="col-md-2 form-group table-search">
                                <label>&nbsp;</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-search"></i></span>
                                    </div>
                                    <input type="text" id="Search" name="Search" placeholder="Search" class="form-control" autocomplete="off" class="form-control" (input)="page=1;GetOrderLists();" [(ngModel)]="name">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer Name</th>
                            <th>Ordered Date</th>
                            <th>Approval</th>
                            <th>Status</th>
                            <th>Total</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of orders| paginate: { id: 'listing_pagination',
               itemsPerPage: 10,
               currentPage: page,
               totalItems: count };">
                            <td>{{user.order_id}}</td>
                            <td>{{user.user_name}}</td>
                            <td>{{user.createdAt |date:'dd MMM yyyy'}}</td>
                            <td>
                                <div>
                                    <span class="badge badge-warning" *ngIf="user.approved==0">pending</span>
                                    <span class="badge badge-success" *ngIf="user.approved==1">Approved</span>
                                    <span class="badge badge-danger" *ngIf="user.approved==2">Declined</span>

                                </div>
                            </td>
                            <td>
                                <div>
                                    <span class="badge badge-warning" *ngIf="user.status==0">In Progress</span>
                                    <span class="badge badge-success" *ngIf="user.status==1">Completed</span>
                                    <span class="badge badge-info" *ngIf="user.status==2">Ready For Pickup</span>
                                    <span class="badge badge-danger" *ngIf="user.status==3">Cancel</span>
                                </div>
                            </td>
                            <td>$ {{user.total_amount}}</td>
                            <td><a href="#/pages/order-details?id={{user.order_id}}" style="cursor: pointer;"><span
                    class="badge badge-success"><i class="fa fa-eye"></i> View</span></a></td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete <strong>All Orders</strong>?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteAll();">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->