<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Breed
            </div>
            <div class="card-body">
                <div class="row">

                    <div class="col-lg-12 my-3">
                        <button type="button" *ngIf="Add" class="btn btn-primary mr-1" data-toggle="modal" (click)="primaryModal.show()">
              Add Breed
            </button>
                        <div class="form-group table-search">
                            <div class="input-group" style="top: 3px;">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" (Change)="GetBreedingLists()"><i class="fa fa-search"></i></span>
                                </div>
                                <input type="text" id="Search" name="Search" placeholder="Search" class="form-control" autocomplete="off" class="form-control" (keydown.enter)="GetBreedingLists()" (input)="GetBreedingLists()" [(ngModel)]="name">
                            </div>
                        </div>

                    </div>
                </div>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Breed Name <i class="fa fa-sort" checked="sort" (click)="Field('name')"></i></th>
                            <th>Breed Code <i class="fa fa-sort" checked="sort" (click)="Field('code')"></i></th>
                            <th>Size <i class="fa fa-sort" checked="sort" (click)="Field('size')"></i></th>
                            <th>Species <i class="fa fa-sort" checked="sort" (click)="Field('type')"></i></th>
                            <!-- <th *ngIf="Edit">Status</th> -->
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of Breedings| paginate: { id: 'listing_pagination',
               itemsPerPage: 10,
               currentPage: page,
               totalItems: count };let i = index;">
                            <td>{{user.name}}</td>
                            <td>{{user.code}}</td>
                            <td>{{user.size}}</td>
                            <td>{{user.type}}</td>
                            <!-- <td *ngIf="Edit"><label class="switch">
                  <input type="checkbox" checked="user.status" (change)="changed(user.status,user._id)"
                    [(ngModel)]="user.status">
                  <span class="slider round"></span>
                </label></td> -->
                            <td>
                                <a data-toggle="modal" *ngIf="Edit" (click)="deleteModal.show();GetBreeding(i);" style="cursor: pointer;"><span class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                <a data-toggle="modal" *ngIf="Delete" (click)="removeModal.show();GetBreeding(user._id);" style="cursor: pointer;"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                    Delete</span></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>

<!-- Add Breeding modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Breed</h4>
            </div>
            <div class="modal-body">
                <form ngNativeValidate>
                    <div class="row">

                        <div class="col-sm-12">

                            <form class="form" [formGroup]="loginForm" autocomplete="off">

                                <div class="form-group">
                                    <label for="species">Species*</label>
                                    <select id="species" name="species" class="form-control" formControlName="species" [ngClass]="{ 'is-invalid': submitted && f.species.errors }">
                    <option value="" disabled selected hidden>--Select--</option>
                    <option *ngFor="let role of Types" [value]="role.name">{{role.name}}</option>
                  </select>
                                    <div *ngIf="submitted && f.species.errors" class="invalid-feedback">
                                        <div *ngIf="f.species.errors.required">Species is mandatory</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="size">Size*</label>
                                    <select id="size" name="size" class="form-control" formControlName="size" [ngClass]="{ 'is-invalid': submitted && f.size.errors }">
                    <option value="" disabled selected hidden>--Select--</option>
                    <option *ngFor="let role of Size" [value]="role">{{role}}</option>
                  </select>
                                    <div *ngIf="submitted && f.size.errors" class="invalid-feedback">
                                        <div *ngIf="f.size.errors.required">Size is mandatory</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="breed">Breed Name*</label>
                                    <input type="text" id="breed" placeholder="e.g. Beagle, Boxer" class="form-control" formControlName="breed" [ngClass]="{ 'is-invalid': submitted && f.breed.errors }">
                                    <div *ngIf="submitted && f.breed.errors" class="invalid-feedback">
                                        <div *ngIf="f.breed.errors.required">Breed name is mandatory</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="code">Breed Code*</label>
                                    <input type="text" id="code" placeholder="e.g. BEAGLE, BOXER" class="form-control" formControlName="code" [ngClass]="{ 'is-invalid': submitted && f.code.errors }">
                                    <div *ngIf="submitted && f.code.errors" class="invalid-feedback">
                                        <div *ngIf="f.code.errors.required">Breed code is mandatory</div>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
                <button type="submit" class="btn btn-primary" (click)="AddBreeding()">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Edit or update Modal -->
<div bsModal #deleteModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Breed</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="loginForm" autocomplete="off">

                            <div class="form-group">
                                <label for="species">Species*</label>
                                <select name="species" class="form-control" formControlName="species" [ngClass]="{ 'is-invalid': submitted && f.species.errors }">
                  <option hidden>--Select--</option>
                  <option *ngFor="let role of Types" [value]="role.name">{{role.name}}</option>
                </select>
                                <div *ngIf="submitted && f.species.errors" class="invalid-feedback">
                                    <div *ngIf="f.species.errors.required">Species is mandatory</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="size">Size*</label>
                                <select name="size" class="form-control" formControlName="size" [ngClass]="{ 'is-invalid': submitted && f.size.errors }">
                  <option value="" disabled selected hidden>--Select--</option>
                  <option *ngFor="let role of Size" [value]="role">{{role}}</option>
                </select>
                                <div *ngIf="submitted && f.size.errors" class="invalid-feedback">
                                    <div *ngIf="f.size.errors.required">Size is mandatory</div>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="breed">Breed Name*</label>
                                <input type="text" placeholder="e.g. Beagle, Boxer" class="form-control" formControlName="breed" [ngClass]="{ 'is-invalid': submitted && f.breed.errors }">
                                <div *ngIf="submitted && f.breed.errors" class="invalid-feedback">
                                    <div *ngIf="f.breed.errors.required">Breed name is mandatory</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="code">Breed Code*</label>
                                <input type="text" placeholder="e.g. BEAGLE, BOXER" class="form-control" formControlName="code" [ngClass]="{ 'is-invalid': submitted && f.code.errors }">
                                <div *ngIf="submitted && f.code.errors" class="invalid-feedback">
                                    <div *ngIf="f.code.errors.required">Breed code is mandatory</div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="deleteModal.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-primary" (click)="EditBreeding(breeding._id)">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Modal -->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Breed?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteBreeding();">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->