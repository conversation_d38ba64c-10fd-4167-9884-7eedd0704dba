{"ast": null, "code": "import baseFor from './_baseFor.js';\nimport keys from './keys.js';\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\n\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\n\nexport default baseForOwn;", "map": null, "metadata": {}, "sourceType": "module"}