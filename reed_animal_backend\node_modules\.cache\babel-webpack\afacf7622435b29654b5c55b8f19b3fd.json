{"ast": null, "code": "/**\n * A specialized version of `_.reduceRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the last element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduceRight(array, iteratee, accumulator, initAccum) {\n  var length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[--length];\n  }\n\n  while (length--) {\n    accumulator = iteratee(accumulator, array[length], length, array);\n  }\n\n  return accumulator;\n}\n\nexport default arrayReduceRight;", "map": null, "metadata": {}, "sourceType": "module"}