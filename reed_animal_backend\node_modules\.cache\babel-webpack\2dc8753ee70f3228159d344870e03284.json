{"ast": null, "code": "import baseMatches from './_baseMatches.js';\nimport baseMatchesProperty from './_baseMatchesProperty.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\nimport property from './property.js';\n/**\n * The base implementation of `_.iteratee`.\n *\n * @private\n * @param {*} [value=_.identity] The value to convert to an iteratee.\n * @returns {Function} Returns the iteratee.\n */\n\nfunction baseIteratee(value) {\n  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.\n  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.\n  if (typeof value == 'function') {\n    return value;\n  }\n\n  if (value == null) {\n    return identity;\n  }\n\n  if (typeof value == 'object') {\n    return isArray(value) ? baseMatchesProperty(value[0], value[1]) : baseMatches(value);\n  }\n\n  return property(value);\n}\n\nexport default baseIteratee;", "map": null, "metadata": {}, "sourceType": "module"}