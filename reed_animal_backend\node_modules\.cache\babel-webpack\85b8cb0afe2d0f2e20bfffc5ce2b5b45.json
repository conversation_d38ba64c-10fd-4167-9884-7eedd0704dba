{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMin = Math.min;\n/**\n * The base implementation of methods like `_.intersection`, without support\n * for iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of shared values.\n */\n\nfunction baseIntersection(arrays, iteratee, comparator) {\n  var includes = comparator ? arrayIncludesWith : arrayIncludes,\n      length = arrays[0].length,\n      othLength = arrays.length,\n      othIndex = othLength,\n      caches = Array(othLength),\n      maxLength = Infinity,\n      result = [];\n\n  while (othIndex--) {\n    var array = arrays[othIndex];\n\n    if (othIndex && iteratee) {\n      array = arrayMap(array, baseUnary(iteratee));\n    }\n\n    maxLength = nativeMin(array.length, maxLength);\n    caches[othIndex] = !comparator && (iteratee || length >= 120 && array.length >= 120) ? new SetCache(othIndex && array) : undefined;\n  }\n\n  array = arrays[0];\n  var index = -1,\n      seen = caches[0];\n\n  outer: while (++index < length && result.length < maxLength) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n    value = comparator || value !== 0 ? value : 0;\n\n    if (!(seen ? cacheHas(seen, computed) : includes(result, computed, comparator))) {\n      othIndex = othLength;\n\n      while (--othIndex) {\n        var cache = caches[othIndex];\n\n        if (!(cache ? cacheHas(cache, computed) : includes(arrays[othIndex], computed, comparator))) {\n          continue outer;\n        }\n      }\n\n      if (seen) {\n        seen.push(computed);\n      }\n\n      result.push(value);\n    }\n  }\n\n  return result;\n}\n\nexport default baseIntersection;", "map": null, "metadata": {}, "sourceType": "module"}