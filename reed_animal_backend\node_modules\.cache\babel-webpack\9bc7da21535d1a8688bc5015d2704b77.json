{"ast": null, "code": "import baseToPairs from './_baseToPairs.js';\nimport getTag from './_getTag.js';\nimport mapToArray from './_mapToArray.js';\nimport setToPairs from './_setToPairs.js';\n/** `Object#toString` result references. */\n\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n/**\n * Creates a `_.toPairs` or `_.toPairsIn` function.\n *\n * @private\n * @param {Function} keysFunc The function to get the keys of a given object.\n * @returns {Function} Returns the new pairs function.\n */\n\nfunction createToPairs(keysFunc) {\n  return function (object) {\n    var tag = getTag(object);\n\n    if (tag == mapTag) {\n      return mapToArray(object);\n    }\n\n    if (tag == setTag) {\n      return setToPairs(object);\n    }\n\n    return baseToPairs(object, keysFunc(object));\n  };\n}\n\nexport default createToPairs;", "map": null, "metadata": {}, "sourceType": "module"}