import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RoleComponent } from './role/role.component';
import { ModuleComponent } from './module/module.component';
import { AnimalTypeComponent } from './animal-type/animal-type.component';
import { TreatmentsComponent } from './treatments/treatments.component';
import { LocationComponent } from './location/location.component';
import { CovertusComponent } from './covertus/covertus.component';
import { EmployeeComponent } from './employee/employee.component';
import { BreedingComponent } from './breeding/breeding.component';
import{AuthGuardService} from '../../authguard'

const routes: Routes = [
  {
    path: '', data: { title: 'Settings' },
    children: [
      { path: 'role', component: RoleComponent, data: { title: 'Role',path: '/settings/role' },canActivate: [AuthGuardService] },
      { path: 'module', component: ModuleComponent, data: { title: 'Module',path: '/settings' },canActivate: [AuthGuardService] },
      { path: 'animal-type', component: AnimalTypeComponent, data: { title: 'Species',path: '/settings/animal-type' },canActivate: [AuthGuardService] },
      { path: 'appointment-types', component: TreatmentsComponent, data: { title: 'Appointment Types',path: '/settings/appointment-types' },canActivate: [AuthGuardService] },
      { path: 'breed', component: BreedingComponent, data: { title: 'Breed',path: '/settings/breed' },canActivate: [AuthGuardService] },
      { path: 'location', component: LocationComponent, data: { title: 'Location',path: '/settings/location' } },
      { path: 'covetrus', component: CovertusComponent, data: { title: 'Covetrus',path: '/settings/covetrus' } ,canActivate: [AuthGuardService]},
      { path: 'employee', component: EmployeeComponent, data: { title: 'Admin Users',path: '/settings/employee' },canActivate: [AuthGuardService] },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MasterRoutingModule { }