<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <form action="" class="form" [formGroup]="Bannerform" autocomplete="off">
                <div *ngIf="Add_button" class="card-header">
                    Add Banners
                </div>
                <div *ngIf="Edit_button" class="card-header">
                    Edit Banners
                </div>

                <h5 style="color:#568d2c; text-align: center;margin-top: 20px;">Upload Banners</h5>
                <input type="file" id="file" class="form-control errorfileval" formControlName="banner" (change)="onSelectFile($event)" accept=".jpeg,.jpg,.png" />
                <label for="file" class="btn-2">upload</label>

                <div>
                    <img style="width: 250px; position: relative;left: 38%;" [src]="url">
                </div>
                <div>
                    <p style="color: #f86c6b; text-align: center;font-size: 80%; margin-top: -30px;" *ngIf="validation">Image is required*</p>
                </div>


                <div>



                    <div class="card" style="text-align: center;width: 50%;margin-left: 25%; margin-top: 20px;">

                        <div class="card-header">
                            <div class="input-group" style="top: 3px; width: 50%; margin-left: 25%;">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-search"></i></span>
                                </div>

                                <!-- <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" (change)="page=1;searchdata=$event.target.value;  getallproduct()" [(ngModel)]="searchdata" /> -->

                                <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" [ngModel]="name" (input)="page=1; searchChangeEvent($event)" />


                            </div>
                        </div>


                        <!-- <div class="input-group" style="top: 3px;">
                            <div class="input-group-prepend">
                                <span class="input-group-text" (click)="page=1; ListProduct()"><i class="fa fa-search"></i></span>
                            </div>
                            <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" (input)="page=1; ListProduct()" [(ngModel)]="name" />
                        </div> -->


                        <table class="table table-striped" *ngFor="let product of getallproducts ;let i = index">
                            <thead>
                                <tr>
                                    <td style="text-align:left;">{{product.title}}
                                        <span style="float: right;" (click)="addproduct(product,i)"><i class="fa fa-plus"></i></span>
                                    </td>


                                </tr>
                            </thead>
                        </table>

                    </div>





                    <div class="card-body">
                        <div class="row">

                            <div class="col-md-6">
                                <div class="col-md-12 form-group table-search" style="width:50%;">
                                    <label style="visibility: hidden;margin: 0;"> &nbsp;</label>
                                    <div class="input-group" style="top: 3px;">
                                        <div class="input-group-prepend">

                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>

                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product Name</th>

                                    <th>SKU</th>
                                    <th>Product Image</th>
                                    <th>Category</th>
                                    <th>Brand</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let data of this.splice_list| paginate: { id: 'listing_pagination',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };let i = index;">

                                    <td>{{data.title}}</td>
                                    <td>{{data.sku}}</td>
                                    <td><img style="width: 50px;" src="{{data.poster_image}}" alt=""></td>
                                    <td>{{data.category}}</td>
                                    <td>{{data.brand}}</td>
                                    <td>{{data.status===true ? "Active":"In-Active"}}</td>
                                    <td (click)="revocket(data,i)"><i class="fa fa-trash"></i></td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="width:100%;">
                            <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                            </pagination-controls>
                        </div>
                    </div>


                </div>
            </form>

            <button class="btn btn-primary" *ngIf="Add_button" style="display: block;
            width: 200px;
            margin: auto;margin-bottom: 20px;" (click)="onsubmit('add')">Save</button>


            <button class="btn btn-primary" *ngIf="Edit_button" style="display: block;
            width: 200px;
            margin: auto;margin-bottom: 20px;" (click)="onsubmit('edit')">Save</button>




        </div>
    </div>

    <!--/.col-->
</div>