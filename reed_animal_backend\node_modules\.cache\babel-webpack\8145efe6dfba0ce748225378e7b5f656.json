{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tamil [ta]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/tk120404\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var symbolMap = {\n    1: '௧',\n    2: '௨',\n    3: '௩',\n    4: '௪',\n    5: '௫',\n    6: '௬',\n    7: '௭',\n    8: '௮',\n    9: '௯',\n    0: '௦'\n  },\n      numberMap = {\n    '௧': '1',\n    '௨': '2',\n    '௩': '3',\n    '௪': '4',\n    '௫': '5',\n    '௬': '6',\n    '௭': '7',\n    '௮': '8',\n    '௯': '9',\n    '௦': '0'\n  };\n  var ta = moment.define<PERSON>ocale('ta', {\n    months: 'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split('_'),\n    monthsShort: 'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split('_'),\n    weekdays: 'ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை'.split('_'),\n    weekdaysShort: 'ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி'.split('_'),\n    weekdaysMin: 'ஞா_தி_செ_பு_வி_வெ_ச'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, HH:mm',\n      LLLL: 'dddd, D MMMM YYYY, HH:mm'\n    },\n    calendar: {\n      sameDay: '[இன்று] LT',\n      nextDay: '[நாளை] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[நேற்று] LT',\n      lastWeek: '[கடந்த வாரம்] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s இல்',\n      past: '%s முன்',\n      s: 'ஒரு சில விநாடிகள்',\n      ss: '%d விநாடிகள்',\n      m: 'ஒரு நிமிடம்',\n      mm: '%d நிமிடங்கள்',\n      h: 'ஒரு மணி நேரம்',\n      hh: '%d மணி நேரம்',\n      d: 'ஒரு நாள்',\n      dd: '%d நாட்கள்',\n      M: 'ஒரு மாதம்',\n      MM: '%d மாதங்கள்',\n      y: 'ஒரு வருடம்',\n      yy: '%d ஆண்டுகள்'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}வது/,\n    ordinal: function (number) {\n      return number + 'வது';\n    },\n    preparse: function (string) {\n      return string.replace(/[௧௨௩௪௫௬௭௮௯௦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // refer http://ta.wikipedia.org/s/1er1\n    meridiemParse: /யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 2) {\n        return ' யாமம்';\n      } else if (hour < 6) {\n        return ' வைகறை'; // வைகறை\n      } else if (hour < 10) {\n        return ' காலை'; // காலை\n      } else if (hour < 14) {\n        return ' நண்பகல்'; // நண்பகல்\n      } else if (hour < 18) {\n        return ' எற்பாடு'; // எற்பாடு\n      } else if (hour < 22) {\n        return ' மாலை'; // மாலை\n      } else {\n        return ' யாமம்';\n      }\n    },\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n\n      if (meridiem === 'யாமம்') {\n        return hour < 2 ? hour : hour + 12;\n      } else if (meridiem === 'வைகறை' || meridiem === 'காலை') {\n        return hour;\n      } else if (meridiem === 'நண்பகல்') {\n        return hour >= 10 ? hour : hour + 12;\n      } else {\n        return hour + 12;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n\n    }\n  });\n  return ta;\n});", "map": null, "metadata": {}, "sourceType": "script"}