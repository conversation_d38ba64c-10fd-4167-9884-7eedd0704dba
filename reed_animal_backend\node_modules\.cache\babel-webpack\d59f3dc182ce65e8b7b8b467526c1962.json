{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport isArrayLike from './isArrayLike.js';\nimport keys from './keys.js';\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\n\nfunction createFind(findIndexFunc) {\n  return function (collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n\n      predicate = function (key) {\n        return iteratee(iterable[key], key, iterable);\n      };\n    }\n\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nexport default createFind;", "map": null, "metadata": {}, "sourceType": "module"}