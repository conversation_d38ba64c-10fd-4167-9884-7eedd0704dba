{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function bufferWhen(closingSelector) {\n  return function (source) {\n    return source.lift(new <PERSON><PERSON>er<PERSON>henOperator(closingSelector));\n  };\n}\n\nclass BufferWhenOperator {\n  constructor(closingSelector) {\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n  }\n\n}\n\nclass Buffer<PERSON>henSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, closingSelector) {\n    super(destination);\n    this.closingSelector = closingSelector;\n    this.subscribing = false;\n    this.openBuffer();\n  }\n\n  _next(value) {\n    this.buffer.push(value);\n  }\n\n  _complete() {\n    const buffer = this.buffer;\n\n    if (buffer) {\n      this.destination.next(buffer);\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    this.buffer = undefined;\n    this.subscribing = false;\n  }\n\n  notifyNext() {\n    this.openBuffer();\n  }\n\n  notifyComplete() {\n    if (this.subscribing) {\n      this.complete();\n    } else {\n      this.openBuffer();\n    }\n  }\n\n  openBuffer() {\n    let {\n      closingSubscription\n    } = this;\n\n    if (closingSubscription) {\n      this.remove(closingSubscription);\n      closingSubscription.unsubscribe();\n    }\n\n    const buffer = this.buffer;\n\n    if (this.buffer) {\n      this.destination.next(buffer);\n    }\n\n    this.buffer = [];\n    let closingNotifier;\n\n    try {\n      const {\n        closingSelector\n      } = this;\n      closingNotifier = closingSelector();\n    } catch (err) {\n      return this.error(err);\n    }\n\n    closingSubscription = new Subscription();\n    this.closingSubscription = closingSubscription;\n    this.add(closingSubscription);\n    this.subscribing = true;\n    closingSubscription.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));\n    this.subscribing = false;\n  }\n\n} //# sourceMappingURL=bufferWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}