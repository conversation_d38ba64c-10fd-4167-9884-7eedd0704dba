import { Component, OnInit, ViewChild, } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Module } from '../../models/module.models';
import { TokenStorageService } from '../../services/token-storage.service';
import { PelfieService } from '../../services/pelfies.services';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import * as moment from "moment";

@Component({
  selector: 'app-pelfies',
  templateUrl: './pelfies.component.html',
  styleUrls: ['./pelfies.component.scss']
})
export class PelfiesComponent implements OnInit {
  @ViewChild('PosterModal') public PosterModal: ModalDirective;

  Pelfies = []
  page = 1;
  count = 0;
  Add = true
  Edit = true
  Delete = true
  name = ''
  today = []
  total = ''
  image = ''
  constructor(private Pelfieservice: PelfieService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // console.log(Role.role_id._id)
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Pelfies") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.GetPeflies();
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //Get all pelfies
  GetPeflies() {
    const skip = this.getrequestparams(this.page);
    this.Pelfieservice.GetPelfiesList(this.name, skip)
      .subscribe((res: any) => {
        // console.log(res)
        this.Pelfies = res.data
        this.count = res.count
        if (this.name == '') {
          this.total = res.count
          res.data.filter(item => {
            var d = moment(item.createdAt)
            var dat = moment(d).format("YYYY-MM-DD")
            if (dat == moment().utc().format("YYYY-MM-DD")) {
              this.today.push(item)
            }
          })
        }
      })
  }

  //Showing data
  Showing(param) {
    // if(param='today'){
    //   this.name=moment().utc().format("YYYY-MM-DD")
    //   console.log(this.name)
    //   this.GetPeflies()
    // }else{
    //   this.name=''
    //   this.GetPeflies()
    // }

  }
  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    this.GetPeflies()
  }

  //block and unblock button
  Status(Id, Status) {
    var data = {
      status: Status
    }
    this.Pelfieservice.UpdatePelfie(Id, data)
      .subscribe((res: any) => {
      })
  }

  //view image
  Image(index) {
    this.image = this.Pelfies[index].image_url
    this.PosterModal.show();
  }


}
