{"ast": null, "code": "/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n/**\n * The base implementation of `_.delay` and `_.defer` which accepts `args`\n * to provide to `func`.\n *\n * @private\n * @param {Function} func The function to delay.\n * @param {number} wait The number of milliseconds to delay invocation.\n * @param {Array} args The arguments to provide to `func`.\n * @returns {number|Object} Returns the timer id or timeout object.\n */\n\nfunction baseDelay(func, wait, args) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n\n  return setTimeout(function () {\n    func.apply(undefined, args);\n  }, wait);\n}\n\nexport default baseDelay;", "map": null, "metadata": {}, "sourceType": "module"}