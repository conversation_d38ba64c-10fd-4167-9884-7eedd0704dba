{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\nvar arrayBufferTag = '[object ArrayBuffer]';\n/**\n * The base implementation of `_.isArrayBuffer` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array buffer, else `false`.\n */\n\nfunction baseIsArrayBuffer(value) {\n  return isObjectLike(value) && baseGetTag(value) == arrayBufferTag;\n}\n\nexport default baseIsArrayBuffer;", "map": null, "metadata": {}, "sourceType": "module"}