{"ast": null, "code": "export const navItems = [// {\n//   name: 'Dashboard',\n//   url: '/dashboard',\n//   icon: 'icon-speedometer',\n//   // badge: {\n//   //   variant: 'info',\n//   //   text: 'NEW'\n//   // }\n// },\n{\n  name: 'Customers',\n  url: '/pages/customers',\n  icon: 'fa fa-group'\n}, {\n  name: 'Appointments',\n  url: '/pages/appointments',\n  icon: 'cil-calendar-check'\n}, {\n  name: 'Availability',\n  url: '/pages/availability',\n  icon: 'fa fa-calendar'\n}, {\n  name: 'Resources',\n  url: '/pages/resources',\n  icon: 'cil-puzzle'\n}, {\n  name: 'Pelfies',\n  url: '/pages/pelfies',\n  icon: 'fa fa-picture-o'\n}, {\n  name: 'Shopping',\n  url: '/pages/shopping',\n  icon: 'fa fa-shopping-cart',\n  children: [{\n    name: 'Products',\n    url: '/pages/shopping',\n    icon: 'fa fa-id-card'\n  }, {\n    name: 'Banners',\n    url: '/pages/banners',\n    icon: 'fa fa-id-card'\n  }, {\n    name: 'Shop Setting',\n    url: '/pages/shop-setting',\n    icon: 'fa fa-id-card'\n  }]\n}, {\n  name: 'Orders',\n  url: '/pages/orders',\n  icon: 'cil-task'\n}, // {\n//   name: 'Report',\n//   url: '/pages/report',\n//   icon: 'fa fa-file-text-o'\n// },\n{\n  name: 'Change Password',\n  url: '/pages/change-password',\n  icon: 'fa fa-key'\n}, {\n  name: 'Settings',\n  url: '/settings',\n  icon: 'fa fa-mortar-board',\n  children: [{\n    name: 'Admin Users',\n    url: '/settings/employee',\n    icon: 'fa fa-id-card'\n  }, {\n    name: 'Role',\n    url: '/settings/role',\n    icon: 'fa fa-cogs'\n  }, // {\n  //   name: 'Module',\n  //   url: '/settings/module',\n  //   icon: 'fa fa-cubes'\n  // },\n  // {\n  //   name: 'Permission',\n  //   url: '/settings/permission',\n  //   icon: 'fa fa-unlock-alt'\n  // },\n  // {\n  //   name: 'Doctor',\n  //   url: '/settings/doctor',\n  //   icon: 'fa fa-hospital-o'\n  // },\n  {\n    name: 'Species',\n    url: '/settings/animal-type',\n    icon: 'fa fa-paw'\n  }, {\n    name: 'Appointment Types',\n    url: '/settings/appointment-types',\n    icon: 'fa fa-user-md'\n  }, {\n    name: 'Location',\n    url: '/settings/location',\n    icon: 'fa fa-location-arrow'\n  }, {\n    name: 'Breed',\n    url: '/settings/breed',\n    icon: 'fa fa-paw'\n  }, {\n    name: 'Covetrus',\n    url: '/settings/covetrus',\n    icon: 'fa fa-unlock-alt'\n  }]\n}];", "map": null, "metadata": {}, "sourceType": "module"}