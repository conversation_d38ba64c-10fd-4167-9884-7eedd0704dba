{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kyrgyz [ky]\n//! author : Chyngyz Arystan uulu : https://github.com/chyngyz\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var suffixes = {\n    0: '-чү',\n    1: '-чи',\n    2: '-чи',\n    3: '-чү',\n    4: '-чү',\n    5: '-чи',\n    6: '-чы',\n    7: '-чи',\n    8: '-чи',\n    9: '-чу',\n    10: '-чу',\n    20: '-чы',\n    30: '-чу',\n    40: '-чы',\n    50: '-чү',\n    60: '-чы',\n    70: '-чи',\n    80: '-чи',\n    90: '-чу',\n    100: '-чү'\n  };\n  var ky = moment.defineLocale('ky', {\n    months: 'январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь'.split('_'),\n    monthsShort: 'янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби'.split('_'),\n    weekdaysShort: 'Жек_Дүй_Шей_Шар_Бей_Жум_Ише'.split('_'),\n    weekdaysMin: 'Жк_Дй_Шй_Шр_Бй_Жм_Иш'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бүгүн саат] LT',\n      nextDay: '[Эртең саат] LT',\n      nextWeek: 'dddd [саат] LT',\n      lastDay: '[Кечээ саат] LT',\n      lastWeek: '[Өткөн аптанын] dddd [күнү] [саат] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ичинде',\n      past: '%s мурун',\n      s: 'бирнече секунд',\n      ss: '%d секунд',\n      m: 'бир мүнөт',\n      mm: '%d мүнөт',\n      h: 'бир саат',\n      hh: '%d саат',\n      d: 'бир күн',\n      dd: '%d күн',\n      M: 'бир ай',\n      MM: '%d ай',\n      y: 'бир жыл',\n      yy: '%d жыл'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(чи|чы|чү|чу)/,\n    ordinal: function (number) {\n      var a = number % 10,\n          b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n\n    }\n  });\n  return ky;\n});", "map": null, "metadata": {}, "sourceType": "script"}