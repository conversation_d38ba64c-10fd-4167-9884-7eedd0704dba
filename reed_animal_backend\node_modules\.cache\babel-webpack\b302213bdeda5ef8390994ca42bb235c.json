{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Albanian [sq]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/flakerimi\n//! author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n//! author : <PERSON><PERSON> : https://github.com/oerd\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var sq = moment.defineLocale('sq', {\n    months: 'Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor'.split('_'),\n    monthsShort: 'Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj'.split('_'),\n    weekdays: 'E Diel_E Hënë_E Mart<PERSON>_E <PERSON>ë_E Enjte_E Premte_E Shtunë'.split('_'),\n    weekdaysShort: 'Die_Hën_Mar_Mër_Enj_Pre_Sht'.split('_'),\n    weekdaysMin: 'D_H_Ma_Më_E_P_Sh'.split('_'),\n    weekdaysParseExact: true,\n    meridiemParse: /PD|MD/,\n    isPM: function (input) {\n      return input.charAt(0) === 'M';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      return hours < 12 ? 'PD' : 'MD';\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Sot në] LT',\n      nextDay: '[Nesër në] LT',\n      nextWeek: 'dddd [në] LT',\n      lastDay: '[Dje në] LT',\n      lastWeek: 'dddd [e kaluar në] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'në %s',\n      past: '%s më parë',\n      s: 'disa sekonda',\n      ss: '%d sekonda',\n      m: 'një minutë',\n      mm: '%d minuta',\n      h: 'një orë',\n      hh: '%d orë',\n      d: 'një ditë',\n      dd: '%d ditë',\n      M: 'një muaj',\n      MM: '%d muaj',\n      y: 'një vit',\n      yy: '%d vite'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return sq;\n});", "map": null, "metadata": {}, "sourceType": "script"}