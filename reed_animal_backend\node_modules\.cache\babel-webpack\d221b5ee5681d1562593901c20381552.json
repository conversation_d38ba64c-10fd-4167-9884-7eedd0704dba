{"ast": null, "code": "import isPrototype from './_isPrototype.js';\nimport nativeKeys from './_nativeKeys.js';\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\n\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n\n  var result = [];\n\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n\n  return result;\n}\n\nexport default baseKeys;", "map": null, "metadata": {}, "sourceType": "module"}