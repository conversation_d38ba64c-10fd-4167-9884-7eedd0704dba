<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Banners
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 my-3">
                        <button type="button" (click)="addbanner()" class="btn btn-primary mr-1">
              Add Banners
            </button>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12 form-group table-search" style="width:50%;">
                            <label style="visibility: hidden;margin: 0;"> &nbsp;</label>
                            <div class="input-group" style="top: 3px;">
                                <div class="input-group-prepend">

                                </div>

                            </div>
                        </div>
                    </div>

                </div>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Banner Image</th>

                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of Products| paginate: { id: 'listing_pagination',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };let i = index;">

                            <td> <img style="width: 70px;" src="{{user.image_url}}" alt=""></td>


                            <td><label class="switch">
                  <input type="checkbox" (change)="changed($event,user._id);event($event);"
                  [checked]="user.status">
                  <span class="slider round"></span>
                </label></td>
                            <td>
                                <a data-toggle="modal" (click)="EditProduct(user._id);" style="cursor: pointer; margin-right: 10px;"><span
                    class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                <a style="cursor: pointer;" (click)="GetProductById(i,'Delete')"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                    Delete</span></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="width:100%;">
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>

<!-- Delete Modal-->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Product?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteProduct(id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->