<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Change Password
            </div>
            <div class="card-body">
                <div class="row">
                <div class="col-sm-4">  </div>
                <div class="col-sm-4">  
                <div class="input-group mb-4">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="Old Password" formControlName="password"/>
                    <!-- <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                      <div *ngIf="f.password.errors.required">*Password cannot be empty</div>
                      <div *ngIf="f.password.errors.minlength">*Password must be at least 8 characters</div>
                    </div> -->
                  </div>
                  <div class="input-group mb-4">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="New Password" formControlName="password"/>
                    <!-- <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                      <div *ngIf="f.password.errors.required">*Password cannot be empty</div>
                      <div *ngIf="f.password.errors.minlength">*Password must be at least 8 characters</div>
                    </div> -->
                  </div>
                  </div>
                  <div class="col-sm-4">  </div>
            </div>
            </div>
        </div>
    </div>
</div>