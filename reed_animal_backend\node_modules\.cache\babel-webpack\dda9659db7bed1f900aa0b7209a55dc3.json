{"ast": null, "code": "import Set from './_Set.js';\nimport noop from './noop.js';\nimport setToArray from './_setToArray.js';\n/** Used as references for various `Number` constants. */\n\nvar INFINITY = 1 / 0;\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\n\nvar createSet = !(Set && 1 / setToArray(new Set([, -0]))[1] == INFINITY) ? noop : function (values) {\n  return new Set(values);\n};\nexport default createSet;", "map": null, "metadata": {}, "sourceType": "module"}