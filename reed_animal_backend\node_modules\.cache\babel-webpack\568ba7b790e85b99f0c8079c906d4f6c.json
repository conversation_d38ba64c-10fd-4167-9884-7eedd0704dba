{"ast": null, "code": "import baseIsNative from './_baseIsNative.js';\nimport getValue from './_getValue.js';\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\n\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nexport default getNative;", "map": null, "metadata": {}, "sourceType": "module"}