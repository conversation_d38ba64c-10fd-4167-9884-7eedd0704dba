{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\n/**\n * Reverses the direction of lazy iteration.\n *\n * @private\n * @name reverse\n * @memberOf LazyWrapper\n * @returns {Object} Returns the new reversed `LazyWrapper` object.\n */\n\nfunction lazyReverse() {\n  if (this.__filtered__) {\n    var result = new LazyWrapper(this);\n    result.__dir__ = -1;\n    result.__filtered__ = true;\n  } else {\n    result = this.clone();\n    result.__dir__ *= -1;\n  }\n\n  return result;\n}\n\nexport default lazyReverse;", "map": null, "metadata": {}, "sourceType": "module"}