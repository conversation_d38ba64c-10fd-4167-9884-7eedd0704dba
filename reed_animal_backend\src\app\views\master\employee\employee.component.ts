import { Component, OnInit, ViewChild, } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Employee } from '../../models/employee.models';
import { TokenStorageService } from '../../services/token-storage.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { PermissionService } from '../../services/permission.service';
import { LocationService } from '../../services/location.sevices';

@Component({
  selector: 'app-employee',
  templateUrl: './employee.component.html',
  styleUrls: ['./employee.component.scss']
})
export class EmployeeComponent implements OnInit {

  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  employees = [];
  viewlog=[];
  roles = [];
  locations = []
  page = 1;
  count = 0;
  search = '';
  name = '';
  email = false
  phone_no='';
  loginForm: FormGroup;
  submitted = false;
  Add = true;
  Edit = true;
  Delete = true;
  public location = '';
  public EditId = '';
  public Index = 0;
  filteredLocations: any[];
  constructor(private employeeService: Employeeservice, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private EmployeeService: Employeeservice, private formBuilder: FormBuilder, private Permission: PermissionService, private LocationService: LocationService) {
    this.GetRoleLists();
    this.GetLocation();
    this.GetEmployeeLists();
  }

  ngOnInit(): void {
    this.SignForm();
    
    

    const Role = this.tokenStorage.getUser();
    // if (key != null) {
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Admin-Users") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
  }

  //clear modal window
  clear(): void {
    this.email = false
    this.loginForm.reset();
    this.submitted = false;
  }

  //Get All Role List
  GetRoleLists(): void {
    this.employeeService.GetRoleList()
      .subscribe((res: any) => {
        this.roles = res.data;
      });
  }

  RemoveEmpText(){
    console.log("phone test");
    this.phone_no='';
  }

  //Get location list
  GetLocation() {
    const data = ''
    const param = ''
    this.LocationService.GetLocationsList(data, param)
      .subscribe((res: any) => {
        this.locations = res.data

        this.locations.filter(item => item.status === true)
        
        this.filteredLocations = this.locations.filter(item => item.status === true);
        console.log("testing locationnnnnnnnn",this.filteredLocations )
      })
      
      for (let item of this.locations){
        console.log("forloop",item[0])
      }
      
  }

  //Get All Role List
  GetEmployeeLists(): void {
    const data = {
      skip: (this.page - 1) * 10,
      search: this.name,
      location: this.location
    }
    this.employeeService.GetEmployeeList(data)
      .subscribe((res: any) => {
        console.log("res",res);
        this.count = res.count;
        this.employees = res.data;
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    this.GetEmployeeLists();
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.pattern('[a-zA-Z. -_]*$')]],
      role_id: [''],
      role_name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
      location: ['', [Validators.required]],
      address: ['', [Validators.minLength(12)]],
      phone_no: [[Validators.minLength(8), Validators.pattern('[0-9-]*$')]]
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  //Add New Employee
  onSubmit(): void {
    console.log(this.loginForm.value)
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    if (this.EditId == '') {
      this.employeeService.NewEmployee(this.loginForm.value).subscribe((res) => {
        if (res.code == 200) {
          this.submitted = false;
          this.loginForm.reset();
          this.primaryModal.hide();
          this.GetRoleLists();
        } else {
          this.email = true;
        }
      })
    } else {
      this.UpdateEmployee(this.EditId, this.loginForm.value);
    }
  }

  UpdateEmployee(Id, data): void {
    this.employeeService.EditEmployeeDetail(Id, data).subscribe((res: any) => {
      if (res.code == 200) {
        this.submitted = false;
        this.loginForm.reset();
        this.primaryModal.hide();
        if (this.EditId != '')
          this.GetEmployeeLists();
      }
    })
  }

  ViewLog(id): void{
    console.log("Admin id",id);
    this.employeeService.GetViewLog(id).subscribe((res)=>{
      this.viewlog=res.data;
      console.log(this.viewlog);
    

    })
  }
  

  EditEmployee(): void {
    this.loginForm.controls['name'].setValue(this.employees[this.Index].name);
    this.loginForm.controls['role_id'].setValue(this.employees[this.Index].role_id);
    this.loginForm.controls['role_name'].setValue(this.employees[this.Index].role_name);
    this.loginForm.controls['email'].setValue(this.employees[this.Index].email);
    this.loginForm.controls['location'].setValue(this.employees[this.Index].location);
    this.loginForm.controls['address'].setValue(this.employees[this.Index].address);
    this.loginForm.controls['phone_no'].setValue(this.employees[this.Index].phone_no);
  }

  //Delete Employee
  DeleteEmployee(id): void {
    this.employeeService.DeleteEmployee(id).subscribe((res) => {
      this.removeModal.hide();
      this.GetEmployeeLists();
    })
  }

  //ON or OFF value in table
  changed(param, id): void {
    const data = { status: param }
    this.UpdateEmployee(id, data);
  }

  RoleSetValue(event): void {
    this.roles.forEach(element => {
      if (element.name === event) {
        this.loginForm.controls['role_id'].setValue(element._id);
      }
    })
  }
}
