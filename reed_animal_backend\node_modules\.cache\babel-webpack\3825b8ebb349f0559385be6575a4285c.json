{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport isFunction from './isFunction.js';\n/**\n * The base implementation of `_.functions` which creates an array of\n * `object` function property names filtered from `props`.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Array} props The property names to filter.\n * @returns {Array} Returns the function names.\n */\n\nfunction baseFunctions(object, props) {\n  return arrayFilter(props, function (key) {\n    return isFunction(object[key]);\n  });\n}\n\nexport default baseFunctions;", "map": null, "metadata": {}, "sourceType": "module"}