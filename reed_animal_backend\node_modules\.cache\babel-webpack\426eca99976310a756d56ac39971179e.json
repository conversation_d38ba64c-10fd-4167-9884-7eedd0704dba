{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\n\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return !start && end >= length ? array : baseSlice(array, start, end);\n}\n\nexport default castSlice;", "map": null, "metadata": {}, "sourceType": "module"}