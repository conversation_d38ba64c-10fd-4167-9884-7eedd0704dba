{"ast": null, "code": "import isSymbol from './isSymbol.js';\n/** Used as references for various `Number` constants. */\n\nvar INFINITY = 1 / 0;\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\n\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n\n  var result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result;\n}\n\nexport default toKey;", "map": null, "metadata": {}, "sourceType": "module"}