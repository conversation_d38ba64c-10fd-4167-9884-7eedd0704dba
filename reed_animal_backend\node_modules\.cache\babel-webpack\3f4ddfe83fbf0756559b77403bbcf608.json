{"ast": null, "code": "/** Used to match words composed of alphanumeric characters. */\nvar reAsciiWord = /[^\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7f]+/g;\n/**\n * Splits an ASCII `string` into an array of its words.\n *\n * @private\n * @param {string} The string to inspect.\n * @returns {Array} Returns the words of `string`.\n */\n\nfunction asciiWords(string) {\n  return string.match(reAsciiWord) || [];\n}\n\nexport default asciiWords;", "map": null, "metadata": {}, "sourceType": "module"}