{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport baseLodash from './_baseLodash.js';\n/** Used as references for the maximum length and index of an array. */\n\nvar MAX_ARRAY_LENGTH = 4294967295;\n/**\n * Creates a lazy wrapper object which wraps `value` to enable lazy evaluation.\n *\n * @private\n * @constructor\n * @param {*} value The value to wrap.\n */\n\nfunction LazyWrapper(value) {\n  this.__wrapped__ = value;\n  this.__actions__ = [];\n  this.__dir__ = 1;\n  this.__filtered__ = false;\n  this.__iteratees__ = [];\n  this.__takeCount__ = MAX_ARRAY_LENGTH;\n  this.__views__ = [];\n} // Ensure `LazyWrapper` is an instance of `baseLodash`.\n\n\nLazyWrapper.prototype = baseCreate(baseLodash.prototype);\nLazyWrapper.prototype.constructor = LazyWrapper;\nexport default LazyWrapper;", "map": null, "metadata": {}, "sourceType": "module"}