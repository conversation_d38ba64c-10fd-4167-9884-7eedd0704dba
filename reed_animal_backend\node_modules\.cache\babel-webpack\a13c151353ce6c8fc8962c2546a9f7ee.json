{"ast": null, "code": "import castPath from './_castPath.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isIndex from './_isIndex.js';\nimport isLength from './isLength.js';\nimport toKey from './_toKey.js';\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\n\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n  var index = -1,\n      length = path.length,\n      result = false;\n\n  while (++index < length) {\n    var key = toKey(path[index]);\n\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n\n    object = object[key];\n  }\n\n  if (result || ++index != length) {\n    return result;\n  }\n\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) && (isArray(object) || isArguments(object));\n}\n\nexport default hasPath;", "map": null, "metadata": {}, "sourceType": "module"}