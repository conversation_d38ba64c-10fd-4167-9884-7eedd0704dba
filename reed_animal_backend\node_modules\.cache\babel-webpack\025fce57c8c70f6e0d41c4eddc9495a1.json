{"ast": null, "code": "import coreJsData from './_coreJsData.js';\n/** Used to detect methods masquerading as native. */\n\nvar maskSrcKey = function () {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? 'Symbol(src)_1.' + uid : '';\n}();\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\n\n\nfunction isMasked(func) {\n  return !!maskSrcKey && maskSrcKey in func;\n}\n\nexport default isMasked;", "map": null, "metadata": {}, "sourceType": "module"}