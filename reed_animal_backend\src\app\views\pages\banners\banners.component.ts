import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductService } from '../../services/product.services'
import { Subscription } from 'rxjs';
import * as moment from 'moment';
@Component({
  selector: 'app-banners',
  templateUrl: './banners.component.html',
  styleUrls: ['./banners.component.scss']
})
export class BannersComponent implements OnInit

{ @ViewChild('removeModal') public removeModal: ModalDirective;

Add = true;
Edit = true;
Delete = true;
Products = [];
page = 1;
count = 0;
id = ''
name = ''

constructor(private productService: ProductService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }
ngOnInit(): void {
  this.tokens();
}
//token verified type
tokens(): void {
  const Role = this.tokenStorage.getUser();
  this.Permission.GetModule(Role.role_id._id)
    .subscribe((res: any) => {
      // console.log(res)
      for (var i = 0; i < res.data.length; i++) {
        if (res.data[i].module_name == "Shopping") {
          this.Add = res.data[i].add
          this.Edit = res.data[i].edit
          this.Delete = res.data[i].delete
          // console.log(this.Add, this.Edit, this.Delete)
        }
      }
    })
  this.EmployeeService.GetEmployeeDetail(Role._id)
    .subscribe((res) => {
      // console.log(res.data[0].status)
      if (res.data.status == false) {
        this.tokenStorage.signOut()
      } else {
        this.ListProduct();
      }
    })
}

newbanner(){
  this.router.navigate(['../../pages/add-banners'])}

//page handle request
getrequestparams(page: number): any {
  let skip: any = {};
  skip[`skip`] = (page - 1) * 10;
  return skip;
}

//List all Product
ListProduct() {
  const skip = this.getrequestparams(this.page);
  this.productService.GetBanners(skip)
    .subscribe((res: any) => {
      this.Products = res.data;
      console.log("new product ",this.Products)
      this.count = res.count;
    })
}

//Page handle 
handlePageChange(event: number) {
  this.page = event;
  this.ListProduct();
}

//Get Product
GetProductById(index, param) {
  if (param == "Delete") {
    this.id = this.Products[index]._id
    console.log("Iddddd",this.id)
    this.removeModal.show();
  }
}

//Delete single product by using id
DeleteProduct(id) {
  this.productService.DeleteBanner(id)
    .subscribe((res: any) => {
      this.removeModal.hide();
      this.ListProduct();
    })
}

//Status changed by using id
changed(event, id) {
  console.log("check box222222222222222222222",event.target.checked)

  const data = {
    status: event.target.checked
  }


  this.productService.UpdateBanner(id, data)
    .subscribe((res: any) => {
      this.ListProduct();
    })
}



//Edit product and navigation to add product
EditProduct(Id) {
  this.router.navigate(['/pages/add-banners'], { queryParams: { 'search': Id,'Edit_button':true } });
}



addbanner(){

  this.router.navigate(['/pages/add-banners'], { queryParams: { 'Add_button':true } });

}


}
