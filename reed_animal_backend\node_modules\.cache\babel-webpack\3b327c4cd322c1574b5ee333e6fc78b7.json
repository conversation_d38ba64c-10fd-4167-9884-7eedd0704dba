{"ast": null, "code": "import toNumber from './toNumber.js';\n/**\n * Creates a function that performs a relational operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @returns {Function} Returns the new relational operation function.\n */\n\nfunction createRelationalOperation(operator) {\n  return function (value, other) {\n    if (!(typeof value == 'string' && typeof other == 'string')) {\n      value = toNumber(value);\n      other = toNumber(other);\n    }\n\n    return operator(value, other);\n  };\n}\n\nexport default createRelationalOperation;", "map": null, "metadata": {}, "sourceType": "module"}