{"ast": null, "code": "/**\n * Converts `set` to its value-value pairs.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the value-value pairs.\n */\nfunction setToPairs(set) {\n  var index = -1,\n      result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = [value, value];\n  });\n  return result;\n}\n\nexport default setToPairs;", "map": null, "metadata": {}, "sourceType": "module"}