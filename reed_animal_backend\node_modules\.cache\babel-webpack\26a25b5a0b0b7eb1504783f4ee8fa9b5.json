{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Chinese (China) [zh-cn]\n//! author : suupic : https://github.com/suupic\n//! author : <PERSON><PERSON> : https://github.com/zenozeng\n//! author : uu109 : https://github.com/uu109\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var zhCn = moment.defineLocale('zh-cn', {\n    months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n    weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n    weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),\n    weekdaysMin: '日_一_二_三_四_五_六'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY/MM/DD',\n      LL: 'YYYY年M月D日',\n      LLL: 'YYYY年M月D日Ah点mm分',\n      LLLL: 'YYYY年M月D日ddddAh点mm分',\n      l: 'YYYY/M/D',\n      ll: 'YYYY年M月D日',\n      lll: 'YYYY年M月D日 HH:mm',\n      llll: 'YYYY年M月D日dddd HH:mm'\n    },\n    meridiemParse: /凌晨|早上|上午|中午|下午|晚上/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n\n      if (meridiem === '凌晨' || meridiem === '早上' || meridiem === '上午') {\n        return hour;\n      } else if (meridiem === '下午' || meridiem === '晚上') {\n        return hour + 12;\n      } else {\n        // '中午'\n        return hour >= 11 ? hour : hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      var hm = hour * 100 + minute;\n\n      if (hm < 600) {\n        return '凌晨';\n      } else if (hm < 900) {\n        return '早上';\n      } else if (hm < 1130) {\n        return '上午';\n      } else if (hm < 1230) {\n        return '中午';\n      } else if (hm < 1800) {\n        return '下午';\n      } else {\n        return '晚上';\n      }\n    },\n    calendar: {\n      sameDay: '[今天]LT',\n      nextDay: '[明天]LT',\n      nextWeek: function (now) {\n        if (now.week() !== this.week()) {\n          return '[下]dddLT';\n        } else {\n          return '[本]dddLT';\n        }\n      },\n      lastDay: '[昨天]LT',\n      lastWeek: function (now) {\n        if (this.week() !== now.week()) {\n          return '[上]dddLT';\n        } else {\n          return '[本]dddLT';\n        }\n      },\n      sameElse: 'L'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(日|月|周)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '日';\n\n        case 'M':\n          return number + '月';\n\n        case 'w':\n        case 'W':\n          return number + '周';\n\n        default:\n          return number;\n      }\n    },\n    relativeTime: {\n      future: '%s后',\n      past: '%s前',\n      s: '几秒',\n      ss: '%d 秒',\n      m: '1 分钟',\n      mm: '%d 分钟',\n      h: '1 小时',\n      hh: '%d 小时',\n      d: '1 天',\n      dd: '%d 天',\n      w: '1 周',\n      ww: '%d 周',\n      M: '1 个月',\n      MM: '%d 个月',\n      y: '1 年',\n      yy: '%d 年'\n    },\n    week: {\n      // GB/T 7408-1994《数据元和交换格式·信息交换·日期和时间表示法》与ISO 8601:1988等效\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return zhCn;\n});", "map": null, "metadata": {}, "sourceType": "script"}