{"ast": null, "code": "import Stack from './_Stack.js';\nimport baseIsEqual from './_baseIsEqual.js';\n/** Used to compose bitmasks for value comparisons. */\n\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n/**\n * The base implementation of `_.isMatch` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Array} matchData The property names, values, and compare flags to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n */\n\nfunction baseIsMatch(object, source, matchData, customizer) {\n  var index = matchData.length,\n      length = index,\n      noCustomizer = !customizer;\n\n  if (object == null) {\n    return !length;\n  }\n\n  object = Object(object);\n\n  while (index--) {\n    var data = matchData[index];\n\n    if (noCustomizer && data[2] ? data[1] !== object[data[0]] : !(data[0] in object)) {\n      return false;\n    }\n  }\n\n  while (++index < length) {\n    data = matchData[index];\n    var key = data[0],\n        objValue = object[key],\n        srcValue = data[1];\n\n    if (noCustomizer && data[2]) {\n      if (objValue === undefined && !(key in object)) {\n        return false;\n      }\n    } else {\n      var stack = new Stack();\n\n      if (customizer) {\n        var result = customizer(objValue, srcValue, key, object, source, stack);\n      }\n\n      if (!(result === undefined ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack) : result)) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n\nexport default baseIsMatch;", "map": null, "metadata": {}, "sourceType": "module"}