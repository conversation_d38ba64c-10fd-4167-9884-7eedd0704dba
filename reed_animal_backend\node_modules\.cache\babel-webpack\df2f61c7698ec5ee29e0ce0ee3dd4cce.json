{"ast": null, "code": "/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n\n  return number;\n}\n\nexport default baseClamp;", "map": null, "metadata": {}, "sourceType": "module"}