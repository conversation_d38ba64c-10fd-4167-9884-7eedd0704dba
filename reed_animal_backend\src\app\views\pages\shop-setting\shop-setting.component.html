<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Shop Settings
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12" style="margin:auto; margin-bottom:50px;">
                        <tabset>
                            <!-- Category -->
                            <tab>
                                <ng-template tabHeading>Category </ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalCategory.show()">
                  Add Category
                </button>
                                <table class="table table-striped">
                                    <thead>

                                        <tr>
                                            <th>Category Name</th>
                                            <th>Action</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of Category| paginate: { id: 'listing_pagination',
                    itemsPerPage: 10,
                    currentPage: page,
                    totalItems: count };let i = index;">

                                            <td>{{user.name}}</td>

                                            <td><a data-toggle="modal" (click)="GetCategoryBy(i,'Edit');" style="cursor: pointer;"><span
                            class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                                <a data-toggle="modal" (click)="GetCategoryBy(i,'Delete');" style="cursor: pointer;"><span
                            class="badge badge-danger"><i class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                                    </pagination-controls>
                                </div>

                            </tab>
                            <!-- Brand -->
                            <tab>
                                <ng-template tabHeading>Brand</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalBrand.show()">
                  Add Brand
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Brand Name</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of brand| paginate: { id: 'listing_brand',
                    itemsPerPage: 10,
                    currentPage: pageB,
                    totalItems: countB };let i = index;">

                                            <td>{{user.name}}</td>

                                            <td><a data-toggle="modal" (click)="GetBrandBy(i,'Edit');" style="cursor: pointer;"><span
                            class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                                <a data-toggle="modal" (click)="GetBrandBy(i,'Delete');" style="cursor: pointer;"><span
                            class="badge badge-danger"><i class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_brand" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChangeB($event)">
                                    </pagination-controls>
                                </div>
                            </tab>
                            <!-- Variant -->
                            <tab>
                                <ng-template tabHeading>Variant</ng-template>
                                <button type="button" class="btn btn-primary mr-1 my-3" data-toggle="modal" (click)="primaryModalVariant.show()">
                  Add Variant
                </button>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>

                                            <th>Variant Name</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let user of Variant| paginate: { id: 'listing_Variant',
                    itemsPerPage: 10,
                    currentPage: pageV,
                    totalItems: countV };let i = index;">

                                            <td>{{user.name}}</td>

                                            <td>
                                                <a data-toggle="modal" (click)="GetVariantBy(i,'Edit');" style="cursor: pointer;"><span
                            class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                                <a data-toggle="modal" (click)="GetVariantBy(i,'Delete');" style="cursor: pointer;"><span
                            class="badge badge-danger"><i class="fa fa-trash"></i>
                            Delete</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div>
                                    <pagination-controls id="listing_Variant" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChangeV($event)">
                                    </pagination-controls>
                                </div>
                            </tab>

                        </tabset>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Add Modal Category -->
<div bsModal #primaryModalCategory="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Category</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="loginForm" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Category Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Dog, Cat" formControlName="name" [ngClass]="{ 'is-invalid': submitted && f.name.errors }" />
                                <div *ngIf="submitted && f.name.errors" class="invalid-feedback">
                                    <div *ngIf="f.name.errors.required">*Category name is mandatory</div>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModalCategory.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="AddCategory();">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Edit Modal Category -->
<div bsModal #EditModalCategory="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Category</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="loginForm" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Category Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Dog, Cat" formControlName="name" [ngClass]="{ 'is-invalid': submitted && f.name.errors }" />
                                <div *ngIf="submitted && f.name.errors" class="invalid-feedback">
                                    <div *ngIf="f.name.errors.required">*Category name is mandatory</div>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="EditModalCategory.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="UpdateCategory(id);">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Modal Category -->
<div bsModal #removeModalCategory="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Category?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModalCategory.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteCategory(id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Add Modal Brand -->
<div bsModal #primaryModalBrand="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Brand</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="Brand" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Brand Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Pedigree, Royal chain" formControlName="name" [ngClass]="{ 'is-invalid': submitted && b.name.errors }" />
                                <div *ngIf="submitted && b.name.errors" class="invalid-feedback">
                                    <div *ngIf="b.name.errors.required">*Brand name is mandatory</div>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModalBrand.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="AddBrand();">Add</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Edit Modal Brand -->
<div bsModal #EditModalBrand="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Brand</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="Brand" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Brand Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Pedigree, Royal chain" formControlName="name" [ngClass]="{ 'is-invalid': submitted && b.name.errors }" />
                                <div *ngIf="submitted && b.name.errors" class="invalid-feedback">
                                    <div *ngIf="b.name.errors.required">*Brand name is mandatory</div>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="EditModalBrand.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="UpdateBrand(id);">Add</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Modal Category -->
<div bsModal #removeModalBrand="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Brand?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModalBrand.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteBrand(id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->


<!-- Add Modal Variant -->
<div bsModal #primaryModalVariant="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Variant </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="variant" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Variant Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Color, Size" formControlName="name" [ngClass]="{ 'is-invalid': submittedV && v.name.errors }" />
                                <div *ngIf="submittedV && v.name.errors" class="invalid-feedback">
                                    <div *ngIf="v.name.errors.required">*Variant name is mandatory</div>
                                </div>
                            </div>

                            <a data-toggle="modal" (click)="addItem()" style="cursor: pointer;"><span class="badge badge-success"
                  style="float:right;margin-bottom: 15px;"><i class="fa fa-plus"></i>
                  Add</span></a>

                            <table class="table ">
                                <thead>
                                    <tr>

                                        <th>Variant Value Name</th>
                                        <th style="align-items: center;">Sort</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr formArrayName="items" *ngFor="let item of variant.get('items')['controls']; let i = index;">
                                        <td [formGroupName]="i"><input class="form-control" type="text" formControlName="value" placeholder="Variant value Name" [ngClass]="{'is-invalid':submittedV && item.get('value').errors?.required} " />
                                            <!-- <div *ngIf="submittedV && item.get('value').errors?.required" class="invalid-feedback">
                        <div *ngIf="item.get('value').errors?.required">*Value name is mandatory</div>
                      </div> -->
                                        </td>
                                        <td [formGroupName]="i"> <input class="form-control" type="number" formControlName="sort" placeholder="Sort" [ngClass]="{'is-invalid':submittedV && item.get('sort').errors?.required} ">
                                            <!-- <div *ngIf="submittedV && item.get('sort').errors?.required" class="invalid-feedback">
                        <div *ngIf="item.get('sort').errors?.required">*Sort number is mandatory</div>
                      </div> -->
                                        </td>

                                        <td [formGroupName]="i" *ngIf='i!=0'>
                                            <a data-toggle="modal" (click)="removeItem(i);" style="cursor: pointer;"><span
                          class="badge badge-danger"><i class="fa fa-trash"></i>
                          Remove</span></a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModalVariant.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="AddVariant();">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Edit Modal Variant -->
<div bsModal #EditModalVariant="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Variant </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="variant" autocomplete="off">

                            <div class="form-group">
                                <label for="firstName">Variant Name*</label>
                                <input type="text" class="form-control" placeholder="e.g. Color, Size" formControlName="name" [ngClass]="{ 'is-invalid': submittedV && v.name.errors }" />
                                <div *ngIf="submittedV && v.name.errors" class="invalid-feedback">
                                    <div *ngIf="v.name.errors.required">*Variant name is mandatory</div>
                                </div>
                            </div>

                            <a data-toggle="modal" (click)="addItem()" style="cursor: pointer;"><span class="badge badge-success"
                  style="float:right;margin-bottom: 15px;"><i class="fa fa-plus"></i>
                  Add</span></a>

                            <table class="table ">
                                <thead>
                                    <tr>

                                        <th>Variant Value Name</th>
                                        <th style="align-items: center;">Sort</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr formArrayName="items" *ngFor="let item of variant.get('items')['controls']; let i = index;">
                                        <td [formGroupName]="i"><input class="form-control" type="text" formControlName="value" placeholder="Variant value Name" [ngClass]="{'is-invalid':submittedV && item.get('value').errors?.required} " />
                                            <div *ngIf="submittedV && item.get('value').errors?.required" class="invalid-feedback">
                                                <div *ngIf="item.get('value').errors?.required">*Value name is mandatory</div>
                                            </div>
                                        </td>
                                        <td [formGroupName]="i"> <input class="form-control" type="number" formControlName="sort" placeholder="Sort" [ngClass]="{'is-invalid':submittedV && item.get('sort').errors?.required} ">
                                            <!-- <div *ngIf="submittedV && item.get('sort').errors?.required" class="invalid-feedback">
                        <div *ngIf="item.get('sort').errors?.required">*Sort number is mandatory</div>
                      </div> -->
                                        </td>

                                        <td [formGroupName]="i" *ngIf='i!=0'>
                                            <a data-toggle="modal" (click)="removeItem(i);" style="cursor: pointer;"><span
                          class="badge badge-danger"><i class="fa fa-trash"></i>
                          Remove</span></a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="EditModalVariant.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="button" (click)="UpdateVariant(id);">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Modal Variant -->
<div bsModal #removeModalVariant="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Variant?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModalVariant.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteVariant(id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->