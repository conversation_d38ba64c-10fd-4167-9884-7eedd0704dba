import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductService } from '../../services/product.services'
import { Subscription } from 'rxjs';
import * as moment from 'moment';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss']
})
export class ProductsComponent implements OnInit {

  Add = true;
  Edit = true;
  Delete = true;
  subscription: Subscription;
  loginForm: FormGroup;
  VariantForm: FormGroup;
  items: FormArray;
  isFormReady = false;
  submitted = false;
  Category = [];
  brand = [];
  Variant = [];
  option = false;
  variant_item = false;
  minDate: Date;
  maxDate: Date;
  selectedFiles?: FileList;
  currentFile?: File;
  urls = '';
  urls1 = [];
  poster_image = ''
  multiple_image = [''];
  multi = []
  data = []
  Json = []
  json1 = []
  Id = ''
  action = true
  attributes = true


  showVariant=true



  constructor(private productService: ProductService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) {
    this.minDate = new Date();
    this.minDate.setDate(this.minDate.getDate());
    this.maxDate = new Date();
    this.maxDate.setDate(this.maxDate.getDate() + 1);
  }

  ngOnInit(): void {
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Products") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.SignForm();
    this.ListCategory();
    this.ListBrand();
    this.ListVariant();
    this.items = this.loginForm.get('items') as FormArray;
    this.items.clear();
    
    this.route.queryParams
      .subscribe((params: any) => {
        this.Id = params['search'];
      }
      );
    if (this.Id == '' || this.Id == undefined) {
      this.showVariant=true
      this.items.push(this.formBuilder.group({
        price: ['', [Validators.required, Validators.min(1)]],
        tax: ['', [Validators.required, Validators.min(1)]],
        inventory: [, [Validators.required, Validators.min(1)]],
        dis_price: [, [Validators.min(1)]],
        dis_start: [''],
        dis_end: [''],
        status: [true]
      }))
      this.f.variant.setValue('single', {
        onlySelf: true
      })
    } else {
      this.action = false
      this.attributes = false
      this.showVariant=false
      this.productService.GetProductById(this.Id)
        .subscribe((res: any) => {
          // console.log(res)
          this.f.title.setValue(res.data.title, { onlySelf: true });
          this.f.sku.setValue(res.data.sku, { onlySelf: true });
          this.f.category.setValue(res.data.category, { onlySelf: true });
          this.f.brand.setValue(res.data.brand, { onlySelf: true });
          this.f.description.setValue(res.data.description, { onlySelf: true });
          this.f.variant.setValue(res.data.variant, { onlySelf: true });
          this.f.variant_type.setValue(res.data.variant_type, { onlySelf: true });
          if (res.data.variant == 'multi') {
            this.option = true;
          }
          this.poster_image = res.data.poster_image;
          this.urls = res.data.poster_image;
          this.urls1 = res.data.multiple_image;
          this.multiple_image = res.data.multiple_image;
          res.data.items.forEach(product => {
            if (res.data.variant == 'single') {
              product.options = 'null',
                product.data = 'null',
                product.sub_sku = 'null'
            }
            this.items.push(this.formBuilder.group({
              _id: [product._id],
              options: [product.options],
              data: [product.data],
              sub_sku: [product.sub_sku, [Validators.required, Validators.min(1)]],
              price: [product.price, [Validators.required, Validators.min(1)]],
              tax: [product.tax, [Validators.required, Validators.min(1)]],
              inventory: [product.inventory, [Validators.required, Validators.min(1)]],
              dis_price: [product.dis_price, [Validators.min(1)]],
              dis_start: [product.dis_start != ('' || null) ? moment(product.dis_start).format('MM/DD/YYYY') : ''],
              dis_end: [product.dis_end != ('' || null) ? moment(product.dis_end).format('MM/DD/YYYY') : ''],
              status: [product.status]
            }))
          });
        })
    }
  }

  //List all Category
  ListCategory() {
    var skip = { limit: 100 }
    this.productService.GetCategory(skip)
      .subscribe((res: any) => {
        this.Category = res.data;
      })
  }

  //List all Brand
  ListBrand() {
    var skip = { limit: 100 }
    this.productService.GetBrand(skip)
      .subscribe((res: any) => {
        this.brand = res.data;
      })
  }

  //list all variant
  ListVariant() {
    var skip = { limit: 100 }
    this.productService.GetVariant(skip)
      .subscribe((res: any) => {
        this.Variant = res.data;
        console.log(res.data)
      })
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      title: ['', [Validators.required]],
      sku: ['', [Validators.required]],
      category: ['', [Validators.required]],
      brand: ['', [Validators.required]],
      description: ['', [Validators.required]],
      variant: [''],
      status: [true],
      items: this.formBuilder.array([this.createItem()]),
      variant_type: []
    });
  }

  createItem(): FormGroup {
    return this.formBuilder.group({
      options: [''],
      sku: ['', [Validators.required]],
      price: ['', [Validators.required, Validators.min(1)]],
      tax: ['', [Validators.required, Validators.min(1)]],
      inventory: [, [Validators.required, Validators.min(1)]],
      dis_price: [, [Validators.min(1)]],
      dis_start: [''],
      dis_end: [''],
      status: [true]
    });
  }

  addItem(): void {
    this.items = this.loginForm.get('items') as FormArray;
    this.items.push(this.createItem());
  }

  removeItem(i: number) {
    this.items.removeAt(i);
  }

  get f() {
    return this.loginForm.controls;
  }

  //Add new products
  AddProduct(): void {
    this.submitted = true;
    // console.log(this.loginForm.value)
    if (this.loginForm.invalid) {
      return
    } else if (this.poster_image == '') {
      return
    }
    else {
      const data = {
        title: this.loginForm.value.title,
        sku: this.loginForm.value.sku,
        category: this.loginForm.value.category,
        brand: this.loginForm.value.brand,
        description: this.loginForm.value.description,
        variant: this.loginForm.value.variant,
        poster_image: this.poster_image,
        multiple_image: this.multiple_image,
        items: this.loginForm.value.items,
        variant_type: this.loginForm.value.variant_type,
      }
      console.log(data)
      if (this.attributes == true) {
        this.productService.AddProduct(data)
          .subscribe((res: any) => {
            if (res.code == 200) {
              this.loginForm.reset();
              this.submitted = false;
              this.multiple_image.length = 0;
              this.poster_image = '';
              this.router.navigate(['/pages/shopping']);
            }
          })
      } else {
        this.productService.UpdateProduct(this.Id, data)
          .subscribe((res: any) => {
            console.log(res)
            if (res.code == 200) {
              this.router.navigate(['/pages/shopping']);
            }
          })
      }
    }
  }

  //Variant checkbox
  change(index, flag) {
    this.Variant[index].flag = !flag
    this.items.clear();
    var commit = [];
    var variant_item = 0;
    this.Variant.forEach((item) => {
      if (item.flag == true) {
        variant_item += 1
        commit.push(item)
      }
    })
    if (variant_item >= 1) {
      this.data.length = 0
      this.Json.length = 0
      this.json1.length = 0
      for (var i = 0; i < this.Variant.length; i++) {
        if (this.Variant[i].flag == true) {
          if (this.data.length == 0) {
            for (var j = 0; j < this.Variant[i].items.length; j++) {
              this.data.push(this.Variant[i].items[j].value)
              this.Json.push({ [this.Variant[i].name]: this.Variant[i].items[j].value })
            }
          } else {
            const data1 = []
            const JSON1 = []
            for (var l = 0; l < this.Variant[i].items.length; l++) {
              for (var k = 0; k < this.data.length; k++) {
                var s = this.data[k] + '-' + this.Variant[i].items[l].value
                data1.push(s)
                var temp = {
                  ...this.Json[k], [this.Variant[i].name]: this.Variant[i].items[l].value
                }
                JSON1.push(temp)
              }
            }
            this.data = data1
            this.Json = JSON1
          }
        }
      }
      this.option = true;
      this.items = this.loginForm.get('items') as FormArray;
      if (this.loginForm.value.sku != '') {

      }
      this.data.forEach((product, index) => {
        var item = '';
        if (this.loginForm.value.sku != '') {
          item = this.loginForm.value.sku + '-' + (index + 1)
        }
        this.items.push(this.formBuilder.group({
          options: [product],
          data: [this.Json[index]],
          sub_sku: [item, [Validators.required]],
          price: ['', [Validators.required, Validators.min(1)]],
          tax: ['', [Validators.required, Validators.min(1)]],
          inventory: [, [Validators.required, Validators.min(1)]],
          dis_price: [, [Validators.min(1)]],
          dis_start: [''],
          dis_end: [''],
          status: [true],
        }))
      })
      this.f.variant.setValue('multi', {
        onlySelf: true
      })
      this.f.variant_type.setValue(commit, {
        onlySelf: true
      })
      variant_item = 0
      // console.log(this.loginForm.value)
      //If all variant flag are false
    } else {
      this.option = false;
      this.items = this.loginForm.get('items') as FormArray;
      this.items.clear();
      this.items.push(this.formBuilder.group({
        // sub_sku: [this.loginForm.value.sku],
        price: ['', [Validators.required, Validators.min(1)]],
        tax: ['', [Validators.required, Validators.min(1)]],
        inventory: [, [Validators.required, Validators.min(1)]],
        dis_price: [, [Validators.min(1)]],
        dis_start: [''],
        dis_end: [''],
        status: [true]
      }))
      this.f.variant.setValue('single', {
        onlySelf: true
      })
      this.f.variant_type.setValue('null', {
        onlySelf: true
      })
      variant_item = 0
    }
  }

  //Poster image
  onSelectFile(event) {
    console.log(event)
    this.poster_image = event.target.files[0]
    // if (event.target.files && event.target.files[0]) {
    //   var filesAmount = event.target.files.length;
    //   for (let i = 0; i < filesAmount; i++) {
    //     var reader = new FileReader();
    //     reader.onload = (event: any) => {
    //       this.urls = event.target.result;
    //       this.poster_image=event.target.result;
    //       console.log(this.urls)
    //     }
    //     reader.readAsDataURL(event.target.files[0]);
    //   }
    // }
    this.productService.uploadFile(event.target.files[0])
      .subscribe((res: any) => {
        this.poster_image = res.data;
      })
  }

  //Multiple Image
  onSelectFile1(event) {
    event.target.files.forEach((product: File) => {
      this.productService.uploadFile(product)
        .subscribe((res: any) => {
          this.multiple_image.push(res.data)
        })
    })
  }
  //image remove by using index
  removeImage(index) {
    if (index == -1) {
      this.urls = '',
        this.poster_image = ''
      // console.log('null-->', this.poster_image)
    } else {
      // this.urls1.splice(index, 1);
      this.multiple_image.splice(index, 1);
      // console.log('null-->', this.multiple_image)

    }
  }


}