import { Component, OnInit } from '@angular/core';
import { CovertusService } from '../../services/covertus.services';
@Component({
  selector: 'app-covertus',
  templateUrl: './covertus.component.html',
  styleUrls: ['./covertus.component.scss']
})
export class CovertusComponent implements OnInit {

  public page = 1;
  public search = '';
  public filter = '';
  public status = '';
  public CovertusList = [];
  public count=0;
  constructor(private covertusService: CovertusService) { }

  ngOnInit(): void {
    this.GetCovertus();
  }

  GetCovertus() {
    const params = {
      skip: (this.page - 1) * 10,
      limit: 10,
      search: this.search,
      filter: this.filter,
      status: this.status,
    };
    this.covertusService.GetCovertusList(params)
      .subscribe((res: any) => {
        console.log('covertusList-->', res);
        this.CovertusList = res.data;
        this.count = res.count;
      })
  }

  UpdateCovertus() {
    this.covertusService.UpdateCovertus({})
      .subscribe((res: any) => {
        console.log('covertusList-->', res);
        this.GetCovertus();
      })
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.GetCovertus();
  }

}
