import { Types } from './../../models/animal_type';
import { Component, OnInit, HostListener, ViewChild } from '@angular/core';
// import { DateTimeAdapter } from '@danielmoncada/angular-datetime-picker';
import { ActivatedRoute, Router } from '@angular/router';
import { OrderService } from '../../services/order.service'

import { ModalDirective } from 'ngx-bootstrap/modal';
import {  LocationService } from '../../services/location.sevices'
import { DateAdapterOptions } from 'chart.js';
import * as moment from 'moment';



@Component({
  selector: 'app-order-details',
  templateUrl: './order-details.component.html',
})
export class OrderDetailsComponent implements OnInit {
  @ViewChild('UpdateModal') public UpdateModal: ModalDirective;
  @ViewChild('Declined') public Declined : ModalDirective;
 
  locations: any;
  count: any;
  final_location: any;
  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    //  console.log('handleKeyboardEvent',event)
  }
  public textareaValue:any="";
  public messagebox : boolean = false;
  public statues :any;
  public  isDropdownDisabled: boolean = true;
  public dropDownStatus : boolean = true
  public PaymentImage = '';
  public Id: any = '';
  public user_details: any = {};
  public Key = '';
  public Value = '';
  public selectedDate:any
  public todayDate:any = new Date();
  public textvalue :boolean = false;
  public reasonEmpty :boolean;
  public dropDownStatusdate : boolean = true

  // public todayDate: any = moment().format();
  public page = 1;
  public name = ""
  public startup : boolean;
  public complete : boolean = true
  public readdTopickup : boolean = false;
  public submit : boolean = true;
  public submitBtn : boolean = true;
  public approval : boolean = false;
  public DeclinsubmitBtn : boolean = false
  public declinsubBtn : boolean = true
  public canclebtn : boolean = false
  constructor(private route: ActivatedRoute, private routerActive: ActivatedRoute, private router: Router, private orderService: OrderService,private locationservice: LocationService,
  ) {
   
    this.route.queryParams
      .subscribe((params: any) => {
        this.Id = params['id'];
        console.log("iddddd",this.Id)
      });
    this.GetOrder();
    // this.todayDate.setHours(0, 0, 0, 0);
    console.log("today date",this.todayDate)

 
  }

  ngOnInit(): void {
    // this.todayDate.setHours(0, 0, 0, 0);
    console.log("reed animal working")
    this. approvel()
    
    
   

   }

  GetOrder() {
    this.orderService.OrderDetail(this.Id)
      .subscribe((res: any) => {

        console.log("data",res);
      


        this.user_details = res.data;
        this.reasonEmpty =(this.user_details.reason == null || this.user_details.reason == "" )
        console.log("texttttttttttttt",this.reasonEmpty )
       this.user_details.delivery_date= (this.user_details.delivery_date!=null)?moment(this.user_details.delivery_date).format("DD-MMM-YYYY"):null
      // this.user_details.delivery_date= new Date(this.user_details.delivery_date).toString()

        console.log("binded data",this.user_details.delivery_date);
        
        // Initialize notes properties for each product
        if (this.user_details.product && this.user_details.product.length > 0) {
          console.log("this.user_details.product: ", this.user_details.product);
          
          this.user_details.product.forEach((item: any) => {
            console.log("item.approval: ",item.approval)
            // Check if notes exist in the database response
            if (item.notes === undefined || item.notes === null) {
              item.notes = '';
            }
            // Initialize refill quantity
            if (item.refill_quantity === undefined || item.refill_quantity === null) {
              item.refill_quantity = 0;
            }
            // Initialize refill status
            if (item.approval === undefined || item.approval === null) {
              item.approval = 0;
            }
            // Initialize refill decline reason
            if (item.refill_decline_reason === undefined || item.refill_decline_reason === null) {
              item.refill_decline_reason = '';
            }
            item.showNotesInput = false;
          });
        }
        if(this.user_details.approved == 2){
          this.messagebox = true;
          this.user_details.reason = this.user_details.reason
         this.declinsubBtn = true;
         this.submitBtn = false;
          
        }
        if(this.user_details.approved == 1){
          this.startup =true;
          this.isDropdownDisabled = false
        }
        if(this.user_details.status == 2){
          this.isDropdownDisabled = false;
          this.complete = false;
          this.readdTopickup = true
          this.user_details.approved = 1
          this.user_details.reason = ""   
          this.approval = true; 
         
     
         

          console.log("workddddd",this.user_details.approved)
        }
        if(this.user_details.status == 1){
          this.submitBtn = false;
          this.approval = true;
          this.isDropdownDisabled = true

        }
        if(this.user_details.status ===3){
          this.canclebtn = true
          this.approval = true; 
          this.submitBtn = false;
          this.isDropdownDisabled = true
         
          
        }
        console.log("user detailsss",this.user_details)
        this. locationLists();
      });

  }
  Update() {
    const data = {
      [this.Key]: this.Value
    }
    
    console.log("datat",this.user_details._id)
    this.orderService.UpdateOrderDetail(this.user_details.order_id, data)
      .subscribe((res: any) => {
        console.log("cancle details")
        this.UpdateModal.hide();

      })
  }
  out(){
    console.log("tests",this.user_details.reason)
    this.textvalue=false
  }

  window(item) {
    window.open(item);
  }
  approvel(){
    console.log("status",this.statues)
    if(this.user_details.approved ==1){
      console.log("accepted")
      this.submit = false;
      this.messagebox = false;
      this.isDropdownDisabled = false;
      this.submitBtn = true;
      
    }
    else if(this.user_details.approved==2){
      console.log("disabele")
      this.isDropdownDisabled = true;
      // this.Declined.show();  
      this.messagebox = true;
      this.submit = true 
      this.submitBtn = false
      // this.user_details.location = ''
      this.user_details.delivery_date = ''
      this.dropDownStatus = true;
      // this.submitBtn = false; 
      
     
    }
  }
  // this.reasonEmpty = (res.reason=="")
  textUpdate(){
     
    debugger   
    console.log("status~~~~~~~",this.user_details.reason)
    console.log("text value",typeof(this.user_details.reason))
    // this.messagebox = false;  

     if(this.user_details.reason == null ||  this.user_details.reason == "" ){
      debugger
      console.log("@@@@@@@@@@@@@@@@@@@@@@@@@")
      this.textvalue = true;
      return
    }


    var query ={
      approved :this.user_details.approved,
      reason:''
  }

  if(this.user_details.reason.includes("Please call Dr. Reed at 408-569-5684")==false){
    query.reason =this.user_details.reason+"\n"+"Please call Dr. Reed at 408-569-5684."
  }
  else{
      query.reason = this.user_details.reason
  }

  console.log("queryyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy",query)
  console.log("text update",this.user_details.order_id)
  this.router.navigate(['/pages/orders']);

  this.orderService.UpdateOrderDetail(this.user_details.order_id, query)
    .subscribe((res: any) => {
      console.log("cancle details")
      this.UpdateModal.hide();


    })



    // if(this.user_details.reason == "" ){
    //   console.log("@@@@@@@@@@@@@@@@@@@@@@@@@")
    //   this.textvalue = true;
    // }


    // if(this.user_details.reason !=''){
    //   console.log("#############################")

    //   const query = {
    //     approved : this.user_details.approved,
    //    reason : (this.user_details.reason+ "\n" +"Please call Dr. Reed at 408-569-5684")
    //   }
     
  

    // }

    

  
  //  this.findWordInLines()
  }

  // findWordInLines(): void {
  //   var text = "Please call Dr. Reed at 408-569-5684"
  //   // Check if the search word exists in line 1
  //   if (this.textareaValue.indexOf(text) !== 1) {
  //     console.log(`Word "${text}" found in line 1`);
  //   } else {
  //     console.log(`Word "${text}" not found in line 1`);
  //   }
  // }

  order_status(){
     
  console.log("testingggggg")
  if(this.user_details.status == 2){
    // this.dropDownStatus=false
    this.dropDownStatusdate = false
    this.submit = true;
    this.user_details.delivery_date = this.todayDate 
    console.log("today date",this.user_details.delivery_date)

  }
  if(this.user_details.status == 1){
    this.submit = false;
    
  }

    
  }

  

  onsubmit(){
 

    const query = {
      approved:this.user_details.approved,
      status :this.user_details.status,
      // delivery_date : moment(this.user_details.delivery_date, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').utc().format(),
      delivery_date : this.user_details.delivery_date,
      pickuplocation : this.user_details.location,
      reason :null,
      location :this.user_details.location,
      product_notes: this.user_details.product.map((item: any) => ({
        product_id: item.product_id._id,
        notes: item.notes || ''
      }))
    
    }
    console.log("detailssssss",query)


    this.orderService.UpdateOrderDetail(this.user_details.order_id, query)
    .subscribe((res: any) => {
      debugger

      console.log("details",res)
      this.UpdateModal.hide();
      this.router.navigate(['/pages/orders']);

    })

    console.log("valuessssss",query)

  }
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  locationLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.locationservice.GetLocationsList(skip, this.name)
      .subscribe((res: any) => {
        this.locations = res.data;
        this.count = res.count;
        console.log("locationsssssssss",this.locations);
        // console.log(this.count);

        this.final_location = this.locations.filter((item) => item.status == true)
        console.log("final location",this.final_location[0]._id)
        this.user_details.location = this.final_location[0]._id

      
     
        // console.log("usder detailsssss",this.user_details.location)
        // for(let location of this.locations){
        //   if(location.status == true){
        //     this.final_location = location
        //     console.log("final",this.final_location)

        //   }

        // }
      
      });

      for(let item of this.final_location){

        this.user_details.location = item._id
        console.log("usder detailsssss",item.name)
      }
  }

  select_location(){

    console.log("datassss...",this.user_details.location,"wwwww",this.user_details.delivery_date ,"\n\nMoment",moment(this.user_details.delivery_date, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').utc().format())
    if(this.user_details.location != null && this.user_details.delivery_date !=  null ){
      console.log("worked on locationaaaa")
      this.submit=false;

    }
  }

  delivery(){
    debugger
    console.log("testing",this.user_details.delivery_date)


    if(this.user_details.location != null && this.user_details.delivery_date !=  null ){
      console.log("worked on locationaaaa")
      this.submit=false;

    }

  
  }

  showNotesInput(index: number) {
    this.user_details.product[index].showNotesInput = true;
  }

  saveNotes(index: number) {
    this.user_details.product[index].showNotesInput = false;
    // Save notes to database for all products
    this.saveAllProductNotes();
  }

  saveAllProductNotes() {
    const notesData = this.user_details.product.map((item: any) => ({
      product_id: item.product_id._id,
      notes: item.notes || ''
    }));

    const updateData = {
      product_notes: notesData
    };

    this.orderService.UpdateOrderDetail(this.user_details.order_id, updateData)
      .subscribe((res: any) => {
        console.log('Product notes saved successfully:', res);
      }, (error) => {
        console.error('Error saving product notes:', error);
      });
  }

  cancelNotes(index: number) {
    this.user_details.product[index].showNotesInput = false;
    // Reset notes to previous value if needed
  }

  onRefillStatusChange(index: number, event: any) {
    const item = this.user_details.product[index];
    const selectedValue = event.target.value;
    
    console.log('=== REFILL STATUS CHANGE DEBUG ===');
    console.log('Product:', item.product_id.title);
    console.log('Selected value:', selectedValue);
    
    // Update the local model
    item.approval = selectedValue;
    
    if (selectedValue === 'approved') {
      // For approved: Make API call immediately (like before)
      console.log('Approved selected - making API call immediately');
      
      const refillData = {
        product_id: item.product_id._id,
        approval: 'approved',
        refill_quantity: item.refill_quantity || 0,
        refill_decline_reason: ''
      };
      
      console.log('Sending approved refill data:', refillData);
      
      // Call API immediately for approved
      this.orderService.UpdateOrderDetail(this.user_details.order_id, {
        product_refills: [refillData]
      }).subscribe((res: any) => {
        console.log('Approved refill data updated successfully:', res);
      }, (error) => {
        console.error('Error updating approved refill data:', error);
      });
      
    } else if (selectedValue === 'declined') {
      // For declined: Just update local model, wait for submit button
      console.log('Declined selected - waiting for submit button, no API call yet');
      
      // Reset decline reason
      item.refill_decline_reason = '';
      
    } else {
      // For no selection: Reset decline reason
      item.refill_decline_reason = '';
    }
    
    console.log('Updated local model - approval:', item.approval);
  }

  submitRefillDecline(index: number) {
    const item = this.user_details.product[index];
    
    if (!item.refill_decline_reason || item.refill_decline_reason.trim() === '') {
      alert('Please enter a reason for declining the refill');
      return;
    }
    
    console.log('Refill declined for product:', item.product_id.title, 'Reason:', item.refill_decline_reason);
    
    // Prepare data for API call according to backend schema
    const refillData = {
      product_id: item.product_id._id,
      approval: 'declined',
      refill_quantity: item.refill_quantity || 0,
      refill_decline_reason: item.refill_decline_reason.trim()
    };
    
    // Call API to update refill data with decline reason
    this.orderService.UpdateOrderDetail(this.user_details.order_id, {
      product_refills: [refillData]
    }).subscribe((res: any) => {
      console.log('Refill decline reason updated successfully:', res);
      // Removed the alert popup
    }, (error) => {
      console.error('Error updating refill decline reason:', error);
      alert('Error submitting decline reason. Please try again.');
    });
  }
 
}