<div *ngIf="TokenFailed">
  <h3 style="color:red;font-weight:600;text-align:center;padding: 200px;">Invalid Request!.</h3>
</div>

<div *ngIf="success" >
  <h3 style="color:green;font-weight:600;text-align:center;padding: 250px 50px 0px;">Password changed
    successfully.</h3>
  <!-- <button type="button" class="btn btn-link px-0 col-7 text-right" [routerLink]="['/login']">Login</button> -->
</div>

<div class="app-body auth-login-sign" *ngIf="TokenPass" style="margin-top: 172px">
  <main class="main d-flex align-items-center">
    <div class="container">
      <div class="row">
        <div class="col-md-5 mx-auto">
          <div class="card-group">
            <div class="card p-4">
              <div class="card-body">
                <form class="form" [formGroup]="validForm" (ngSubmit)="onSubmit()" autocomplete="off">
                  <h1 style="margin-bottom: 20px;">Reset Password</h1>

                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-lock icons"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="New Password" required
                      formControlName="firstName" [ngClass]="{ 'is-invalid': submitted && f.firstName.errors }"
                      (click)="getfocus()" />
                    <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
                      <div *ngIf="f.firstName.errors.required">*Password cannot be empty</div>
                      <div *ngIf="f.firstName.errors.minlength">Password must be 8 digits</div>
                      <div *ngIf="f.firstName.errors.pattern">Password must contain uppercase, lowercase, special character and numbers</div>
                    </div>
                  </div>

                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-lock icons"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="Confirm New Password" required
                      formControlName="password" [ngClass]="{ 'is-invalid': submitted && f.password.errors }"
                      (click)="getfocus()" />
                    <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                      <div *ngIf="f.password.errors.required">*Password cannot be empty</div>
                      <div *ngIf="f.password.errors.minlength">Password must be a 8 digits</div>
                      <div *ngIf="f.firstName.errors.pattern">Password must contain uppercase, lowercase, special character and numbers</div>

                    </div>
                  </div>
                  
                  <div class="form-group">
                    <div class="alert alert-danger" role="alert" *ngIf="failed">Please make sure your passwords match
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12">
                      <button class="btn btn-primary px-4" >Submit</button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>