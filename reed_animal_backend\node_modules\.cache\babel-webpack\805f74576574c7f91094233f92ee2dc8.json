{"ast": null, "code": "import realNames from './_realNames.js';\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Gets the name of `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {string} Returns the function name.\n */\n\nfunction getFuncName(func) {\n  var result = func.name + '',\n      array = realNames[result],\n      length = hasOwnProperty.call(realNames, result) ? array.length : 0;\n\n  while (length--) {\n    var data = array[length],\n        otherFunc = data.func;\n\n    if (otherFunc == null || otherFunc == func) {\n      return data.name;\n    }\n  }\n\n  return result;\n}\n\nexport default getFuncName;", "map": null, "metadata": {}, "sourceType": "module"}