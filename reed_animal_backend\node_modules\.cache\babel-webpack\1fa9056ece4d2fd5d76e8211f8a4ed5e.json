{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeToObservable = obj => subscriber => {\n  const obs = obj[Symbol_observable]();\n\n  if (typeof obs.subscribe !== 'function') {\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  } else {\n    return obs.subscribe(subscriber);\n  }\n}; //# sourceMappingURL=subscribeToObservable.js.map", "map": null, "metadata": {}, "sourceType": "module"}