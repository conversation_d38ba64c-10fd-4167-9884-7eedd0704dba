{"ast": null, "code": "import arraySample from './_arraySample.js';\nimport values from './values.js';\n/**\n * The base implementation of `_.sample`.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @returns {*} Returns the random element.\n */\n\nfunction baseSample(collection) {\n  return arraySample(values(collection));\n}\n\nexport default baseSample;", "map": null, "metadata": {}, "sourceType": "module"}