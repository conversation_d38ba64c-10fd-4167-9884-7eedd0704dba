{"ast": null, "code": "import baseSum from './_baseSum.js';\n/** Used as references for various `Number` constants. */\n\nvar NAN = 0 / 0;\n/**\n * The base implementation of `_.mean` and `_.meanBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the mean.\n */\n\nfunction baseMean(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSum(array, iteratee) / length : NAN;\n}\n\nexport default baseMean;", "map": null, "metadata": {}, "sourceType": "module"}