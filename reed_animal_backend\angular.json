{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ng": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets"], "styles": ["node_modules/@coreui/icons/css/free.css", "node_modules/flag-icon-css/css/flag-icon.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/simple-line-icons/dist/styles/simple-line-icons.css", "src/scss/style.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "scripts": [], "allowedCommonJsDependencies": ["chart.js", "classnames"]}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "2mb"}, {"type": "initial", "maximumWarning": "9mb", "maximumError": "9mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "ng:build"}, "configurations": {"production": {"browserTarget": "ng:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ng:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": [], "styles": ["node_modules/@coreui/icons/css/free.css", "node_modules/flag-icon-css/css/flag-icon.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/simple-line-icons/dist/styles/simple-line-icons.css", "src/scss/style.scss"], "stylePreprocessorOptions": {"includePaths": ["./node_modules"]}, "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "ng-e2e": {"root": "", "sourceRoot": "", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "ng:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "ng", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}, "cli": {"analytics": false}}