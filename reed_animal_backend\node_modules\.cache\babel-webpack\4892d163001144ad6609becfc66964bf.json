{"ast": null, "code": "import getWrapDetails from './_getWrapDetails.js';\nimport insertWrapDetails from './_insertWrapDetails.js';\nimport setToString from './_setToString.js';\nimport updateWrapDetails from './_updateWrapDetails.js';\n/**\n * Sets the `toString` method of `wrapper` to mimic the source of `reference`\n * with wrapper details in a comment at the top of the source body.\n *\n * @private\n * @param {Function} wrapper The function to modify.\n * @param {Function} reference The reference function.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Function} Returns `wrapper`.\n */\n\nfunction setWrapToString(wrapper, reference, bitmask) {\n  var source = reference + '';\n  return setToString(wrapper, insertWrapDetails(source, updateWrapDetails(getWrapDetails(source), bitmask)));\n}\n\nexport default setWrapToString;", "map": null, "metadata": {}, "sourceType": "module"}