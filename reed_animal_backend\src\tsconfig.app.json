{"extends": "../tsconfig.json", "compilerOptions": {"outDir": "../out-tsc/app", "baseUrl": "./", "module": "es2020", "target": "es2015", "types": [], "paths": {"@angular/*": ["../node_modules/@angular/*"]}}, "angularCompilerOptions": {"enableIvy": true, "importHelpers": true}, "files": ["main.ts", "polyfills.ts"], "include": ["**/*.d.ts"], "exclude": ["src/**/*.spec.ts", "src/test.ts", "src/environments/environment.prod.ts"]}