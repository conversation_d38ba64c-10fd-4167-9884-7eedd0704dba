{"ast": null, "code": "/**\n * The base implementation of `_.slice` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseSlice(array, start, end) {\n  var index = -1,\n      length = array.length;\n\n  if (start < 0) {\n    start = -start > length ? 0 : length + start;\n  }\n\n  end = end > length ? length : end;\n\n  if (end < 0) {\n    end += length;\n  }\n\n  length = start > end ? 0 : end - start >>> 0;\n  start >>>= 0;\n  var result = Array(length);\n\n  while (++index < length) {\n    result[index] = array[index + start];\n  }\n\n  return result;\n}\n\nexport default baseSlice;", "map": null, "metadata": {}, "sourceType": "module"}