import { Component, OnInit } from '@angular/core';
import {LoginUser} from '../models/login.models';
import { TokenStorageService } from '../services/token-storage.service';
import {Loginservice} from '../services/login.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit {

  forgot:LoginUser = {
    email:''
  };
  isFailed = true;
  data:'';
  errormessage:'';
  isLoginFailed = false;
  constructor(private userservice: Loginservice,private route: ActivatedRoute,private router: Router, private tokenStorage: TokenStorageService) { }

  ngOnInit(): void {
  }

  getfocus():void{
    this.isLoginFailed = false;
  }
  ForgotPassword():void{
    if(this.forgot.email!=''){
      const data ={
        email:this.forgot.email
      }
      console.log("data",data)
      this.userservice.ForgotPassword(data)
      .subscribe((res)=>{
        console.log("testtttt")
        if (res.code === 200) {
          // this.router.navigate(['/users']);
          // this.tokenStorage.saveToken(res.data.tokens);
          //this.tokenStorage.saveUser(res.data);
          // console.log('err', res);
          this.data=res.message
          this.isLoginFailed = true;
          
        } else {
          // console.log('err', res.message);
          this.data = res.message;
          this.isLoginFailed = true;
        }
      })
    }
    
  }
}
