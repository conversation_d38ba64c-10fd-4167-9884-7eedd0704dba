(function () {
  "use strict";

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it["return"] != null) it["return"](); } finally { if (didErr) throw err; } } }; }

  function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

  function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

  function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

  function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

  function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

  (self["webpackChunkDr_Reed_Admin_Panel"] = self["webpackChunkDr_Reed_Admin_Panel"] || []).push([[891], {
    /***/
    91740: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "CovertusComponent": function CovertusComponent() {
          return (
            /* binding */
            _CovertusComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_covertus_services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/covertus.services */
      72945);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ngx-pagination */
      45055);

      function CovertusComponent_tr_70_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r1 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.DBID);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.Id);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.Code);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.CodeCategory);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.CodeCategoryDescription);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.CodeDescription);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.CodeType);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.MinimumPrice);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.MaximumPrice);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r1.Inactive === "false" ? "Active" : "Inactive");
        }
      }

      var _c0 = function _c0(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _CovertusComponent = /*#__PURE__*/function () {
        var CovertusComponent = /*#__PURE__*/function () {
          function CovertusComponent(covertusService) {
            _classCallCheck(this, CovertusComponent);

            this.covertusService = covertusService;
            this.page = 1;
            this.search = '';
            this.filter = '';
            this.status = '';
            this.CovertusList = [];
            this.count = 0;
          }

          _createClass(CovertusComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.GetCovertus();
            }
          }, {
            key: "GetCovertus",
            value: function GetCovertus() {
              var _this = this;

              var params = {
                skip: (this.page - 1) * 10,
                limit: 10,
                search: this.search,
                filter: this.filter,
                status: this.status
              };
              this.covertusService.GetCovertusList(params).subscribe(function (res) {
                console.log('covertusList-->', res);
                _this.CovertusList = res.data;
                _this.count = res.count;
              });
            }
          }, {
            key: "UpdateCovertus",
            value: function UpdateCovertus() {
              var _this2 = this;

              this.covertusService.UpdateCovertus({}).subscribe(function (res) {
                console.log('covertusList-->', res);

                _this2.GetCovertus();
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.GetCovertus();
            }
          }]);

          return CovertusComponent;
        }();

        CovertusComponent.ɵfac = function CovertusComponent_Factory(t) {
          return new (t || CovertusComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_covertus_services__WEBPACK_IMPORTED_MODULE_0__.CovertusService));
        };

        CovertusComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
          type: CovertusComponent,
          selectors: [["app-covertus"]],
          decls: 74,
          vars: 8,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col", 2, "margin-bottom", "12px"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", 3, "click"], [1, "col"], ["id", "select1", "name", "select1", 1, "form-control", 2, "width", "100%", 3, "change"], ["value", ""], ["value", "false"], ["value", "true"], ["value", "Diagnostic"], ["value", "Inventory"], ["value", "Service"], ["value", "Payment"], ["value", "Problem"], [1, "col", "input-group"], [1, "input-group-prepend"], [1, "input-group-text", 2, "height", "35px"], [1, "fa", "fa-search", 3, "click"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"]],
          template: function CovertusComponent_Template(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, " Covertus ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CovertusComponent_Template_button_click_8_listener() {
                return ctx.UpdateCovertus();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, " Update List ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](10, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "select", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("change", function CovertusComponent_Template_select_change_12_listener($event) {
                ctx.status = $event.target.value;
                return ctx.GetCovertus();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "option", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "--Status--");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "option", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16, "Active");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "option", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18, "Inactive");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "select", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("change", function CovertusComponent_Template_select_change_20_listener($event) {
                ctx.filter = $event.target.value;
                return ctx.GetCovertus();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "option", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, "--Code Type--");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "option", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, "Diagnostic");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](25, "option", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](26, "Inventory");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](27, "option", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](28, "Service");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](29, "option", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](30, "Payment");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "option", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](32, "Problem");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](33, "div", 17);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](34, "div", 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](35, "span", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](36, "i", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function CovertusComponent_Template_i_click_36_listener() {
                ctx.page = 1;
                return ctx.GetCovertus();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "input", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("input", function CovertusComponent_Template_input_input_37_listener() {
                ctx.page = 1;
                return ctx.GetCovertus();
              })("ngModelChange", function CovertusComponent_Template_input_ngModelChange_37_listener($event) {
                return ctx.search = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](38, "table", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](39, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](40, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](41, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](42, "DBID");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](43, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](44, "ID");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](45, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](46, "Code");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](47, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](48, "Code ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](49, "br");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](50, "Category");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](51, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](52, "Code Category ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](53, "br");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](54, "Description");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](55, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](56, "Code Description");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](57, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](58, "Code Type");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](59, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](60, "Minimum ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](61, "br");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](62, "Price");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](63, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](64, "Maximum ");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](65, "br");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](66, "Price");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](67, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](68, "Status");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](69, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](70, CovertusComponent_tr_70_Template, 21, 10, "tr", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](71, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](72, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](73, "pagination-controls", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("pageChange", function CovertusComponent_Template_pagination_controls_pageChange_73_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](37);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", ctx.search);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](33);

              _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind2"](71, 2, ctx.CovertusList, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction2"](5, _c0, ctx.page, ctx.count)));
            }
          },
          directives: [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_3__["ɵangular_packages_forms_forms_x"], _angular_forms__WEBPACK_IMPORTED_MODULE_3__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginationControlsComponent],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_1__.PaginatePipe],
          styles: [""]
        });
        return CovertusComponent;
      }();
      /***/

    },

    /***/
    32789: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "EmployeeComponent": function EmployeeComponent() {
          return (
            /* binding */
            _EmployeeComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_location_sevices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/location.sevices */
      87188);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["primaryModal"];
      var _c1 = ["removeModal"];

      function EmployeeComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 62);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r19);

            var ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](40);

            ctx_r18.EditId = "";
            ctx_r18.RemoveEmpText();
            return _r4.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Admin User ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_option_13_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "option", 63);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var item_r20 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", item_r20.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](item_r20.name);
        }
      }

      function EmployeeComponent_th_31_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Status");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_tr_35_td_9_Template(rf, ctx) {
        if (rf & 1) {
          var _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "label", 69);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "input", 70);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function EmployeeComponent_tr_35_td_9_Template_input_change_2_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

            var user_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r26.changed(user_r21.status, user_r21._id);
          })("ngModelChange", function EmployeeComponent_tr_35_td_9_Template_input_ngModelChange_2_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

            var user_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            return user_r21.status = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "span", 71);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r21.status);
        }
      }

      function EmployeeComponent_tr_35_a_11_Template(rf, ctx) {
        if (rf & 1) {
          var _r33 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 72);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_tr_35_a_11_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r33);

            var ctx_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var user_r21 = ctx_r34.$implicit;
            var i_r22 = ctx_r34.index;

            var ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](40);

            ctx_r32.EditId = user_r21._id;
            ctx_r32.Index = i_r22;
            ctx_r32.EditEmployee();
            return _r4.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 73);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 74);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_tr_35_a_12_Template(rf, ctx) {
        if (rf & 1) {
          var _r36 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 66);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_tr_35_a_12_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r36);

            var user_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r35 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](135);

            ctx_r35.EditId = user_r21._id;
            return _r17.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 75);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 76);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_tr_35_Template(rf, ctx) {
        if (rf & 1) {
          var _r39 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](9, EmployeeComponent_tr_35_td_9_Template, 4, 1, "td", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, EmployeeComponent_tr_35_a_11_Template, 4, 0, "a", 64);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](12, EmployeeComponent_tr_35_a_12_Template, 4, 0, "a", 65);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](13, "\xA0 ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "a", 66);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_tr_35_Template_a_click_14_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r39);

            var user_r21 = ctx.$implicit;

            var ctx_r38 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](101);

            ctx_r38.EditId = user_r21._id;
            ctx_r38.ViewLog(ctx_r38.EditId);
            return _r14.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "span", 67);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](16, "i", 68);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](17, "\xA0 view log");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r21 = ctx.$implicit;

          var ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r21.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r21.email);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r21.role_name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r21.location);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r3.Delete);
        }
      }

      function EmployeeComponent_div_56_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Name is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_56_div_2_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Alphabet characters only");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_56_div_3_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Name isn't long enough, minimum of 3 characters");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_56_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_56_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, EmployeeComponent_div_56_div_2_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, EmployeeComponent_div_56_div_3_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.f.name.errors.required);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.f.name.errors.pattern);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.f.name.errors.minlength);
        }
      }

      function EmployeeComponent_div_63_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Email is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_63_div_2_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Please enter a valid Email address");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_63_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_63_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, EmployeeComponent_div_63_div_2_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.email.errors.required);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.email.errors.email || ctx_r6.f.email.errors.pattern);
        }
      }

      function EmployeeComponent_div_64_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 78);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Email is already registered with us");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_option_73_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "option", 63);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var role_r45 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", role_r45.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", role_r45.name, "");
        }
      }

      function EmployeeComponent_div_74_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Role is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_74_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_74_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r9.f.role_name.errors.required);
        }
      }

      function EmployeeComponent_option_83_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "option", 63);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var item_r48 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", item_r48.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](item_r48.name);
        }
      }

      function EmployeeComponent_div_84_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Location is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_84_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_84_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r11.f.location.errors.required);
        }
      }

      function EmployeeComponent_div_89_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Phone number isn't long enough, minimum of 8 characters");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_89_div_2_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Numberic characters only");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_89_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_89_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, EmployeeComponent_div_89_div_2_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r12.f.phone_no.errors.minlength);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r12.f.phone_no.errors.pattern);
        }
      }

      function EmployeeComponent_div_94_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Address isn't long enough, minimum of 12 characters");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function EmployeeComponent_div_94_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 77);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_div_94_div_1_Template, 2, 0, "div", 18);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r13.f.address.errors.minlength);
        }
      }

      function EmployeeComponent_tr_120_td_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r53 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"]("", user_r53.description, " to ", user_r53.additional_info.status, "");
        }
      }

      function EmployeeComponent_tr_120_ng_template_2_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r53 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r53.description);
        }
      }

      function EmployeeComponent_tr_120_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr", 53);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, EmployeeComponent_tr_120_td_1_Template, 2, 2, "td", 79);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, EmployeeComponent_tr_120_ng_template_2_Template, 2, 1, "ng-template", null, 80, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplateRefExtractor"]);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](6, "date");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r53 = ctx.$implicit;

          var _r55 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](3);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", user_r53.additional_info)("ngIfElse", _r55);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](6, 3, user_r53.updatedAt, "short"));
        }
      }

      var _c2 = function _c2(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c3 = function _c3() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c4 = function _c4(a0) {
        return {
          "is-invalid": a0
        };
      };

      var _EmployeeComponent = /*#__PURE__*/function () {
        var EmployeeComponent = /*#__PURE__*/function () {
          function EmployeeComponent(employeeService, route, router, tokenStorage, EmployeeService, formBuilder, Permission, LocationService) {
            _classCallCheck(this, EmployeeComponent);

            this.employeeService = employeeService;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.EmployeeService = EmployeeService;
            this.formBuilder = formBuilder;
            this.Permission = Permission;
            this.LocationService = LocationService;
            this.employees = [];
            this.viewlog = [];
            this.roles = [];
            this.locations = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.email = false;
            this.phone_no = '';
            this.submitted = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
            this.location = '';
            this.EditId = '';
            this.Index = 0;
            this.GetRoleLists();
            this.GetLocation();
            this.GetEmployeeLists();
          }

          _createClass(EmployeeComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              var _this3 = this;

              this.SignForm();
              var Role = this.tokenStorage.getUser(); // if (key != null) {

              this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                // console.log(res)
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].module_name == "Admin-Users") {
                    _this3.Add = res.data[i].add;
                    _this3.Edit = res.data[i].edit;
                    _this3.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                  }
                }
              });
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.email = false;
              this.loginForm.reset();
              this.submitted = false;
            } //Get All Role List

          }, {
            key: "GetRoleLists",
            value: function GetRoleLists() {
              var _this4 = this;

              this.employeeService.GetRoleList().subscribe(function (res) {
                _this4.roles = res.data;
              });
            }
          }, {
            key: "RemoveEmpText",
            value: function RemoveEmpText() {
              console.log("phone test");
              this.phone_no = '';
            } //Get location list

          }, {
            key: "GetLocation",
            value: function GetLocation() {
              var _this5 = this;

              var data = '';
              var param = '';
              this.LocationService.GetLocationsList(data, param).subscribe(function (res) {
                _this5.locations = res.data;

                _this5.locations.filter(function (item) {
                  return item.status === true;
                });

                _this5.filteredLocations = _this5.locations.filter(function (item) {
                  return item.status === true;
                });
                console.log("testing locationnnnnnnnn", _this5.filteredLocations);
              });

              var _iterator = _createForOfIteratorHelper(this.locations),
                  _step;

              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  var item = _step.value;
                  console.log("forloop", item[0]);
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
            } //Get All Role List

          }, {
            key: "GetEmployeeLists",
            value: function GetEmployeeLists() {
              var _this6 = this;

              var data = {
                skip: (this.page - 1) * 10,
                search: this.name,
                location: this.location
              };
              this.employeeService.GetEmployeeList(data).subscribe(function (res) {
                console.log("res", res);
                _this6.count = res.count;
                _this6.employees = res.data;
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event;
              this.GetEmployeeLists();
            }
          }, {
            key: "SignForm",
            value: function SignForm() {
              this.loginForm = this.formBuilder.group({
                name: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(3), _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.pattern('[a-zA-Z. -_]*$')]],
                role_id: [''],
                role_name: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required]],
                email: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.email, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
                location: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required]],
                address: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(12)]],
                phone_no: [[_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.minLength(8), _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.pattern('[0-9-]*$')]]
              });
            }
          }, {
            key: "f",
            get: function get() {
              return this.loginForm.controls;
            } //Add New Employee

          }, {
            key: "onSubmit",
            value: function onSubmit() {
              var _this7 = this;

              console.log(this.loginForm.value);
              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              }

              if (this.EditId == '') {
                this.employeeService.NewEmployee(this.loginForm.value).subscribe(function (res) {
                  if (res.code == 200) {
                    _this7.submitted = false;

                    _this7.loginForm.reset();

                    _this7.primaryModal.hide();

                    _this7.GetRoleLists();
                  } else {
                    _this7.email = true;
                  }
                });
              } else {
                this.UpdateEmployee(this.EditId, this.loginForm.value);
              }
            }
          }, {
            key: "UpdateEmployee",
            value: function UpdateEmployee(Id, data) {
              var _this8 = this;

              this.employeeService.EditEmployeeDetail(Id, data).subscribe(function (res) {
                if (res.code == 200) {
                  _this8.submitted = false;

                  _this8.loginForm.reset();

                  _this8.primaryModal.hide();

                  if (_this8.EditId != '') _this8.GetEmployeeLists();
                }
              });
            }
          }, {
            key: "ViewLog",
            value: function ViewLog(id) {
              var _this9 = this;

              console.log("Admin id", id);
              this.employeeService.GetViewLog(id).subscribe(function (res) {
                _this9.viewlog = res.data;
                console.log(_this9.viewlog);
              });
            }
          }, {
            key: "EditEmployee",
            value: function EditEmployee() {
              this.loginForm.controls['name'].setValue(this.employees[this.Index].name);
              this.loginForm.controls['role_id'].setValue(this.employees[this.Index].role_id);
              this.loginForm.controls['role_name'].setValue(this.employees[this.Index].role_name);
              this.loginForm.controls['email'].setValue(this.employees[this.Index].email);
              this.loginForm.controls['location'].setValue(this.employees[this.Index].location);
              this.loginForm.controls['address'].setValue(this.employees[this.Index].address);
              this.loginForm.controls['phone_no'].setValue(this.employees[this.Index].phone_no);
            } //Delete Employee

          }, {
            key: "DeleteEmployee",
            value: function DeleteEmployee(id) {
              var _this10 = this;

              this.employeeService.DeleteEmployee(id).subscribe(function (res) {
                _this10.removeModal.hide();

                _this10.GetEmployeeLists();
              });
            } //ON or OFF value in table

          }, {
            key: "changed",
            value: function changed(param, id) {
              var data = {
                status: param
              };
              this.UpdateEmployee(id, data);
            }
          }, {
            key: "RoleSetValue",
            value: function RoleSetValue(event) {
              var _this11 = this;

              this.roles.forEach(function (element) {
                if (element.name === event) {
                  _this11.loginForm.controls['role_id'].setValue(element._id);
                }
              });
            }
          }]);

          return EmployeeComponent;
        }();

        EmployeeComponent.ɵfac = function EmployeeComponent_Factory(t) {
          return new (t || EmployeeComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_1__.Employeeservice), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_1__.Employeeservice), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_location_sevices__WEBPACK_IMPORTED_MODULE_3__.LocationService));
        };

        EmployeeComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: EmployeeComponent,
          selectors: [["app-employee"]],
          viewQuery: function EmployeeComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.removeModal = _t.first);
            }
          },
          decls: 151,
          vars: 53,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "filter"], [1, "form-control", 3, "ngModel", "ngModelChange", "change"], ["value", "", "selected", ""], [3, "value", 4, "ngFor", "ngForOf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text", 3, "click"], [1, "fa", "fa-search"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngIf"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["primaryModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], ["autocomplete", "off", 1, "form", 3, "formGroup"], [1, "modal-body"], [1, "col-sm-12"], [1, "form-group"], ["for", "name"], [2, "color", "red"], ["type", "text", "placeholder", "Name", "formControlName", "name", "autocomplete", "off", 1, "form-control", 3, "ngClass"], ["class", "invalid-feedback", 4, "ngIf"], ["for", "email"], ["type", "email", "placeholder", "e.g. <EMAIL>", "formControlName", "email", "autocomplete", "off", 1, "form-control", 3, "ngClass", "readonly"], ["style", "font-size: smaller;color: #f86c6b;margin-top: -14px;\n              margin-bottom: 12px;", 4, "ngIf"], ["for", "select2"], ["id", "Role", "name", "select1", "formControlName", "role_name", 1, "form-control", 3, "ngClass", "change"], ["value", "", "selected", "", 3, "hidden"], ["for", "location"], ["id", "location", "name", "location", "formControlName", "location", 1, "form-control", 3, "ngClass"], ["for", "phone_no"], ["id", "phone_no", "type", "text", "autocomplete", "off", "placeholder", "e.g. 9874563210", "formControlName", "phone_no", 1, "form-control", 3, "ngModel", "ngClass", "ngModelChange"], ["for", "Address"], ["type", "text", "autocomplete", "off", "placeholder", "e.g. No.70,Mission street, Florida", "formControlName", "address", 1, "form-control", 3, "ngClass"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "click"], ["primaryModal1", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-primary", 2, "max-width", "60%"], [1, "modal-title", 2, "padding", "2%"], [2, "text-align", "center"], ["style", "text-align: center;", 4, "ngFor", "ngForOf"], ["type", "button", 1, "btn", "btn-primary", 2, "width", "15%", "padding", "2px", "margin-left", "83%", 3, "click"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade"], ["okayModal", "bs-modal"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["removeModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], [3, "value"], ["data-toggle", "modal", "style", "cursor: pointer; margin-right: 10px;", 3, "click", 4, "ngIf"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-secondary", "sucpad", 2, "padding", "7px", "font-size", "small", "color", "white", "background-color", "#2f23ef"], [1, "fa", "fa-list"], [1, "switch"], ["type", "checkbox", "checked", "user.status", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"], ["data-toggle", "modal", 2, "cursor", "pointer", "margin-right", "10px", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [1, "invalid-feedback"], [2, "font-size", "smaller", "color", "#f86c6b", "margin-top", "-14px", "margin-bottom", "12px"], [4, "ngIf", "ngIfElse"], ["elseBlock", ""]],
          template: function EmployeeComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r59 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Admin Users ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, EmployeeComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "select", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function EmployeeComponent_Template_select_ngModelChange_10_listener($event) {
                return ctx.location = $event;
              })("change", function EmployeeComponent_Template_select_change_10_listener() {
                ctx.page = 1;
                return ctx.GetEmployeeLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "option", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](12, "--Location--");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](13, EmployeeComponent_option_13_Template, 2, 2, "option", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "div", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "div", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "div", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "span", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_span_click_17_listener() {
                ctx.page = 1;
                return ctx.GetEmployeeLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](18, "i", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](19, "input", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function EmployeeComponent_Template_input_input_19_listener() {
                ctx.page = 1;
                return ctx.GetEmployeeLists();
              })("ngModelChange", function EmployeeComponent_Template_input_ngModelChange_19_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "table", 17);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](24, "Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](25, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](26, "E-mail");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](28, "Role");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](30, "Location");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](31, EmployeeComponent_th_31_Template, 2, 0, "th", 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](33, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](34, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](35, EmployeeComponent_tr_35_Template, 18, 7, "tr", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](36, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "pagination-controls", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function EmployeeComponent_Template_pagination_controls_pageChange_38_listener($event) {
                ctx.page = $event;
                return ctx.GetEmployeeLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "div", 21, 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](41, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](42, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "h4", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](45);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](46, "form", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](47, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](48, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](50, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "label", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](52, "Name ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "span", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](54, "*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](55, "input", 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](56, EmployeeComponent_div_56_Template, 4, 3, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "label", 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](59, "Email ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](60, "span", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](61, "*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](62, "input", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](63, EmployeeComponent_div_63_Template, 3, 2, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](64, EmployeeComponent_div_64_Template, 2, 0, "div", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](66, "label", 38);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](67, "Role ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](68, "span", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](69, "*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](70, "select", 39);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function EmployeeComponent_Template_select_change_70_listener($event) {
                return ctx.RoleSetValue($event.target.value);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](71, "option", 40);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](72, "--Select Role--");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](73, EmployeeComponent_option_73_Template, 2, 2, "option", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](74, EmployeeComponent_div_74_Template, 2, 1, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](76, "label", 41);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](77, "Location ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](78, "span", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](79, "*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "select", 42);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](81, "option", 40);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](82, "--Select--");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](83, EmployeeComponent_option_83_Template, 2, 2, "option", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](84, EmployeeComponent_div_84_Template, 2, 1, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](85, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](86, "label", 43);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](87, "Phone Number");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](88, "input", 44);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function EmployeeComponent_Template_input_ngModelChange_88_listener($event) {
                return ctx.phone_no = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](89, EmployeeComponent_div_89_Template, 3, 2, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](90, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](91, "label", 45);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](92, "Address");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](93, "textarea", 46);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](94, EmployeeComponent_div_94_Template, 2, 1, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](95, "div", 47);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](96, "button", 48);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_96_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r59);

                var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](40);

                _r4.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](97, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](98, "button", 49);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_98_listener() {
                return ctx.onSubmit();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](99, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](100, "div", 21, 50);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](102, "div", 51);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](103, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](104, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](105, "h4", 52);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](106, "Employee Activity");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](107, "form", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](108, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](109, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](110, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](111, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](112, "table", 17);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](113, "thead", 53);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](114, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](115, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](116, "Description");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](117, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](118, "Timestamp");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](119, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](120, EmployeeComponent_tr_120_Template, 7, 6, "tr", 54);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](121, "div", 47);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](122, "button", 55);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_122_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r59);

                var _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](101);

                return _r14.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](123, "Close");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](124, "div", 56, 57);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](126, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](127, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](128, "h4", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](129, "Admin User Created Successfully");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](130, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](131, "please, Check the mail and set new password");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](132, "button", 48);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_132_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r59);

                var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](125);

                _r16.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](133, "ok");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](134, "div", 58, 59);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](136, "div", 60);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](137, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](138, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](139, "h4", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](140, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](141, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](142, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](143, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](144, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](145, "Do you want to delete this User?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](146, "div", 47);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](147, "button", 48);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_147_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r59);

                var _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](135);

                return _r17.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](148, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](149, "button", 61);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EmployeeComponent_Template_button_click_149_listener() {
                return ctx.DeleteEmployee(ctx.EditId);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](150, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.location);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.locations);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Edit);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](36, 32, ctx.employees, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](35, _c2, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](38, _c3));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx.EditId == "" ? "Add Admin User" : "Edit Admin User");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](39, _c4, ctx.submitted && ctx.f.name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](41, _c4, ctx.submitted && ctx.f.email.errors))("readonly", ctx.EditId != "");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.email.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.email);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](43, _c4, ctx.submitted && ctx.f.role_name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("hidden", ctx.EditId != "");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.roles);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.role_name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](45, _c4, ctx.submitted && ctx.f.location.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("hidden", ctx.EditId != "");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.filteredLocations);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.location.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.phone_no)("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](47, _c4, ctx.submitted && ctx.f.phone_no.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.phone_no.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](49, _c4, ctx.submitted && ctx.f.address.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.address.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](51, _c3));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.viewlog);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](52, _c3));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgModel, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_x"], _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe, _angular_common__WEBPACK_IMPORTED_MODULE_8__.DatePipe],
          styles: ["#select1[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.filter[_ngcontent-%COMP%] {\n  width: 127px;\n  display: inline-block;\n  margin-left: 45%;\n  margin-top: 3px;\n}"]
        });
        return EmployeeComponent;
      }();
      /***/

    },

    /***/
    7173: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "LocationComponent": function LocationComponent() {
          return (
            /* binding */
            _LocationComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_location_sevices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/location.sevices */
      87188);
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["primaryModal"];
      var _c1 = ["AddModal"];
      var _c2 = ["removeModal"];

      function LocationComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 38);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);

            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

            return _r3.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Location ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function LocationComponent_th_20_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Status");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function LocationComponent_tr_24_td_3_Template(rf, ctx) {
        if (rf & 1) {
          var _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "label", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "input", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function LocationComponent_tr_24_td_3_Template_input_change_2_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r15);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r13.changed(user_r9.status, user_r9._id);
          })("ngModelChange", function LocationComponent_tr_24_td_3_Template_input_ngModelChange_2_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r15);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            return user_r9.status = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "span", 42);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r9.status);
        }
      }

      function LocationComponent_tr_24_a_5_Template(rf, ctx) {
        if (rf & 1) {
          var _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_tr_24_a_5_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r20);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

            _r5.show();

            return ctx_r19.Getlocation(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 44);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function LocationComponent_tr_24_a_6_Template(rf, ctx) {
        if (rf & 1) {
          var _r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_tr_24_a_6_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r23);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](68);

            _r6.show();

            return ctx_r22.Getlocation(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function LocationComponent_tr_24_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, LocationComponent_tr_24_td_3_Template, 4, 1, "td", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, LocationComponent_tr_24_a_5_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](6, LocationComponent_tr_24_a_6_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r9 = ctx.$implicit;

          var ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r9.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Delete);
        }
      }

      function LocationComponent_div_42_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*please enter location");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5() {
        return {
          standalone: true
        };
      };

      var _LocationComponent = /*#__PURE__*/function () {
        var LocationComponent = /*#__PURE__*/function () {
          function LocationComponent(locationservice, route, router, tokenStorage, Permission, EmployeeService) {
            _classCallCheck(this, LocationComponent);

            this.locationservice = locationservice;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.locations = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.location = {};
            this.rolefailed = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
          }

          _createClass(LocationComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
            }
          }, {
            key: "show",
            value: function show() {
              this.AddModal.show();
              this.primaryModal.show();
              this.removeModal.show();
            }
          }, {
            key: "hide",
            value: function hide() {
              this.AddModal.hide();
              this.primaryModal.hide();
              this.removeModal.hide();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.location = {};
              this.rolefailed = false;
            } //token verified location

          }, {
            key: "tokens",
            value: function tokens() {
              var _this12 = this;

              var key = this.tokenStorage.getToken();
              var Role = this.tokenStorage.getUser();

              if (key != null) {
                this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                  // console.log(res)
                  for (var i = 0; i < res.data.length; i++) {
                    if (res.data[i].module_name == "Location") {
                      _this12.Add = res.data[i].add;
                      _this12.Edit = res.data[i].edit;
                      _this12.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                    }
                  }
                });
                this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                  // console.log(res.data[0].status)
                  if (res.data[0].status == false) {
                    _this12.tokenStorage.signOut();
                  }
                });
                this.locationLists();
              } else {
                this.router.navigate(['/login']);
              }
            }
          }, {
            key: "getfocus",
            value: function getfocus() {
              this.rolefailed = false;
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              return skip;
            } //Get All location List

          }, {
            key: "locationLists",
            value: function locationLists() {
              var _this13 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.locationservice.GetLocationsList(skip, this.name).subscribe(function (res) {
                _this13.locations = res.data;
                _this13.count = res.count; // console.log(this.locations);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.locationLists();
            } //Edit or update location 

          }, {
            key: "Getlocation",
            value: function Getlocation(id) {
              var _this14 = this;

              // console.log('id-->', id);
              this.locationservice.GetLocationDetail(id).subscribe(function (res) {
                _this14.location = res.data[0]; // console.log(res.data)
              });
            }
          }, {
            key: "Editlocation",
            value: function Editlocation(id) {
              var _this15 = this;

              // console.log('id-->', id, this.location.name)
              var data = {
                name: this.location.name
              };
              this.locationservice.UpdateLocation(id, data).subscribe(function (res) {
                // console.log('res-->', res);
                _this15.location = {};

                _this15.locationLists();
              });
            } //Status ON & OFF

          }, {
            key: "changed",
            value: function changed(active, id) {
              var data = {
                status: active
              };
              this.locationservice.UpdateLocation(id, data).subscribe(function (res) {// console.log('res-->', res);
              });
            } //Add new location

          }, {
            key: "Addlocation",
            value: function Addlocation() {
              var _this16 = this;

              // console.log('name-->', this.location.name)
              if (this.location.name != undefined && this.location.name != '') {
                this.AddModal.hide();
                var data = {
                  name: this.location.name
                };
                this.locationservice.NewLocation(data).subscribe(function (res) {
                  _this16.location = {}; // console.log('new-->', res)

                  _this16.locationLists();
                });
              }

              this.rolefailed = true;
            } //Delete location using id

          }, {
            key: "Deletelocation",
            value: function Deletelocation(id) {
              var _this17 = this;

              // console.log('id-->', id)
              this.locationservice.DeleteLocation(id).subscribe(function (res) {
                // console.log('res-->', res)
                _this17.removeModal.hide();

                _this17.locationLists();
              });
            }
          }]);

          return LocationComponent;
        }();

        LocationComponent.ɵfac = function LocationComponent_Factory(t) {
          return new (t || LocationComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_location_sevices__WEBPACK_IMPORTED_MODULE_0__.LocationService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_3__.Employeeservice));
        };

        LocationComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: LocationComponent,
          selectors: [["app-location"]],
          viewQuery: function LocationComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.AddModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.removeModal = _t.first);
            }
          },
          decls: 84,
          vars: 23,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text", 3, "click"], [1, "fa", "fa-search"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngIf"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "Module", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["AddModal", "bs-modal"], ["Module", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "col-sm-12"], [1, "form-group"], ["for", "name"], ["type", "text", "id", "Type-name", "placeholder", "Enter Location Name", "autocomplete", "off", "required", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter", "click"], ["style", "font-size: smaller;color: red;", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["primaryModal", "bs-modal"], ["type", "text", "id", "edit-name", "placeholder", "Location Name", "autocomplete", "off", "required", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["removeModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], [1, "switch"], ["type", "checkbox", "checked", "user.status", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [2, "font-size", "smaller", "color", "red"]],
          template: function LocationComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Location ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, LocationComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_span_click_12_listener() {
                return ctx.locationLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function LocationComponent_Template_input_input_14_listener() {
                return ctx.locationLists();
              })("ngModelChange", function LocationComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Location Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](20, LocationComponent_th_20_Template, 2, 0, "th", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](22, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](24, LocationComponent_tr_24_Template, 7, 4, "tr", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](25, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "pagination-controls", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function LocationComponent_Template_pagination_controls_pageChange_27_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "div", 17, 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, "Add Location");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "label", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](40, "Location Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](41, "input", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function LocationComponent_Template_input_ngModelChange_41_listener($event) {
                return ctx.location.name = $event;
              })("keydown.enter", function LocationComponent_Template_input_keydown_enter_41_listener() {
                return ctx.Addlocation();
              })("click", function LocationComponent_Template_input_click_41_listener() {
                return ctx.getfocus();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](42, LocationComponent_div_42_Template, 2, 0, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_44_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

                _r3.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](45, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](46, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_46_listener() {
                return ctx.Addlocation();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](47, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](48, "div", 17, 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](50, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](54, "Edit Location");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](55, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](56, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](59, "label", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](60, "Location Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "input", 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function LocationComponent_Template_input_ngModelChange_61_listener($event) {
                return ctx.location.name = $event;
              })("keydown.enter", function LocationComponent_Template_input_keydown_enter_61_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                ctx.Editlocation(ctx.location._id);
                return _r5.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](62, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](63, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_63_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                _r5.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](64, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_65_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                ctx.Editlocation(ctx.location._id);
                return _r5.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](66, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](67, "div", 34, 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](69, "div", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](70, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](71, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](72, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](73, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](74, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](76, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](77, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](78, "Do you want to delete this Location?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](79, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_80_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](68);

                _r6.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](81, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](82, "button", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function LocationComponent_Template_button_click_82_listener() {
                return ctx.Deletelocation(ctx.location._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](83, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Edit);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](25, 12, ctx.locations, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](15, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](18, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.location.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](19, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.rolefailed);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](20, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.location.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](21, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](22, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe],
          styles: [""]
        });
        return LocationComponent;
      }();
      /***/

    },

    /***/
    63196: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "MasterRoutingModule": function MasterRoutingModule() {
          return (
            /* binding */
            _MasterRoutingModule
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _role_role_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./role/role.component */
      35479);
      /* harmony import */


      var _module_module_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./module/module.component */
      58478);
      /* harmony import */


      var _animal_type_animal_type_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ./animal-type/animal-type.component */
      19148);
      /* harmony import */


      var _treatments_treatments_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ./treatments/treatments.component */
      31473);
      /* harmony import */


      var _location_location_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ./location/location.component */
      7173);
      /* harmony import */


      var _covertus_covertus_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! ./covertus/covertus.component */
      91740);
      /* harmony import */


      var _employee_employee_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! ./employee/employee.component */
      32789);
      /* harmony import */


      var _breeding_breeding_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! ./breeding/breeding.component */
      8816);
      /* harmony import */


      var _authguard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! ../../authguard */
      57481);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! @angular/core */
      3048);

      var routes = [{
        path: '',
        data: {
          title: 'Settings'
        },
        children: [{
          path: 'role',
          component: _role_role_component__WEBPACK_IMPORTED_MODULE_0__.RoleComponent,
          data: {
            title: 'Role',
            path: '/settings/role'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'module',
          component: _module_module_component__WEBPACK_IMPORTED_MODULE_1__.ModuleComponent,
          data: {
            title: 'Module',
            path: '/settings'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'animal-type',
          component: _animal_type_animal_type_component__WEBPACK_IMPORTED_MODULE_2__.AnimalTypeComponent,
          data: {
            title: 'Species',
            path: '/settings/animal-type'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'appointment-types',
          component: _treatments_treatments_component__WEBPACK_IMPORTED_MODULE_3__.TreatmentsComponent,
          data: {
            title: 'Appointment Types',
            path: '/settings/appointment-types'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'breed',
          component: _breeding_breeding_component__WEBPACK_IMPORTED_MODULE_7__.BreedingComponent,
          data: {
            title: 'Breed',
            path: '/settings/breed'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'location',
          component: _location_location_component__WEBPACK_IMPORTED_MODULE_4__.LocationComponent,
          data: {
            title: 'Location',
            path: '/settings/location'
          }
        }, {
          path: 'covetrus',
          component: _covertus_covertus_component__WEBPACK_IMPORTED_MODULE_5__.CovertusComponent,
          data: {
            title: 'Covetrus',
            path: '/settings/covetrus'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }, {
          path: 'employee',
          component: _employee_employee_component__WEBPACK_IMPORTED_MODULE_6__.EmployeeComponent,
          data: {
            title: 'Admin Users',
            path: '/settings/employee'
          },
          canActivate: [_authguard__WEBPACK_IMPORTED_MODULE_8__.AuthGuardService]
        }]
      }];

      var _MasterRoutingModule = /*#__PURE__*/function () {
        var MasterRoutingModule = function MasterRoutingModule() {
          _classCallCheck(this, MasterRoutingModule);
        };

        MasterRoutingModule.ɵmod = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineNgModule"]({
          type: MasterRoutingModule
        });
        MasterRoutingModule.ɵinj = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineInjector"]({
          factory: function MasterRoutingModule_Factory(t) {
            return new (t || MasterRoutingModule)();
          },
          imports: [[_angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule.forChild(routes)], _angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule]
        });
        return MasterRoutingModule;
      }();

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsetNgModuleScope"](_MasterRoutingModule, {
          imports: [_angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule],
          exports: [_angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterModule]
        });
      })();
      /***/

    },

    /***/
    94891: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "MasterModule": function MasterModule() {
          return (
            /* binding */
            _MasterModule
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _master_routing_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./master-routing.module */
      63196);
      /* harmony import */


      var _role_role_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ./role/role.component */
      35479);
      /* harmony import */


      var _module_module_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ./module/module.component */
      58478);
      /* harmony import */


      var _animal_type_animal_type_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ./animal-type/animal-type.component */
      19148);
      /* harmony import */


      var _treatments_treatments_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! ./treatments/treatments.component */
      31473);
      /* harmony import */


      var _location_location_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! ./location/location.component */
      7173);
      /* harmony import */


      var _covertus_covertus_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! ./covertus/covertus.component */
      91740);
      /* harmony import */


      var _employee_employee_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! ./employee/employee.component */
      32789);
      /* harmony import */


      var _pages_change_password_change_password_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ../pages/change-password/change-password.component */
      81467);
      /* harmony import */


      var _breeding_breeding_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(
      /*! ./breeding/breeding.component */
      8816);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(
      /*! @angular/core */
      3048); // import { DoctorComponent } from './doctor/doctor.component';


      var _MasterModule = /*#__PURE__*/function () {
        var MasterModule = function MasterModule() {
          _classCallCheck(this, MasterModule);
        };

        MasterModule.ɵmod = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineNgModule"]({
          type: MasterModule
        });
        MasterModule.ɵinj = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineInjector"]({
          factory: function MasterModule_Factory(t) {
            return new (t || MasterModule)();
          },
          imports: [[_angular_common__WEBPACK_IMPORTED_MODULE_12__.CommonModule, _master_routing_module__WEBPACK_IMPORTED_MODULE_1__.MasterRoutingModule, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_13__.ModalModule.forRoot(), ngx_pagination__WEBPACK_IMPORTED_MODULE_0__.NgxPaginationModule, _angular_forms__WEBPACK_IMPORTED_MODULE_14__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_14__.ReactiveFormsModule]]
        });
        return MasterModule;
      }();

      (function () {
        (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵsetNgModuleScope"](_MasterModule, {
          declarations: [_role_role_component__WEBPACK_IMPORTED_MODULE_2__.RoleComponent, _module_module_component__WEBPACK_IMPORTED_MODULE_3__.ModuleComponent, _breeding_breeding_component__WEBPACK_IMPORTED_MODULE_10__.BreedingComponent, _animal_type_animal_type_component__WEBPACK_IMPORTED_MODULE_4__.AnimalTypeComponent, _treatments_treatments_component__WEBPACK_IMPORTED_MODULE_5__.TreatmentsComponent, _location_location_component__WEBPACK_IMPORTED_MODULE_6__.LocationComponent, _covertus_covertus_component__WEBPACK_IMPORTED_MODULE_7__.CovertusComponent, _employee_employee_component__WEBPACK_IMPORTED_MODULE_8__.EmployeeComponent, _pages_change_password_change_password_component__WEBPACK_IMPORTED_MODULE_9__.ChangePasswordComponent],
          imports: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.CommonModule, _master_routing_module__WEBPACK_IMPORTED_MODULE_1__.MasterRoutingModule, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_13__.ModalModule, ngx_pagination__WEBPACK_IMPORTED_MODULE_0__.NgxPaginationModule, _angular_forms__WEBPACK_IMPORTED_MODULE_14__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_14__.ReactiveFormsModule]
        });
      })();
      /***/

    },

    /***/
    58478: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "ModuleComponent": function ModuleComponent() {
          return (
            /* binding */
            _ModuleComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_module_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/module.service */
      49533);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["AddModal"];
      var _c1 = ["primaryModal"];
      var _c2 = ["deleteModal"];

      function ModuleComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);

            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

            return _r3.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Module ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function ModuleComponent_th_20_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Status");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function ModuleComponent_tr_24_td_3_Template(rf, ctx) {
        if (rf & 1) {
          var _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "label", 42);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "input", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function ModuleComponent_tr_24_td_3_Template_input_change_2_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r15);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r13.changed(user_r9.acc_activation, user_r9._id);
          })("ngModelChange", function ModuleComponent_tr_24_td_3_Template_input_ngModelChange_2_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r15);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            return user_r9.acc_activation = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "span", 44);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r9.acc_activation);
        }
      }

      function ModuleComponent_tr_24_a_5_Template(rf, ctx) {
        if (rf & 1) {
          var _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_tr_24_a_5_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r20);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

            _r5.show();

            return ctx_r19.GetModule(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function ModuleComponent_tr_24_a_6_Template(rf, ctx) {
        if (rf & 1) {
          var _r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_tr_24_a_6_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r23);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](68);

            _r6.show();

            return ctx_r22.GetModulee(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 49);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function ModuleComponent_tr_24_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, ModuleComponent_tr_24_td_3_Template, 4, 1, "td", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, ModuleComponent_tr_24_a_5_Template, 4, 0, "a", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](6, ModuleComponent_tr_24_a_6_Template, 4, 0, "a", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r9 = ctx.$implicit;

          var ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r9.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Delete);
        }
      }

      function ModuleComponent_div_42_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 50);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*please enter Module");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5() {
        return {
          standalone: true
        };
      };

      var _ModuleComponent = /*#__PURE__*/function () {
        var ModuleComponent = /*#__PURE__*/function () {
          function ModuleComponent(moduleService, route, router, tokenStorage, Permission, EmployeeService) {
            _classCallCheck(this, ModuleComponent);

            this.moduleService = moduleService;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.modules = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.module = {
              id: '',
              name: '',
              acc_activation: ''
            };
            this.rolefailed = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
          }

          _createClass(ModuleComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
            }
          }, {
            key: "show",
            value: function show() {
              this.AddModal.show();
              this.primaryModal.show();
            }
          }, {
            key: "hide",
            value: function hide() {
              this.AddModal.hide();
              this.primaryModal.hide();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.module = {};
            } //token verified module

          }, {
            key: "tokens",
            value: function tokens() {
              var _this18 = this;

              var key = this.tokenStorage.getToken();
              var Role = this.tokenStorage.getUser();

              if (key != null) {
                this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                  // console.log(res)
                  for (var i = 0; i < res.data.length; i++) {
                    if (res.data[i].module_name == "Module") {
                      _this18.Add = res.data[i].add;
                      _this18.Edit = res.data[i].edit;
                      _this18.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                    }
                  }
                });
                this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                  // console.log(res.data[0].status)
                  if (res.data.status == false) {
                    _this18.tokenStorage.signOut();
                  }
                });
                this.GetModuleLists();
              } else {
                this.router.navigate(['/login']);
              }
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              return skip;
            } //Get All module List

          }, {
            key: "GetModuleLists",
            value: function GetModuleLists() {
              var _this19 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.moduleService.GetModuleList(skip, this.name).subscribe(function (res) {
                _this19.modules = res.data;
                _this19.count = res.count; // console.log(this.modules);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.GetModuleLists();
            } //Edit or update module 

          }, {
            key: "GetModule",
            value: function GetModule(id) {
              var _this20 = this;

              // console.log('id-->', id);
              this.moduleService.GetModuleDetail(id).subscribe(function (res) {
                _this20.module = res.data[0]; // console.log(res.data)
              });
            }
          }, {
            key: "EditModule",
            value: function EditModule(id) {
              var _this21 = this;

              // console.log('id-->', id, this.module.name)
              var data = {
                name: this.module.name
              };
              this.moduleService.UpdateModule(id, data).subscribe(function (res) {
                // console.log('res-->', res);
                _this21.module = {};

                _this21.GetModuleLists();
              });
            } //Status ON & OFF

          }, {
            key: "changed",
            value: function changed(active, id) {
              var data = {
                acc_activation: active
              }; // console.log('data-->', data);

              this.moduleService.UpdateModule(id, data).subscribe(function (res) {// console.log('res-->', res);
              });
            } //Add new module

          }, {
            key: "AddModule",
            value: function AddModule() {
              var _this22 = this;

              // console.log('name-->', this.module.name)
              if (this.module.name != undefined && this.module.name != '') {
                this.AddModal.hide();
                var data = {
                  name: this.module.name
                };
                this.moduleService.NewModule(data).subscribe(function (res) {
                  _this22.module = {};
                  _this22.rolefailed = false; // console.log('new-->', res)

                  _this22.GetModuleLists();
                });
              }

              this.rolefailed = true;
            } //Delete module using id

          }, {
            key: "DeleteModule",
            value: function DeleteModule() {
              var _this23 = this;

              // console.log('id-->', id)
              this.moduleService.DeleteModule(this.Id).subscribe(function (res) {
                // console.log('res-->', res)
                _this23.GetModuleLists();
              });
            }
          }, {
            key: "GetModulee",
            value: function GetModulee(id) {
              this.Id = id;
            }
          }]);

          return ModuleComponent;
        }();

        ModuleComponent.ɵfac = function ModuleComponent_Factory(t) {
          return new (t || ModuleComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_module_service__WEBPACK_IMPORTED_MODULE_1__.ModuleService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_3__.Employeeservice));
        };

        ModuleComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: ModuleComponent,
          selectors: [["app-module"]],
          viewQuery: function ModuleComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.AddModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.deleteModal = _t.first);
            }
          },
          decls: 84,
          vars: 23,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "fa", "fa-search", 3, "click"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngIf"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "module", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["AddModal", "bs-modal"], ["rodule", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "col-sm-12"], [1, "form-group"], ["for", "name"], ["type", "text", "id", "module-name", "placeholder", "Enter your Module Name", "autocomplete", "off", "required", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter"], ["style", "font-size: smaller;color: red;", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "Module", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["primaryModal", "bs-modal"], ["Module", "document", 1, "modal-dialog", "modal-primary"], ["type", "text", "id", "edit-name", "placeholder", "Enter your Module Name", "autocomplete", "off", "required", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["deleteModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], [1, "switch"], ["type", "checkbox", "checked", "user.acc_activation", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [2, "font-size", "smaller", "color", "red"]],
          template: function ModuleComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Module ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, ModuleComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_i_click_13_listener() {
                return ctx.GetModuleLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function ModuleComponent_Template_input_input_14_listener() {
                return ctx.GetModuleLists();
              })("ngModelChange", function ModuleComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Module Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](20, ModuleComponent_th_20_Template, 2, 0, "th", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](22, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](24, ModuleComponent_tr_24_Template, 7, 4, "tr", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](25, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "pagination-controls", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function ModuleComponent_Template_pagination_controls_pageChange_27_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "div", 17, 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, "Add Module");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "label", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](40, "Module Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](41, "input", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function ModuleComponent_Template_input_ngModelChange_41_listener($event) {
                return ctx.module.name = $event;
              })("keydown.enter", function ModuleComponent_Template_input_keydown_enter_41_listener() {
                return ctx.AddModule();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](42, ModuleComponent_div_42_Template, 2, 0, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_44_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

                _r3.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](45, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](46, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_46_listener() {
                return ctx.AddModule();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](47, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](48, "div", 32, 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](50, "div", 34);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](54, "Edit Module");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](55, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](56, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "div", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](59, "label", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](60, "Module Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "input", 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function ModuleComponent_Template_input_ngModelChange_61_listener($event) {
                return ctx.module.name = $event;
              })("keydown.enter", function ModuleComponent_Template_input_keydown_enter_61_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                ctx.EditModule(ctx.module.id);
                return _r5.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](62, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](63, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_63_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                _r5.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](64, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_65_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](49);

                ctx.EditModule(ctx.module.id);
                return _r5.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](66, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](67, "div", 36, 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](69, "div", 38);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](70, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](71, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](72, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](73, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](74, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](76, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](77, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](78, "Do you want to delete this Module?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](79, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_80_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](68);

                return _r6.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](81, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](82, "button", 39);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ModuleComponent_Template_button_click_82_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r25);

                var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](68);

                ctx.DeleteModule();
                return _r6.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](83, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Edit);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](25, 12, ctx.modules, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](15, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](18, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.module.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](19, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.rolefailed);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](20, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.module.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](21, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](22, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe],
          styles: [""]
        });
        return ModuleComponent;
      }();
      /***/

    },

    /***/
    35479: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "RoleComponent": function RoleComponent() {
          return (
            /* binding */
            _RoleComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_role_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/role.service */
      83711);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["AddModal"];
      var _c1 = ["primaryModal"];
      var _c2 = ["deleteModal"];

      function RoleComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 38);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);

            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](28);

            return _r2.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Role ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function RoleComponent_tr_23_a_4_Template(rf, ctx) {
        if (rf & 1) {
          var _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_tr_23_a_4_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r13);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](48);

            _r4.show();

            ctx_r12.GetRole(user_r9._id);
            return ctx_r12.onChange(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 42);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function RoleComponent_tr_23_a_5_Template(rf, ctx) {
        if (rf & 1) {
          var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_tr_23_a_5_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r16);

            var user_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](86);

            _r6.show();

            return ctx_r15.GetRolee(user_r9._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 44);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function RoleComponent_tr_23_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, RoleComponent_tr_23_a_4_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, RoleComponent_tr_23_a_5_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r9 = ctx.$implicit;

          var ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r9.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.Delete);
        }
      }

      function RoleComponent_div_41_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "*Role is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function RoleComponent_tr_76_Template(rf, ctx) {
        if (rf & 1) {
          var _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "label", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "input", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function RoleComponent_tr_76_Template_input_change_5_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r18 = ctx.$implicit;
            var i_r19 = ctx.index;

            var ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r20.changed("add", user_r18.add, user_r18._id, i_r19);
          })("ngModelChange", function RoleComponent_tr_76_Template_input_ngModelChange_5_listener($event) {
            var user_r18 = ctx.$implicit;
            return user_r18.add = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](6, "span", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "label", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "input", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function RoleComponent_tr_76_Template_input_change_9_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r18 = ctx.$implicit;
            var i_r19 = ctx.index;

            var ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r23.changed("edit", user_r18.edit, user_r18._id, i_r19);
          })("ngModelChange", function RoleComponent_tr_76_Template_input_ngModelChange_9_listener($event) {
            var user_r18 = ctx.$implicit;
            return user_r18.edit = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](10, "span", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "label", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "input", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function RoleComponent_tr_76_Template_input_change_13_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r18 = ctx.$implicit;
            var i_r19 = ctx.index;

            var ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r25.changed("delete", user_r18["delete"], user_r18._id, i_r19);
          })("ngModelChange", function RoleComponent_tr_76_Template_input_ngModelChange_13_listener($event) {
            var user_r18 = ctx.$implicit;
            return user_r18["delete"] = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](14, "span", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "label", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "input", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function RoleComponent_tr_76_Template_input_change_17_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r18 = ctx.$implicit;
            var i_r19 = ctx.index;

            var ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r27.changed("all", user_r18.status, user_r18._id, i_r19);
          })("ngModelChange", function RoleComponent_tr_76_Template_input_ngModelChange_17_listener($event) {
            var user_r18 = ctx.$implicit;
            return user_r18.status = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](18, "span", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r18 = ctx.$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r18.module_name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r18.add);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r18.edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r18["delete"]);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r18.status);
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5() {
        return {
          standalone: true
        };
      };

      var _c6 = function _c6(a2, a3) {
        return {
          id: "listing_paginations",
          itemsPerPage: 6,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _RoleComponent = /*#__PURE__*/function () {
        var RoleComponent = /*#__PURE__*/function () {
          function RoleComponent(roleService, route, router, tokenStorage, Permission, EmployeeService) {
            _classCallCheck(this, RoleComponent);

            this.roleService = roleService;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.permissions = [];
            this.roles = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.role = {};
            this.rolefailed = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
            this.page1 = 1;
            this.count1 = 0;
          }

          _createClass(RoleComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.role = {};
              this.AddModal.hide();
            } //token verified module

          }, {
            key: "tokens",
            value: function tokens() {
              var _this24 = this;

              var key = this.tokenStorage.getToken();
              var Role = this.tokenStorage.getUser(); // if (key != null) {

              this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                // console.log(res)
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].module_name == "Role") {
                    _this24.Add = res.data[i].add;
                    _this24.Edit = res.data[i].edit;
                    _this24.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                  }
                }
              });
              this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                // console.log(res.data[0].status)
                if (res.data.status == false) {
                  _this24.tokenStorage.signOut();
                }
              });
              this.GetRoleLists(); // }
              // else {
              // this.router.navigate(['/login']);
              // }
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              return skip;
            } //page handle request

          }, {
            key: "getrequestparams1",
            value: function getrequestparams1(page1) {
              var skip = {};
              skip["skip"] = (page1 - 1) * 6; // skip['limit'] = 6  

              return skip;
            } //Get All Role List

          }, {
            key: "GetRoleLists",
            value: function GetRoleLists() {
              var _this25 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.roleService.GetRoleList(skip, this.name).subscribe(function (res) {
                _this25.count = res.count - 1;
                var arr = [];

                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].name !== "Super Admin") {
                    arr.push(res.data[i]);
                  }
                }

                _this25.roles = arr; // console.log(this.roles);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.GetRoleLists();
            } //Edit or update role 

          }, {
            key: "GetRole",
            value: function GetRole(id) {
              var _this26 = this;

              // console.log('id-->', id);
              this.Id = id;
              this.page1 = 1;
              this.count1 = 0;
              this.roleService.GetRoleDetail(id).subscribe(function (res) {
                _this26.role = res.data[0]; // console.log(res.data)
              });
            } //Role based search in permission collection

          }, {
            key: "onChange",
            value: function onChange(id) {
              var _this27 = this;

              // console.log('search-->', this.search)
              var skip = this.getrequestparams1(this.page1);
              this.Permission.GetRoleDetails(id, skip).subscribe(function (res) {
                _this27.permissions = res.data;
                _this27.count1 = res.count; // console.log(res)
              });
            } //Page handle 

          }, {
            key: "handlePageChange1",
            value: function handlePageChange1(event) {
              this.page1 = event; // console.log(this.page);

              this.onChange(this.Id);
            }
          }, {
            key: "EditRole",
            value: function EditRole(id) {
              var _this28 = this;

              // console.log('id-->', id, this.role.name)
              var data = {
                name: this.role.name
              };
              this.roleService.UpdateRole(id, data).subscribe(function (res) {
                // console.log('res-->', res);
                _this28.role = {};

                _this28.GetRoleLists();
              });
            } //Status ON & OFF

          }, {
            key: "changeing",
            value: function changeing(active, id) {
              var data = {
                acc_activation: active
              }; // console.log('data-->', data);

              this.roleService.UpdateRole(id, data).subscribe(function (res) {// console.log('res-->', res);
              });
            } //Add new Role

          }, {
            key: "AddRole",
            value: function AddRole() {
              var _this29 = this;

              // console.log('name-->', this.role.name)
              if (this.role.name != undefined && this.role.name != '') {
                var data = {
                  name: this.role.name
                };
                this.roleService.NewRole(data).subscribe(function (res) {
                  _this29.role = {};
                  _this29.rolefailed = false;

                  _this29.AddModal.hide(); // console.log('new-->', res)


                  _this29.GetRoleLists();
                });
              }

              this.rolefailed = true;
            } //Delete Role using id

          }, {
            key: "Deleterole",
            value: function Deleterole() {
              var _this30 = this;

              // console.log('id-->', id)
              this.roleService.DeleteRole(this.Id).subscribe(function (res) {
                // console.log('res-->', res)
                _this30.GetRoleLists();
              });
            }
          }, {
            key: "GetRolee",
            value: function GetRolee(id) {
              this.Id = id;
            } //ON or OFF value in table

          }, {
            key: "changed",
            value: function changed(param, value, id, i) {
              // console.log(param, value, id, i)
              var data1 = param;

              var data = _defineProperty({}, data1, value);

              if (param == 'all') {
                var _data2 = {
                  status: value,
                  "delete": value,
                  edit: value,
                  add: value
                };
                this.permissions[i]["delete"] = value;
                this.permissions[i].edit = value;
                this.permissions[i].add = value;
                this.Permission.UpdatePermission(id, _data2).subscribe(function (res) {});
              }

              this.Permission.UpdatePermission(id, data).subscribe(function (res) {});
            }
          }]);

          return RoleComponent;
        }();

        RoleComponent.ɵfac = function RoleComponent_Factory(t) {
          return new (t || RoleComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_role_service__WEBPACK_IMPORTED_MODULE_1__.RoleService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_0__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_3__.Employeeservice));
        };

        RoleComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: RoleComponent,
          selectors: [["app-role"]],
          viewQuery: function RoleComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.AddModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.deleteModal = _t.first);
            }
          },
          decls: 102,
          vars: 29,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text"], [1, "fa", "fa-search", 3, "click"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["AddModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "col-sm-12"], [1, "form-group"], ["for", "name"], ["type", "text", "id", "role-name", "placeholder", "e.g. Manager, Doctor", "autocomplete", "off", "required", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter"], ["style", "font-size: smaller;color: red;", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["primaryModal", "bs-modal"], ["type", "text", "id", "edit-name", "placeholder", "e.g. Manager, Doctor", "autocomplete", "off", "required", "", "readonly", "", 1, "form-control", 3, "ngModel", "ngModelOptions", "ngModelChange", "keydown.enter"], ["id", "listing_paginations", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["deleteModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [2, "font-size", "smaller", "color", "red"], [1, "switch"], ["type", "checkbox", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"]],
          template: function RoleComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r29 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Role ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, RoleComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_i_click_13_listener() {
                return ctx.GetRoleLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function RoleComponent_Template_input_input_14_listener() {
                return ctx.GetRoleLists();
              })("ngModelChange", function RoleComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Role Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](21, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](23, RoleComponent_tr_23_Template, 6, 3, "tr", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](24, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](25, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "pagination-controls", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function RoleComponent_Template_pagination_controls_pageChange_26_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "div", 16, 17);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "div", 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "h4", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](33, "Add Role");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](34, "div", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "label", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](39, "Role Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](40, "input", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function RoleComponent_Template_input_ngModelChange_40_listener($event) {
                return ctx.role.name = $event;
              })("keydown.enter", function RoleComponent_Template_input_keydown_enter_40_listener() {
                return ctx.AddRole();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](41, RoleComponent_div_41_Template, 2, 0, "div", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](42, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "button", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_43_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](28);

                _r2.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](44, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_45_listener() {
                return ctx.AddRole();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](46, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](47, "div", 16, 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "div", 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](50, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "h4", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](53, "Edit Role");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](54, "div", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](55, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](56, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "label", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](59, "Role Name*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](60, "input", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function RoleComponent_Template_input_ngModelChange_60_listener($event) {
                return ctx.role.name = $event;
              })("keydown.enter", function RoleComponent_Template_input_keydown_enter_60_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](48);

                ctx.EditRole(ctx.role._id);
                return _r4.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](62, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](63, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](64, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](66, "Module Name");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](67, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](68, "Add");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](69, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](70, "Edit");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](71, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](72, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](73, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](74, "View");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](76, RoleComponent_tr_76_Template, 19, 5, "tr", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](77, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](78, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](79, "pagination-controls", 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function RoleComponent_Template_pagination_controls_pageChange_79_listener($event) {
                return ctx.handlePageChange1($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](81, "button", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_81_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](48);

                _r4.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](82, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](83, "button", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_83_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](48);

                ctx.EditRole(ctx.role._id);
                return _r4.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](84, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](85, "div", 34, 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](87, "div", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](88, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](89, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](90, "h4", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](91, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](92, "div", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](93, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](94, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](95, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](96, "Do you want to delete this Role?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](97, "div", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](98, "button", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_98_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](86);

                return _r6.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](99, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](100, "button", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function RoleComponent_Template_button_click_100_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r29);

                var _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](86);

                ctx.Deleterole();
                return _r6.hide();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](101, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](24, 12, ctx.roles, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](18, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](21, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.role.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](22, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.rolefailed);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](23, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.role.name)("ngModelOptions", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](24, _c5));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](77, 15, ctx.permissions, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](25, _c6, ctx.page1, ctx.count1)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](28, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe],
          styles: [""]
        });
        return RoleComponent;
      }();
      /***/

    },

    /***/
    31473: function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "TreatmentsComponent": function TreatmentsComponent() {
          return (
            /* binding */
            _TreatmentsComponent
          );
        }
        /* harmony export */

      });
      /* harmony import */


      var _services_treatment_services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../services/treatment.services */
      21771);
      /* harmony import */


      var _services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ../../services/token-storage.service */
      11192);
      /* harmony import */


      var _services_permission_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../services/permission.service */
      6642);
      /* harmony import */


      var _services_employee_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ../../services/employee.services */
      26415);
      /* harmony import */


      var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(
      /*! @angular/forms */
      33865);
      /* harmony import */


      var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! @angular/core */
      3048);
      /* harmony import */


      var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(
      /*! @angular/router */
      3079);
      /* harmony import */


      var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(
      /*! @angular/common */
      63237);
      /* harmony import */


      var ngx_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ngx-pagination */
      45055);
      /* harmony import */


      var ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(
      /*! ngx-bootstrap/modal */
      30386);

      var _c0 = ["primaryModal"];
      var _c1 = ["AddModal"];
      var _c2 = ["removeModal"];

      function TreatmentsComponent_button_8_Template(rf, ctx) {
        if (rf & 1) {
          var _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 38);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_button_8_Template_button_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r9);

            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

            return _r3.show();
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, " Add Type ");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_th_20_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "th");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Status");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_tr_24_td_3_Template(rf, ctx) {
        if (rf & 1) {
          var _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "label", 40);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "input", 41);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("change", function TreatmentsComponent_tr_24_td_3_Template_input_change_2_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r16);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            return ctx_r14.changed(user_r10.status, user_r10._id);
          })("ngModelChange", function TreatmentsComponent_tr_24_td_3_Template_input_ngModelChange_2_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r16);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            return user_r10.status = $event;
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "span", 42);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", user_r10.status);
        }
      }

      function TreatmentsComponent_tr_24_a_5_Template(rf, ctx) {
        if (rf & 1) {
          var _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_tr_24_a_5_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r21);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](50);

            _r5.show();

            return ctx_r20.GetTreatment(user_r10._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 44);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 45);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Edit");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_tr_24_a_6_Template(rf, ctx) {
        if (rf & 1) {
          var _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "a", 43);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_tr_24_a_6_Template_a_click_0_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r24);

            var user_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;

            var ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

            var _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](71);

            _r7.show();

            return ctx_r23.GetTreatment(user_r10._id);
          });

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "span", 46);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 47);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Delete");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_tr_24_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "tr");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, TreatmentsComponent_tr_24_td_3_Template, 4, 1, "td", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "td");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](5, TreatmentsComponent_tr_24_a_5_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](6, TreatmentsComponent_tr_24_a_6_Template, 4, 0, "a", 39);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var user_r10 = ctx.$implicit;

          var ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r10.name);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Edit);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r2.Delete);
        }
      }

      function TreatmentsComponent_div_43_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Type is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_div_43_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, TreatmentsComponent_div_43_div_1_Template, 2, 0, "div", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r4.f.name.errors.required);
        }
      }

      function TreatmentsComponent_div_64_div_1_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Type is mandatory");

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
      }

      function TreatmentsComponent_div_64_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 48);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, TreatmentsComponent_div_64_div_1_Template, 2, 0, "div", 14);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }

        if (rf & 2) {
          var ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.f.name.errors.required);
        }
      }

      var _c3 = function _c3(a2, a3) {
        return {
          id: "listing_pagination",
          itemsPerPage: 10,
          currentPage: a2,
          totalItems: a3
        };
      };

      var _c4 = function _c4() {
        return {
          "backdrop": "static",
          "keyboard": false
        };
      };

      var _c5 = function _c5(a0) {
        return {
          "is-invalid": a0
        };
      };

      var _TreatmentsComponent = /*#__PURE__*/function () {
        var TreatmentsComponent = /*#__PURE__*/function () {
          function TreatmentsComponent(formBuilder, TreatmentService, route, router, tokenStorage, Permission, EmployeeService) {
            _classCallCheck(this, TreatmentsComponent);

            this.formBuilder = formBuilder;
            this.TreatmentService = TreatmentService;
            this.route = route;
            this.router = router;
            this.tokenStorage = tokenStorage;
            this.Permission = Permission;
            this.EmployeeService = EmployeeService;
            this.isFormReady = false;
            this.submitted = false;
            this.treatments = [];
            this.page = 1;
            this.count = 0;
            this.search = '';
            this.name = '';
            this.treatment = {};
            this.nameFailed = false;
            this.Add = true;
            this.Edit = true;
            this.Delete = true;
          }

          _createClass(TreatmentsComponent, [{
            key: "ngOnInit",
            value: function ngOnInit() {
              this.tokens();
              this.SignForm();
            } //clear modal window

          }, {
            key: "clear",
            value: function clear() {
              this.treatment = {};
              this.nameFailed = false;
              this.isFormReady = false;
              this.submitted = false;
              this.loginForm.reset();
            }
          }, {
            key: "getfocus",
            value: function getfocus() {
              this.nameFailed = false;
            } //token verified treatment

          }, {
            key: "tokens",
            value: function tokens() {
              var _this31 = this;

              var key = this.tokenStorage.getToken();
              var Role = this.tokenStorage.getUser(); // if (key != null) {

              this.Permission.GetModule(Role.role_id._id).subscribe(function (res) {
                // console.log(res)
                for (var i = 0; i < res.data.length; i++) {
                  if (res.data[i].module_name == "Treatments") {
                    _this31.Add = res.data[i].add;
                    _this31.Edit = res.data[i].edit;
                    _this31.Delete = res.data[i]["delete"]; // console.log(this.Add, this.Edit, this.Delete)
                  }
                }
              });
              this.EmployeeService.GetEmployeeDetail(Role._id).subscribe(function (res) {
                // console.log(res.data[0].status)
                if (res.data.status == false) {
                  _this31.tokenStorage.signOut();
                }
              });
              this.treatmentLists(); // }
              // else {
              //   this.router.navigate(['/login']);
              // }
            } //page handle request

          }, {
            key: "getrequestparams",
            value: function getrequestparams(page) {
              var skip = {};
              skip["skip"] = (page - 1) * 10;
              return skip;
            } //Get All treatment List

          }, {
            key: "treatmentLists",
            value: function treatmentLists() {
              var _this32 = this;

              // console.log('search-->', this.name)
              var skip = this.getrequestparams(this.page);
              this.TreatmentService.GetTreatmentsList(skip, this.name).subscribe(function (res) {
                _this32.treatments = res.data;
                _this32.count = res.count; // console.log(this.treatments);
                // console.log(this.count);
              });
            } //Page handle 

          }, {
            key: "handlePageChange",
            value: function handlePageChange(event) {
              this.page = event; // console.log(this.page);

              this.treatmentLists();
            } //Edit or update treatment 

          }, {
            key: "GetTreatment",
            value: function GetTreatment(id) {
              var _this33 = this;

              // console.log('id-->', id);
              this.TreatmentService.GetTreatmentDetail(id).subscribe(function (res) {
                _this33.treatment = res.data[0];

                _this33.f.name.setValue(res.data[0].name, {
                  onlySelf: true
                }); // console.log(res.data)

              });
            }
          }, {
            key: "EditTreatment",
            value: function EditTreatment(id) {
              var _this34 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.name
                };
                this.TreatmentService.UpdateTreatment(id, data).subscribe(function (res) {
                  // console.log('res-->', res);
                  _this34.primaryModal.hide();

                  _this34.clear();

                  _this34.treatmentLists();
                });
              }
            } //Status ON & OFF

          }, {
            key: "changed",
            value: function changed(active, id) {
              var data = {
                status: active
              };
              this.TreatmentService.UpdateTreatment(id, data).subscribe(function (res) {// console.log('res-->', res);
              });
            }
          }, {
            key: "SignForm",
            value: function SignForm() {
              this.loginForm = this.formBuilder.group({
                name: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required]]
              });
            }
          }, {
            key: "f",
            get: function get() {
              return this.loginForm.controls;
            } //Add new treatment

          }, {
            key: "AddTreatment",
            value: function AddTreatment() {
              var _this35 = this;

              this.submitted = true;

              if (this.loginForm.invalid) {
                return;
              } else {
                var data = {
                  name: this.loginForm.value.name
                };
                this.AddModal.hide();
                this.TreatmentService.NewTreatment(data).subscribe(function (res) {
                  _this35.clear(); // console.log('new-->', res)


                  _this35.treatmentLists();
                });
              }
            } //Delete treatment using id

          }, {
            key: "DeleteTreatment",
            value: function DeleteTreatment(id) {
              var _this36 = this;

              // console.log('id-->', id)
              this.TreatmentService.DeleteTreatment(id).subscribe(function (res) {
                // console.log('res-->', res)
                _this36.removeModal.hide();

                _this36.treatmentLists();
              });
            }
          }]);

          return TreatmentsComponent;
        }();

        TreatmentsComponent.ɵfac = function TreatmentsComponent_Factory(t) {
          return new (t || TreatmentsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_treatment_services__WEBPACK_IMPORTED_MODULE_0__.treatmentService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_token_storage_service__WEBPACK_IMPORTED_MODULE_1__.TokenStorageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_permission_service__WEBPACK_IMPORTED_MODULE_2__.PermissionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_employee_services__WEBPACK_IMPORTED_MODULE_3__.Employeeservice));
        };

        TreatmentsComponent.ɵcmp = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
          type: TreatmentsComponent,
          selectors: [["app-treatments"]],
          viewQuery: function TreatmentsComponent_Query(rf, ctx) {
            if (rf & 1) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c1, 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c2, 1);
            }

            if (rf & 2) {
              var _t;

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.primaryModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.AddModal = _t.first);
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.removeModal = _t.first);
            }
          },
          decls: 87,
          vars: 26,
          consts: [[1, "row"], [1, "col-lg-12"], [1, "card"], [1, "card-header"], [1, "card-body"], [1, "col-lg-12", "my-3"], ["type", "button", "class", "btn btn-primary mr-1", "data-toggle", "modal", 3, "click", 4, "ngIf"], [1, "form-group", "table-search"], [1, "input-group", 2, "top", "3px"], [1, "input-group-prepend"], [1, "input-group-text", 3, "click"], [1, "fa", "fa-search"], ["type", "text", "id", "Search", "name", "Search", "placeholder", "Search", "autocomplete", "off", 1, "form-control", 3, "ngModel", "input", "ngModelChange"], [1, "table", "table-striped"], [4, "ngIf"], [4, "ngFor", "ngForOf"], ["id", "listing_pagination", "maxSize", "5", "directionLinks", "true", 2, "text-align", "right", 3, "pageChange"], ["bsModal", "", "id", "myModal", "tabindex", "-1", "Module", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["AddModal", "bs-modal"], ["Module", "document", 1, "modal-dialog", "modal-primary"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "col-sm-12"], ["autocomplete", "off", 1, "form", 3, "formGroup"], [1, "form-group"], ["for", "name"], ["type", "text", "placeholder", "e.g. Injury, UTI, Vaccine", "formControlName", "name", 1, "form-control", 3, "ngClass"], ["class", "invalid-feedback", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], ["primaryModal", "bs-modal"], ["bsModal", "", "tabindex", "-1", "role", "dialog", "aria-labelledby", "myModalLabel", "aria-hidden", "true", 1, "modal", "fade", 3, "config"], ["removeModal", "bs-modal"], ["role", "document", 1, "modal-dialog", "modal-danger", "modal-sm"], ["type", "button", 1, "btn", "btn-danger", 3, "click"], ["type", "button", "data-toggle", "modal", 1, "btn", "btn-primary", "mr-1", 3, "click"], ["data-toggle", "modal", "style", "cursor: pointer;", 3, "click", 4, "ngIf"], [1, "switch"], ["type", "checkbox", "checked", "user.status", 3, "ngModel", "change", "ngModelChange"], [1, "slider", "round"], ["data-toggle", "modal", 2, "cursor", "pointer", 3, "click"], [1, "badge", "badge-success"], [1, "fa", "fa-edit"], [1, "badge", "badge-danger"], [1, "fa", "fa-trash"], [1, "invalid-feedback"]],
          template: function TreatmentsComponent_Template(rf, ctx) {
            if (rf & 1) {
              var _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 2);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 3);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " Appointment Types ");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 5);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, TreatmentsComponent_button_8_Template, 2, 0, "button", 6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 7);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 9);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "span", 10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_span_click_12_listener() {
                return ctx.treatmentLists();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](13, "i", 11);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "input", 12);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("input", function TreatmentsComponent_Template_input_input_14_listener() {
                return ctx.treatmentLists();
              })("ngModelChange", function TreatmentsComponent_Template_input_ngModelChange_14_listener($event) {
                return ctx.name = $event;
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "table", 13);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "thead");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "tr");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Type");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](20, TreatmentsComponent_th_20_Template, 2, 0, "th", 14);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](21, "th");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](22, "Action");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "tbody");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](24, TreatmentsComponent_tr_24_Template, 7, 4, "tr", 15);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](25, "paginate");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "pagination-controls", 16);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("pageChange", function TreatmentsComponent_Template_pagination_controls_pageChange_27_listener($event) {
                return ctx.handlePageChange($event);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "div", 17, 18);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](34, "Add Type");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](36, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "form", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "div", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](40, "label", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](41, "Type*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](42, "input", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](43, TreatmentsComponent_div_43_Template, 2, 1, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](44, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_45_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](29);

                _r3.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](46, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](47, "button", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_47_listener() {
                return ctx.AddTreatment();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](48, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "div", 17, 33);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](51, "div", 19);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](52, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](53, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](54, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](55, "Edit Type");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](56, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](57, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](58, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](59, "form", 25);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](60, "div", 26);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](61, "label", 27);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](62, "Type*");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](63, "input", 28);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](64, TreatmentsComponent_div_64_Template, 2, 1, "div", 29);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](66, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_66_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](50);

                _r5.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](67, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](68, "button", 32);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_68_listener() {
                return ctx.EditTreatment(ctx.treatment._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](69, "Save");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](70, "div", 34, 35);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](72, "div", 36);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](73, "div", 20);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](74, "div", 21);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "h4", 22);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](76, "Are you sure ?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](77, "div", 23);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](78, "div", 0);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](79, "div", 24);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](80, "p");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](81, "Do you want to delete this Appointment?");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](82, "div", 30);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](83, "button", 31);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_83_listener() {
                _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r28);

                var _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵreference"](71);

                _r7.hide();

                return ctx.clear();
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](84, "Cancel");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](85, "button", 37);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function TreatmentsComponent_Template_button_click_85_listener() {
                return ctx.DeleteTreatment(ctx.treatment._id);
              });

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](86, "Delete");

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
            }

            if (rf & 2) {
              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Add);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.name);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.Edit);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](25, 13, ctx.treatments, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction2"](16, _c3, ctx.page, ctx.count)));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](19, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](20, _c5, ctx.submitted && ctx.f.name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](22, _c4));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.loginForm);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](23, _c5, ctx.submitted && ctx.f.name.errors));

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.submitted && ctx.f.name.errors);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);

              _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("config", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](25, _c4));
            }
          },
          directives: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationControlsComponent, ngx_bootstrap_modal__WEBPACK_IMPORTED_MODULE_9__.ModalDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵangular_packages_forms_forms_y"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.CheckboxControlValueAccessor],
          pipes: [ngx_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginatePipe],
          styles: [""]
        });
        return TreatmentsComponent;
      }();
      /***/

    }
  }]);
})();