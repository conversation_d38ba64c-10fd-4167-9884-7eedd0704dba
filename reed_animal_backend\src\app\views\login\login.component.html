<div class="app-body auth-login-sign">
  <main class="main d-flex align-items-center">
    <div class="container">
      <div class="row">
        <div class="col-md-5 mx-auto">
          <div class="card-group">
            <div class="card p-4" visible=true>
              <div class="card-body">
                <form class="form" [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="off">
                  <h1>Login</h1>
                  <p class="text-muted">Sign In to your account</p>
                  
                  <div class="input-group mb-4">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-user"></i></span>
                    </div>
                    <input type="email" class="form-control" placeholder="Email" formControlName="email"
                      [ngClass]="{ 'is-invalid': submitted && f.email.errors }" (click)="getfocus()" />
                    <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                      <div *ngIf="f.email.errors.required">*Email cannot be empty</div>
                      <div *ngIf="f.email.errors.email || f.email.errors.pattern">*Please enter a valid Email address</div>
                    </div>
                  </div>

                  <div class="input-group mb-4">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="Password" formControlName="password"
                      [ngClass]="{ 'is-invalid': submitted && f.password.errors }" (click)="getfocus()"/>
                    <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                      <div *ngIf="f.password.errors.required">*Password cannot be empty</div>
                      <div *ngIf="f.password.errors.minlength">*Password must be at least 8 characters</div>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="alert alert-danger" role="alert" *ngIf="isLoginFailed">
                      {{ errormessage }}
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12 text-right">
                      <button type="button" class="btn btn-link px-0" [routerLink]="['/forgot-password']">Forgot
                        password?</button>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <button class="btn btn-primary px-4">Login</button>
                    </div>

                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
