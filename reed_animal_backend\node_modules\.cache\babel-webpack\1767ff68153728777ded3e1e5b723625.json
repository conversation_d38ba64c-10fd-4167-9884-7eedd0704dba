{"ast": null, "code": "import freeGlobal from './_freeGlobal.js';\n/** Detect free variable `exports`. */\n\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */\n\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */\n\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n/** Detect free variable `process` from Node.js. */\n\nvar freeProcess = moduleExports && freeGlobal.process;\n/** Used to access faster Node.js helpers. */\n\nvar nodeUtil = function () {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    } // Legacy `process.binding('util')` for Node.js < 10.\n\n\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}();\n\nexport default nodeUtil;", "map": null, "metadata": {}, "sourceType": "module"}