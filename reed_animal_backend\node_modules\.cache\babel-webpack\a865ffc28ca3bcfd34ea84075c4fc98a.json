{"ast": null, "code": "import arrayReduce from './_arrayReduce.js';\nimport deburr from './deburr.js';\nimport words from './words.js';\n/** Used to compose unicode capture groups. */\n\nvar rsApos = \"['\\u2019]\";\n/** Used to match apostrophes. */\n\nvar reApos = RegExp(rsApos, 'g');\n/**\n * Creates a function like `_.camelCase`.\n *\n * @private\n * @param {Function} callback The function to combine each word.\n * @returns {Function} Returns the new compounder function.\n */\n\nfunction createCompounder(callback) {\n  return function (string) {\n    return arrayReduce(words(deburr(string).replace(reApos, '')), callback, '');\n  };\n}\n\nexport default createCompounder;", "map": null, "metadata": {}, "sourceType": "module"}