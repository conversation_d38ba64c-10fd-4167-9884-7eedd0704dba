import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class LocationService extends Api {

    //Add New Location 
    NewLocation(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Location?token=${localStorage.auth_token}`, data);
    }

    //Get All Location Type 
    GetLocationsList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Location?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular Location by using Location id 
    GetLocationDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit Location details
    UpdateLocation(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Location by using id
    DeleteLocation(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Location/${id}?token=${localStorage.auth_token}`);
    }
}