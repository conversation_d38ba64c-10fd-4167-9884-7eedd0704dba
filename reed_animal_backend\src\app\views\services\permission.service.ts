import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class PermissionService extends Api {
    //Get All Module 
    GetRolelist(): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`);
    }

    //Get role details in Permission collection
    GetRoleDetails(id, params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/permission?search=${id}&token=${localStorage.auth_token}`, { params });
    }

    //Get role details in Permission collection
    UpdatePermission(id, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/permission/${id}?token=${localStorage.auth_token}`, params);
    }

    //Get Module details in Permission collection
    GetModule(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/permission/${id}?token=${localStorage.auth_token}`);
    }
}