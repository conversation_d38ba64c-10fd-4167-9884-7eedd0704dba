import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { BreedingService } from '../../services/breeding.services'
import { AnimalTypeService } from '../../services/animal_type.services'
import { TokenStorageService } from '../../services/token-storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Breeding } from '../../../views/models/breeding.models'
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { partitionArray } from '@angular/compiler/src/util';
import { FormGroup, FormBuilder, Validators } from "@angular/forms";

@Component({
  selector: 'app-breeding',
  templateUrl: './breeding.component.html',
  styleUrls: ['./breeding.component.scss']
})
export class BreedingComponent implements OnInit {

  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('deleteModal') public deleteModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  Types = [];
  Breedings = [];
  Size = ['Toy', 'Small', 'Medium', 'Large', 'Giant']
  page = 1;
  count = 0;
  search = '';
  name = '';
  ddlFileId = ''
  value1 = ''
  breeding = {
    _id: "",
    name: "",
    status: false,
    type: "",
    size: '',
    code: ''
  };
  isLoginFailed = false;
  loginForm: FormGroup;
  isFormReady = false;
  submitted = false;
  Add = true;
  Edit = true;
  Delete = true;
  sort = false
  value = 1
  field = "name"
  typeFailed = false
  delete_id:any;
  constructor(private BreedingService: BreedingService, private AnimaltypeService: AnimalTypeService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService, private EmployeeService: Employeeservice, private formBuilder: FormBuilder,) { }

  ngOnInit(): void {
    this.tokens();
    this.SignForm()
  }

  //clear modal window
  clear(): void {
    this.loginForm.reset();
    this.submitted = false;
    this.Types = [];
    this.GetTypeLists();
    this.breeding = {
      _id: "",
      name: "",
      status: false,
      type: "",
      size: '',
      code: ''
    };
  }

  //token verified module
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Breed") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        } else {
          this.GetTypeLists();
        }
      })

  }

  //Get All Type List
  GetTypeLists(): void {
    this.BreedingService.GetTypesList()
      .subscribe((res: any) => {
        this.Types = res.data;
        // console.log(this.Types);
        this.GetBreedingLists()
      });
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    skip['value'] = this.value
    skip['field'] = this.field
    return skip;
  }

  //Get All Type List
  GetBreedingLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.BreedingService.GetBreedingsList(skip, this.name)
      .subscribe((res: any) => {
        this.Breedings = res.data;
        const bre = res.data
        this.count = res.count;
        return bre
        // console.log(this.Breedings);
        // console.log(this.count);
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.GetBreedingLists();
  }

  //
  searched(id, param) {
    this.breeding[param] = id
    // console.log('id-->', param, id)
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      species: ['', [Validators.required]],
      size: ['', [Validators.required]],
      breed: ['', [Validators.required]],
      code: ['', [Validators.required]],

    });
  }

  get f() {
    return this.loginForm.controls;
  }
  //Add New Breeding
  AddBreeding(): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    } else {
      const data = {
        name: this.loginForm.value.breed,
        type: this.loginForm.value.species,
        code: this.loginForm.value.code,
        size: this.loginForm.value.size
      }
      // console.log('new-->', data)
      this.BreedingService.NewBreeding(data)
        .subscribe((res) => {
          this.submitted = false;
          this.loginForm.reset();
          this.primaryModal.hide();
          // console.log('new-->', res)
          this.GetTypeLists();
          this.GetBreedingLists();
        })
    }

  }

  //Update or Edit Breeding
  GetBreeding(id): void {
    console.log("id",id)

    this.delete_id = id;
    
    this.breeding=this.Breedings[id]
    // console.log(this.Breedings[id])
    this.f.species.setValue(this.Breedings[id].type, {
      onlySelf: true
    });
    this.f.size.setValue(this.Breedings[id].size, {
      onlySelf: true
    });
    this.f.breed.setValue(this.Breedings[id].name, {
      onlySelf: true
    });
    this.f.code.setValue(this.Breedings[id].code, {
      onlySelf: true
    })

  }

  EditBreeding(id): void {
 
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    } else {
      const data = {
        name: this.loginForm.value.breed,
        type: this.loginForm.value.species,
        code: this.loginForm.value.code,
        size: this.loginForm.value.size
      }
      this.BreedingService.UpdateBreeding(id, data)
        .subscribe((res) => {
          // console.log('res-->', res);
          this.clear();
          this.deleteModal.hide();
          this.GetBreedingLists();
        })
    }
  }

  //Delete Breeding
  DeleteBreeding(): void {
   
    // console.log('id-->', id)
    this.BreedingService.DeleteBreeding(this.delete_id )
      .subscribe((res) => {
        // console.log('res-->', res)
        this.removeModal.hide();
        this.GetBreedingLists();
      })
  }

  //ON or OFF value in table
  changed(param, id): void {
    // console.log(param, id)
    const data = {
      status: param
    }
    this.BreedingService.UpdateBreeding(id, data)
      .subscribe((res: any) => {
        // console.log(res)
      })
  }

  //Field name sorted
  Field(param) {
    // console.log(this.Id)
    if (this.sort == true) {
      // console.log('hi', param)
      this.sort = false,
        this.field = param
      this.value = -1
      this.GetBreedingLists();
    } else {
      // console.log('hi1', param)
      this.sort = true
      this.field = param
      this.value = 1
      this.GetBreedingLists();
    }
  }
}
