{"ast": null, "code": "/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0:\n      return func.call(thisArg);\n\n    case 1:\n      return func.call(thisArg, args[0]);\n\n    case 2:\n      return func.call(thisArg, args[0], args[1]);\n\n    case 3:\n      return func.call(thisArg, args[0], args[1], args[2]);\n  }\n\n  return func.apply(thisArg, args);\n}\n\nexport default apply;", "map": null, "metadata": {}, "sourceType": "module"}