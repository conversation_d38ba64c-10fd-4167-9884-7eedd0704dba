{"ast": null, "code": "import asciiToArray from './_asciiToArray.js';\nimport hasUnicode from './_hasUnicode.js';\nimport unicodeToArray from './_unicodeToArray.js';\n/**\n * Converts `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\n\nfunction stringToArray(string) {\n  return hasUnicode(string) ? unicodeToArray(string) : asciiToArray(string);\n}\n\nexport default stringToArray;", "map": null, "metadata": {}, "sourceType": "module"}