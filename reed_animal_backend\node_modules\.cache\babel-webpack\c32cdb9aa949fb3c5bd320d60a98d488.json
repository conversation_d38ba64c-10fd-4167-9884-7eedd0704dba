{"ast": null, "code": "import arrayPush from './_arrayPush.js';\nimport getPrototype from './_getPrototype.js';\nimport getSymbols from './_getSymbols.js';\nimport stubArray from './stubArray.js';\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\n\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function (object) {\n  var result = [];\n\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n\n  return result;\n};\nexport default getSymbolsIn;", "map": null, "metadata": {}, "sourceType": "module"}