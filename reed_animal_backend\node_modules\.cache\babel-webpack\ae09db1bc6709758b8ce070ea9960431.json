{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n/**\n * Gets the view, applying any `transforms` to the `start` and `end` positions.\n *\n * @private\n * @param {number} start The start of the view.\n * @param {number} end The end of the view.\n * @param {Array} transforms The transformations to apply to the view.\n * @returns {Object} Returns an object containing the `start` and `end`\n *  positions of the view.\n */\n\nfunction getView(start, end, transforms) {\n  var index = -1,\n      length = transforms.length;\n\n  while (++index < length) {\n    var data = transforms[index],\n        size = data.size;\n\n    switch (data.type) {\n      case 'drop':\n        start += size;\n        break;\n\n      case 'dropRight':\n        end -= size;\n        break;\n\n      case 'take':\n        end = nativeMin(end, start + size);\n        break;\n\n      case 'takeRight':\n        start = nativeMax(start, end - size);\n        break;\n    }\n  }\n\n  return {\n    'start': start,\n    'end': end\n  };\n}\n\nexport default getView;", "map": null, "metadata": {}, "sourceType": "module"}