{"ast": null, "code": "import shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n/**\n * The base implementation of `_.shuffle`.\n *\n * @private\n * @param {Array|Object} collection The collection to shuffle.\n * @returns {Array} Returns the new shuffled array.\n */\n\nfunction baseShuffle(collection) {\n  return shuffleSelf(values(collection));\n}\n\nexport default baseShuffle;", "map": null, "metadata": {}, "sourceType": "module"}