{"ast": null, "code": "import isArrayLike from './isArrayLike.js';\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\n\nfunction createBaseEach(eachFunc, fromRight) {\n  return function (collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n\n    var length = collection.length,\n        index = fromRight ? length : -1,\n        iterable = Object(collection);\n\n    while (fromRight ? index-- : ++index < length) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n\n    return collection;\n  };\n}\n\nexport default createBaseEach;", "map": null, "metadata": {}, "sourceType": "module"}