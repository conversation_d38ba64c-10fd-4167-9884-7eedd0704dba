import { treatmentService } from './../../services/treatment.services';
import { Treatment } from './../../models/treatment.models';
import { Component, OnInit, ViewChild ,Output,EventEmitter,ElementRef } from '@angular/core';
import { Appointment } from '../../models/schedule.models';
import { TokenStorageService } from '../../services/token-storage.service';
import { AppointmentService } from '../../services/appointments.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Employeeservice } from '../../services/employee.services';
import { PermissionService } from '../../services/permission.service';
import { BsDatepickerConfig } from 'ngx-bootstrap/datepicker';
import { LocationService } from '../../services/location.sevices';
import { Configuration } from '../../../../configuration';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef,HostListener,Renderer2 } from '@angular/core';

import * as moment from 'moment';
import html2canvas from 'html2canvas';
// import { log } from 'console';

@Component({
  selector: 'app-appointments',
  templateUrl: './appointments.component.html',
  styleUrls: ['./appointments.component.scss']
})
export class AppointmentsComponent implements OnInit {
  @ViewChild('toggleButton') toggleButton: ElementRef;
  @ViewChild('menu') menu: ElementRef;
  @Output() clickOutside = new EventEmitter<void>();
  @ViewChild('removeModal') public removeModal: ModalDirective;
  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('secondaryModal') public secondaryModal: ModalDirective;
  @ViewChild('AddAppointmentModal') public AddAppointmentModal: ModalDirective;

  @ViewChild('petNameInput') petNameInput!: ElementRef;
  @ViewChild('speciesInput') speciesInput!: ElementRef;
  @ViewChild('breedInput') breedInput!: ElementRef;
  @ViewChild('ageInput') ageInput!: ElementRef;
  @ViewChild('genderInput') genderInput!: ElementRef;
  @ViewChild('dobInput') dobInput!: ElementRef;
  @ViewChild('colorInput') colorInput!: ElementRef;
  @ViewChild('spayInput') spayInput!: ElementRef;

  pet = {
    pet_name: '',
    animal_type:'',
    breed : '',
    petage:'',
    gender:'',
    dob:'',
    color:'',
    spay:'',
  };
  newpet_image_url: any;
  Newspecies: any;
  Newbreed: any;
  selectNewbrd: any;
  newpetgender: any;
  Newpetage: string;
  NewDOB: string;
  newpetcolor: string;
  newpetspayed: any;
  newpet_animal_type: string;
  addnewbreed: string;
  addnewdob: string;
  NewpetColor: string;
  newpetspay: string;
  final_data: any;
  newarray: any=[];
  totalAmount: any = 0;
  message: any;
  date: any;
  reasons: any;
  note: any;
  oldDateTime: any;
  ownerName: any;
  Name: any;
  confirmbtn :boolean = true
  clearInput() {
    console.log("add new pet")
  //  this. petselect("")

    // this.selectedPetId="";
    // this.pet.pet_name = '';  // Clear the ngModel binding
    // this.petNameInput.nativeElement.value = '';  // Clear the actual input element

    // this.pet.animal_type='';
    // this.speciesInput.nativeElement.value = '';

    // this.pet.breed = '';
    // this.breedInput.nativeElement.value = '';
    // this.breedInput.nativeElement.dispatchEvent(new Event('change'));
    // this.cdr.detectChanges();

    // this.pet.petage = ''
    // this.ageInput.nativeElement.value = '';

    // this.pet.gender = ''
    // this.genderInput.nativeElement.value = '';

    // this.pet.dob = ''
    // this.dobInput.nativeElement.value = '';

    // this.pet.color = ''
    // this.colorInput.nativeElement.value = '';

    // this.pet.spay = ''
    // this.spayInput.nativeElement.value = '';

    // moment(this.formattedDate+" "+this.time,"MM/DD/YYYY h:mm a").utc().format()
    this.newpetdetails= true
    this.oldpetdetails=false
    this.selectedPetId="";
    this.newpetLable = true;


    this.addpetName="" 
    this.newpet_animal_type=""
    this.newpet_image_url = ""
    this.addnewbreed = ""
    this.Newpetage=""
    this.newpetgender=""
    this.addnewdob=""
    this.NewpetColor=""
    this.newpetspay=""

  }

 
  datePickerConfig: Partial<BsDatepickerConfig>;
  time_Details = {
    date: new Date()  // Initialize with today's date
  };

  
  // App_Details ={ confirmed: false };
  Appointments = [];
  PastVisit = [];
  Past = [];
  doctors = [];
  past_page = 1;
  past_count = 0;
  page = 1;
  count = 0;
  start: any;
  end: any;
  name = '';
  find = '';
  confirm = '';
  get = '';
  Id = '';

  appointment: {};

  current_owner = {
    first_name: '',
    email: '',
    phone_number: '',
  }

  current_pet = {
    pet_name: '',
    pet_mid: '',
    animal_type: '',
    color: '',
    gender: "",
    dob: ''
  };

  deleteId: any;
  bsValue = new Date();
  fromdate: any;
  todate: any;
  enabledDates: any;
  maxDate = new Date();
  minDate = new Date();
  Add = true;
  Edit = true;
  Delete = true;
  sort = false
  value = 1
  field = "apt_date_time"
  Doctorfailed = false
  petId = '';

  reschedule:any=[];
  cancelled:any = [];

  public App_Details = {
    confirm:"",
    history:[],

    user_id: [{
      login_type: "",
      social_media_id: "",
      first_name: "",
      last_name: "",
      phone_number: "",
      active: "",
      pelfies: [],
      tokens: "",
      device_token: "",
      stripe_id: "",
      _id: "",
      email: "",
      createdAt: "",
      updatedAt: "",
      __v: 0,
      password: "",
      notification_status: "",
      confirmed:"",
    }],
    doctor_id: [{
       confirmed:"",
      _id: "",
      name: "",
      email: "",
      password: "",
      role_id: "",
      location: "",
      address: "",
      phone_no: "",
      status: Boolean,
      resetPasswordToken: "",
      createdAt: "",
      updatedAt: "",
      tokens: "",
    }],
    pet_id: [{
      confirmed:"",
      user_id: "",
      _id: "",
      pet_mid: "",
      pet_name: "",
      age: 0,
      animal_type: "",
      color: "",
      breed: "",
      dob: "",
      spay: "",
      gender: "",
      current_vet: "",
      owner_name: "",
      createdAt: "",
      updatedAt: "",
      image_url: "",
    }],
    payment_completed: Boolean,
    video_status: Boolean,
    notifi: Boolean,
    _id: "",
    kind_appointment: "",
    prefer: "",
    location:"",
    doctor_name: "",
    time: "",
    date: "",
    day: "",
    apt_date_time: "",
    pet_name: "",
    species: "",
    breed_name: "",
    status: "",
    name: "",
    createdAt: "",

    cancelled_At:"",

    updatedAt: "",
    confirmed:false

  }
  treatment: {
    weight: "",
    temp: "",
    pulse: "",
    resp: "",
    vaccinationdata: {
      DHP: {
        checked: 0,
        date: ""
      },
      BORD: {
        checked: Boolean,
        date: ""
      },
      LEPTO: {
        checked: Boolean,
        date: ""
      },
      Rabies: {
        checked: Boolean,
        date: ""
      },
      HWT: {
        checked: Boolean,
        date: ""
      },
      Fecal: {
        checked: Boolean,
        date: ""
      },
      Bloodwork: {
        checked: Boolean,
        date: ""
      },
      Influenza: {
        checke: Boolean,
        date: ""
      }
    },
    placePet: "",
    activityPet: "",
    weightchange: "",
    drinkingHabits: "",
    Stool: "",
    UrinaryHabits: "",
    Appetite: "",
    diet: "",
    flea: "",
    suppliment: "",
    EDUD: "",
    CSVD: "",
    RxRefill: {
      Value: "",
      index: null
    },
    Dentalcare: {
      Value: "",
      index: null
    },
    Nailtrim: {
      Value: "",
      index: ''
    },
    notes: "",
    RefillNotes: "",
    DentalNotes: "",
    NailTrimNotes: "",
    roomno: "",
    bcs: "",
    crt: "",
    diseaselist: {
      General: 0,
      EENT: 0,
      Oral: 1,
      Respiritory: 0,
      Cardiovascular: 0,
      GI_Abdomen: 0,
      Musculoskel: 0,
      Integument: 0,
      Uro_Genital: 1,
      Lymphatic: 0,
      Neurologic: 0,
      Endocrin: 0
    },
    commonAsse: "",
    plan: ""
  }
  doctor = ''
  status: boolean;
  bending: boolean;
  appointment_status: any;
  treatment_value: any;
  doctor_name: any;
  doctorName: any;
  formContainer: any;
  formattedDate: string;
  dayOfWeek: string;
  Doctor_id:any;
  Appointments_time: any;
  time: string;
  ReasonData: any;
  locations: any;
  final_location: any;
  select_location: any;
  selectedDate: any;
  select_Dr: any;
  New_Appointments_time: any;
  firstName:any;
  email:any;
  phoneNumber:any;
  petgender: any;
  petweight: any;
  species: any;
  breed: any;
  locDoctor: any;
  public todayDate:any = new Date();
  selectedDates: any;
  SelectedLocation: any;
  selectedReason: any;
  SelectedAppointment: any;
  customerName: any;
  phoneNumbermber: any;
  selectedmailId: any;
  existPetId:any;
  petName: any;
  selectbrd: any;
  selectColor: any;
  petage:any="Age"
  DOB: string;
  petSpayed: any;
  imageUrl: string | ArrayBuffer | null = null;
  file: any;
  image_url: any;
  location_id: any;
  select_DrName: any;
  AllDetails: string;
  editselectdoctor: any;
  editselectdoctor_id: any;
  searchValue:any;
  Search_Data: any;
  searchemail: any;
  pet_Details: any;
  user_Details: any;
  showSuggestions: boolean=true ;
  emailSave: boolean = false;
  Save: boolean = true;
  userId: any;
  pet_Details2: any;
  testing: any;
  searchdata: any;
  selectedLocation:any;
  selectDoctorName:any;
  selectReason:any;
  selectTime:any;
  selectAppointment:any;
  binding: any;
  editcustomer: string;
  editphone: string;
  arr: any = [];
  editage: string;
  editpetname: string;
  editgender: string;
  editColr: string;
  locDoctorlist: any;
  dropdownOpen: boolean;
  editdoctor: any;
  reason: any;
  validation: boolean = false;
  validationTime: boolean;
  list: any[];
  pdindex=0;
  isChecked:any;
  static: any;
  Selectpet: any;
  selectedPetId: any;
  petvalidation: boolean;
  colorvalidation: boolean;
  animaltypevaldation: boolean;
  breedvalidation: boolean;
  sexvalidation: boolean;
  dobvalidation: boolean;
  spayedvalidation: boolean;
  oldpetdetails:boolean= false;
  newpetdetails:boolean=false;
  newpetLable:boolean=false;
  addpetName:any
  messageText:boolean=false;
  changeDate:any;
  savebtn:boolean=true
  


  constructor(private Appointmentservice: AppointmentService, private cdr: ChangeDetectorRef ,private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private EmployeeService: Employeeservice,public http: HttpClient,public config: Configuration, private Permission: PermissionService, private locationservice: LocationService,private elementRef: ElementRef,private renderer: Renderer2) {
    this.GetDoctorLists();
    // this.App_Details.time = '12:00';
    this.datePickerConfig = Object.assign({}, {
      isAnimated: true,
      dateInputFormat: 'MM-DD-YYYY',
      showWeekNumbers: false,
      minDate: new Date() , // Set minimum date to today,
    });
    this.fromdate = moment().format("MM/DD/YYYY ")


//     this.renderer.listen('window', 'click',(e:Event)=>{

//      if(e.target != this.toggleButton.nativeElement && e.target!=this.menu.nativeElement){
//          this.showSuggestions=false;
//      }
//  });
  }

  @HostListener('document:click', ['$event.target'])
  public onClick(target: any) {
    const clickedInside = this.elementRef.nativeElement.contains(target);
    if (!clickedInside) {
      this.clickOutside.emit();
    }
  }


  ngOnInit(): void {
   
    
  
   
    this.petage=""
   
    this.tokens();
    this.getReason()
    this.locationLists()
    this.Allist()
    this.selectbreed()
    this.editspecies()
    // this.user_Details.first_name=''
   this. user_Details = { first_name: '' ,phone_number:'',email:''};
   this.pet_Details  = {pet_name:''}
//  this.  pet_Details = [
//     {
//       pet_name: '',
//       image_url: '',
//       species: '',
//       breed: '',
//       age: '',
//       color: '',
//       spayed: '',
//       sex: '',
//       dob: ''
//     }
//   ];
   
    
  // this.getReason()

   var search="Saratoga"
   
   console.log("=== INITIALIZING DOCTOR LIST ===")
   console.log("Search location:", search)
   this.arr = [] // Initialize the array
   console.log("Initialized arr array:", this.arr)

  this.Appointmentservice.getDoctor(search)
  .subscribe((res: any) => {
    console.log("ressss======================================>",res)

    console.log("=== PROCESSING DOCTORS ===")
    for(let doc of res.data){
      console.log("Checking doctor:", doc)
      console.log("Doctor role_name:", doc.role_name)
      if(doc.role_name == 'Doctor'){
        console.log("✅ Adding doctor to array:", doc.name)
        this.arr.push(doc)
      } else {
        console.log("❌ Skipping non-doctor:", doc.name, "Role:", doc.role_name)
      }
    }
    
    this.locDoctor = this.arr
    console.log("=== FINAL RESULTS ===")
    console.log("Final locDoctor array:", this.locDoctor)
    console.log("Final arr array:", this.arr)
    console.log("locDoctor length:", this.locDoctor ? this.locDoctor.length : 0)
    console.log("=== END DOCTOR DEBUG ===")
  
  }, (error) => {
    console.error("❌ Error fetching doctors:", error)
    this.locDoctor = []
  })

  console.log("petdetailsss",this.pet_Details)

  const name = this.tokenStorage.getUser()
    this.Name = name.name;
  
  }

  ngAfterViewInit() {
    this.renderer.listen('window', 'click', (e: Event) => {
      if (this.toggleButton && this.toggleButton.nativeElement &&
          this.menu && this.menu.nativeElement) {
        if (e.target !== this.toggleButton.nativeElement && e.target !== this.menu.nativeElement) {
          this.showSuggestions = false;
        }
      } else {
        console.error('toggleButton or menu is undefined');
      }
    });
  }

  //token verified module
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Appointments") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })

    // }
    // else {
    //   this.router.navigate(['/login']);
    // }
  }



  //Get All Doctor List
  GetDoctorLists(): void {
    // console.log('search-->', this.name)
    const Role = this.tokenStorage.getUser();
    const data = {
      limit: 1000,
      search: ''
    }
    this.Appointmentservice.GetDoctorlist(data)
      .subscribe((res: any) => {
        // this.doctors = res.data;
        var arr = []
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].role_id.name == "Doctor") {
            arr.push(res.data[i])
          }
        }
        this.doctors = arr
        // console.log(res)
        if (Role.role_id.name == "Doctor") {
          this.Id = Role._id
          this.start = moment().utc().startOf('day').toISOString();
          this.Doctorfailed = false
          this.end = moment().utc().add(20, 'day').endOf('day').toISOString()
          this.onChange();
        } else {
          this.Doctorfailed = true
          this.start = moment().utc().startOf('day').toISOString();
          this.Id = 'all'
          this.end = moment().utc().add(20, 'day').endOf('day').toISOString()
          this.Allist()
          // this.Id = res.data[0]._id;
          // console.log(this.doctors);
        }
      });
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    skip['limit'] = 10;
    skip['value'] = this.value
    skip['field'] = this.field
    skip['find'] = this.find
    skip['confirm'] = this.confirm
    skip['get'] = this.get
    return skip;
  }

  searched(id): void {
    this.Id = id;
    this.page = 1
    this.count = 0
    // console.log('id-->', this.Id)
    if (id == 'all') {
      // console.log(id)
      this.Allist()
    } else {
      this.onChange();
    }
  }

  onChanging() {
    if (this.Id == 'all' || this.Id == '') {
      this.page = 1
      this.count = 0
      this.Allist()
    } else {
      this.page = 1
      this.count = 0
      this.onChange()
    }
  }

  perfer(i) {
    this.find = i
    if (this.Id == 'all') {
      this.page = 1
      this.count = 0
      this.Allist()
    } else {
      this.page = 1
      this.count = 0
      this.onChange()
    }
  }

  confirmation(i){
    this.confirm = i
    if (this.Id == 'all') {
      this.page = 1
      this.count = 0
      this.Allist()
    } else {
      this.page = 1
      this.count = 0
      this.onChange()
    }
  }

  //Location
  location(i) {
    this.get = i
    if (this.Id == 'all') {
      this.page = 1
      this.count = 0
      this.Allist()
    } else {
      this.page = 1
      this.count = 0
      this.onChange()
    }
  }

  //Get all appointment list
  Allist() {
    // const skip = this.getrequestparams(this.page);
    const data = {
      skip: (this.page - 1) * 10,
      limit: 10,
      value: this.value,
      field: this.field,
      find: this.find,
      confirm: this.confirm,
      get: this.get,
      search: this.name,
      start: this.start,
      end: this.end
    }
    this.Appointmentservice.GetAllappointment(data)
      .subscribe((res: any) => {
        if(res.data){
          this.Appointments = res.data;
        // this.confirm = this.Appointments[0].confirmed
        

        console.log("testing@@@@####",this.Appointments)
        console.log("data",this.appointment)        
        // res.data = res.data.map((item:any)=>{
        //   item.reschedule = item.history.filter((items:any)=>items.task=="Reschedule")
        //   item.cancelled = item.history.filter((items:any)=>items.task=="Cancelled")

        //   return item
        // })

        // Don't set App_Details to the entire array - it should be set to individual appointments
        // this.App_Details = res.data;
        console.log("testing@@@@####",res.data)
        this.count = res.count;
        }
        
      })
  }
  edit(value){
    console.log("editk data",value)
  }

  //Role based search in permission collection
  onChange(): void {
    const skip = this.getrequestparams(this.page);
    this.Appointmentservice.GetDoctorDetails(this.Id, this.name, this.start, this.end, skip)
      .subscribe((res: any) => {
        this.Appointments = res.data;
        this.count = res.count
        console.log("binding data",this.Appointments)
      })
  }

  // takeScreenshot() {
  //   const element = document.getElementById('myModal2'); // Replace 'formDetails' with the ID of your form container
  //   html2canvas(element).then((canvas) => {
  //     // Convert the canvas to a data URL and open it in a new tab
  //     const imgData = canvas.toDataURL('image/png');
  //     const link = document.createElement('a');
  //     link.download = 'form_screenshot.png';
  //     link.href = imgData;
  //     link.click();
  //   });
  // }

  // takeScreenshot() {
  //   const formContainer = document.getElementById('myModal2')
  //   const originalOverflow = formContainer.style.overflow; // Store original overflow property
  //   formContainer.style.overflow = 'visible'; // Expand container to show entire form

  //   html2canvas(formContainer, { scrollX: -window.scrollY }).then((canvas) => {
  //     // Convert the canvas to a data URL and open it in a new tab
  //     const imgData = canvas.toDataURL('image/png');
  //     const link = document.createElement('a');
  //     link.download = 'form_screenshot.png';
  //     link.href = imgData;
  //     link.click();

  //     formContainer.style.overflow = originalOverflow; // Reset overflow property
  //   });
  // }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    if (this.Id == '' || this.Id == 'all') {
      this.Allist()
    } else {
      this.onChange();
    }
  }

  //UTC to MM/DD/YYYY convert
  change(data) {
    let dd = data.getDate();
    let mm = data.getMonth() + 1;
    let yyyy = data.getFullYear();
    var startdate = mm + "-" + dd + "-" + yyyy
    return startdate
  }

  //Date Picker 
  Fromchanged(i, param) {
    if (param == 'from') {
      // this.todate = new Date(i.getTime() + (1 * 24 * 60 * 60 * 1000)) 
      this.start = moment(this.change(i)).format()
      // console.log(this.start)
    } else {
      this.end = moment(this.change(i)).endOf('day').format()
      // console.log(this.end)
    }
    // console.log(i, param)
    this.onChanging()
  }

  GetId(id) {
    // console.log('id-->', id)
    this.deleteId = id
  }

  //Delete Appointmnet
  DeleteAppointment(id): void {
    // console.log('id-->', id)
    this.Appointmentservice.DeleteBooked(id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.removeModal.hide();
        this.onChange();
      })
  }

  //Field name sorted
  Field(param) {
    console.log("testing new",param)
    if (this.Id == 'all') {
      if (this.sort == true) {
        // console.log('hi', param)
        this.sort = false,
          this.field = param
        this.value = -1
        this.Allist();
      } else {
        // console.log('hi1', param)
        this.sort = true
        this.field = param
        this.value = 1
        this.Allist();
      }
    }
    else {
      if (this.sort == true) {
        // console.log('hi', param)
        this.sort = false,
          this.field = param
        this.value = -1
        this.onChange();
      } else {
        // console.log('hi1', param)
        this.sort = true
        this.field = param
        this.value = 1
        this.onChange();
      }
    }

  }

  //Routing to Customer Details
  Router(Index,status,id,user) {
    this.messageText= false;
    this.validation = false;
    console.log("old dateesss",user)
    console.log("old dateesss",status)
    this.ownerName = user.name,
    this.oldDateTime = user.date+" "+user.time+" "+user.day

    console.log("statues=====>",status,Index,id,user)
    console.log("statues",user.doctor_id[0]._id,
    this.Doctor_id = user.doctor_id[0]._id
  )
  this.reschedule =  this.Appointments[Index]?.history?.filter((items:any)=>items.task=="Reschedule") || []
  this.cancelled = this.Appointments[Index]?.history?.filter((items:any)=>items.task=="Cancelled") || []
  //   item.cancelled = item.history.filter((items:any)=>items.task=="Cancelled")
  if(status ==="Cancelled"){
    this.Save = false;
    this.savebtn = false;
    this.confirmbtn = false

  }
 

  if(status === 'Upcoming'){
  
    this.Save = true;
  this.savebtn = true;
  this.confirmbtn = true}

    if(status === 'Completed'){
  
      this.Save = true;
    this.savebtn = true;
    this.confirmbtn = true

      this.Appointmentservice.appointmentDetail(id) .subscribe((res: any) => {
        console.log("RES",res.data)
        this.appointment_status = res.data

        this.final_data = res.data
        // this.doctorName = res.data.doctor_name
        this.secondaryModal.show()
        this.totalAmount = this.getTotalAmount(res.data[0].treatment.prescription_data.dataArray)
        this.totalAmount = this.totalAmount.toFixed(2)
 
      })


    }

    else{
      console.log("-=-=--=-=->",this.Appointments[Index])

      this.App_Details = this.Appointments[Index];
      if(this.App_Details && this.App_Details.confirmed){

      }else{
        this.App_Details.confirmed=false;
      }
      this.isChecked=this.App_Details.confirmed
      this.static=this.App_Details.confirmed
      
      // Initialize date and time variables for partial updates
      this.changeDate = this.App_Details.date;
      this.formattedDate = this.App_Details.date;
      this.time = this.App_Details.time;
      this.dayOfWeek = this.getDayOfWeek(new Date(this.App_Details.date));

      // this.isChecked = this.App_Details.

      this.current_pet = this.Appointments[Index].pet_id[0];
      

      this.current_owner = this.Appointments[Index].user_id[0];
      
      console.log("customer @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ appdetail",this.App_Details?.history)
      console.log("customer @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ current pet",this.current_pet )
      console.log("customer @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ owner",this.current_owner )
      this.primaryModal.show();
      this.past_page = 1;
      this.pastvisit();
      // debugger
    
      console.log("id",id)
    }
   
 

  
  }
  // details(userDetail:any){
  //   for(let detail of userDetail){
  //     console.log("detailsssss",detail)
  //   }

  // }


  pastvisit() {
    console.log("testing", this.App_Details.user_id)
    const param = {
      skip: this.past_page - 1,
      limit: 1,
      apt_date_time: moment().utc().format()
    }
    this.Appointmentservice.GetPastVisit(this.App_Details.user_id[0]._id, param)
      .subscribe((res: any) => {
        this.Past.length = 0;
        this.PastVisit = res.data;
        this.past_count = res.count;
        this.Past.push(this.PastVisit[0]);
        if(res.data[0].treatment&&res.data[0].treatment.prescription_data && res.data[0].treatment.prescription_data.dataArray.length >0){
           this.totalAmount = this.getTotalAmount(res.data[0].treatment.prescription_data.dataArray)
        this.totalAmount = this.totalAmount.toFixed(2)
        }
       
      })
  }

  PasthandlePageChange(event: number) {
    //Page handle 
    this.past_page = event;
    this.pastvisit();
  }
  
  getDetailsdate(event: Date): void {
    console.log("-------",event)
    this.changeDate = this.formatDate(event);
    // let testingDate = this.changeDate
    if(this.changeDate != this.App_Details.date){
      console.log("testing1")
      this.messageText=true;
      console.log("########",this.changeDate)
      console.log("########",this.App_Details.date)
    }
    // if(this.changeDate === this.App_Details.date){
    //   console.log("testing1")
    //   this.messageText=false;
    // }
    // if(this.changeDate == testingDate){
    //   console.log("testing2")
    //   this.messageText=false;
    // }

    if (event) {
      console.log("===============================>",event)
      this.formattedDate = this.formatDate(event);
      this.dayOfWeek = this.getDayOfWeek(event);
      console.log('Selected date:', this.formattedDate);
      console.log('Day of the week:', this.dayOfWeek);

    

    const param = {
     day: this.dayOfWeek.toLowerCase(),
     date:this.formattedDate
    
    }
    console.log("ressss=======>11",this.Doctor_id)
    if(this.Doctor_id){
      this.Appointmentservice.getappointment(this.Doctor_id, param)
      .subscribe((res: any) => {
        console.log("ressss=======>11",res)
        this.Appointments_time = res.Arr
        this.validation=false
      console.log("this.Appointments_time",this.Appointments_time)
      })
    }
  }

  }

  onTimeChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.time = selectElement.value;
    console.log('Selected time:', this.time);
    console.log("testing time", this.App_Details)
    if(this.App_Details.time != this.time){
      this.messageText =  true;
    }
    // Additional logic can be added here if needed
  }

  appointment_update(){
    // this.formattedDate = this.formatDate(this.App_Details.date);
    console.log("daateee",this.formattedDate)
    console.log("time",this.time)
    console.log("datyyyyy",this.dayOfWeek.toLowerCase())
    console.log("id",this.App_Details._id)
    console.log("detail",this.App_Details)
    console.log("doctor id222",this.Doctor_id)
    

    // Allow partial updates - either date or time can be changed independently
    if(this.time=="" && this.formattedDate==""){
      console.log("not updating, at least date or time must be provided")
      this.validation = true
      return false;
    }
    else{
      let param2: any = {};
      
      // Handle partial updates - use existing values if new ones are not provided
      const finalDate = this.formattedDate || this.App_Details.date;
      const finalTime = this.time || this.App_Details.time;
      const finalDay = this.dayOfWeek || this.getDayOfWeek(new Date(this.App_Details.date));
      
      // Always set the final values
      param2.date = finalDate;
      param2.time = finalTime;
      param2.day = finalDay.toLowerCase();
      param2.apt_date_time = moment(finalDate+" "+finalTime,"MM/DD/YYYY h:mm a").utc().format();
        
      if(this.editdoctor)
        param2.doctor_name = this.editdoctor;
      if(this.reason)
        param2.kind_appointment = this.reason;
      if(this.Doctor_id)
        param2.doctor = this.Doctor_id;
    
        param2.confirmed = this.isChecked
    // if(this.message)
    //   param2.note=this.message
      
    if(this.date)
      param2.date=this.date
      
      // const param = {
      //   day: this.dayOfWeek?this.dayOfWeek.toLowerCase():'',
      //   date:this.formattedDate?this.formattedDate:'',
      //   time:this.time?this.time:'',
      //   apt_date_time: moment(this.formattedDate+" "+this.time,"MM/DD/YYYY h:mm a").utc().format(),
      //   doctor_name:this.editdoctor?this.editdoctor:"",
      //   kind_appointment:this.reason?this.reason:'',      
      //   doctor : this.Doctor_id,
      //   // user:{
      //   // user_id:
      //   // user:this.editcustomer,
      //   // user_phone:this.editphone

      //   // },

      //   // pet:{
      //   //   pet_name : this.editpetname,
      //   //   animal_type :this.species,
      //   //   breed :this.selectbrd,
      //   //   color : this.editColr,
      //   //   gender:this.editgender ,
      //   //   age :this.petage,
      //   //   dob :this.DOB,   
      //   // },
        
      
      //  }

      console.log("paramssssssss",this.App_Details)
      console.log("11111111111111111111111",param2)
      // this.formattedDate = this.formatDate(this.selectedDate);
      // this.dayOfWeek = this.getDayOfWeek(this.selectedDate);
   
      if((this.App_Details.time != finalTime) || (this.App_Details.date != finalDate) ){
        console.log("testing one - Reschedule detected")      
        console.log("Original:", this.App_Details.date, this.App_Details.time);
        console.log("Final:", finalDate, finalTime);
      
        const newData = {
          task :"Reschedule",
          name:this.ownerName,
          oldDateTime:this.oldDateTime,
          newDateTime:finalDate+" "+finalTime+" "+finalDay.toLowerCase(),        
          reason:this.note,
          cancelDate:"",
          date:finalDate,
          time:finalTime,
        };
        // console.log("11111111111",newData)
        // console.log("22222222222",this.App_Details.history)
        // console.log("333333333333", this.App_Details.history.push(newData))
        // console.log("22222222222",this.App_Details)
       if (!this.App_Details.history) {
         this.App_Details.history = [];
       }
       this.App_Details.history.push(newData)
      // this.Appointmentservice.update_appointment_Reson(this.App_Details._id, param2,this.App_Details.history.push(newData))
      // .subscribe((res: any) => {
      //   console.log("ressss======================================>",res)
      //   this.primaryModal.hide()     
      //   this.Allist()
      //   this.GetDoctorLists();
      
      // })
      console.log("22222222222",newData )
      }
      // console.log("22222222222",this.App_Details)
      param2.history=this.App_Details.history || [];
      console.log("22222222222",this.App_Details )
    
      this.Appointmentservice.update_appointment(this.App_Details._id, param2)
      .subscribe((res: any) => {
        console.log("ressss======================================>",res)
        this.primaryModal.hide()     
        this.Allist()
        this.GetDoctorLists();
      
      })
    }
  }

  close(){
    this.Allist()

  }

  // formatDate(date: Date): string {
  //   const options = { year: 'numeric', month: 'short', day: '2-digit' } as const;
  //   return date.toLocaleDateString('en-US', options);
  // }

  getDayOfWeek(date: Date): string {
    const options = { weekday: 'short' } as const;
    return date.toLocaleDateString('en-US', options);
  }

  formatDate(date: Date): string {
    const month = ('0' + (date.getMonth() + 1)).slice(-2); // Adding 1 because months are zero-based
    const day = ('0' + date.getDate()).slice(-2);
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  }

  getReason(){

    const param = {
      search:'',
      limit:20,
     
     
     }

    this.Appointmentservice.getReson( param)
    .subscribe((res: any) => {
     
      this.ReasonData= res.data.sort((a, b) => a.name.localeCompare(b.name))
      console.log("ressss======================================>",this.ReasonData)
    
    
    })



  }

  locationLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.locationservice.GetLocationsList(skip, this.name)
      .subscribe((res: any) => {
        this.locations = res.data;
        this.count = res.count;
        console.log("locationsssssssss",this.locations);
        // console.log(this.count);

        this.final_location = this.locations.filter((item) => item.status == true)
        console.log("final location",this.final_location)
     
      
      });
  }

  selectDr(event){
    console.log("testingggg",event)
    
    this.select_Dr = event

    if(this.locDoctor && this.locDoctor.length > 0){
      for(let dr of this.locDoctor){
        if(dr._id == this.select_Dr){
          console.log("dr",dr.name)
          this.select_DrName = dr.name
        }
      }
    }


    if (event) {
      console.log("===============================>",event)
      this.formattedDate = this.formatDate(this.selectedDate);
      this.dayOfWeek = this.getDayOfWeek(this.selectedDate);
      console.log('Selected date:', this.formattedDate);
      console.log('Day of the week:', this.dayOfWeek);
    
    }

    const param = {
     day: this.dayOfWeek.toLowerCase(),
     date:this.formattedDate,
     doctor:this.select_Dr
    
    }
    console.log("ressss=======>22",this.select_Dr)
    this.Appointmentservice.getappointment(this.select_Dr, param)
    .subscribe((res: any) => {
      
      this.New_Appointments_time = res.Arr
      console.log("ressss=======>22",this.New_Appointments_time)

    
    })

  }
  NewAppType(event){
    console.log("tewsting",event)
    this.SelectedAppointment = event

  }
  SelectReason(event){
    console.log("tesing",event)
    this.selectedReason = event

  }

  // selectloc(event){
  //   console.log("location",event)
  //   this.SelectedLocation = event

  //   for(let location of this.final_location){
  //     if(location.name == this.SelectedLocation ){
  //       console.log("location@@@@@@@@@@@@", location._id)
  //       this.location_id = location._id
  //       console.log("location", location)

  //     }
  //   }

    
    

  // }

  // editselectdr(event){
  //   console.log("testing", event)
  //   this.editselectdoctor= event

  //   var search="Saratoga"
   



  //   this.Appointmentservice.getDoctor(search)
  //   .subscribe((res: any) => {
  //     console.log("ressss======================================>",res)
  //     this.locDoctor = res.data
  //     this.locDoctor.map((res:any)=>{ 
  //       if(res.name == this.editselectdoctor){
  //         console.log("res",res)
  //         this.editselectdoctor_id= res._id

  //       }

        


  //     })
     
    
  //   })



  // }
  selectLocation(event){
    console.log("location@@@@@@@@@@@@@@@@@@@@@@@@@@",event)
    this.SelectedLocation = event;

    for(let item of this.final_location){
      if(item.name == this.SelectedLocation ){
    
        this.location_id = item._id
      

      }
    }

    const param = {
      search:event     
     }

    this.Appointmentservice.getDoctor(event)
    .subscribe((res: any) => {
      console.log("=== LOCATION SELECTED - DOCTOR API RESPONSE ===")
      console.log("Location selected:", event)
      console.log("Full API response:", res)
      console.log("API data array:", res.data)
      console.log("Data length:", res.data ? res.data.length : 0)
      
      // Filter for doctors only
      var doctorArr = []
      console.log("=== FILTERING DOCTORS BY LOCATION ===")
      for(let doc of res.data){
        console.log("Checking doctor:", doc.name, "Role:", doc.role_name)
        if(doc.role_name == 'Doctor'){
          console.log("✅ Adding doctor to location array:", doc.name)
          doctorArr.push(doc)
        } else {
          console.log("❌ Skipping non-doctor:", doc.name, "Role:", doc.role_name)
        }
      }
      this.locDoctor = doctorArr
      console.log("=== LOCATION DOCTOR RESULTS ===")
      console.log("Final locDoctor array for location:", this.locDoctor)
      console.log("locDoctor length:", this.locDoctor ? this.locDoctor.length : 0)
      console.log("=== END LOCATION DOCTOR DEBUG ===")
    
    })


  }
  selectDate(event){
    this.selectedDate=event;
    console.log("selected date", this.selectedDate)

    if (event) {
      console.log("===============================>",event)
      this.formattedDate = this.formatDate(event);
      this.dayOfWeek = this.getDayOfWeek(event);
      console.log('Selected date:', this.formattedDate);
      console.log('Day of the week:', this.dayOfWeek);
     
    

    const param = {
     day: this.dayOfWeek.toLowerCase(),
     date:this.formattedDate,
     doctor:this.select_Dr
    
    }
    console.log("ressss=======>33",this.select_Dr)
    this.Appointmentservice.getappointment(this.select_Dr, param)
    .subscribe((res: any) => {
      
      this.New_Appointments_time = res.Arr
      console.log("ressss=======>33",this.New_Appointments_time)

    
    })
  }

  }

  closepopup(){
    this.oldpetdetails = false;
    this.newpetdetails = false;

  }
  AddAppointment(){
   this.oldpetdetails = true
   this.newpetdetails = false;
   this.newpetLable = false
   
   
    this.searchValue = ''
this.AddAppointmentModal.show()
this.rest_details()
this.showSuggestions=false
this.searchValue=""
this.user_Details.first_name=""
this.user_Details.phone_number=""
this.user_Details.email=""
this.selectedDate=""
this.selectReason=""
this.selectDoctorName=""
this.selectedLocation=""
this.selectTime=""
this.selectAppointment=""

this.petvalidation = false;
this.colorvalidation = false;
this.animaltypevaldation = false;
this.breedvalidation = false;
this.sexvalidation = false;
this.dobvalidation = false;
this.spayedvalidation = false;
}

  //Pet detailasss
  petGender(event){
    this.petgender = event
    console.log("pet gender",this.petgender)

  }
  ensurelbs(): void {
    // Append "kg" if not already present
    if (!this.petweight.endsWith(' lbs')) {
      this.petweight = `${this.petweight} lbs`;
    }
  }

  petWeight(event){
    this.petweight = event
    console.log("PET WEIGHT", this.petweight)

  }
  SelectSpecies(event){
    
    this.species = event

    console.log("select speciesssss", this.species)
    this.selectbreed()
  }

  selectbreed(){

    const param = {
      search: this.species,
      limit:400,

     }
 
     this.Appointmentservice.getbreed(param)
     .subscribe((res: any) => {
       
       
       console.log("ressss=======>44",res)
       this.breed=res.data
 
     
     })
    

   

  }
  customer(event){
    console.log("log",event)
    this.customerName = event
  }
  phone(event){
    console.log("log",event)
     this.phoneNumbermber = event
  }
  Selectemail(event){
    this.selectedmailId = event
    console.log("mail",this.selectedmailId)
    this.showSuggestions = false; 
 }
 backpetName(event){
  console.log("working")
  console.log(event)
  this.petName= event
  

 }
 selectedbreed(event){
  this.selectbrd = event
  console.log("breeeeddddd",this.selectbrd)

 }

 color(event){
  this.selectColor = event

 }
 dod(event){

  var dateOfBirth = moment(event).format("MM/DD/YYYY")

  var petAge = moment().diff(moment(event).format("L"), 'years').toString()

  console.log("selected dataeeeeeeeeeee",dateOfBirth)

  this.petage= petAge
  this.DOB = dateOfBirth
  console.log(this.DOB)

 }
 spayed(event){
  this.petSpayed = event
  console.log("testttttt",this.petSpayed)

 }
//  addImages(data: File): Observable<any> {
//   const formData: FormData = new FormData();
//   formData.append('file', data);
//     console.log('service', formData)
//   return this.http.post(this.config.UploadFile, formData, { reportProgress: true, responseType: 'json' });
// }
 onFileSelected(event: any): void {

  const formData: FormData = new FormData();

  const file = event.target.files[0];
  console.log("file", event)

  formData.append("file", file),{ reportProgress: true, responseType: 'json' };
  console.log("fffff",formData)

  const datas = new FormData()
  this.Appointmentservice.uploadFile(file)
  .subscribe((res: any) => {
    console.log("tesing",res.data)
    this.image_url=res.data
    
    

  
  })
}

Editreason(event){
  console.log(event)
  this.reason = event
  this.messageText=true;

}


  


  
  
  // data.append('file',{ url: file.name, name: Date.now().toString(), type:file.type })

  // formData.append('file', file, `${Date.now().toString()}_${file.name}`);



  // if (file) {
  //   const reader = new FileReader();
  //   reader.onload = (e) => {
  //     this.imageUrl = e.target?.result;
  //   };
  //   reader.readAsDataURL(file);
  // }

  rest_details(){
    
    // this.petage=0   
    this.customerName=""
    this.phoneNumbermber=""
    this.selectedmailId=""   
    this.petName=""
    this.selectedPetId=""
    this.existPetId=""
    this.species=""
    this.selectbrd=""
     this.selectColor==""
    this.petgender =""
    this.petage=""
    this.DOB=""
    this.petSpayed=""
        
    this.image_url=""

    this.select_Dr=""
    this.select_DrName=""
    this.dayOfWeek=""
    this.formattedDate=""
    this.time=""
    this.SelectedAppointment=""
    this.selectedReason=""
    this.SelectedLocation=""
    this.pet_Details = [
      {
           
        pet_name: '',
        age: 0,
        animal_type: '',
        color: '',
        breed: '',
        dob: '',
        spay: '',
        gender: '',  
        image_url: '',  
        
     
      }
    ];
   
    moment(this.formattedDate+" "+this.time,"MM/DD/YYYY h:mm a").utc().format()

  };

  getAllDetails(){

    this.AllDetails =""
  }



//   AddBackendAppointment(){
//     if(this.petName == ''){
//       this.petvalidation = true
//     }

//     if( this.selectColor == ""){
//       this.colorvalidation = true
//     }
//     if( this.species== ""){
//       this.animaltypevaldation = true;
//     }
//     if(this.selectbrd == ""){
//       this.breedvalidation = true
//     }

//     if( this.petgender == ''){
//       this.sexvalidation = true
//     }
//     if(this.petSpayed == "" ){
//       this.spayedvalidation = true
//     }
//     if(this.DOB == ''){
//       this.dobvalidation = true;
//     }
// else{
//   const params = {

//     user:{
//       user:this.customerName,
//       user_phone :this.phoneNumbermber,
//       user_email : this.selectedmailId, 
      
//     },
//     pet:{
//       pet_name : this.petName,
//       animal_type :this.species,
//       breed :this.selectbrd,
//       color : this.selectColor,
//       gender:this.petgender ,
//       age :this.petage,
//       dob : this.DOB,
//       spay:this.petSpayed,
//       current_vet:'',    
//       image_url:this.image_url,
//     },
  
       
//       status: "Upcoming",
//       doctor : this.select_Dr,
//       doctor_name:this.select_DrName,
//       day: this.dayOfWeek.toLowerCase(),
//       date:this.formattedDate,
//       time:this.time,
//       prefer:this.SelectedAppointment,
//       kind_appointment : this.selectedReason,
//       location :this.SelectedLocation,
//       location_id : this.location_id,
//       apt_date_time: moment(this.formattedDate+" "+this.time,"MM/DD/YYYY h:mm a").utc().format(),
//       // pet_name : this.petName,
//       // breed_name :this.selectbrd,
//       // species :this.species,
//       // user_name:this.customerName,
//   }
//     console.log("finalsssss",params)

//     this.Appointmentservice.backendappointment(params)
//     .subscribe((res: any) => {
      
//       this.New_Appointments_time = res
      
//       this.AddAppointmentModal.hide();
//       window.location.reload();
//       console.log("ressss=======>55",this.New_Appointments_time)

    
//     })


// }
   

   
    

    

 


//   }

AddBackendAppointment() {
  let isValid = true;  // Flag to track the overall validity

  // if (this.petName === '') {
  //   this.petvalidation = true;
  //   isValid = false;  // Set the flag to false if validation fails
  // } else {
  //   this.petvalidation = false;
  // }


  // if (this.selectColor === "") {
  //   this.colorvalidation = true;
  //   isValid = false;
  // } else {
  //   this.colorvalidation = false;
  // }


  // if (this.species === "") {
  //   this.animaltypevaldation = true;
  //   isValid = false;
  // } else {
  //   this.animaltypevaldation = false;
  // }



  // if (this.selectbrd === "") {
  //   this.breedvalidation = true;
  //   isValid = false;
  // } else {
  //   this.breedvalidation = false;
  // }



  // if (this.petgender === '') {
  //   this.sexvalidation = true;
  //   isValid = false;
  // } else {
  //   this.sexvalidation = false;
  // }



  // if (this.petSpayed === "") {
  //   this.spayedvalidation = true;
  //   isValid = false;
  // } else {
  //   this.spayedvalidation = false;
  // }


  // if (this.DOB === '') {
  //   this.dobvalidation = true;
  //   isValid = false;
  // } else {
  //   this.dobvalidation = false;
  // }

  



  // Proceed only if all validations pass
  if (isValid) {
    const params = {
      user: {
        user: this.customerName,
        user_phone: this.phoneNumbermber,
        user_email: this.selectedmailId,
      },
      pet: {
        // pet_Id :this.selectedPetId,
        pet_id:this.selectedPetId,
        pet_name: this.petName,
        animal_type: this.species,
        breed: this.selectbrd,
        color: this.selectColor,
        gender: this.petgender,
        age: this.petage,
        dob: this.DOB,
        spay: this.petSpayed,
        current_vet: '',
        image_url: this.image_url?this.image_url:"",
      },
      status: "Upcoming",
      doctor: this.select_Dr,
      doctor_name: this.select_DrName,
      day: this.dayOfWeek.toLowerCase(),
      date: this.formattedDate,
      time: this.time,
      prefer: this.SelectedAppointment,
      kind_appointment: this.selectedReason,
      location: this.SelectedLocation,
      location_id: this.location_id,
      apt_date_time: moment(this.formattedDate + " " + this.time, "MM/DD/YYYY h:mm a").utc().format(),
    };

    console.log("finalsssssno search", params);

    this.Appointmentservice.backendappointment(params)
      .subscribe((res: any) => {
        this.New_Appointments_time = res;
        this.AddAppointmentModal.hide();
        window.location.reload();
        console.log("ressss=======>55", this.New_Appointments_time);
      });
  }
}



  resetpet(pet){
    // this.selectedPetId="";
   pet.pet_name="";
   pet.image_url="";
   pet.animal_type ="";
   pet.breed ="";
  this.petage= 'Age';
  pet.gender = "";
  pet.dob = "";
  pet.color = "";
  pet.spay = "";
   
  
  }

  search(){
    console.log("lllll",this.searchValue)
    this.showSuggestions=true;
    const param = {
      emailString:this.searchValue
    }


    this.Appointmentservice. GetUserSearch(param)
    .subscribe((res: any) => {
      console.log("all datassss",res.users )
      this.Search_Data = res.users
    })


  }

  searchMail(data:any){
    console.log("testing data", data)
    console.log("testing data", data.email)


      this.searchemail=data.email
      console.log("maillllll",this.searchemail)


    const params = {
      email:this.searchemail
    }

    this.Appointmentservice.emailSearchData(params)
    .subscribe((res: any) => {
      console.log("testinggggggg",res)
     
        this.pet_Details = res.pet != undefined && res.pet != null && res.pet.length >0? res.pet:this.pet_Details;
        this.user_Details= res.user;
        this.searchdata=res.pet
        console.log("c-h=e-=-==--=====================",this.pet_Details.length >0)
  
        console.log("result@@@@@@@@@@@@@@@@@@@@@@@@@2",this.searchdata)
        console.log("result",res.user)
        this.species =res.pet[0]?.animal_type
        this.SelectSpecies(this.species)
        this.userId=res.user._id
        this.customerName=res.user.first_name,
        this.phoneNumbermber=res.user.phone_number,
        this.selectedmailId=res.user.email,
        console.log("user id", this.userId)
     
       const length = this.pet_Details.length
 
  
     
     if(this.pet_Details.length >0){
      // this.pet_Details = res.pet[0];
      this.selectedPetId=res.pet[0]._id;
     }
     console.log("lengthhhhhhhhhhhhhhhhhhhhhhhhhhhhhh", this.selectedPetId)
      

    })
    this.showSuggestions = false; 

    // https://reedapp.net:3000/api/v1/user/findUserByEmailId

    this.emailSave = true
    this.Save = false

  }

  AddBackendPet(){
  

    const params = {

      user:{
        user:this.customerName,
        user_phone :this.phoneNumbermber,
        user_email : this.selectedmailId,
        user_id :this.userId 
        
      },
      
      pet:{        
        pet_id:this.selectedPetId,
        pet_name : '',
        animal_type :"",
        breed :'',
        color : '',
        gender:'' ,
        age :'',
        dob : '',
        spay:'',
        current_vet:'',    
        image_url:'',
      },
    
         
        status: "Upcoming",
        doctor : this.select_Dr,
        doctor_name:this.select_DrName,
        day: this.dayOfWeek.toLowerCase(),
        date:this.formattedDate,
        time:this.time,
        prefer:this.SelectedAppointment,
        kind_appointment : this.selectedReason,
        location :this.SelectedLocation,
        location_id : this.location_id,
        apt_date_time: moment(this.formattedDate+" "+this.time,"MM/DD/YYYY h:mm a").utc().format(),
     
    }

    if(this.newpetdetails == true){
      

      params.pet.pet_name =this.addpetName,
 
      params.pet.animal_type =this.Newspecies,
      params.pet.breed =this.selectNewbrd,
      params.pet.color = this.newpetcolor,
      params.pet.gender=this.newpetgender,
      params.pet.age =this.Newpetage,
      params.pet.dob = this.NewDOB,
      params.pet.spay=this.newpetspayed
      params.pet.current_vet='',    
      params.pet.image_url=this.newpet_image_url?this.newpet_image_url:"";
    }


    if(this.oldpetdetails == true){

      params.pet.pet_name =this.petName?this.petName:this.pet_Details[0].pet_name ,
      params.pet.pet_name = this.petName?this.petName:this.pet_Details[0].pet_name ,
      params.pet.animal_type =this.species?this.species:this.pet_Details[0].animal_type,
      params.pet.breed =this.selectbrd?this.selectbrd:this.pet_Details[0].breed,
      params.pet.color = this.selectColor?this.selectColor:this.pet_Details[0].color,
      params.pet.gender=this.petgender?this.petgender:this.pet_Details[0].gender ,
      params.pet.age =this.petage?this.petage:this.pet_Details[0].age,
      params.pet.dob = this.DOB?this.DOB:this.pet_Details[0].dob,
      params.pet.spay=this.petSpayed?this.petSpayed:this.pet_Details[0].spay,
      params.pet.current_vet='',    
      params.pet.image_url=this.image_url?this.image_url:this.pet_Details[0].image_url
      }
        



    if(this.selectedPetId){
      params.pet.pet_id=this.selectedPetId;
    }
    // if(){
    //   // pet_id:this.selectedPetId?this.selectedPetId:this.pet_Details[0]._id,
    // }
      console.log("finalsssss",params)
  
      this.Appointmentservice.backendappointment(params)
      .subscribe((res: any) => {
      
        
        this.New_Appointments_time = res
        this.AddAppointmentModal.hide();
        console.log("ressss=======>66",this.New_Appointments_time)
        window.location.reload(); 
      
      })

  }

  petselect(data){
    this.oldpetdetails = true;
    this.newpetdetails = false
    this.selectedPetId = data
    this.newpetLable = false
  
    
 
    const pet = []
    this.binding=[]
    // this.pet_Details=[]
    
  console.log("testliveeeeeeeeeeeeeeeeeeeeeeeeeeeeee",data)
  console.log(this.pet_Details)
  // this.pet_Details.
  for(let pet of this.pet_Details){
    if(data == pet._id){
      this.Selectpet = pet.pet_name
      console.log("selected pet name",this.Selectpet)
    }

  }

  
  //  this.list = []
  //  this.list.push(this.pet_Details)

  //  console.log("listttttt",this.list)
   
    


  // for(let item of this.pet_Details){
  //   if(item._id == data){
      
  //     pet.push(item)
  //     this.binding = pet
  //     console.log("teeeeee",this.binding)

  //   }
  // }


this.pet_Details.forEach((item, index) => {
  if (item._id === data) {
    console.log("pet name",this.petName)
    this.petName=item.pet_name;
    this.pdindex = index; 
  }
});



// this.testing = data.animal_type
  
    // const pet = []
    // pet.push(data)
    // this.pet_Details = pet

    // console.log("@@@@@@@@",this.pet_Details)
 
    

  }

  editcustomername(){
    this.editcustomer=this.current_owner.first_name
  //  const data= this.current_owner.first_name
  //  console.log(data)
  }
  editphoneNumber(){
    this.editphone=this.current_owner.phone_number
    // const data=this.current_owner.phone_number
    // console.log(data)
  }
  editpetName(){
    this.editpetname = this.current_pet.pet_name
  

  }
  editspecies(){

    const item = this.current_pet.animal_type
    console.log(item)

    this.species = item
    this.selectbreed()

  }

  editcolor(){
    this.editColr = this.current_pet.color
    console.log(this.editColr)

  }
  editsex(){
    this.editgender= this.current_pet.gender
 

  }
  editdob(event){
    var dateOfBirth = moment(event).format("MM/DD/YYYY")

    var petAge = moment().diff(moment(event).format("L"), 'years').toString()
  
   
  
    this.editage= petAge
    console.log("selected dataeeeeeeeeeee",dateOfBirth)

    this.DOB= dateOfBirth

  }
  editdorct(data){
    this.messageText=true;
    console.log(data)
    this.App_Details.date=""
   this.time=''
   this.App_Details.time="--Select Time--"
   this.Appointments_time=[]
   this.editdoctor = data
  }
  locationchange(){
    this.messageText=true;
  }

  cancleAppointment(){

    this.Save = false;
    this.savebtn=false;
    this.confirmbtn = false;
     const now = new Date();
    const dataTime = moment().format("MM/DD/YYYY hh:mm:ss a")
 
     // New history entry
     var cancel =   {
        task :"Cancelled",
        name:"",    
        reason:"Cancelled",
        datatime:dataTime
    }

     console.log("cancel date",cancel)

    var history = this.App_Details.history || []
    history.push(cancel)

    const params={
      status : "Cancelled",
      history

    }
    console.log("=--=-=-=-=-=-=-=-=-=-=-->",params)
    //  const cancel = 
    //   cancelDate:""

    
    // this.App_Details.cancelled_At = formattedDate

    
    // this.App_Details.history.push(cancel);
    // this.App_Details.status = "Cancelled"

          // console.log("22222222222",this.App_Details)
          // let history= this.App_Details.cancelled_At;
          // console.log("historyhistoryhistory  === ",history )
    
          


    // this.App_Details.history.push(cancelEntry);
    
   const id = this.App_Details._id

    this.Appointmentservice.Cancleappointment(id,params)
    .subscribe((res: any) => {
      this.primaryModal.hide();
      this.Allist()
      console.log("result",res)})


  }



  onCheckboxChange() {
    this.isChecked = !this.isChecked
    console.log("valie",this.isChecked)
    // console.log(event)
    // const inputElement = event.target as HTMLInputElement;
    // this.isChecked = inputElement.checked;
   
    // console.log("valuessssss",this.isChecked)

    // console.log(`Checkbox is now ${this.isChecked ? 'checked' : 'unchecked'}`);
    // Add your custom logic here
  }
  onClickOutside(){
   this.showSuggestions = false;
  // //  this.searchValue = ''

  }



  petname(){
    
    if(this.petName == ''){
      this.petvalidation = true
    }
    if(this.petName != ''){
      this.petvalidation = false
    }
    }
  focuspet(){
    if(this.petName !=""){
      this.petvalidation =false
    }
  }


  colorname(){
    if( this.selectColor == ""){
      this.colorvalidation = true
    }
    if(this.selectColor != ''){
      this.colorvalidation = false
    }
  }
  focuscolor(){
    if(this.selectColor != ''){
      this.colorvalidation = false
    }
  }

 
  animaltype(){
    if( this.species== ""){
      this.animaltypevaldation = true;
    }
    if( this.species != ""){
      this.animaltypevaldation = false;
    }
  }

  focusanimaltype(){
    if( this.species != ""){
      this.animaltypevaldation = false;
    }
  }

  breedtype(){
    if(this.selectbrd == ""){
      this.breedvalidation = true
    }
    if(this.selectbrd != ""){
      this.breedvalidation = false
    }
  }
  focusbreed(){
    if(this.selectbrd != ""){
      this.breedvalidation = false
    }
  }


  sextype(){
    if( this.petgender == ''){
      this.sexvalidation = true
    }

    if( this.petgender != ''){
      this.sexvalidation = false
    }

  }
  focussex(){
    if( this.petgender != ''){
      this.sexvalidation = false
    }
  }

  dobtype(){
    if(this.DOB == ''){
      this.dobvalidation = true;
    }
    if(this.DOB != ''){
      this.dobvalidation = false;
    }
  }

  focusdob(){
    if(this.DOB != ''){
      this.dobvalidation = false;
    }
  }

  spayedname(){
    if(this.petSpayed == "" ){
      this.spayedvalidation = true
    }
    if(this.petSpayed != "" ){
      this.spayedvalidation = false
    }
  }
  focusspayed(){
    if(this.petSpayed != "" ){
      this.spayedvalidation = false
    }
    
  }
  

  closeSuggestionBox(){
    this.showSuggestions=false;
  }

  newpetimage(event: any): void {

    const formData: FormData = new FormData();
  
    const file = event.target.files[0];
    console.log("file", event)
  
    formData.append("file", file),{ reportProgress: true, responseType: 'json' };
    console.log("fffff",formData)
  
    const datas = new FormData()
    this.Appointmentservice.uploadFile(file)
    .subscribe((res: any) => {
      console.log("tesing",res.data)
      this.newpet_image_url=res.data
      
      
  
    
    })
  }

  SelectNewSpecies(event){
    
    this.Newspecies = event

    console.log("select speciesssss", this.species)
    this.selectNewbreed()
  }

  selectNewbreed(){

    const param = {
      search: this.Newspecies,
      limit:400,

     }
 
     this.Appointmentservice.getbreed(param)
     .subscribe((res: any) => {
       
       
       console.log("ressss=======>44",res)
       this.Newbreed=res.data
     
     })
    
  }

  selectedNewbreed(event){
    this.selectNewbrd = event
    console.log("breeeeddddd",this.selectNewbrd)
  
   }

   NewpetGender(event){
    this.newpetgender = event
    console.log("pet gender",this.newpetgender)

  }

  Newdod(event){

    var dateOfBirth = moment(event).format("MM/DD/YYYY")
  
    var petAge = moment().diff(moment(event).format("L"), 'years').toString()
  
    console.log("selected dataeeeeeeeeeee",dateOfBirth)
  
    this.Newpetage= petAge
    this.NewDOB = dateOfBirth

    console.log("testing dob",this.Newpetage+"------",this.NewDOB)
   
  
   }

   NewPetcolor(event){
    this.newpetcolor = event
    console.log(this.editColr)

  }
  Newspayed(event){
   this.newpetspayed = event

  }


   
  addpet(){
    console.log("pet name",this.addpetName)
    console.log("image url", this.newpet_image_url)
    console.log("image url", this.Newspecies)
    console.log("image url", this.selectNewbrd)
    console.log("image url", this.newpetgender)
    console.log("image url", this.newpetcolor)
    console.log("image url", this.Newpetage)
    console.log("image url", this.NewDOB)
    console.log("image url", this.newpetspayed )

  }

  getTotalAmount(data): number {
    // return this.final_data.treatment.prescription_data.dataArray.reduce((total, item) => {
    //   return total + (item.Decline ? 0 : item.BasePrice * item.medicine_qty);
    // }, 0);
    console.log("-=-=-=-==-=--",data)

    return  data
    .reduce((total, item)=>{
    //   total + item.BasePrice
    // <=21.85?(21.85*item.medicine_qty):(item.BasePrice * item.medicine_qty)
    if(item.BasePrice <= 21.85){
      item.BasePrice = 21.85
    }
    if(item.Decline){
      item.BasePrice = 0
    }
    total += item.BasePrice * item.medicine_qty
    return total
    }, 0);
    
}


// this.editdoctor = data
}