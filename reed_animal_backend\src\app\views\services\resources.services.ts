import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class ResourcesService extends Api {

    //Get All Health tips
    GetTips(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`, { params });
    }

    //Add new health tips
    AddTips(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/health_tip?token=${localStorage.auth_token}`, data);
    }
    //Update health tips
    Updatetips(id: any, data: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/health_tip/${id}?token=${localStorage.auth_token}`, data);
    }
    //Picutre upload
    uploadFile(data: File): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('file', data);
        return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
            reportProgress: true,
            responseType: 'json'
        });
    }

    //Delete Tips by using id
    DeleteTips(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/health_tip/${id}?token=${localStorage.auth_token}`);
    }

    //Add new Video
    AddVideo(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`, data);
    }

    //Get All Video
    GetVideos(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/video?token=${localStorage.auth_token}`, { params });
    }

    //Update video
    UpdateVideo(id: any, data: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/video/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Videos by using id
    DeleteVideo(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/video/${id}?token=${localStorage.auth_token}`);
    }

    //Add new Audio
    AddAudio(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`, data);
    }

    //Get All Audio
    GetAudios(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/audio?token=${localStorage.auth_token}`, { params });
    }

    //Update Audio
    UpdateAudio(id: any, data: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/audio/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Audios by using id
    DeleteAudio(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/audio/${id}?token=${localStorage.auth_token}`);
    }

    //Add new FAQ
    AddFAQ(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`, data);
    }

    //Get All faqs
    GetFAQs(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/FAQ?token=${localStorage.auth_token}`, { params });
    }

    //update FAQs by using id
    UpdateFAQ(id: any, data: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/FAQ/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete FAQs by using id
    DeleteFAQ(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/FAQ/${id}?token=${localStorage.auth_token}`);
    }
}