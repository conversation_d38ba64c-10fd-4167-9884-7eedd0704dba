{"ast": null, "code": "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\n\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function (value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;", "map": null, "metadata": {}, "sourceType": "module"}