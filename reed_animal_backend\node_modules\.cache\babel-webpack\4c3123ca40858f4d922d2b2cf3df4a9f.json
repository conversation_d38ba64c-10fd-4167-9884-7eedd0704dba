{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nexport function bufferTime(bufferTimeSpan) {\n  let length = arguments.length;\n  let scheduler = async;\n\n  if (isScheduler(arguments[arguments.length - 1])) {\n    scheduler = arguments[arguments.length - 1];\n    length--;\n  }\n\n  let bufferCreationInterval = null;\n\n  if (length >= 2) {\n    bufferCreationInterval = arguments[1];\n  }\n\n  let maxBufferSize = Number.POSITIVE_INFINITY;\n\n  if (length >= 3) {\n    maxBufferSize = arguments[2];\n  }\n\n  return function bufferTimeOperatorFunction(source) {\n    return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));\n  };\n}\n\nclass BufferTimeOperator {\n  constructor(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));\n  }\n\n}\n\nclass Context {\n  constructor() {\n    this.buffer = [];\n  }\n\n}\n\nclass BufferTimeSubscriber extends Subscriber {\n  constructor(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    super(destination);\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n    this.contexts = [];\n    const context = this.openContext();\n    this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;\n\n    if (this.timespanOnly) {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    } else {\n      const closeState = {\n        subscriber: this,\n        context\n      };\n      const creationState = {\n        bufferTimeSpan,\n        bufferCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));\n    }\n  }\n\n  _next(value) {\n    const contexts = this.contexts;\n    const len = contexts.length;\n    let filledBufferContext;\n\n    for (let i = 0; i < len; i++) {\n      const context = contexts[i];\n      const buffer = context.buffer;\n      buffer.push(value);\n\n      if (buffer.length == this.maxBufferSize) {\n        filledBufferContext = context;\n      }\n    }\n\n    if (filledBufferContext) {\n      this.onBufferFull(filledBufferContext);\n    }\n  }\n\n  _error(err) {\n    this.contexts.length = 0;\n\n    super._error(err);\n  }\n\n  _complete() {\n    const {\n      contexts,\n      destination\n    } = this;\n\n    while (contexts.length > 0) {\n      const context = contexts.shift();\n      destination.next(context.buffer);\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    this.contexts = null;\n  }\n\n  onBufferFull(context) {\n    this.closeContext(context);\n    const closeAction = context.closeAction;\n    closeAction.unsubscribe();\n    this.remove(closeAction);\n\n    if (!this.closed && this.timespanOnly) {\n      context = this.openContext();\n      const bufferTimeSpan = this.bufferTimeSpan;\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    }\n  }\n\n  openContext() {\n    const context = new Context();\n    this.contexts.push(context);\n    return context;\n  }\n\n  closeContext(context) {\n    this.destination.next(context.buffer);\n    const contexts = this.contexts;\n    const spliceIndex = contexts ? contexts.indexOf(context) : -1;\n\n    if (spliceIndex >= 0) {\n      contexts.splice(contexts.indexOf(context), 1);\n    }\n  }\n\n}\n\nfunction dispatchBufferTimeSpanOnly(state) {\n  const subscriber = state.subscriber;\n  const prevContext = state.context;\n\n  if (prevContext) {\n    subscriber.closeContext(prevContext);\n  }\n\n  if (!subscriber.closed) {\n    state.context = subscriber.openContext();\n    state.context.closeAction = this.schedule(state, state.bufferTimeSpan);\n  }\n}\n\nfunction dispatchBufferCreation(state) {\n  const {\n    bufferCreationInterval,\n    bufferTimeSpan,\n    subscriber,\n    scheduler\n  } = state;\n  const context = subscriber.openContext();\n  const action = this;\n\n  if (!subscriber.closed) {\n    subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, {\n      subscriber,\n      context\n    }));\n    action.schedule(state, bufferCreationInterval);\n  }\n}\n\nfunction dispatchBufferClose(arg) {\n  const {\n    subscriber,\n    context\n  } = arg;\n  subscriber.closeContext(context);\n} //# sourceMappingURL=bufferTime.js.map", "map": null, "metadata": {}, "sourceType": "module"}