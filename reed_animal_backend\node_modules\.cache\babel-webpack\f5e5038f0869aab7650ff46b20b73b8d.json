{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Klingon [tlh]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/amaranthrose\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var numbersNouns = 'pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut'.split('_');\n\n  function translateFuture(output) {\n    var time = output;\n    time = output.indexOf('jaj') !== -1 ? time.slice(0, -3) + 'leS' : output.indexOf('jar') !== -1 ? time.slice(0, -3) + 'waQ' : output.indexOf('DIS') !== -1 ? time.slice(0, -3) + 'nem' : time + ' pIq';\n    return time;\n  }\n\n  function translatePast(output) {\n    var time = output;\n    time = output.indexOf('jaj') !== -1 ? time.slice(0, -3) + 'Hu’' : output.indexOf('jar') !== -1 ? time.slice(0, -3) + 'wen' : output.indexOf('DIS') !== -1 ? time.slice(0, -3) + 'ben' : time + ' ret';\n    return time;\n  }\n\n  function translate(number, withoutSuffix, string, isFuture) {\n    var numberNoun = numberAsNoun(number);\n\n    switch (string) {\n      case 'ss':\n        return numberNoun + ' lup';\n\n      case 'mm':\n        return numberNoun + ' tup';\n\n      case 'hh':\n        return numberNoun + ' rep';\n\n      case 'dd':\n        return numberNoun + ' jaj';\n\n      case 'MM':\n        return numberNoun + ' jar';\n\n      case 'yy':\n        return numberNoun + ' DIS';\n    }\n  }\n\n  function numberAsNoun(number) {\n    var hundred = Math.floor(number % 1000 / 100),\n        ten = Math.floor(number % 100 / 10),\n        one = number % 10,\n        word = '';\n\n    if (hundred > 0) {\n      word += numbersNouns[hundred] + 'vatlh';\n    }\n\n    if (ten > 0) {\n      word += (word !== '' ? ' ' : '') + numbersNouns[ten] + 'maH';\n    }\n\n    if (one > 0) {\n      word += (word !== '' ? ' ' : '') + numbersNouns[one];\n    }\n\n    return word === '' ? 'pagh' : word;\n  }\n\n  var tlh = moment.defineLocale('tlh', {\n    months: 'tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’'.split('_'),\n    monthsShort: 'jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    weekdaysShort: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    weekdaysMin: 'lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[DaHjaj] LT',\n      nextDay: '[wa’leS] LT',\n      nextWeek: 'LLL',\n      lastDay: '[wa’Hu’] LT',\n      lastWeek: 'LLL',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: translateFuture,\n      past: translatePast,\n      s: 'puS lup',\n      ss: translate,\n      m: 'wa’ tup',\n      mm: translate,\n      h: 'wa’ rep',\n      hh: translate,\n      d: 'wa’ jaj',\n      dd: translate,\n      M: 'wa’ jar',\n      MM: translate,\n      y: 'wa’ DIS',\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return tlh;\n});", "map": null, "metadata": {}, "sourceType": "script"}