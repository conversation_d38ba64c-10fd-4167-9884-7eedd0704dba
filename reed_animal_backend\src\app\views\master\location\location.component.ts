import { Component, OnInit,ViewChild } from '@angular/core';
import {ModalDirective} from 'ngx-bootstrap/modal';
import { Location } from '../../../views/models/location.models'
import {  LocationService } from '../../services/location.sevices'
import { TokenStorageService } from '../../services/token-storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';

@Component({
  selector: 'app-location',
  templateUrl: './location.component.html',
  styleUrls: ['./location.component.scss']
})
export class LocationComponent implements OnInit {

  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('AddModal') public AddModal: ModalDirective;
  @ViewChild('removeModal') public removeModal: ModalDirective;

  locations = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  location: Location = {};
  rolefailed=false;
  Add = true;
  Edit = true;
  Delete = true;
  constructor(private locationservice: LocationService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService,private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    this.tokens();
  }

  show() {
    this.AddModal.show();
    this.primaryModal.show();
    this.removeModal.show();
  }
  hide() {
    this.AddModal.hide();
    this.primaryModal.hide();
    this.removeModal.hide();
  }

  //clear modal window
  clear(): void {
    this.location = {};
    this.rolefailed=false;

  }

  //token verified location
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    if (key != null) {
      this.Permission.GetModule(Role.role_id._id)
        .subscribe((res: any) => {
          // console.log(res)
          for (var i = 0; i < res.data.length; i++) {
            if (res.data[i].module_name == "Location") {
              this.Add = res.data[i].add
              this.Edit = res.data[i].edit
              this.Delete = res.data[i].delete
              // console.log(this.Add, this.Edit, this.Delete)
            }
          }
        })
      this.EmployeeService.GetEmployeeDetail(Role._id)
        .subscribe((res) => {
          // console.log(res.data[0].status)
          if (res.data[0].status == false) {
            this.tokenStorage.signOut()
          }
        })
      this.locationLists();
    }
    else {
      this.router.navigate(['/login']);
    }
  }

  getfocus(){
    this.rolefailed=false;
  }
  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //Get All location List
  locationLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.locationservice.GetLocationsList(skip, this.name)
      .subscribe((res: any) => {
        this.locations = res.data;
        this.count = res.count;
        // console.log(this.locations);
        // console.log(this.count);
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.locationLists();
  }

  //Edit or update location 
  Getlocation(id): void {
    // console.log('id-->', id);
    this.locationservice.GetLocationDetail(id)
      .subscribe((res) => {
        this.location = res.data[0];
        // console.log(res.data)
      })
  }

  Editlocation(id): void {
    // console.log('id-->', id, this.location.name)
    const data = {
      name: this.location.name
    }
    this.locationservice.UpdateLocation(id, data)
      .subscribe((res) => {
        // console.log('res-->', res);
        this.location = {};
        this.locationLists();
      })
  }

  //Status ON & OFF
  changed(active, id) {
    const data = {
      status: active
    }
    this.locationservice.UpdateLocation(id, data)
      .subscribe((res: any) => {
        // console.log('res-->', res);
      })
  }

  //Add new location
  Addlocation(): void {
    // console.log('name-->', this.location.name)
    if (this.location.name != undefined && this.location.name != '') {
      this.AddModal.hide();
      const data = {
        name: this.location.name
      }
      this.locationservice.NewLocation(data)
        .subscribe((res) => {
          this.location = {};
          // console.log('new-->', res)
          this.locationLists();
        })
    }this.rolefailed=true;
  }

  //Delete location using id
  Deletelocation(id): void {
    // console.log('id-->', id)
    this.locationservice.DeleteLocation(id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.removeModal.hide();
        this.locationLists();
      })
  }
}
