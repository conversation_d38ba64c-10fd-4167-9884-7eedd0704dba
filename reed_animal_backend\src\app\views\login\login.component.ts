import { Component, Compiler } from '@angular/core';
import { LoginUser } from '../models/login.models';
import { TokenStorageService } from '../services/token-storage.service';
import { Loginservice } from '../services/login.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../services/permission.service';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { AppComponent } from '../../app.component';
import { INavData } from '@coreui/angular';
import { navItems } from '../../_nav';
import { LocationStrategy } from '@angular/common';

@Component({
  selector: 'app-dashboard',
  templateUrl: 'login.component.html'
})
export class LoginComponent {

  login: LoginUser = {
    email: '',
    password: ''
  }
  errormessage = '';
  required = false;
  isLoginFailed = false;
  loginForm: FormGroup;
  isFormReady = false;
  submitted = false;
  resultName: any;
  loader = false
  data: String
  constructor(private userservice: Loginservice, private permissionService: PermissionService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private appComponent: AppComponent, private formBuilder: FormBuilder, private _compiler: Compiler) { }

  ngOnInit(): void {
    const key = this.tokenStorage.getModule();
    // console.log(key)
    if (key != null) {
      // return
      this.router.navigate([key[0].url]);
    } else {
      this.SignForm()
    }
    this.SignForm()
  }

  getfocus(): void {
    this.isLoginFailed = false;
  }

  SignForm() {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
      password: ['', [Validators.required, Validators.minLength(8)]],
    });
  }

  get f() {
    return this.loginForm.controls;
  }

  onSubmit() {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    const inputRequest = {
      email: this.loginForm.value.email,
      password: this.loginForm.value.password,
    }
    // console.log('result-->', inputRequest);
    // this.loader = true;

    this.userservice.AdminLogin(inputRequest).subscribe((result: any) => {
      // console.log(result)
      if (result.code === 200) {
        this._compiler.clearCache();
        // console.log('result-->', result);
        this.resultName = result
        this.tokenStorage.saveToken(result.data.tokens);
        this.tokenStorage.saveUser(result.data);
        //search the role id in the permission collection
        this.permissionService.GetModule(result.data.role_id._id)
          .subscribe((res: any) => {

            let navitem = [];
            let store = [];
            for (var i = 0; i < res.data.length; i++) {
              for (var j = 0; j < navItems.length; j++) {
                if (res.data[i].module_name === navItems[j].name) {
                  navitem.push(navItems[j]);
                  store.push(navItems[j]);
                  if (navItems[j].children) {
                    const Arr = []
                    for (var k = 0; k < navItems[j].children.length; k++) {
                      for (var h = 0; h < res.data.length; h++) {
                        if (res.data[h].module_name === navItems[j].children[k].name) {
                          Arr.push(navItems[j].children[k]);
                          store.push(navItems[j].children[k]);
                        }
                      }
                    }
                    // console.log("AAARR", Arr)
                    navItems[j].children = Arr;
                  }
                }
              }
            }
            const navItem = navitem.reverse();
            this.loginForm.reset();
            const Verify = store.map(element => {
              return element.url
            });
            localStorage.setItem('Verify', JSON.stringify(Verify))
            // console.log(navItem)
            // this.router.navigate(['./dashboard']);
            if (navItem.length != 0) {
              this.tokenStorage.saveModule(navItem);
              // console.log(navItem)
              this.submitted = false;
              this.loginForm.reset();
              this.isFormReady = true;
              // console.log(navItem[0].url)
              // window.history.go(-(history.length - 1));
              this._compiler.clearCache();
              this.router.navigate([navItem[0].url]);//navigate to home page
              // this.location.replaceState('questions/1');
              // window.location.replace("/#"+navItem[0].url);
              // this.loader = false
            }
          })
      } else {
        // this.loader = false
        // console.log('err', result.message);
        this.errormessage = result.message;
        this.isLoginFailed = true;
      }
    });

  }


}