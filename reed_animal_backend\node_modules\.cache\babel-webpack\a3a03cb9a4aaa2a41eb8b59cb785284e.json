{"ast": null, "code": "import Symbol from './_Symbol.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\n/** Built-in value references. */\n\nvar spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\n\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) || !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\nexport default isFlattenable;", "map": null, "metadata": {}, "sourceType": "module"}