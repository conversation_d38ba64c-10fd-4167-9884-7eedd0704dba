{"ast": null, "code": "import { Injectable, EventEmitter, Component, Renderer2, ElementRef, Directive, ViewContainerRef, Input, Output, forwardRef, Host, ChangeDetectorRef, ChangeDetectionStrategy, NgModule, ViewChild } from '@angular/core';\nimport { filter, map, take } from 'rxjs/operators';\nimport { getMonth, getFullYear, isFirstDayOfWeek, getDay, shiftDate, isBefore, endOf, isAfter, startOf, isSame, getFirstDayOfMonth, formatDate, getLocale, isSameMonth, isSameDay, isDisabledDay, isSameYear, setFullDate, isDateValid, isArray, isDate, parseDate, utcAsLocal } from 'ngx-bootstrap/chronos';\nimport { BehaviorSubject } from 'rxjs';\nimport { MiniStore, MiniState } from 'ngx-bootstrap/mini-ngrx';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { isBs3 } from 'ngx-bootstrap/utils';\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * For date range picker there are `BsDaterangepickerConfig` which inherits all properties,\n * except `displayMonths`, for range picker it default to `2`\n */\n\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from 'ngx-bootstrap/positioning';\nimport * as ɵngcc2 from '@angular/common';\nimport * as ɵngcc3 from 'ngx-bootstrap/component-loader';\n\nfunction BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-days-calendar-view\", 9);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r8.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r10.setViewMode($event);\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r11 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r11.dayHoverHandler($event);\n    })(\"onHoverWeek\", function BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r12.weekHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r13 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r13.daySelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r7 = ctx.$implicit;\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 4, ctx_r6.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r7)(\"options\", ɵngcc0.ɵɵpipeBind1(2, 6, ctx_r6.options));\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template, 3, 8, \"bs-days-calendar-view\", 8);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r1.daysCalendar));\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-month-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r16 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r16.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r18 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r18.setViewMode($event);\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r19 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r19.monthHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r20 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r20.monthSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r15 = ctx.$implicit;\n    const ctx_r14 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r14.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r15);\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 2, 5, \"bs-month-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r2.monthsCalendar));\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-years-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r23 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r23.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r25.setViewMode($event);\n    })(\"onHover\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r26 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r26.yearHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r27 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r27.yearSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r22 = ctx.$implicit;\n    const ctx_r21 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r21.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r22);\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 2, 5, \"bs-years-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r3.yearsCalendar));\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 12);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 13);\n    ɵngcc0.ɵɵtext(2, \"Apply\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"button\", 14);\n    ɵngcc0.ɵɵtext(4, \"Cancel\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 15);\n    ɵngcc0.ɵɵelement(1, \"bs-custom-date-view\", 16);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ranges\", ctx_r5._customRangesFish);\n  }\n}\n\nfunction BsDatepickerContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"div\", 2);\n    ɵngcc0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDatepickerContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r29);\n      const ctx_r28 = ɵngcc0.ɵɵnextContext();\n      return ctx_r28.positionServiceEnable();\n    });\n    ɵngcc0.ɵɵelementStart(2, \"div\", 3);\n    ɵngcc0.ɵɵpipe(3, \"async\");\n    ɵngcc0.ɵɵtemplate(4, BsDatepickerContainerComponent_div_0_div_4_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(5, BsDatepickerContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(6, BsDatepickerContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(7, BsDatepickerContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(8, BsDatepickerContainerComponent_div_0_div_8_Template, 2, 1, \"div\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"@datepickerAnimation\", ctx_r0.animationState);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitch\", ɵngcc0.ɵɵpipeBind1(3, 8, ctx_r0.viewMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-days-calendar-view\", 9);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r8.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r10.setViewMode($event);\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r11 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r11.dayHoverHandler($event);\n    })(\"onHoverWeek\", function BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r12.weekHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r13 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r13.daySelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r7 = ctx.$implicit;\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 4, ctx_r6.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r7)(\"options\", ɵngcc0.ɵɵpipeBind1(2, 6, ctx_r6.options));\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template, 3, 8, \"bs-days-calendar-view\", 8);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r1.daysCalendar));\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-month-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r16 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r16.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r18 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r18.setViewMode($event);\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r19 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r19.monthHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r20 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r20.monthSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r15 = ctx.$implicit;\n    const ctx_r14 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r14.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r15);\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 2, 5, \"bs-month-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r2.monthsCalendar));\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-years-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r23 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r23.navigateTo($event);\n    })(\"onViewMode\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r25.setViewMode($event);\n    })(\"onHover\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r26 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r26.yearHoverHandler($event);\n    })(\"onSelect\", function BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r27 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r27.yearSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r22 = ctx.$implicit;\n    const ctx_r21 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r21.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r22);\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDatepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 2, 5, \"bs-years-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r3.yearsCalendar));\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 12);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 13);\n    ɵngcc0.ɵɵtext(2, \"Apply\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"button\", 14);\n    ɵngcc0.ɵɵtext(4, \"Cancel\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 15);\n    ɵngcc0.ɵɵelement(1, \"bs-custom-date-view\", 16);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ranges\", ctx_r5._customRangesFish);\n  }\n}\n\nfunction BsDatepickerInlineContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"div\", 2);\n    ɵngcc0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDatepickerInlineContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r29);\n      const ctx_r28 = ɵngcc0.ɵɵnextContext();\n      return ctx_r28.positionServiceEnable();\n    });\n    ɵngcc0.ɵɵelementStart(2, \"div\", 3);\n    ɵngcc0.ɵɵpipe(3, \"async\");\n    ɵngcc0.ɵɵtemplate(4, BsDatepickerInlineContainerComponent_div_0_div_4_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(5, BsDatepickerInlineContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(6, BsDatepickerInlineContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(7, BsDatepickerInlineContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(8, BsDatepickerInlineContainerComponent_div_0_div_8_Template, 2, 1, \"div\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"@datepickerAnimation\", ctx_r0.animationState);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitch\", ɵngcc0.ɵɵpipeBind1(3, 8, ctx_r0.viewMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-days-calendar-view\", 9);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r8.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r10.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r11 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r11.dayHoverHandler($event);\n    })(\"onHoverWeek\", function BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r12.weekHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r13 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r13.daySelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r7 = ctx.$implicit;\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 4, ctx_r6.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r7)(\"options\", ɵngcc0.ɵɵpipeBind1(2, 6, ctx_r6.options));\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template, 3, 8, \"bs-days-calendar-view\", 8);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r1.daysCalendar));\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-month-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r16 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r16.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r18 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r18.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r19 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r19.monthHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r20 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r20.monthSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r15 = ctx.$implicit;\n    const ctx_r14 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r14.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r15);\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 2, 5, \"bs-month-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r2.monthsCalendar));\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-years-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r23 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r23.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r25.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r26 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r26.yearHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r27 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r27.yearSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r22 = ctx.$implicit;\n    const ctx_r21 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r21.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r22);\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 2, 5, \"bs-years-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r3.yearsCalendar));\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 12);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 13);\n    ɵngcc0.ɵɵtext(2, \"Apply\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"button\", 14);\n    ɵngcc0.ɵɵtext(4, \"Cancel\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 15);\n    ɵngcc0.ɵɵelement(1, \"bs-custom-date-view\", 16);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ranges\", ctx_r5._customRangesFish);\n  }\n}\n\nfunction BsDaterangepickerContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"div\", 2);\n    ɵngcc0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDaterangepickerContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r29);\n      const ctx_r28 = ɵngcc0.ɵɵnextContext();\n      return ctx_r28.positionServiceEnable();\n    });\n    ɵngcc0.ɵɵelementStart(2, \"div\", 3);\n    ɵngcc0.ɵɵpipe(3, \"async\");\n    ɵngcc0.ɵɵtemplate(4, BsDaterangepickerContainerComponent_div_0_div_4_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(5, BsDaterangepickerContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(6, BsDaterangepickerContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(7, BsDaterangepickerContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(8, BsDaterangepickerContainerComponent_div_0_div_8_Template, 2, 1, \"div\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"@datepickerAnimation\", ctx_r0.animationState);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitch\", ɵngcc0.ɵɵpipeBind1(3, 8, ctx_r0.viewMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-days-calendar-view\", 9);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r8.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r10.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r11 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r11.dayHoverHandler($event);\n    })(\"onHoverWeek\", function BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onHoverWeek_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r12.weekHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template_bs_days_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r13 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r13.daySelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r7 = ctx.$implicit;\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 4, ctx_r6.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r7)(\"options\", ɵngcc0.ɵɵpipeBind1(2, 6, ctx_r6.options));\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_4_bs_days_calendar_view_1_Template, 3, 8, \"bs-days-calendar-view\", 8);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r1.daysCalendar));\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-month-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r16 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r16.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r18 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r18.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r19 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r19.monthHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template_bs_month_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r17);\n      const ctx_r20 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r20.monthSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r15 = ctx.$implicit;\n    const ctx_r14 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r14.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r15);\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_5_bs_month_calendar_view_1_Template, 2, 5, \"bs-month-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r2.monthsCalendar));\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"bs-years-calendar-view\", 11);\n    ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onNavigate_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r23 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r23.navigateTo($event);\n    })(\"onViewMode\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onViewMode_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r25.setViewMode($event);\n    })(\"onHover\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onHover_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r26 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r26.yearHoverHandler($event);\n    })(\"onSelect\", function BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template_bs_years_calendar_view_onSelect_0_listener($event) {\n      ɵngcc0.ɵɵrestoreView(_r24);\n      const ctx_r27 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r27.yearSelectHandler($event);\n    });\n    ɵngcc0.ɵɵpipe(1, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const calendar_r22 = ctx.$implicit;\n    const ctx_r21 = ɵngcc0.ɵɵnextContext(3);\n    let tmp_0_0 = null;\n    ɵngcc0.ɵɵclassProp(\"bs-datepicker-multiple\", ((tmp_0_0 = ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r21.daysCalendar)) == null ? null : tmp_0_0.length) > 1);\n    ɵngcc0.ɵɵproperty(\"calendar\", calendar_r22);\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 7);\n    ɵngcc0.ɵɵtemplate(1, BsDaterangepickerInlineContainerComponent_div_0_div_6_bs_years_calendar_view_1_Template, 2, 5, \"bs-years-calendar-view\", 10);\n    ɵngcc0.ɵɵpipe(2, \"async\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx_r3.yearsCalendar));\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 12);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 13);\n    ɵngcc0.ɵɵtext(2, \"Apply\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(3, \"button\", 14);\n    ɵngcc0.ɵɵtext(4, \"Cancel\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 15);\n    ɵngcc0.ɵɵelement(1, \"bs-custom-date-view\", 16);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ranges\", ctx_r5._customRangesFish);\n  }\n}\n\nfunction BsDaterangepickerInlineContainerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"div\", 2);\n    ɵngcc0.ɵɵlistener(\"@datepickerAnimation.done\", function BsDaterangepickerInlineContainerComponent_div_0_Template_div_animation_datepickerAnimation_done_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r29);\n      const ctx_r28 = ɵngcc0.ɵɵnextContext();\n      return ctx_r28.positionServiceEnable();\n    });\n    ɵngcc0.ɵɵelementStart(2, \"div\", 3);\n    ɵngcc0.ɵɵpipe(3, \"async\");\n    ɵngcc0.ɵɵtemplate(4, BsDaterangepickerInlineContainerComponent_div_0_div_4_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(5, BsDaterangepickerInlineContainerComponent_div_0_div_5_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵtemplate(6, BsDaterangepickerInlineContainerComponent_div_0_div_6_Template, 3, 3, \"div\", 4);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(7, BsDaterangepickerInlineContainerComponent_div_0_div_7_Template, 5, 0, \"div\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵtemplate(8, BsDaterangepickerInlineContainerComponent_div_0_div_8_Template, 2, 1, \"div\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"@datepickerAnimation\", ctx_r0.animationState);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitch\", ɵngcc0.ɵɵpipeBind1(3, 8, ctx_r0.viewMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"day\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"month\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"year\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", false);\n  }\n}\n\nfunction BsCalendarLayoutComponent_bs_current_date_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"bs-current-date\", 4);\n  }\n}\n\nfunction BsCalendarLayoutComponent_bs_timepicker_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"bs-timepicker\");\n  }\n}\n\nconst _c0 = [[[\"bs-datepicker-navigation-view\"]], \"*\"];\nconst _c1 = [\"bs-datepicker-navigation-view\", \"*\"];\n\nfunction BsCustomDatesViewComponent_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 3);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const range_r2 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(range_r2.label);\n  }\n}\n\nfunction BsCustomDatesViewComponent_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 3);\n    ɵngcc0.ɵɵtext(1, \"Custom Range\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = [\"bsDatepickerDayDecorator\", \"\"];\n\nfunction BsDatepickerNavigationViewComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 2);\n    ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_button_4_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r2);\n      const ctx_r1 = ɵngcc0.ɵɵnextContext();\n      return ctx_r1.view(\"month\");\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r0.calendar.monthTitle);\n  }\n}\n\nfunction BsDaysCalendarViewComponent_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"th\");\n  }\n}\n\nfunction BsDaysCalendarViewComponent_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"th\", 5);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r4 = ctx.index;\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\"\", ctx_r1.calendar.weekdays[i_r4], \" \");\n  }\n}\n\nfunction BsDaysCalendarViewComponent_tr_8_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 8);\n    ɵngcc0.ɵɵelementStart(1, \"span\", 9);\n    ɵngcc0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_1_Template_span_click_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r11);\n      const week_r5 = ɵngcc0.ɵɵnextContext().$implicit;\n      const ctx_r9 = ɵngcc0.ɵɵnextContext();\n      return ctx_r9.selectWeek(week_r5);\n    })(\"mouseenter\", function BsDaysCalendarViewComponent_tr_8_td_1_Template_span_mouseenter_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r11);\n      const week_r5 = ɵngcc0.ɵɵnextContext().$implicit;\n      const ctx_r12 = ɵngcc0.ɵɵnextContext();\n      return ctx_r12.weekHoverHandler(week_r5, true);\n    })(\"mouseleave\", function BsDaysCalendarViewComponent_tr_8_td_1_Template_span_mouseleave_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r11);\n      const week_r5 = ɵngcc0.ɵɵnextContext().$implicit;\n      const ctx_r14 = ɵngcc0.ɵɵnextContext();\n      return ctx_r14.weekHoverHandler(week_r5, false);\n    });\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r6 = ɵngcc0.ɵɵnextContext().index;\n    const ctx_r7 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassProp(\"active-week\", ctx_r7.isWeekHovered);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r7.calendar.weekNumbers[i_r6]);\n  }\n}\n\nfunction BsDaysCalendarViewComponent_tr_8_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 10);\n    ɵngcc0.ɵɵelementStart(1, \"span\", 11);\n    ɵngcc0.ɵɵlistener(\"click\", function BsDaysCalendarViewComponent_tr_8_td_2_Template_span_click_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r19);\n      const day_r17 = ctx.$implicit;\n      const ctx_r18 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r18.selectDay(day_r17);\n    })(\"mouseenter\", function BsDaysCalendarViewComponent_tr_8_td_2_Template_span_mouseenter_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r19);\n      const day_r17 = ctx.$implicit;\n      const ctx_r20 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r20.hoverDay(day_r17, true);\n    })(\"mouseleave\", function BsDaysCalendarViewComponent_tr_8_td_2_Template_span_mouseleave_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r19);\n      const day_r17 = ctx.$implicit;\n      const ctx_r21 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r21.hoverDay(day_r17, false);\n    });\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const day_r17 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"day\", day_r17);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(day_r17.label);\n  }\n}\n\nfunction BsDaysCalendarViewComponent_tr_8_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, BsDaysCalendarViewComponent_tr_8_td_1_Template, 3, 3, \"td\", 6);\n    ɵngcc0.ɵɵtemplate(2, BsDaysCalendarViewComponent_tr_8_td_2_Template, 3, 2, \"td\", 7);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const week_r5 = ctx.$implicit;\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r2.options.showWeekNumbers);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", week_r5.days);\n  }\n}\n\nfunction BsMonthCalendarViewComponent_tr_4_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 4);\n    ɵngcc0.ɵɵlistener(\"click\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const month_r3 = ctx.$implicit;\n      const ctx_r4 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r4.viewMonth(month_r3);\n    })(\"mouseenter\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_mouseenter_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const month_r3 = ctx.$implicit;\n      const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r6.hoverMonth(month_r3, true);\n    })(\"mouseleave\", function BsMonthCalendarViewComponent_tr_4_td_1_Template_td_mouseleave_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const month_r3 = ctx.$implicit;\n      const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r7.hoverMonth(month_r3, false);\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const month_r3 = ctx.$implicit;\n    ɵngcc0.ɵɵclassProp(\"disabled\", month_r3.isDisabled)(\"is-highlighted\", month_r3.isHovered);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵclassProp(\"selected\", month_r3.isSelected);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(month_r3.label);\n  }\n}\n\nfunction BsMonthCalendarViewComponent_tr_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, BsMonthCalendarViewComponent_tr_4_td_1_Template, 3, 7, \"td\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r1 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", row_r1);\n  }\n}\n\nfunction BsYearsCalendarViewComponent_tr_4_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 4);\n    ɵngcc0.ɵɵlistener(\"click\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const year_r3 = ctx.$implicit;\n      const ctx_r4 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r4.viewYear(year_r3);\n    })(\"mouseenter\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_mouseenter_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const year_r3 = ctx.$implicit;\n      const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r6.hoverYear(year_r3, true);\n    })(\"mouseleave\", function BsYearsCalendarViewComponent_tr_4_td_1_Template_td_mouseleave_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r5);\n      const year_r3 = ctx.$implicit;\n      const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r7.hoverYear(year_r3, false);\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const year_r3 = ctx.$implicit;\n    ɵngcc0.ɵɵclassProp(\"disabled\", year_r3.isDisabled)(\"is-highlighted\", year_r3.isHovered);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵclassProp(\"selected\", year_r3.isSelected);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(year_r3.label);\n  }\n}\n\nfunction BsYearsCalendarViewComponent_tr_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, BsYearsCalendarViewComponent_tr_4_td_1_Template, 3, 7, \"td\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r1 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", row_r1);\n  }\n}\n\nfunction DatePickerInnerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 1);\n    ɵngcc0.ɵɵprojection(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nconst _c3 = [\"*\"];\n\nfunction DayPickerComponent_table_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 8);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_button_4_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r9);\n      const ctx_r8 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r8.datePicker.move(-1);\n    });\n    ɵngcc0.ɵɵtext(1, \"\\u2039\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction DayPickerComponent_table_0_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 8);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_button_5_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r11);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r10.datePicker.move(-1);\n    });\n    ɵngcc0.ɵɵtext(1, \"<\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction DayPickerComponent_table_0_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 9);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_button_11_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r13);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r12.datePicker.move(1);\n    });\n    ɵngcc0.ɵɵtext(1, \"\\u203A\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction DayPickerComponent_table_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 9);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_button_12_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r15);\n      const ctx_r14 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r14.datePicker.move(1);\n    });\n    ɵngcc0.ɵɵtext(1, \"> \");\n    ɵngcc0.ɵɵelementEnd();\n  }\n}\n\nfunction DayPickerComponent_table_0_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"th\");\n  }\n}\n\nfunction DayPickerComponent_table_0_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"th\", 10);\n    ɵngcc0.ɵɵelementStart(1, \"small\", 11);\n    ɵngcc0.ɵɵelementStart(2, \"b\");\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const labelz_r16 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(3);\n    ɵngcc0.ɵɵtextInterpolate(labelz_r16.abbr);\n  }\n}\n\nfunction DayPickerComponent_table_0_ng_template_17_tr_0_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"td\", 10);\n    ɵngcc0.ɵɵelementStart(1, \"em\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const index_r18 = ɵngcc0.ɵɵnextContext(2).index;\n    const ctx_r20 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r20.weekNumbers[index_r18]);\n  }\n}\n\nconst _c4 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"btn-secondary\": a0,\n    \"btn-info\": a1,\n    disabled: a2,\n    active: a3,\n    \"btn-default\": a4\n  };\n};\n\nconst _c5 = function (a0, a1) {\n  return {\n    \"text-muted\": a0,\n    \"text-info\": a1\n  };\n};\n\nfunction DayPickerComponent_table_0_ng_template_17_tr_0_td_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 16);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_ng_template_17_tr_0_td_2_button_1_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r27);\n      const dtz_r23 = ɵngcc0.ɵɵnextContext().$implicit;\n      const ctx_r25 = ɵngcc0.ɵɵnextContext(4);\n      return ctx_r25.datePicker.select(dtz_r23.date);\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 17);\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const dtz_r23 = ɵngcc0.ɵɵnextContext().$implicit;\n    const ctx_r24 = ɵngcc0.ɵɵnextContext(4);\n    ɵngcc0.ɵɵclassMapInterpolate1(\"btn btn-sm \", dtz_r23.customClass, \"\");\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction5(7, _c4, ctx_r24.isBs4 && !dtz_r23.selected && !ctx_r24.datePicker.isActive(dtz_r23), dtz_r23.selected, dtz_r23.disabled, !ctx_r24.isBs4 && ctx_r24.datePicker.isActive(dtz_r23), !ctx_r24.isBs4))(\"disabled\", dtz_r23.disabled);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction2(13, _c5, dtz_r23.secondary || dtz_r23.current, !ctx_r24.isBs4 && dtz_r23.current));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(dtz_r23.label);\n  }\n}\n\nfunction DayPickerComponent_table_0_ng_template_17_tr_0_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"td\", 14);\n    ɵngcc0.ɵɵtemplate(1, DayPickerComponent_table_0_ng_template_17_tr_0_td_2_button_1_Template, 3, 16, \"button\", 15);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const dtz_r23 = ctx.$implicit;\n    const ctx_r21 = ɵngcc0.ɵɵnextContext(4);\n    ɵngcc0.ɵɵproperty(\"id\", dtz_r23.uid);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !(ctx_r21.datePicker.onlyCurrentMonth && dtz_r23.secondary));\n  }\n}\n\nfunction DayPickerComponent_table_0_ng_template_17_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, DayPickerComponent_table_0_ng_template_17_tr_0_td_1_Template, 3, 1, \"td\", 12);\n    ɵngcc0.ɵɵtemplate(2, DayPickerComponent_table_0_ng_template_17_tr_0_td_2_Template, 2, 2, \"td\", 13);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowz_r17 = ɵngcc0.ɵɵnextContext().$implicit;\n    const ctx_r19 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r19.datePicker.showWeeks);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", rowz_r17);\n  }\n}\n\nfunction DayPickerComponent_table_0_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, DayPickerComponent_table_0_ng_template_17_tr_0_Template, 3, 2, \"tr\", 5);\n  }\n\n  if (rf & 2) {\n    const rowz_r17 = ctx.$implicit;\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !(ctx_r7.datePicker.onlyCurrentMonth && rowz_r17[0].secondary && rowz_r17[6].secondary));\n  }\n}\n\nconst _c6 = function (a0) {\n  return {\n    disabled: a0\n  };\n};\n\nfunction DayPickerComponent_table_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"table\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"thead\");\n    ɵngcc0.ɵɵelementStart(2, \"tr\");\n    ɵngcc0.ɵɵelementStart(3, \"th\");\n    ɵngcc0.ɵɵtemplate(4, DayPickerComponent_table_0_button_4_Template, 2, 0, \"button\", 2);\n    ɵngcc0.ɵɵtemplate(5, DayPickerComponent_table_0_button_5_Template, 2, 0, \"button\", 2);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(6, \"th\");\n    ɵngcc0.ɵɵelementStart(7, \"button\", 3);\n    ɵngcc0.ɵɵlistener(\"click\", function DayPickerComponent_table_0_Template_button_click_7_listener() {\n      ɵngcc0.ɵɵrestoreView(_r31);\n      const ctx_r30 = ɵngcc0.ɵɵnextContext();\n      return ctx_r30.datePicker.toggleMode(0);\n    });\n    ɵngcc0.ɵɵelementStart(8, \"strong\");\n    ɵngcc0.ɵɵtext(9);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(10, \"th\");\n    ɵngcc0.ɵɵtemplate(11, DayPickerComponent_table_0_button_11_Template, 2, 0, \"button\", 4);\n    ɵngcc0.ɵɵtemplate(12, DayPickerComponent_table_0_button_12_Template, 2, 0, \"button\", 4);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(13, \"tr\");\n    ɵngcc0.ɵɵtemplate(14, DayPickerComponent_table_0_th_14_Template, 1, 0, \"th\", 5);\n    ɵngcc0.ɵɵtemplate(15, DayPickerComponent_table_0_th_15_Template, 4, 1, \"th\", 6);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(16, \"tbody\");\n    ɵngcc0.ɵɵtemplate(17, DayPickerComponent_table_0_ng_template_17_Template, 1, 1, \"ng-template\", 7);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵattribute(\"aria-labelledby\", ctx_r0.datePicker.uniqueId + \"-title\");\n    ɵngcc0.ɵɵadvance(4);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !ctx_r0.isBs4);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.isBs4);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵattribute(\"colspan\", 5 + (ctx_r0.datePicker.showWeeks ? 1 : 0));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"id\", ctx_r0.datePicker.uniqueId + \"-title\")(\"disabled\", ctx_r0.datePicker.datepickerMode === ctx_r0.datePicker.maxMode)(\"ngClass\", ɵngcc0.ɵɵpureFunction1(13, _c6, ctx_r0.datePicker.datepickerMode === ctx_r0.datePicker.maxMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r0.title);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !ctx_r0.isBs4);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.isBs4);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.datePicker.showWeeks);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ctx_r0.labels);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ctx_r0.rows);\n  }\n}\n\nconst _c7 = function (a0, a1, a2, a3) {\n  return {\n    \"btn-link\": a0,\n    \"btn-info\": a1,\n    disabled: a2,\n    active: a3\n  };\n};\n\nconst _c8 = function (a0, a1) {\n  return {\n    \"text-success\": a0,\n    \"text-info\": a1\n  };\n};\n\nfunction MonthPickerComponent_table_0_tr_14_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 7);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 8);\n    ɵngcc0.ɵɵlistener(\"click\", function MonthPickerComponent_table_0_tr_14_td_1_Template_button_click_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const dtz_r4 = ctx.$implicit;\n      const ctx_r5 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r5.datePicker.select(dtz_r4.date);\n    });\n    ɵngcc0.ɵɵelementStart(2, \"span\", 9);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const dtz_r4 = ctx.$implicit;\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵproperty(\"ngClass\", dtz_r4.customClass);\n    ɵngcc0.ɵɵattribute(\"id\", dtz_r4.uid);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction4(6, _c7, ctx_r3.isBs4 && !dtz_r4.selected && !ctx_r3.datePicker.isActive(dtz_r4), dtz_r4.selected || ctx_r3.isBs4 && !dtz_r4.selected && ctx_r3.datePicker.isActive(dtz_r4), dtz_r4.disabled, !ctx_r3.isBs4 && ctx_r3.datePicker.isActive(dtz_r4)))(\"disabled\", dtz_r4.disabled);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction2(11, _c8, ctx_r3.isBs4 && dtz_r4.current, !ctx_r3.isBs4 && dtz_r4.current));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(dtz_r4.label);\n  }\n}\n\nfunction MonthPickerComponent_table_0_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, MonthPickerComponent_table_0_tr_14_td_1_Template, 4, 14, \"td\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowz_r2 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", rowz_r2);\n  }\n}\n\nfunction MonthPickerComponent_table_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"table\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"thead\");\n    ɵngcc0.ɵɵelementStart(2, \"tr\");\n    ɵngcc0.ɵɵelementStart(3, \"th\");\n    ɵngcc0.ɵɵelementStart(4, \"button\", 2);\n    ɵngcc0.ɵɵlistener(\"click\", function MonthPickerComponent_table_0_Template_button_click_4_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext();\n      return ctx_r7.datePicker.move(-1);\n    });\n    ɵngcc0.ɵɵtext(5, \"\\u2039\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(6, \"th\");\n    ɵngcc0.ɵɵelementStart(7, \"button\", 3);\n    ɵngcc0.ɵɵlistener(\"click\", function MonthPickerComponent_table_0_Template_button_click_7_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r9 = ɵngcc0.ɵɵnextContext();\n      return ctx_r9.datePicker.toggleMode(0);\n    });\n    ɵngcc0.ɵɵelementStart(8, \"strong\");\n    ɵngcc0.ɵɵtext(9);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(10, \"th\");\n    ɵngcc0.ɵɵelementStart(11, \"button\", 4);\n    ɵngcc0.ɵɵlistener(\"click\", function MonthPickerComponent_table_0_Template_button_click_11_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext();\n      return ctx_r10.datePicker.move(1);\n    });\n    ɵngcc0.ɵɵtext(12, \"\\u203A\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(13, \"tbody\");\n    ɵngcc0.ɵɵtemplate(14, MonthPickerComponent_table_0_tr_14_Template, 2, 1, \"tr\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(6);\n    ɵngcc0.ɵɵattribute(\"colspan\", ctx_r0.datePicker.monthColLimit - 2 <= 0 ? 1 : ctx_r0.datePicker.monthColLimit - 2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"id\", ctx_r0.datePicker.uniqueId + \"-title\")(\"disabled\", ctx_r0.datePicker.datepickerMode === ctx_r0.maxMode)(\"ngClass\", ɵngcc0.ɵɵpureFunction1(6, _c6, ctx_r0.datePicker.datepickerMode === ctx_r0.maxMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r0.title);\n    ɵngcc0.ɵɵadvance(5);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ctx_r0.rows);\n  }\n}\n\nfunction YearPickerComponent_table_0_tr_14_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"td\", 7);\n    ɵngcc0.ɵɵelementStart(1, \"button\", 8);\n    ɵngcc0.ɵɵlistener(\"click\", function YearPickerComponent_table_0_tr_14_td_1_Template_button_click_1_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const dtz_r4 = ctx.$implicit;\n      const ctx_r5 = ɵngcc0.ɵɵnextContext(3);\n      return ctx_r5.datePicker.select(dtz_r4.date);\n    });\n    ɵngcc0.ɵɵelementStart(2, \"span\", 9);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const dtz_r4 = ctx.$implicit;\n    const ctx_r3 = ɵngcc0.ɵɵnextContext(3);\n    ɵngcc0.ɵɵattribute(\"id\", dtz_r4.uid);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction4(5, _c7, ctx_r3.isBs4 && !dtz_r4.selected && !ctx_r3.datePicker.isActive(dtz_r4), dtz_r4.selected || ctx_r3.isBs4 && !dtz_r4.selected && ctx_r3.datePicker.isActive(dtz_r4), dtz_r4.disabled, !ctx_r3.isBs4 && ctx_r3.datePicker.isActive(dtz_r4)))(\"disabled\", dtz_r4.disabled);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction2(10, _c8, ctx_r3.isBs4 && dtz_r4.current, !ctx_r3.isBs4 && dtz_r4.current));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(dtz_r4.label);\n  }\n}\n\nfunction YearPickerComponent_table_0_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"tr\");\n    ɵngcc0.ɵɵtemplate(1, YearPickerComponent_table_0_tr_14_td_1_Template, 4, 13, \"td\", 6);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowz_r2 = ctx.$implicit;\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", rowz_r2);\n  }\n}\n\nfunction YearPickerComponent_table_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"table\", 1);\n    ɵngcc0.ɵɵelementStart(1, \"thead\");\n    ɵngcc0.ɵɵelementStart(2, \"tr\");\n    ɵngcc0.ɵɵelementStart(3, \"th\");\n    ɵngcc0.ɵɵelementStart(4, \"button\", 2);\n    ɵngcc0.ɵɵlistener(\"click\", function YearPickerComponent_table_0_Template_button_click_4_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r7 = ɵngcc0.ɵɵnextContext();\n      return ctx_r7.datePicker.move(-1);\n    });\n    ɵngcc0.ɵɵtext(5, \"\\u2039\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(6, \"th\");\n    ɵngcc0.ɵɵelementStart(7, \"button\", 3);\n    ɵngcc0.ɵɵlistener(\"click\", function YearPickerComponent_table_0_Template_button_click_7_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r9 = ɵngcc0.ɵɵnextContext();\n      return ctx_r9.datePicker.toggleMode(0);\n    });\n    ɵngcc0.ɵɵelementStart(8, \"strong\");\n    ɵngcc0.ɵɵtext(9);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(10, \"th\");\n    ɵngcc0.ɵɵelementStart(11, \"button\", 4);\n    ɵngcc0.ɵɵlistener(\"click\", function YearPickerComponent_table_0_Template_button_click_11_listener() {\n      ɵngcc0.ɵɵrestoreView(_r8);\n      const ctx_r10 = ɵngcc0.ɵɵnextContext();\n      return ctx_r10.datePicker.move(1);\n    });\n    ɵngcc0.ɵɵtext(12, \"\\u203A\");\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementStart(13, \"tbody\");\n    ɵngcc0.ɵɵtemplate(14, YearPickerComponent_table_0_tr_14_Template, 2, 1, \"tr\", 5);\n    ɵngcc0.ɵɵelementEnd();\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(6);\n    ɵngcc0.ɵɵattribute(\"colspan\", ctx_r0.datePicker.yearColLimit - 2 <= 0 ? 1 : ctx_r0.datePicker.yearColLimit - 2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"id\", ctx_r0.datePicker.uniqueId + \"-title\")(\"disabled\", ctx_r0.datePicker.datepickerMode === ctx_r0.datePicker.maxMode)(\"ngClass\", ɵngcc0.ɵɵpureFunction1(6, _c6, ctx_r0.datePicker.datepickerMode === ctx_r0.datePicker.maxMode));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r0.title);\n    ɵngcc0.ɵɵadvance(5);\n    ɵngcc0.ɵɵproperty(\"ngForOf\", ctx_r0.rows);\n  }\n}\n\nconst _c9 = \"[_nghost-%COMP%]   .btn-info[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n      color: #fff !important;\\n    }\";\nlet BsDatepickerConfig = /*#__PURE__*/(() => {\n  class BsDatepickerConfig {\n    constructor() {\n      /**\n       * sets use adaptive position\n       */\n      this.adaptivePosition = false;\n      /**\n       * sets use UTC date time format\n       */\n\n      this.useUtc = false;\n      /**\n       * turn on/off animation\n       */\n\n      this.isAnimated = false;\n      /**\n       * CSS class which will be applied to datepicker container,\n       * usually used to set color theme\n       */\n\n      this.containerClass = 'theme-green'; // DatepickerRenderOptions\n\n      this.displayMonths = 1;\n      /**\n       * Allows to hide week numbers in datepicker\n       */\n\n      this.showWeekNumbers = true;\n      this.dateInputFormat = 'L'; // range picker\n\n      this.rangeSeparator = ' - ';\n      /**\n       * Date format for date range input field\n       */\n\n      this.rangeInputFormat = 'L'; // DatepickerFormatOptions\n\n      this.monthTitle = 'MMMM';\n      this.yearTitle = 'YYYY';\n      this.dayLabel = 'D';\n      this.monthLabel = 'MMMM';\n      this.yearLabel = 'YYYY';\n      this.weekNumbers = 'w';\n    }\n\n  }\n\n  BsDatepickerConfig.ɵfac = function BsDatepickerConfig_Factory(t) {\n    return new (t || BsDatepickerConfig)();\n  };\n\n  BsDatepickerConfig.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDatepickerConfig,\n    factory: BsDatepickerConfig.ɵfac\n  });\n  return BsDatepickerConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @abstract\n */\n\n\nclass BsDatepickerAbstractComponent {\n  constructor() {\n    this._customRangesFish = [];\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set minDate(value) {\n    this._effects.setMinDate(value);\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set maxDate(value) {\n    this._effects.setMaxDate(value);\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set daysDisabled(value) {\n    this._effects.setDaysDisabled(value);\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set datesDisabled(value) {\n    this._effects.setDatesDisabled(value);\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set isDisabled(value) {\n    this._effects.setDisabled(value);\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n\n\n  set dateCustomClasses(value) {\n    this._effects.setDateCustomClasses(value);\n  }\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  setViewMode(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  navigateTo(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  dayHoverHandler(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  weekHoverHandler(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  monthHoverHandler(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  yearHoverHandler(event) {}\n  /**\n   * @param {?} day\n   * @return {?}\n   */\n\n\n  daySelectHandler(day) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  monthSelectHandler(event) {}\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  yearSelectHandler(event) {}\n  /* tslint:disable-next-line: no-any */\n\n  /**\n   * @param {?} event\n   * @return {?}\n   */\n\n\n  _stopPropagation(event) {\n    event.stopPropagation();\n  }\n\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerActions = /*#__PURE__*/(() => {\n  class BsDatepickerActions {\n    /**\n     * @return {?}\n     */\n    calculate() {\n      return {\n        type: BsDatepickerActions.CALCULATE\n      };\n    }\n    /**\n     * @return {?}\n     */\n\n\n    format() {\n      return {\n        type: BsDatepickerActions.FORMAT\n      };\n    }\n    /**\n     * @return {?}\n     */\n\n\n    flag() {\n      return {\n        type: BsDatepickerActions.FLAG\n      };\n    }\n    /**\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    select(date) {\n      return {\n        type: BsDatepickerActions.SELECT,\n        payload: date\n      };\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    changeViewMode(event) {\n      return {\n        type: BsDatepickerActions.CHANGE_VIEWMODE,\n        payload: event\n      };\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    navigateTo(event) {\n      return {\n        type: BsDatepickerActions.NAVIGATE_TO,\n        payload: event\n      };\n    }\n    /**\n     * @param {?} step\n     * @return {?}\n     */\n\n\n    navigateStep(step) {\n      return {\n        type: BsDatepickerActions.NAVIGATE_OFFSET,\n        payload: step\n      };\n    }\n    /**\n     * @param {?} options\n     * @return {?}\n     */\n\n\n    setOptions(options) {\n      return {\n        type: BsDatepickerActions.SET_OPTIONS,\n        payload: options\n      };\n    } // date range picker\n\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    selectRange(value) {\n      return {\n        type: BsDatepickerActions.SELECT_RANGE,\n        payload: value\n      };\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    hoverDay(event) {\n      return {\n        type: BsDatepickerActions.HOVER,\n        payload: event.isHovered ? event.cell.date : null\n      };\n    }\n    /**\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    minDate(date) {\n      return {\n        type: BsDatepickerActions.SET_MIN_DATE,\n        payload: date\n      };\n    }\n    /**\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    maxDate(date) {\n      return {\n        type: BsDatepickerActions.SET_MAX_DATE,\n        payload: date\n      };\n    }\n    /**\n     * @param {?} days\n     * @return {?}\n     */\n\n\n    daysDisabled(days) {\n      return {\n        type: BsDatepickerActions.SET_DAYSDISABLED,\n        payload: days\n      };\n    }\n    /**\n     * @param {?} dates\n     * @return {?}\n     */\n\n\n    datesDisabled(dates) {\n      return {\n        type: BsDatepickerActions.SET_DATESDISABLED,\n        payload: dates\n      };\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    isDisabled(value) {\n      return {\n        type: BsDatepickerActions.SET_IS_DISABLED,\n        payload: value\n      };\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setDateCustomClasses(value) {\n      return {\n        type: BsDatepickerActions.SET_DATE_CUSTOM_CLASSES,\n        payload: value\n      };\n    }\n    /**\n     * @param {?} locale\n     * @return {?}\n     */\n\n\n    setLocale(locale) {\n      return {\n        type: BsDatepickerActions.SET_LOCALE,\n        payload: locale\n      };\n    }\n\n  }\n\n  BsDatepickerActions.ɵfac = function BsDatepickerActions_Factory(t) {\n    return new (t || BsDatepickerActions)();\n  };\n\n  BsDatepickerActions.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDatepickerActions,\n    factory: BsDatepickerActions.ɵfac\n  });\n  BsDatepickerActions.CALCULATE = '[datepicker] calculate dates matrix';\n  BsDatepickerActions.FORMAT = '[datepicker] format datepicker values';\n  BsDatepickerActions.FLAG = '[datepicker] set flags';\n  BsDatepickerActions.SELECT = '[datepicker] select date';\n  BsDatepickerActions.NAVIGATE_OFFSET = '[datepicker] shift view date';\n  BsDatepickerActions.NAVIGATE_TO = '[datepicker] change view date';\n  BsDatepickerActions.SET_OPTIONS = '[datepicker] update render options';\n  BsDatepickerActions.HOVER = '[datepicker] hover date';\n  BsDatepickerActions.CHANGE_VIEWMODE = '[datepicker] switch view mode';\n  BsDatepickerActions.SET_MIN_DATE = '[datepicker] set min date';\n  BsDatepickerActions.SET_MAX_DATE = '[datepicker] set max date';\n  BsDatepickerActions.SET_DAYSDISABLED = '[datepicker] set days disabled';\n  BsDatepickerActions.SET_DATESDISABLED = '[datepicker] set dates disabled';\n  BsDatepickerActions.SET_IS_DISABLED = '[datepicker] set is disabled';\n  BsDatepickerActions.SET_DATE_CUSTOM_CLASSES = '[datepicker] set date custom classes';\n  BsDatepickerActions.SET_LOCALE = '[datepicker] set datepicker locale';\n  BsDatepickerActions.SELECT_RANGE = '[daterangepicker] select dates range';\n  return BsDatepickerActions;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsLocaleService = /*#__PURE__*/(() => {\n  class BsLocaleService {\n    constructor() {\n      this._defaultLocale = 'en';\n      this._locale = new BehaviorSubject(this._defaultLocale);\n      this._localeChange = this._locale.asObservable();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get locale() {\n      return this._locale;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get localeChange() {\n      return this._localeChange;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get currentLocale() {\n      return this._locale.getValue();\n    }\n    /**\n     * @param {?} locale\n     * @return {?}\n     */\n\n\n    use(locale) {\n      if (locale === this.currentLocale) {\n        return;\n      }\n\n      this._locale.next(locale);\n    }\n\n  }\n\n  BsLocaleService.ɵfac = function BsLocaleService_Factory(t) {\n    return new (t || BsLocaleService)();\n  };\n\n  BsLocaleService.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsLocaleService,\n    factory: BsLocaleService.ɵfac\n  });\n  return BsLocaleService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerEffects = /*#__PURE__*/(() => {\n  class BsDatepickerEffects {\n    /**\n     * @param {?} _actions\n     * @param {?} _localeService\n     */\n    constructor(_actions, _localeService) {\n      this._actions = _actions;\n      this._localeService = _localeService;\n      this._subs = [];\n    }\n    /**\n     * @param {?} _bsDatepickerStore\n     * @return {?}\n     */\n\n\n    init(_bsDatepickerStore) {\n      this._store = _bsDatepickerStore;\n      return this;\n    }\n    /**\n     * setters\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setValue(value) {\n      this._store.dispatch(this._actions.select(value));\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setRangeValue(value) {\n      this._store.dispatch(this._actions.selectRange(value));\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setMinDate(value) {\n      this._store.dispatch(this._actions.minDate(value));\n\n      return this;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setMaxDate(value) {\n      this._store.dispatch(this._actions.maxDate(value));\n\n      return this;\n    }\n    /**\n     * @template THIS\n     * @this {THIS}\n     * @param {?} value\n     * @return {THIS}\n     */\n\n\n    setDaysDisabled(value) {\n      this._store.dispatch(this._actions.daysDisabled(value));\n\n      return this;\n    }\n    /**\n     * @template THIS\n     * @this {THIS}\n     * @param {?} value\n     * @return {THIS}\n     */\n\n\n    setDatesDisabled(value) {\n      this._store.dispatch(this._actions.datesDisabled(value));\n\n      return this;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setDisabled(value) {\n      this._store.dispatch(this._actions.isDisabled(value));\n\n      return this;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    setDateCustomClasses(value) {\n      this._store.dispatch(this._actions.setDateCustomClasses(value));\n\n      return this;\n    }\n    /* Set rendering options */\n\n    /**\n     * @param {?} _config\n     * @return {?}\n     */\n\n\n    setOptions(_config) {\n      /** @type {?} */\n      const _options = Object.assign({\n        locale: this._localeService.currentLocale\n      }, _config);\n\n      this._store.dispatch(this._actions.setOptions(_options));\n\n      return this;\n    }\n    /**\n     * view to mode bindings\n     * @param {?} container\n     * @return {?}\n     */\n\n\n    setBindings(container) {\n      container.daysCalendar = this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.flaggedMonths).pipe(filter(\n      /**\n      * @param {?} months\n      * @return {?}\n      */\n      months => !!months)); // month calendar\n\n      container.monthsCalendar = this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.flaggedMonthsCalendar).pipe(filter(\n      /**\n      * @param {?} months\n      * @return {?}\n      */\n      months => !!months)); // year calendar\n\n      container.yearsCalendar = this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.yearsCalendarFlagged).pipe(filter(\n      /**\n      * @param {?} years\n      * @return {?}\n      */\n      years => !!years));\n      container.viewMode = this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.view.mode);\n      container.options = this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.showWeekNumbers).pipe(map(\n      /**\n      * @param {?} showWeekNumbers\n      * @return {?}\n      */\n      showWeekNumbers => ({\n        showWeekNumbers\n      })));\n      return this;\n    }\n    /**\n     * event handlers\n     * @param {?} container\n     * @return {?}\n     */\n\n\n    setEventHandlers(container) {\n      container.setViewMode =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        this._store.dispatch(this._actions.changeViewMode(event));\n      };\n\n      container.navigateTo =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        this._store.dispatch(this._actions.navigateStep(event.step));\n      };\n\n      container.dayHoverHandler =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        /** @type {?} */\n        const _cell = event.cell;\n\n        if (_cell.isOtherMonth || _cell.isDisabled) {\n          return;\n        }\n\n        this._store.dispatch(this._actions.hoverDay(event));\n\n        _cell.isHovered = event.isHovered;\n      };\n\n      container.monthHoverHandler =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        event.cell.isHovered = event.isHovered;\n      };\n\n      container.yearHoverHandler =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        event.cell.isHovered = event.isHovered;\n      };\n\n      container.monthSelectHandler =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        if (event.isDisabled) {\n          return;\n        }\n\n        this._store.dispatch(this._actions.navigateTo({\n          unit: {\n            month: getMonth(event.date),\n            year: getFullYear(event.date)\n          },\n          viewMode: 'day'\n        }));\n      };\n\n      container.yearSelectHandler =\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        if (event.isDisabled) {\n          return;\n        }\n\n        this._store.dispatch(this._actions.navigateTo({\n          unit: {\n            year: getFullYear(event.date)\n          },\n          viewMode: 'month'\n        }));\n      };\n\n      return this;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    registerDatepickerSideEffects() {\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.view).subscribe(\n      /**\n      * @param {?} view\n      * @return {?}\n      */\n      view => {\n        this._store.dispatch(this._actions.calculate());\n      })); // format calendar values on month model change\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.monthsModel).pipe(filter(\n      /**\n      * @param {?} monthModel\n      * @return {?}\n      */\n      monthModel => !!monthModel)).subscribe(\n      /**\n      * @param {?} month\n      * @return {?}\n      */\n      month => this._store.dispatch(this._actions.format()))); // flag day values\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.formattedMonths).pipe(filter(\n      /**\n      * @param {?} month\n      * @return {?}\n      */\n      month => !!month)).subscribe(\n      /**\n      * @param {?} month\n      * @return {?}\n      */\n      month => this._store.dispatch(this._actions.flag()))); // flag day values\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.selectedDate).pipe(filter(\n      /**\n      * @param {?} selectedDate\n      * @return {?}\n      */\n      selectedDate => !!selectedDate)).subscribe(\n      /**\n      * @param {?} selectedDate\n      * @return {?}\n      */\n      selectedDate => this._store.dispatch(this._actions.flag()))); // flag for date range picker\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.selectedRange).pipe(filter(\n      /**\n      * @param {?} selectedRange\n      * @return {?}\n      */\n      selectedRange => !!selectedRange)).subscribe(\n      /**\n      * @param {?} selectedRange\n      * @return {?}\n      */\n      selectedRange => this._store.dispatch(this._actions.flag()))); // monthsCalendar\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.monthsCalendar).subscribe(\n      /**\n      * @return {?}\n      */\n      () => this._store.dispatch(this._actions.flag()))); // years calendar\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.yearsCalendarModel).pipe(filter(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => !!state)).subscribe(\n      /**\n      * @return {?}\n      */\n      () => this._store.dispatch(this._actions.flag()))); // on hover\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.hoveredDate).pipe(filter(\n      /**\n      * @param {?} hoveredDate\n      * @return {?}\n      */\n      hoveredDate => !!hoveredDate)).subscribe(\n      /**\n      * @param {?} hoveredDate\n      * @return {?}\n      */\n      hoveredDate => this._store.dispatch(this._actions.flag()))); // date custom classes\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.dateCustomClasses).pipe(filter(\n      /**\n      * @param {?} dateCustomClasses\n      * @return {?}\n      */\n      dateCustomClasses => !!dateCustomClasses)).subscribe(\n      /**\n      * @param {?} dateCustomClasses\n      * @return {?}\n      */\n      dateCustomClasses => this._store.dispatch(this._actions.flag()))); // on locale change\n\n\n      this._subs.push(this._localeService.localeChange.subscribe(\n      /**\n      * @param {?} locale\n      * @return {?}\n      */\n      locale => this._store.dispatch(this._actions.setLocale(locale))));\n\n      return this;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    destroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n    }\n\n  }\n\n  BsDatepickerEffects.ɵfac = function BsDatepickerEffects_Factory(t) {\n    return new (t || BsDatepickerEffects)(ɵngcc0.ɵɵinject(BsDatepickerActions), ɵngcc0.ɵɵinject(BsLocaleService));\n  };\n\n  BsDatepickerEffects.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDatepickerEffects,\n    factory: BsDatepickerEffects.ɵfac\n  });\n  /** @nocollapse */\n\n  return BsDatepickerEffects;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst defaultMonthOptions = {\n  width: 7,\n  height: 6\n};\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\nconst _initialView = {\n  date: new Date(),\n  mode: 'day'\n};\n/** @type {?} */\n\nconst initialDatepickerState = Object.assign(new BsDatepickerConfig(), {\n  locale: 'en',\n  view: _initialView,\n  selectedRange: [],\n  monthViewOptions: defaultMonthOptions\n});\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} date\n * @param {?} options\n * @return {?}\n */\n\nfunction getStartingDayOfCalendar(date, options) {\n  if (isFirstDayOfWeek(date, options.firstDayOfWeek)) {\n    return date;\n  }\n  /** @type {?} */\n\n\n  const weekDay = getDay(date);\n  /** @type {?} */\n\n  const offset = calculateDateOffset(weekDay, options.firstDayOfWeek);\n  return shiftDate(date, {\n    day: -offset\n  });\n}\n/**\n * @param {?} weekday\n * @param {?} startingDayOffset\n * @return {?}\n */\n\n\nfunction calculateDateOffset(weekday, startingDayOffset) {\n  if (startingDayOffset === 0) {\n    return weekday;\n  }\n  /** @type {?} */\n\n\n  const offset = weekday - startingDayOffset % 7;\n  return offset < 0 ? offset + 7 : offset;\n}\n/**\n * @param {?} date\n * @param {?} min\n * @param {?} max\n * @return {?}\n */\n\n\nfunction isMonthDisabled(date, min, max) {\n  /** @type {?} */\n  const minBound = min && isBefore(endOf(date, 'month'), min, 'day');\n  /** @type {?} */\n\n  const maxBound = max && isAfter(startOf(date, 'month'), max, 'day');\n  return minBound || maxBound;\n}\n/**\n * @param {?} date\n * @param {?} min\n * @param {?} max\n * @return {?}\n */\n\n\nfunction isYearDisabled(date, min, max) {\n  /** @type {?} */\n  const minBound = min && isBefore(endOf(date, 'year'), min, 'day');\n  /** @type {?} */\n\n  const maxBound = max && isAfter(startOf(date, 'year'), max, 'day');\n  return minBound || maxBound;\n}\n/**\n * @param {?} date\n * @param {?} datesDisabled\n * @return {?}\n */\n\n\nfunction isDisabledDate(date, datesDisabled) {\n  if (datesDisabled === undefined || !datesDisabled || !datesDisabled.length) {\n    return false;\n  }\n\n  return datesDisabled.some(\n  /**\n  * @param {?} dateDisabled\n  * @return {?}\n  */\n  dateDisabled => isSame(date, dateDisabled, 'date'));\n}\n/**\n * @param {?} state\n * @param {?=} calendarIndex\n * @return {?}\n */\n\n\nfunction getYearsCalendarInitialDate(state, calendarIndex = 0) {\n  /** @type {?} */\n  const model = state && state.yearsCalendarModel && state.yearsCalendarModel[calendarIndex];\n  return model && model.years && model.years[0] && model.years[0][0] && model.years[0][0].date;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @template T\n * @param {?} options\n * @param {?} fn\n * @return {?}\n */\n\n\nfunction createMatrix(options, fn) {\n  /** @type {?} */\n  let prevValue = options.initialDate;\n  /** @type {?} */\n\n  const matrix = new Array(options.height);\n\n  for (let i = 0; i < options.height; i++) {\n    matrix[i] = new Array(options.width);\n\n    for (let j = 0; j < options.width; j++) {\n      matrix[i][j] = fn(prevValue);\n      prevValue = shiftDate(prevValue, options.shift);\n    }\n  }\n\n  return matrix;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} startingDate\n * @param {?} options\n * @return {?}\n */\n\n\nfunction calcDaysCalendar(startingDate, options) {\n  /** @type {?} */\n  const firstDay = getFirstDayOfMonth(startingDate);\n  /** @type {?} */\n\n  const initialDate = getStartingDayOfCalendar(firstDay, options);\n  /** @type {?} */\n\n  const matrixOptions = {\n    width: options.width,\n    height: options.height,\n    initialDate,\n    shift: {\n      day: 1\n    }\n  };\n  /** @type {?} */\n\n  const daysMatrix = createMatrix(matrixOptions,\n  /**\n  * @param {?} date\n  * @return {?}\n  */\n  date => date);\n  return {\n    daysMatrix,\n    month: firstDay\n  };\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} daysCalendar\n * @param {?} formatOptions\n * @param {?} monthIndex\n * @return {?}\n */\n\n\nfunction formatDaysCalendar(daysCalendar, formatOptions, monthIndex) {\n  return {\n    month: daysCalendar.month,\n    monthTitle: formatDate(daysCalendar.month, formatOptions.monthTitle, formatOptions.locale),\n    yearTitle: formatDate(daysCalendar.month, formatOptions.yearTitle, formatOptions.locale),\n    weekNumbers: getWeekNumbers(daysCalendar.daysMatrix, formatOptions.weekNumbers, formatOptions.locale),\n    weekdays: getShiftedWeekdays(formatOptions.locale),\n    weeks: daysCalendar.daysMatrix.map(\n    /**\n    * @param {?} week\n    * @param {?} weekIndex\n    * @return {?}\n    */\n    (week, weekIndex) => ({\n      days: week.map(\n      /**\n      * @param {?} date\n      * @param {?} dayIndex\n      * @return {?}\n      */\n      (date, dayIndex) => ({\n        date,\n        label: formatDate(date, formatOptions.dayLabel, formatOptions.locale),\n        monthIndex,\n        weekIndex,\n        dayIndex\n      }))\n    }))\n  };\n}\n/**\n * @param {?} daysMatrix\n * @param {?} format\n * @param {?} locale\n * @return {?}\n */\n\n\nfunction getWeekNumbers(daysMatrix, format, locale) {\n  return daysMatrix.map(\n  /**\n  * @param {?} days\n  * @return {?}\n  */\n  days => days[0] ? formatDate(days[0], format, locale) : '');\n}\n/**\n * @param {?} locale\n * @return {?}\n */\n\n\nfunction getShiftedWeekdays(locale) {\n  /** @type {?} */\n  const _locale = getLocale(locale);\n  /** @type {?} */\n\n\n  const weekdays = _locale.weekdaysShort();\n  /** @type {?} */\n\n\n  const firstDayOfWeek = _locale.firstDayOfWeek();\n\n  return [...weekdays.slice(firstDayOfWeek), ...weekdays.slice(0, firstDayOfWeek)];\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} formattedMonth\n * @param {?} options\n * @return {?}\n */\n\n\nfunction flagDaysCalendar(formattedMonth, options) {\n  formattedMonth.weeks.forEach(\n  /**\n  * @param {?} week\n  * @return {?}\n  */\n  week => {\n    /* tslint:disable-next-line: cyclomatic-complexity */\n    week.days.forEach(\n    /**\n    * @param {?} day\n    * @param {?} dayIndex\n    * @return {?}\n    */\n    (day, dayIndex) => {\n      // datepicker\n\n      /** @type {?} */\n      const isOtherMonth = !isSameMonth(day.date, formattedMonth.month);\n      /** @type {?} */\n\n      const isHovered = !isOtherMonth && isSameDay(day.date, options.hoveredDate); // date range picker\n\n      /** @type {?} */\n\n      const isSelectionStart = !isOtherMonth && options.selectedRange && isSameDay(day.date, options.selectedRange[0]);\n      /** @type {?} */\n\n      const isSelectionEnd = !isOtherMonth && options.selectedRange && isSameDay(day.date, options.selectedRange[1]);\n      /** @type {?} */\n\n      const isSelected = !isOtherMonth && isSameDay(day.date, options.selectedDate) || isSelectionStart || isSelectionEnd;\n      /** @type {?} */\n\n      const isInRange = !isOtherMonth && options.selectedRange && isDateInRange(day.date, options.selectedRange, options.hoveredDate);\n      /** @type {?} */\n\n      const isDisabled = options.isDisabled || isBefore(day.date, options.minDate, 'day') || isAfter(day.date, options.maxDate, 'day') || isDisabledDay(day.date, options.daysDisabled) || isDisabledDate(day.date, options.datesDisabled);\n      /** @type {?} */\n\n      const currentDate = new Date();\n      /** @type {?} */\n\n      const isToday = !isOtherMonth && isSameDay(day.date, currentDate);\n      /** @type {?} */\n\n      const customClasses = options.dateCustomClasses && options.dateCustomClasses.map(\n      /**\n      * @param {?} dcc\n      * @return {?}\n      */\n      dcc => isSameDay(day.date, dcc.date) ? dcc.classes : []).reduce(\n      /**\n      * @param {?} previousValue\n      * @param {?} currentValue\n      * @return {?}\n      */\n      (previousValue, currentValue) => previousValue.concat(currentValue), []).join(' ') || ''; // decide update or not\n\n      /** @type {?} */\n\n      const newDay = Object.assign({}, day, {\n        isOtherMonth,\n        isHovered,\n        isSelected,\n        isSelectionStart,\n        isSelectionEnd,\n        isInRange,\n        isDisabled,\n        isToday,\n        customClasses\n      });\n\n      if (day.isOtherMonth !== newDay.isOtherMonth || day.isHovered !== newDay.isHovered || day.isSelected !== newDay.isSelected || day.isSelectionStart !== newDay.isSelectionStart || day.isSelectionEnd !== newDay.isSelectionEnd || day.isDisabled !== newDay.isDisabled || day.isInRange !== newDay.isInRange || day.customClasses !== newDay.customClasses) {\n        week.days[dayIndex] = newDay;\n      }\n    });\n  }); // todo: add check for linked calendars\n\n  formattedMonth.hideLeftArrow = options.isDisabled || options.monthIndex > 0 && options.monthIndex !== options.displayMonths;\n  formattedMonth.hideRightArrow = options.isDisabled || options.monthIndex < options.displayMonths && options.monthIndex + 1 !== options.displayMonths;\n  formattedMonth.disableLeftArrow = isMonthDisabled(shiftDate(formattedMonth.month, {\n    month: -1\n  }), options.minDate, options.maxDate);\n  formattedMonth.disableRightArrow = isMonthDisabled(shiftDate(formattedMonth.month, {\n    month: 1\n  }), options.minDate, options.maxDate);\n  return formattedMonth;\n}\n/**\n * @param {?} date\n * @param {?} selectedRange\n * @param {?} hoveredDate\n * @return {?}\n */\n\n\nfunction isDateInRange(date, selectedRange, hoveredDate) {\n  if (!date || !selectedRange[0]) {\n    return false;\n  }\n\n  if (selectedRange[1]) {\n    return date > selectedRange[0] && date <= selectedRange[1];\n  }\n\n  if (hoveredDate) {\n    return date > selectedRange[0] && date <= hoveredDate;\n  }\n\n  return false;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} mode\n * @param {?=} minMode\n * @return {?}\n */\n\n\nfunction canSwitchMode(mode, minMode) {\n  return minMode ? mode >= minMode : true;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst height = 4;\n/** @type {?} */\n\nconst width = 3;\n/** @type {?} */\n\nconst shift = {\n  month: 1\n};\n/**\n * @param {?} viewDate\n * @param {?} formatOptions\n * @return {?}\n */\n\nfunction formatMonthsCalendar(viewDate, formatOptions) {\n  /** @type {?} */\n  const initialDate = startOf(viewDate, 'year');\n  /** @type {?} */\n\n  const matrixOptions = {\n    width,\n    height,\n    initialDate,\n    shift\n  };\n  /** @type {?} */\n\n  const monthMatrix = createMatrix(matrixOptions,\n  /**\n  * @param {?} date\n  * @return {?}\n  */\n  date => ({\n    date,\n    label: formatDate(date, formatOptions.monthLabel, formatOptions.locale)\n  }));\n  return {\n    months: monthMatrix,\n    monthTitle: '',\n    yearTitle: formatDate(viewDate, formatOptions.yearTitle, formatOptions.locale)\n  };\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} monthCalendar\n * @param {?} options\n * @return {?}\n */\n\n\nfunction flagMonthsCalendar(monthCalendar, options) {\n  monthCalendar.months.forEach(\n  /**\n  * @param {?} months\n  * @param {?} rowIndex\n  * @return {?}\n  */\n  (months, rowIndex) => {\n    months.forEach(\n    /**\n    * @param {?} month\n    * @param {?} monthIndex\n    * @return {?}\n    */\n    (month, monthIndex) => {\n      /** @type {?} */\n      const isHovered = isSameMonth(month.date, options.hoveredMonth);\n      /** @type {?} */\n\n      const isDisabled = options.isDisabled || isMonthDisabled(month.date, options.minDate, options.maxDate);\n      /** @type {?} */\n\n      const isSelected = isSameMonth(month.date, options.selectedDate);\n      /** @type {?} */\n\n      const newMonth = Object.assign(\n      /*{},*/\n      month, {\n        isHovered,\n        isDisabled,\n        isSelected\n      });\n\n      if (month.isHovered !== newMonth.isHovered || month.isDisabled !== newMonth.isDisabled || month.isSelected !== newMonth.isSelected) {\n        monthCalendar.months[rowIndex][monthIndex] = newMonth;\n      }\n    });\n  }); // todo: add check for linked calendars\n\n  monthCalendar.hideLeftArrow = options.monthIndex > 0 && options.monthIndex !== options.displayMonths;\n  monthCalendar.hideRightArrow = options.monthIndex < options.displayMonths && options.monthIndex + 1 !== options.displayMonths;\n  monthCalendar.disableLeftArrow = isYearDisabled(shiftDate(monthCalendar.months[0][0].date, {\n    year: -1\n  }), options.minDate, options.maxDate);\n  monthCalendar.disableRightArrow = isYearDisabled(shiftDate(monthCalendar.months[0][0].date, {\n    year: 1\n  }), options.minDate, options.maxDate);\n  return monthCalendar;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst height$1 = 4;\n/** @type {?} */\n\nconst width$1 = 4;\n/** @type {?} */\n\nconst yearsPerCalendar = height$1 * width$1;\n/** @type {?} */\n\nconst initialYearShift = (Math.floor(yearsPerCalendar / 2) - 1) * -1;\n/** @type {?} */\n\nconst shift$1 = {\n  year: 1\n};\n/**\n * @param {?} viewDate\n * @param {?} formatOptions\n * @param {?=} previousInitialDate\n * @return {?}\n */\n\nfunction formatYearsCalendar(viewDate, formatOptions, previousInitialDate) {\n  /** @type {?} */\n  const initialDate = calculateInitialDate(viewDate, previousInitialDate);\n  /** @type {?} */\n\n  const matrixOptions = {\n    width: width$1,\n    height: height$1,\n    initialDate,\n    shift: shift$1\n  };\n  /** @type {?} */\n\n  const yearsMatrix = createMatrix(matrixOptions,\n  /**\n  * @param {?} date\n  * @return {?}\n  */\n  date => ({\n    date,\n    label: formatDate(date, formatOptions.yearLabel, formatOptions.locale)\n  }));\n  /** @type {?} */\n\n  const yearTitle = formatYearRangeTitle(yearsMatrix, formatOptions);\n  return {\n    years: yearsMatrix,\n    monthTitle: '',\n    yearTitle\n  };\n}\n/**\n * @param {?} viewDate\n * @param {?=} previousInitialDate\n * @return {?}\n */\n\n\nfunction calculateInitialDate(viewDate, previousInitialDate) {\n  if (previousInitialDate && viewDate.getFullYear() >= previousInitialDate.getFullYear() && viewDate.getFullYear() < previousInitialDate.getFullYear() + yearsPerCalendar) {\n    return previousInitialDate;\n  }\n\n  return shiftDate(viewDate, {\n    year: initialYearShift\n  });\n}\n/**\n * @param {?} yearsMatrix\n * @param {?} formatOptions\n * @return {?}\n */\n\n\nfunction formatYearRangeTitle(yearsMatrix, formatOptions) {\n  /** @type {?} */\n  const from = formatDate(yearsMatrix[0][0].date, formatOptions.yearTitle, formatOptions.locale);\n  /** @type {?} */\n\n  const to = formatDate(yearsMatrix[height$1 - 1][width$1 - 1].date, formatOptions.yearTitle, formatOptions.locale);\n  return `${from} - ${to}`;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @param {?} yearsCalendar\n * @param {?} options\n * @return {?}\n */\n\n\nfunction flagYearsCalendar(yearsCalendar, options) {\n  yearsCalendar.years.forEach(\n  /**\n  * @param {?} years\n  * @param {?} rowIndex\n  * @return {?}\n  */\n  (years, rowIndex) => {\n    years.forEach(\n    /**\n    * @param {?} year\n    * @param {?} yearIndex\n    * @return {?}\n    */\n    (year, yearIndex) => {\n      /** @type {?} */\n      const isHovered = isSameYear(year.date, options.hoveredYear);\n      /** @type {?} */\n\n      const isDisabled = options.isDisabled || isYearDisabled(year.date, options.minDate, options.maxDate);\n      /** @type {?} */\n\n      const isSelected = isSameYear(year.date, options.selectedDate);\n      /** @type {?} */\n\n      const newMonth = Object.assign(\n      /*{},*/\n      year, {\n        isHovered,\n        isDisabled,\n        isSelected\n      });\n\n      if (year.isHovered !== newMonth.isHovered || year.isDisabled !== newMonth.isDisabled || year.isSelected !== newMonth.isSelected) {\n        yearsCalendar.years[rowIndex][yearIndex] = newMonth;\n      }\n    });\n  }); // todo: add check for linked calendars\n\n  yearsCalendar.hideLeftArrow = options.yearIndex > 0 && options.yearIndex !== options.displayMonths;\n  yearsCalendar.hideRightArrow = options.yearIndex < options.displayMonths && options.yearIndex + 1 !== options.displayMonths;\n  yearsCalendar.disableLeftArrow = isYearDisabled(shiftDate(yearsCalendar.years[0][0].date, {\n    year: -1\n  }), options.minDate, options.maxDate);\n  /** @type {?} */\n\n  const i = yearsCalendar.years.length - 1;\n  /** @type {?} */\n\n  const j = yearsCalendar.years[i].length - 1;\n  yearsCalendar.disableRightArrow = isYearDisabled(shiftDate(yearsCalendar.years[i][j].date, {\n    year: 1\n  }), options.minDate, options.maxDate);\n  return yearsCalendar;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/* tslint:disable-next-line: cyclomatic-complexity */\n\n/**\n * @param {?=} state\n * @param {?=} action\n * @return {?}\n */\n\n\nfunction bsDatepickerReducer(state = initialDatepickerState, action) {\n  switch (action.type) {\n    case BsDatepickerActions.CALCULATE:\n      {\n        return calculateReducer(state);\n      }\n\n    case BsDatepickerActions.FORMAT:\n      {\n        return formatReducer(state);\n      }\n\n    case BsDatepickerActions.FLAG:\n      {\n        return flagReducer(state);\n      }\n\n    case BsDatepickerActions.NAVIGATE_OFFSET:\n      {\n        return navigateOffsetReducer(state, action);\n      }\n\n    case BsDatepickerActions.NAVIGATE_TO:\n      {\n        /** @type {?} */\n        const payload = action.payload;\n        /** @type {?} */\n\n        const date = setFullDate(state.view.date, payload.unit);\n        /** @type {?} */\n\n        let newState;\n        /** @type {?} */\n\n        let mode;\n\n        if (canSwitchMode(payload.viewMode, state.minMode)) {\n          mode = payload.viewMode;\n          newState = {\n            view: {\n              date,\n              mode\n            }\n          };\n        } else {\n          mode = state.view.mode;\n          newState = {\n            selectedDate: date,\n            view: {\n              date,\n              mode\n            }\n          };\n        }\n\n        return Object.assign({}, state, newState);\n      }\n\n    case BsDatepickerActions.CHANGE_VIEWMODE:\n      {\n        if (!canSwitchMode(action.payload, state.minMode)) {\n          return state;\n        }\n        /** @type {?} */\n\n\n        const date = state.view.date;\n        /** @type {?} */\n\n        const mode = action.payload;\n        /** @type {?} */\n\n        const newState = {\n          view: {\n            date,\n            mode\n          }\n        };\n        return Object.assign({}, state, newState);\n      }\n\n    case BsDatepickerActions.HOVER:\n      {\n        return Object.assign({}, state, {\n          hoveredDate: action.payload\n        });\n      }\n\n    case BsDatepickerActions.SELECT:\n      {\n        /** @type {?} */\n        const newState = {\n          selectedDate: action.payload,\n          view: state.view\n        };\n        /** @type {?} */\n\n        const mode = state.view.mode;\n        /** @type {?} */\n\n        const _date = action.payload || state.view.date;\n        /** @type {?} */\n\n\n        const date = getViewDate(_date, state.minDate, state.maxDate);\n        newState.view = {\n          mode,\n          date\n        };\n        return Object.assign({}, state, newState);\n      }\n\n    case BsDatepickerActions.SET_OPTIONS:\n      {\n        /** @type {?} */\n        const newState = action.payload; // preserve view mode\n\n        /** @type {?} */\n\n        const mode = newState.minMode ? newState.minMode : state.view.mode;\n        /** @type {?} */\n\n        const _viewDate = isDateValid(newState.value) && newState.value || isArray(newState.value) && isDateValid(newState.value[0]) && newState.value[0] || state.view.date;\n        /** @type {?} */\n\n\n        const date = getViewDate(_viewDate, newState.minDate, newState.maxDate);\n        newState.view = {\n          mode,\n          date\n        }; // update selected value\n\n        if (newState.value) {\n          // if new value is array we work with date range\n          if (isArray(newState.value)) {\n            newState.selectedRange = newState.value;\n          } // if new value is a date -> datepicker\n\n\n          if (newState.value instanceof Date) {\n            newState.selectedDate = newState.value;\n          } // provided value is not supported :)\n          // need to report it somehow\n\n        }\n\n        return Object.assign({}, state, newState);\n      }\n    // date range picker\n\n    case BsDatepickerActions.SELECT_RANGE:\n      {\n        /** @type {?} */\n        const newState = {\n          selectedRange: action.payload,\n          view: state.view\n        };\n        /** @type {?} */\n\n        const mode = state.view.mode;\n        /** @type {?} */\n\n        const _date = action.payload && action.payload[0] || state.view.date;\n        /** @type {?} */\n\n\n        const date = getViewDate(_date, state.minDate, state.maxDate);\n        newState.view = {\n          mode,\n          date\n        };\n        return Object.assign({}, state, newState);\n      }\n\n    case BsDatepickerActions.SET_MIN_DATE:\n      {\n        return Object.assign({}, state, {\n          minDate: action.payload\n        });\n      }\n\n    case BsDatepickerActions.SET_MAX_DATE:\n      {\n        return Object.assign({}, state, {\n          maxDate: action.payload\n        });\n      }\n\n    case BsDatepickerActions.SET_IS_DISABLED:\n      {\n        return Object.assign({}, state, {\n          isDisabled: action.payload\n        });\n      }\n\n    case BsDatepickerActions.SET_DATE_CUSTOM_CLASSES:\n      {\n        return Object.assign({}, state, {\n          dateCustomClasses: action.payload\n        });\n      }\n\n    default:\n      return state;\n  }\n}\n/**\n * @param {?} state\n * @return {?}\n */\n\n\nfunction calculateReducer(state) {\n  // how many calendars\n\n  /** @type {?} */\n  const displayMonths = state.displayMonths; // use selected date on initial rendering if set\n\n  /** @type {?} */\n\n  let viewDate = state.view.date;\n\n  if (state.view.mode === 'day') {\n    if (state.showPreviousMonth && state.selectedRange.length === 0) {\n      viewDate = shiftDate(viewDate, {\n        month: -1\n      });\n    }\n\n    state.monthViewOptions.firstDayOfWeek = getLocale(state.locale).firstDayOfWeek();\n    /** @type {?} */\n\n    const monthsModel = new Array(displayMonths);\n\n    for (let monthIndex = 0; monthIndex < displayMonths; monthIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsModel[monthIndex] = calcDaysCalendar(viewDate, state.monthViewOptions);\n      viewDate = shiftDate(viewDate, {\n        month: 1\n      });\n    }\n\n    return Object.assign({}, state, {\n      monthsModel\n    });\n  }\n\n  if (state.view.mode === 'month') {\n    /** @type {?} */\n    const monthsCalendar = new Array(displayMonths);\n\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsCalendar[calendarIndex] = formatMonthsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 1\n      });\n    }\n\n    return Object.assign({}, state, {\n      monthsCalendar\n    });\n  }\n\n  if (state.view.mode === 'year') {\n    /** @type {?} */\n    const yearsCalendarModel = new Array(displayMonths);\n\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      yearsCalendarModel[calendarIndex] = formatYearsCalendar(viewDate, getFormatOptions(state), state.minMode === 'year' ? getYearsCalendarInitialDate(state, calendarIndex) : undefined);\n      viewDate = shiftDate(viewDate, {\n        year: yearsPerCalendar\n      });\n    }\n\n    return Object.assign({}, state, {\n      yearsCalendarModel\n    });\n  }\n\n  return state;\n}\n/**\n * @param {?} state\n * @param {?} action\n * @return {?}\n */\n\n\nfunction formatReducer(state, action) {\n  if (state.view.mode === 'day') {\n    /** @type {?} */\n    const formattedMonths = state.monthsModel.map(\n    /**\n    * @param {?} month\n    * @param {?} monthIndex\n    * @return {?}\n    */\n    (month, monthIndex) => formatDaysCalendar(month, getFormatOptions(state), monthIndex));\n    return Object.assign({}, state, {\n      formattedMonths\n    });\n  } // how many calendars\n\n  /** @type {?} */\n\n\n  const displayMonths = state.displayMonths; // check initial rendering\n  // use selected date on initial rendering if set\n\n  /** @type {?} */\n\n  let viewDate = state.view.date;\n\n  if (state.view.mode === 'month') {\n    /** @type {?} */\n    const monthsCalendar = new Array(displayMonths);\n\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      monthsCalendar[calendarIndex] = formatMonthsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 1\n      });\n    }\n\n    return Object.assign({}, state, {\n      monthsCalendar\n    });\n  }\n\n  if (state.view.mode === 'year') {\n    /** @type {?} */\n    const yearsCalendarModel = new Array(displayMonths);\n\n    for (let calendarIndex = 0; calendarIndex < displayMonths; calendarIndex++) {\n      // todo: for unlinked calendars it will be harder\n      yearsCalendarModel[calendarIndex] = formatYearsCalendar(viewDate, getFormatOptions(state));\n      viewDate = shiftDate(viewDate, {\n        year: 16\n      });\n    }\n\n    return Object.assign({}, state, {\n      yearsCalendarModel\n    });\n  }\n\n  return state;\n}\n/**\n * @param {?} state\n * @param {?} action\n * @return {?}\n */\n\n\nfunction flagReducer(state, action) {\n  if (state.view.mode === 'day') {\n    /** @type {?} */\n    const flaggedMonths = state.formattedMonths.map(\n    /**\n    * @param {?} formattedMonth\n    * @param {?} monthIndex\n    * @return {?}\n    */\n    (formattedMonth, monthIndex) => flagDaysCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      daysDisabled: state.daysDisabled,\n      datesDisabled: state.datesDisabled,\n      hoveredDate: state.hoveredDate,\n      selectedDate: state.selectedDate,\n      selectedRange: state.selectedRange,\n      displayMonths: state.displayMonths,\n      dateCustomClasses: state.dateCustomClasses,\n      monthIndex\n    }));\n    return Object.assign({}, state, {\n      flaggedMonths\n    });\n  }\n\n  if (state.view.mode === 'month') {\n    /** @type {?} */\n    const flaggedMonthsCalendar = state.monthsCalendar.map(\n    /**\n    * @param {?} formattedMonth\n    * @param {?} monthIndex\n    * @return {?}\n    */\n    (formattedMonth, monthIndex) => flagMonthsCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      hoveredMonth: state.hoveredMonth,\n      selectedDate: state.selectedDate,\n      displayMonths: state.displayMonths,\n      monthIndex\n    }));\n    return Object.assign({}, state, {\n      flaggedMonthsCalendar\n    });\n  }\n\n  if (state.view.mode === 'year') {\n    /** @type {?} */\n    const yearsCalendarFlagged = state.yearsCalendarModel.map(\n    /**\n    * @param {?} formattedMonth\n    * @param {?} yearIndex\n    * @return {?}\n    */\n    (formattedMonth, yearIndex) => flagYearsCalendar(formattedMonth, {\n      isDisabled: state.isDisabled,\n      minDate: state.minDate,\n      maxDate: state.maxDate,\n      hoveredYear: state.hoveredYear,\n      selectedDate: state.selectedDate,\n      displayMonths: state.displayMonths,\n      yearIndex\n    }));\n    return Object.assign({}, state, {\n      yearsCalendarFlagged\n    });\n  }\n\n  return state;\n}\n/**\n * @param {?} state\n * @param {?} action\n * @return {?}\n */\n\n\nfunction navigateOffsetReducer(state, action) {\n  /** @type {?} */\n  const newState = {\n    view: {\n      mode: state.view.mode,\n      date: shiftViewDate(state, action)\n    }\n  };\n  return Object.assign({}, state, newState);\n}\n/**\n * @param {?} state\n * @param {?} action\n * @return {?}\n */\n\n\nfunction shiftViewDate(state, action) {\n  if (state.view.mode === 'year' && state.minMode === 'year') {\n    /** @type {?} */\n    const initialDate = getYearsCalendarInitialDate(state, 0);\n    /** @type {?} */\n\n    const middleDate = shiftDate(initialDate, {\n      year: -initialYearShift\n    });\n    return shiftDate(middleDate, action.payload);\n  }\n\n  return shiftDate(startOf(state.view.date, 'month'), action.payload);\n}\n/**\n * @param {?} state\n * @return {?}\n */\n\n\nfunction getFormatOptions(state) {\n  return {\n    locale: state.locale,\n    monthTitle: state.monthTitle,\n    yearTitle: state.yearTitle,\n    dayLabel: state.dayLabel,\n    monthLabel: state.monthLabel,\n    yearLabel: state.yearLabel,\n    weekNumbers: state.weekNumbers\n  };\n}\n/**\n * if view date is provided (bsValue|ngModel) it should be shown\n * if view date is not provider:\n * if minDate>currentDate (default view value), show minDate\n * if maxDate<currentDate(default view value) show maxDate\n * @param {?} viewDate\n * @param {?} minDate\n * @param {?} maxDate\n * @return {?}\n */\n\n\nfunction getViewDate(viewDate, minDate, maxDate) {\n  /** @type {?} */\n  const _date = Array.isArray(viewDate) ? viewDate[0] : viewDate;\n\n  if (minDate && isAfter(minDate, _date, 'day')) {\n    return minDate;\n  }\n\n  if (maxDate && isBefore(maxDate, _date, 'day')) {\n    return maxDate;\n  }\n\n  return _date;\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerStore = /*#__PURE__*/(() => {\n  class BsDatepickerStore extends MiniStore {\n    constructor() {\n      /** @type {?} */\n      const _dispatcher = new BehaviorSubject({\n        type: '[datepicker] dispatcher init'\n      });\n      /** @type {?} */\n\n\n      const state = new MiniState(initialDatepickerState, _dispatcher, bsDatepickerReducer);\n      super(_dispatcher, bsDatepickerReducer, state);\n    }\n\n  }\n\n  BsDatepickerStore.ɵfac = function BsDatepickerStore_Factory(t) {\n    return new (t || BsDatepickerStore)();\n  };\n\n  BsDatepickerStore.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDatepickerStore,\n    factory: BsDatepickerStore.ɵfac\n  });\n  /** @nocollapse */\n\n  return BsDatepickerStore;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst DATEPICKER_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\n/** @type {?} */\n\nconst datepickerAnimation = trigger('datepickerAnimation', [state('animated-down', style({\n  height: '*',\n  overflow: 'hidden'\n})), transition('* => animated-down', [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DATEPICKER_ANIMATION_TIMING)]), state('animated-up', style({\n  height: '*',\n  overflow: 'hidden'\n})), transition('* => animated-up', [style({\n  height: '*',\n  overflow: 'hidden'\n}), animate(DATEPICKER_ANIMATION_TIMING)]), transition('* => unanimated', animate('0s'))]);\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nlet BsDatepickerContainerComponent = /*#__PURE__*/(() => {\n  class BsDatepickerContainerComponent extends BsDatepickerAbstractComponent {\n    /**\n     * @param {?} _renderer\n     * @param {?} _config\n     * @param {?} _store\n     * @param {?} _element\n     * @param {?} _actions\n     * @param {?} _effects\n     * @param {?} _positionService\n     */\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positionService) {\n      super();\n      this._config = _config;\n      this._store = _store;\n      this._element = _element;\n      this._actions = _actions;\n      this._positionService = _positionService;\n      this.valueChange = new EventEmitter();\n      this.animationState = 'void';\n      this._subs = [];\n      this._effects = _effects;\n\n      _renderer.setStyle(_element.nativeElement, 'display', 'block');\n\n      _renderer.setStyle(_element.nativeElement, 'position', 'absolute');\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set value(value) {\n      this._effects.setValue(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this._config.adaptivePosition\n          }\n        },\n        allowedPositions: ['top', 'bottom']\n      });\n\n      this._positionService.event$.pipe(take(1)).subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        this._positionService.disable();\n\n        if (this._config.isAnimated) {\n          this.animationState = this.isTopPosition ? 'animated-up' : 'animated-down';\n          return;\n        }\n\n        this.animationState = 'unanimated';\n      });\n\n      this.isOtherMonthsActive = this._config.selectFromOtherMonth;\n      this.containerClass = this._config.containerClass;\n\n      this._effects.init(this._store) // intial state options\n      .setOptions(this._config) // data binding view --> model\n      .setBindings(this) // set event handlers\n      .setEventHandlers(this).registerDatepickerSideEffects(); // todo: move it somewhere else\n      // on selected date change\n\n\n      this._subs.push(this._store\n      /* tslint:disable-next-line: no-any */\n      .select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.selectedDate)\n      /* tslint:disable-next-line: no-any */\n      .subscribe(\n      /**\n      * @param {?} date\n      * @return {?}\n      */\n      date => this.valueChange.emit(date)));\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get isTopPosition() {\n      return this._element.nativeElement.classList.contains('top');\n    }\n    /**\n     * @return {?}\n     */\n\n\n    positionServiceEnable() {\n      this._positionService.enable();\n    }\n    /**\n     * @param {?} day\n     * @return {?}\n     */\n\n\n    daySelectHandler(day) {\n      /** @type {?} */\n      const isDisabled = this.isOtherMonthsActive ? day.isDisabled : day.isOtherMonth || day.isDisabled;\n\n      if (isDisabled) {\n        return;\n      }\n\n      this._store.dispatch(this._actions.select(day.date));\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n\n      this._effects.destroy();\n    }\n\n  }\n\n  BsDatepickerContainerComponent.ɵfac = function BsDatepickerContainerComponent_Factory(t) {\n    return new (t || BsDatepickerContainerComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(BsDatepickerStore), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(BsDatepickerActions), ɵngcc0.ɵɵdirectiveInject(BsDatepickerEffects), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.PositioningService));\n  };\n\n  BsDatepickerContainerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDatepickerContainerComponent,\n    selectors: [[\"bs-datepicker-container\"]],\n    hostAttrs: [\"role\", \"dialog\", \"aria-label\", \"calendar\", 1, \"bottom\"],\n    hostBindings: function BsDatepickerContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerContainerComponent_click_HostBindingHandler($event) {\n          return ctx._stopPropagation($event);\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), ɵngcc0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [1, \"bs-datepicker-custom-range\"], [3, \"ranges\"]],\n    template: function BsDatepickerContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, BsDatepickerContainerComponent_div_0_Template, 9, 10, \"div\", 0);\n        ɵngcc0.ɵɵpipe(1, \"async\");\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgForOf, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, BsCustomDatesViewComponent];\n    },\n    pipes: function () {\n      return [ɵngcc2.AsyncPipe];\n    },\n    encapsulation: 2,\n    data: {\n      animation: [datepickerAnimation]\n    }\n  });\n  /** @nocollapse */\n\n  return BsDatepickerContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerDirective = /*#__PURE__*/(() => {\n  class BsDatepickerDirective {\n    /**\n     * @param {?} _config\n     * @param {?} _elementRef\n     * @param {?} _renderer\n     * @param {?} _viewContainerRef\n     * @param {?} cis\n     */\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      /**\n       * Placement of a datepicker. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n\n      this.placement = 'bottom';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n\n      this.triggers = 'click';\n      /**\n       * Close datepicker on outside click\n       */\n\n      this.outsideClick = true;\n      /**\n       * A selector specifying the element the datepicker should be appended to.\n       */\n\n      this.container = 'body';\n      this.outsideEsc = true;\n      /**\n       * Emits when datepicker value has been changed\n       */\n\n      this.bsValueChange = new EventEmitter();\n      this._subs = []; // todo: assign only subset of fields\n\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n      this.onShown = this._datepicker.onShown;\n      this.onHidden = this._datepicker.onHidden;\n    }\n    /**\n     * Returns whether or not the datepicker is currently being shown\n     * @return {?}\n     */\n\n\n    get isOpen() {\n      return this._datepicker.isShown;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set isOpen(value) {\n      if (value) {\n        this.show();\n      } else {\n        this.hide();\n      }\n    }\n    /**\n     * Initial value of datepicker\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set bsValue(value) {\n      if (this._bsValue && value && this._bsValue.getTime() === value.getTime()) {\n        return;\n      }\n\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this._datepicker.listen({\n        outsideClick: this.outsideClick,\n        outsideEsc: this.outsideEsc,\n        triggers: this.triggers,\n        show:\n        /**\n        * @return {?}\n        */\n        () => this.show()\n      });\n\n      this.setConfig();\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n\n\n    ngOnChanges(changes) {\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n\n      if (changes.minDate) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n\n      if (changes.maxDate) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n\n      if (changes.daysDisabled) {\n        this._datepickerRef.instance.daysDisabled = this.daysDisabled;\n      }\n\n      if (changes.datesDisabled) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n\n      if (changes.isDisabled) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n\n      if (changes.dateCustomClasses) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n    }\n    /**\n     * Opens an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     * @return {?}\n     */\n\n\n    show() {\n      if (this._datepicker.isShown) {\n        return;\n      }\n\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDatepickerContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        placement: this.placement\n      }); // if date changes from external source (model -> view)\n\n      this._subs.push(this.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this._datepickerRef.instance.value = value;\n      })); // if date changes from picker (view -> model)\n\n\n      this._subs.push(this._datepickerRef.instance.valueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this.bsValue = value;\n        this.hide();\n      }));\n    }\n    /**\n     * Closes an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     * @return {?}\n     */\n\n\n    hide() {\n      if (this.isOpen) {\n        this._datepicker.hide();\n      }\n\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n    }\n    /**\n     * Toggles an element’s datepicker. This is considered a “manual” triggering\n     * of the datepicker.\n     * @return {?}\n     */\n\n\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n\n      this.show();\n    }\n    /**\n     * Set config for datepicker\n     * @return {?}\n     */\n\n\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this._bsValue,\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        daysDisabled: this.daysDisabled || this.bsConfig && this.bsConfig.daysDisabled,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled,\n        minMode: this.minMode || this.bsConfig && this.bsConfig.minMode\n      });\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      this._datepicker.dispose();\n    }\n\n  }\n\n  BsDatepickerDirective.ɵfac = function BsDatepickerDirective_Factory(t) {\n    return new (t || BsDatepickerDirective)(ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc3.ComponentLoaderFactory));\n  };\n\n  BsDatepickerDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDatepickerDirective,\n    selectors: [[\"\", \"bsDatepicker\", \"\"]],\n    inputs: {\n      placement: \"placement\",\n      triggers: \"triggers\",\n      outsideClick: \"outsideClick\",\n      container: \"container\",\n      outsideEsc: \"outsideEsc\",\n      isOpen: \"isOpen\",\n      bsValue: \"bsValue\",\n      bsConfig: \"bsConfig\",\n      isDisabled: \"isDisabled\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      minMode: \"minMode\",\n      daysDisabled: \"daysDisabled\",\n      datesDisabled: \"datesDisabled\",\n      dateCustomClasses: \"dateCustomClasses\"\n    },\n    outputs: {\n      bsValueChange: \"bsValueChange\",\n      onShown: \"onShown\",\n      onHidden: \"onHidden\"\n    },\n    exportAs: [\"bsDatepicker\"],\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  /** @nocollapse */\n\n  return BsDatepickerDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerInlineConfig = /*#__PURE__*/(() => {\n  class BsDatepickerInlineConfig extends BsDatepickerConfig {}\n\n  BsDatepickerInlineConfig.ɵfac = function BsDatepickerInlineConfig_Factory(t) {\n    return ɵBsDatepickerInlineConfig_BaseFactory(t || BsDatepickerInlineConfig);\n  };\n\n  BsDatepickerInlineConfig.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDatepickerInlineConfig,\n    factory: BsDatepickerInlineConfig.ɵfac\n  });\n  return BsDatepickerInlineConfig;\n})();\nconst ɵBsDatepickerInlineConfig_BaseFactory = /*@__PURE__*/ɵngcc0.ɵɵgetInheritedFactory(BsDatepickerInlineConfig);\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerInlineContainerComponent = /*#__PURE__*/(() => {\n  class BsDatepickerInlineContainerComponent extends BsDatepickerContainerComponent {\n    /**\n     * @param {?} _renderer\n     * @param {?} _config\n     * @param {?} _store\n     * @param {?} _element\n     * @param {?} _actions\n     * @param {?} _effects\n     * @param {?} _positioningService\n     */\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positioningService) {\n      super(_renderer, _config, _store, _element, _actions, _effects, _positioningService);\n\n      _renderer.setStyle(_element.nativeElement, 'display', 'inline-block');\n\n      _renderer.setStyle(_element.nativeElement, 'position', 'static');\n    }\n\n  }\n\n  BsDatepickerInlineContainerComponent.ɵfac = function BsDatepickerInlineContainerComponent_Factory(t) {\n    return new (t || BsDatepickerInlineContainerComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(BsDatepickerStore), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(BsDatepickerActions), ɵngcc0.ɵɵdirectiveInject(BsDatepickerEffects), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.PositioningService));\n  };\n\n  BsDatepickerInlineContainerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDatepickerInlineContainerComponent,\n    selectors: [[\"bs-datepicker-inline-container\"]],\n    hostBindings: function BsDatepickerInlineContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerInlineContainerComponent_click_HostBindingHandler($event) {\n          return ctx._stopPropagation($event);\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), ɵngcc0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [1, \"bs-datepicker-custom-range\"], [3, \"ranges\"]],\n    template: function BsDatepickerInlineContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, BsDatepickerInlineContainerComponent_div_0_Template, 9, 10, \"div\", 0);\n        ɵngcc0.ɵɵpipe(1, \"async\");\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgForOf, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, BsCustomDatesViewComponent];\n    },\n    pipes: function () {\n      return [ɵngcc2.AsyncPipe];\n    },\n    encapsulation: 2,\n    data: {\n      animation: [datepickerAnimation]\n    }\n  });\n  /** @nocollapse */\n\n  return BsDatepickerInlineContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerInlineDirective = /*#__PURE__*/(() => {\n  class BsDatepickerInlineDirective {\n    /**\n     * @param {?} _config\n     * @param {?} _elementRef\n     * @param {?} _renderer\n     * @param {?} _viewContainerRef\n     * @param {?} cis\n     */\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      /**\n       * Emits when datepicker value has been changed\n       */\n\n      this.bsValueChange = new EventEmitter();\n      this._subs = []; // todo: assign only subset of fields\n\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n    }\n    /**\n     * Initial value of datepicker\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDatepickerInlineContainerComponent).to(this._elementRef).show(); // if date changes from external source (model -> view)\n\n      this._subs.push(this.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this._datepickerRef.instance.value = value;\n      })); // if date changes from picker (view -> model)\n\n\n      this._subs.push(this._datepickerRef.instance.valueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this.bsValue = value;\n      }));\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n\n\n    ngOnChanges(changes) {\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n\n      if (changes.minDate) {\n        this._datepickerRef.instance.minDate = this.minDate;\n        this._datepickerRef.instance.value = this._bsValue;\n      }\n\n      if (changes.maxDate) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n        this._datepickerRef.instance.value = this._bsValue;\n      }\n\n      if (changes.datesDisabled) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n        this._datepickerRef.instance.value = this._bsValue;\n      }\n\n      if (changes.isDisabled) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n\n      if (changes.dateCustomClasses) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n    }\n    /**\n     * Set config for datepicker\n     * @return {?}\n     */\n\n\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this._bsValue,\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled\n      });\n\n      if (this._datepickerRef !== undefined) {\n        this._datepicker.hide();\n\n        this._datepicker.show();\n      }\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      this._datepicker.dispose();\n    }\n\n  }\n\n  BsDatepickerInlineDirective.ɵfac = function BsDatepickerInlineDirective_Factory(t) {\n    return new (t || BsDatepickerInlineDirective)(ɵngcc0.ɵɵdirectiveInject(BsDatepickerInlineConfig), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc3.ComponentLoaderFactory));\n  };\n\n  BsDatepickerInlineDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDatepickerInlineDirective,\n    selectors: [[\"bs-datepicker-inline\"]],\n    inputs: {\n      bsValue: \"bsValue\",\n      bsConfig: \"bsConfig\",\n      isDisabled: \"isDisabled\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateCustomClasses: \"dateCustomClasses\",\n      datesDisabled: \"datesDisabled\"\n    },\n    outputs: {\n      bsValueChange: \"bsValueChange\"\n    },\n    exportAs: [\"bsDatepickerInline\"],\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  /** @nocollapse */\n\n  return BsDatepickerInlineDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerInlineConfig = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineConfig extends BsDatepickerConfig {\n    constructor() {\n      super(...arguments); // DatepickerRenderOptions\n\n      this.displayMonths = 2;\n      /**\n       * turn on/off animation\n       */\n\n      this.isAnimated = false;\n    }\n\n  }\n\n  BsDaterangepickerInlineConfig.ɵfac = function BsDaterangepickerInlineConfig_Factory(t) {\n    return ɵBsDaterangepickerInlineConfig_BaseFactory(t || BsDaterangepickerInlineConfig);\n  };\n\n  BsDaterangepickerInlineConfig.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDaterangepickerInlineConfig,\n    factory: BsDaterangepickerInlineConfig.ɵfac\n  });\n  return BsDaterangepickerInlineConfig;\n})();\nconst ɵBsDaterangepickerInlineConfig_BaseFactory = /*@__PURE__*/ɵngcc0.ɵɵgetInheritedFactory(BsDaterangepickerInlineConfig);\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerContainerComponent = /*#__PURE__*/(() => {\n  class BsDaterangepickerContainerComponent extends BsDatepickerAbstractComponent {\n    /**\n     * @param {?} _renderer\n     * @param {?} _config\n     * @param {?} _store\n     * @param {?} _element\n     * @param {?} _actions\n     * @param {?} _effects\n     * @param {?} _positionService\n     */\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positionService) {\n      super();\n      this._config = _config;\n      this._store = _store;\n      this._element = _element;\n      this._actions = _actions;\n      this._positionService = _positionService;\n      this.valueChange = new EventEmitter();\n      this.animationState = 'void';\n      this._rangeStack = [];\n      this._subs = [];\n      this._effects = _effects;\n\n      _renderer.setStyle(_element.nativeElement, 'display', 'block');\n\n      _renderer.setStyle(_element.nativeElement, 'position', 'absolute');\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set value(value) {\n      this._effects.setRangeValue(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this._positionService.setOptions({\n        modifiers: {\n          flip: {\n            enabled: this._config.adaptivePosition\n          }\n        },\n        allowedPositions: ['top', 'bottom']\n      });\n\n      this._positionService.event$.pipe(take(1)).subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        this._positionService.disable();\n\n        if (this._config.isAnimated) {\n          this.animationState = this.isTopPosition ? 'animated-up' : 'animated-down';\n          return;\n        }\n\n        this.animationState = 'unanimated';\n      });\n\n      this.containerClass = this._config.containerClass;\n      this.isOtherMonthsActive = this._config.selectFromOtherMonth;\n\n      this._effects.init(this._store) // intial state options\n      // todo: fix this, split configs\n      .setOptions(this._config) // data binding view --> model\n      .setBindings(this) // set event handlers\n      .setEventHandlers(this).registerDatepickerSideEffects(); // todo: move it somewhere else\n      // on selected date change\n\n\n      this._subs.push(this._store.select(\n      /**\n      * @param {?} state\n      * @return {?}\n      */\n      state => state.selectedRange).subscribe(\n      /**\n      * @param {?} date\n      * @return {?}\n      */\n      date => this.valueChange.emit(date)));\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get isTopPosition() {\n      return this._element.nativeElement.classList.contains('top');\n    }\n    /**\n     * @return {?}\n     */\n\n\n    positionServiceEnable() {\n      this._positionService.enable();\n    }\n    /**\n     * @param {?} day\n     * @return {?}\n     */\n\n\n    daySelectHandler(day) {\n      /** @type {?} */\n      const isDisabled = this.isOtherMonthsActive ? day.isDisabled : day.isOtherMonth || day.isDisabled;\n\n      if (isDisabled) {\n        return;\n      } // if only one date is already selected\n      // and user clicks on previous date\n      // start selection from new date\n      // but if new date is after initial one\n      // than finish selection\n\n\n      if (this._rangeStack.length === 1) {\n        this._rangeStack = day.date >= this._rangeStack[0] ? [this._rangeStack[0], day.date] : [day.date];\n      }\n\n      if (this._rangeStack.length === 0) {\n        this._rangeStack = [day.date];\n      }\n\n      this._store.dispatch(this._actions.selectRange(this._rangeStack));\n\n      if (this._rangeStack.length === 2) {\n        this._rangeStack = [];\n      }\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n\n      this._effects.destroy();\n    }\n\n  }\n\n  BsDaterangepickerContainerComponent.ɵfac = function BsDaterangepickerContainerComponent_Factory(t) {\n    return new (t || BsDaterangepickerContainerComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(BsDatepickerStore), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(BsDatepickerActions), ɵngcc0.ɵɵdirectiveInject(BsDatepickerEffects), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.PositioningService));\n  };\n\n  BsDaterangepickerContainerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDaterangepickerContainerComponent,\n    selectors: [[\"bs-daterangepicker-container\"]],\n    hostAttrs: [\"role\", \"dialog\", \"aria-label\", \"calendar\", 1, \"bottom\"],\n    hostBindings: function BsDaterangepickerContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function BsDaterangepickerContainerComponent_click_HostBindingHandler($event) {\n          return ctx._stopPropagation($event);\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), ɵngcc0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [1, \"bs-datepicker-custom-range\"], [3, \"ranges\"]],\n    template: function BsDaterangepickerContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, BsDaterangepickerContainerComponent_div_0_Template, 9, 10, \"div\", 0);\n        ɵngcc0.ɵɵpipe(1, \"async\");\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgForOf, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, BsCustomDatesViewComponent];\n    },\n    pipes: function () {\n      return [ɵngcc2.AsyncPipe];\n    },\n    encapsulation: 2,\n    data: {\n      animation: [datepickerAnimation]\n    }\n  });\n  /** @nocollapse */\n\n  return BsDaterangepickerContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerInlineContainerComponent = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineContainerComponent extends BsDaterangepickerContainerComponent {\n    /**\n     * @param {?} _renderer\n     * @param {?} _config\n     * @param {?} _store\n     * @param {?} _element\n     * @param {?} _actions\n     * @param {?} _effects\n     * @param {?} _positioningService\n     */\n    constructor(_renderer, _config, _store, _element, _actions, _effects, _positioningService) {\n      super(_renderer, _config, _store, _element, _actions, _effects, _positioningService);\n\n      _renderer.setStyle(_element.nativeElement, 'display', 'inline-block');\n\n      _renderer.setStyle(_element.nativeElement, 'position', 'static');\n    }\n\n  }\n\n  BsDaterangepickerInlineContainerComponent.ɵfac = function BsDaterangepickerInlineContainerComponent_Factory(t) {\n    return new (t || BsDaterangepickerInlineContainerComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(BsDatepickerStore), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(BsDatepickerActions), ɵngcc0.ɵɵdirectiveInject(BsDatepickerEffects), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.PositioningService));\n  };\n\n  BsDaterangepickerInlineContainerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDaterangepickerInlineContainerComponent,\n    selectors: [[\"bs-daterangepicker-inline-container\"]],\n    hostBindings: function BsDaterangepickerInlineContainerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function BsDaterangepickerInlineContainerComponent_click_HostBindingHandler($event) {\n          return ctx._stopPropagation($event);\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BsDatepickerStore, BsDatepickerEffects]), ɵngcc0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 3,\n    consts: [[\"class\", \"bs-datepicker\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bs-datepicker\", 3, \"ngClass\"], [1, \"bs-datepicker-container\"], [\"role\", \"application\", 1, \"bs-calendar-container\", 3, \"ngSwitch\"], [\"class\", \"bs-media-container\", 4, \"ngSwitchCase\"], [\"class\", \"bs-datepicker-buttons\", 4, \"ngIf\"], [\"class\", \"bs-datepicker-custom-range\", 4, \"ngIf\"], [1, \"bs-media-container\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"options\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onHoverWeek\", \"onSelect\"], [3, \"bs-datepicker-multiple\", \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\", 4, \"ngFor\", \"ngForOf\"], [3, \"calendar\", \"onNavigate\", \"onViewMode\", \"onHover\", \"onSelect\"], [1, \"bs-datepicker-buttons\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\"], [1, \"bs-datepicker-custom-range\"], [3, \"ranges\"]],\n    template: function BsDaterangepickerInlineContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, BsDaterangepickerInlineContainerComponent_div_0_Template, 9, 10, \"div\", 0);\n        ɵngcc0.ɵɵpipe(1, \"async\");\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx.viewMode));\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgForOf, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsYearsCalendarViewComponent, BsCustomDatesViewComponent];\n    },\n    pipes: function () {\n      return [ɵngcc2.AsyncPipe];\n    },\n    encapsulation: 2,\n    data: {\n      animation: [datepickerAnimation]\n    }\n  });\n  /** @nocollapse */\n\n  return BsDaterangepickerInlineContainerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerInlineDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerInlineDirective {\n    /**\n     * @param {?} _config\n     * @param {?} _elementRef\n     * @param {?} _renderer\n     * @param {?} _viewContainerRef\n     * @param {?} cis\n     */\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      this._elementRef = _elementRef;\n      /**\n       * Emits when daterangepicker value has been changed\n       */\n\n      this.bsValueChange = new EventEmitter();\n      this._subs = []; // todo: assign only subset of fields\n\n      Object.assign(this, this._config);\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n    }\n    /**\n     * Initial value of datepicker\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDaterangepickerInlineContainerComponent).to(this._elementRef).show(); // if date changes from external source (model -> view)\n\n      this._subs.push(this.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this._datepickerRef.instance.value = value;\n      })); // if date changes from picker (view -> model)\n\n\n      this._subs.push(this._datepickerRef.instance.valueChange.pipe(filter(\n      /**\n      * @param {?} range\n      * @return {?}\n      */\n      range => range && range[0] && !!range[1])).subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this.bsValue = value;\n      }));\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n\n\n    ngOnChanges(changes) {\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n\n      if (changes.minDate) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n\n      if (changes.maxDate) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n\n      if (changes.datesDisabled) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n\n      if (changes.isDisabled) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n\n      if (changes.dateCustomClasses) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n    }\n    /**\n     * Set config for datepicker\n     * @return {?}\n     */\n\n\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this._bsValue,\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled\n      });\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      this._datepicker.dispose();\n    }\n\n  }\n\n  BsDaterangepickerInlineDirective.ɵfac = function BsDaterangepickerInlineDirective_Factory(t) {\n    return new (t || BsDaterangepickerInlineDirective)(ɵngcc0.ɵɵdirectiveInject(BsDaterangepickerInlineConfig), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc3.ComponentLoaderFactory));\n  };\n\n  BsDaterangepickerInlineDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDaterangepickerInlineDirective,\n    selectors: [[\"bs-daterangepicker-inline\"]],\n    inputs: {\n      bsValue: \"bsValue\",\n      bsConfig: \"bsConfig\",\n      isDisabled: \"isDisabled\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateCustomClasses: \"dateCustomClasses\",\n      datesDisabled: \"datesDisabled\"\n    },\n    outputs: {\n      bsValueChange: \"bsValueChange\"\n    },\n    exportAs: [\"bsDaterangepickerInline\"],\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  /** @nocollapse */\n\n  return BsDaterangepickerInlineDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst BS_DATEPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(\n  /**\n  * @return {?}\n  */\n  () => BsDatepickerInputDirective),\n  multi: true\n};\n/** @type {?} */\n\nconst BS_DATEPICKER_VALIDATOR = {\n  provide: NG_VALIDATORS,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(\n  /**\n  * @return {?}\n  */\n  () => BsDatepickerInputDirective),\n  multi: true\n};\nlet BsDatepickerInputDirective = /*#__PURE__*/(() => {\n  class BsDatepickerInputDirective {\n    /**\n     * @param {?} _picker\n     * @param {?} _localeService\n     * @param {?} _renderer\n     * @param {?} _elRef\n     * @param {?} changeDetection\n     */\n    constructor(_picker, _localeService, _renderer, _elRef, changeDetection) {\n      this._picker = _picker;\n      this._localeService = _localeService;\n      this._renderer = _renderer;\n      this._elRef = _elRef;\n      this.changeDetection = changeDetection;\n      this._onChange = Function.prototype;\n      this._onTouched = Function.prototype;\n      /* tslint:disable-next-line: no-unused-variable */\n\n      this._validatorChange = Function.prototype; // update input value on datepicker value update\n\n      this._picker.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        /** @type {?} */\n        let preValue = value;\n\n        if (value) {\n          /** @type {?} */\n          const _localeKey = this._localeService.currentLocale;\n          /** @type {?} */\n\n          const _locale = getLocale(_localeKey);\n\n          if (!_locale) {\n            throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n          }\n\n          preValue = _locale.preinput(value);\n        }\n\n        this._setInputValue(preValue);\n\n        if (this._value !== preValue) {\n          this._value = preValue;\n\n          this._onChange(preValue);\n\n          this._onTouched();\n        }\n\n        this.changeDetection.markForCheck();\n      }); // update input value on locale change\n\n\n      this._localeService.localeChange.subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        this._setInputValue(this._value);\n      });\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    _setInputValue(value) {\n      /** @type {?} */\n      const initialDate = !value ? '' : formatDate(value, this._picker._config.dateInputFormat, this._localeService.currentLocale);\n\n      this._renderer.setProperty(this._elRef.nativeElement, 'value', initialDate);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    onChange(event) {\n      /* tslint:disable-next-line: no-any*/\n      this.writeValue(event.target.value);\n\n      this._onChange(this._value);\n\n      this._onTouched();\n    }\n    /**\n     * @param {?} c\n     * @return {?}\n     */\n\n\n    validate(c) {\n      /** @type {?} */\n      const _value = c.value;\n      /* tslint:disable-next-line: prefer-switch */\n\n      if (_value === null || _value === undefined || _value === '') {\n        return null;\n      }\n\n      if (isDate(_value)) {\n        /** @type {?} */\n        const _isDateValid = isDateValid(_value);\n\n        if (!_isDateValid) {\n          return {\n            bsDate: {\n              invalid: _value\n            }\n          };\n        }\n\n        if (this._picker && this._picker.minDate && isBefore(_value, this._picker.minDate, 'date')) {\n          this.writeValue(this._picker.minDate);\n          return {\n            bsDate: {\n              minDate: this._picker.minDate\n            }\n          };\n        }\n\n        if (this._picker && this._picker.maxDate && isAfter(_value, this._picker.maxDate, 'date')) {\n          this.writeValue(this._picker.maxDate);\n          return {\n            bsDate: {\n              maxDate: this._picker.maxDate\n            }\n          };\n        }\n      }\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnValidatorChange(fn) {\n      this._validatorChange = fn;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    writeValue(value) {\n      if (!value) {\n        this._value = null;\n      } else {\n        /** @type {?} */\n        const _localeKey = this._localeService.currentLocale;\n        /** @type {?} */\n\n        const _locale = getLocale(_localeKey);\n\n        if (!_locale) {\n          throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n        }\n\n        this._value = parseDate(value, this._picker._config.dateInputFormat, this._localeService.currentLocale);\n\n        if (this._picker._config.useUtc) {\n          this._value = utcAsLocal(this._value);\n        }\n      }\n\n      this._picker.bsValue = this._value;\n    }\n    /**\n     * @param {?} isDisabled\n     * @return {?}\n     */\n\n\n    setDisabledState(isDisabled) {\n      this._picker.isDisabled = isDisabled;\n\n      if (isDisabled) {\n        this._renderer.setAttribute(this._elRef.nativeElement, 'disabled', 'disabled');\n\n        return;\n      }\n\n      this._renderer.removeAttribute(this._elRef.nativeElement, 'disabled');\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    onBlur() {\n      this._onTouched();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    hide() {\n      this._picker.hide();\n\n      this._renderer.selectRootElement(this._elRef.nativeElement).blur();\n    }\n\n  }\n\n  BsDatepickerInputDirective.ɵfac = function BsDatepickerInputDirective_Factory(t) {\n    return new (t || BsDatepickerInputDirective)(ɵngcc0.ɵɵdirectiveInject(BsDatepickerDirective, 1), ɵngcc0.ɵɵdirectiveInject(BsLocaleService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  BsDatepickerInputDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDatepickerInputDirective,\n    selectors: [[\"input\", \"bsDatepicker\", \"\"]],\n    hostBindings: function BsDatepickerInputDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"change\", function BsDatepickerInputDirective_change_HostBindingHandler($event) {\n          return ctx.onChange($event);\n        })(\"keyup.esc\", function BsDatepickerInputDirective_keyup_esc_HostBindingHandler() {\n          return ctx.hide();\n        })(\"blur\", function BsDatepickerInputDirective_blur_HostBindingHandler() {\n          return ctx.onBlur();\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BS_DATEPICKER_VALUE_ACCESSOR, BS_DATEPICKER_VALIDATOR])]\n  });\n  /** @nocollapse */\n\n  return BsDatepickerInputDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerConfig = /*#__PURE__*/(() => {\n  class BsDaterangepickerConfig extends BsDatepickerConfig {\n    constructor() {\n      super(...arguments); // DatepickerRenderOptions\n\n      this.displayMonths = 2;\n    }\n\n  }\n\n  BsDaterangepickerConfig.ɵfac = function BsDaterangepickerConfig_Factory(t) {\n    return ɵBsDaterangepickerConfig_BaseFactory(t || BsDaterangepickerConfig);\n  };\n\n  BsDaterangepickerConfig.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: BsDaterangepickerConfig,\n    factory: BsDaterangepickerConfig.ɵfac\n  });\n  return BsDaterangepickerConfig;\n})();\nconst ɵBsDaterangepickerConfig_BaseFactory = /*@__PURE__*/ɵngcc0.ɵɵgetInheritedFactory(BsDaterangepickerConfig);\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaterangepickerDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerDirective {\n    /**\n     * @param {?} _config\n     * @param {?} _elementRef\n     * @param {?} _renderer\n     * @param {?} _viewContainerRef\n     * @param {?} cis\n     */\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis) {\n      this._config = _config;\n      /**\n       * Placement of a daterangepicker. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n       */\n\n      this.placement = 'bottom';\n      /**\n       * Specifies events that should trigger. Supports a space separated list of\n       * event names.\n       */\n\n      this.triggers = 'click';\n      /**\n       * Close daterangepicker on outside click\n       */\n\n      this.outsideClick = true;\n      /**\n       * A selector specifying the element the daterangepicker should be appended to.\n       */\n\n      this.container = 'body';\n      this.outsideEsc = true;\n      /**\n       * Emits when daterangepicker value has been changed\n       */\n\n      this.bsValueChange = new EventEmitter();\n      this._subs = [];\n      this._datepicker = cis.createLoader(_elementRef, _viewContainerRef, _renderer);\n      Object.assign(this, _config);\n      this.onShown = this._datepicker.onShown;\n      this.onHidden = this._datepicker.onHidden;\n    }\n    /**\n     * Returns whether or not the daterangepicker is currently being shown\n     * @return {?}\n     */\n\n\n    get isOpen() {\n      return this._datepicker.isShown;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set isOpen(value) {\n      if (value) {\n        this.show();\n      } else {\n        this.hide();\n      }\n    }\n    /**\n     * Initial value of daterangepicker\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set bsValue(value) {\n      if (this._bsValue === value) {\n        return;\n      }\n\n      this._bsValue = value;\n      this.bsValueChange.emit(value);\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      this._datepicker.listen({\n        outsideClick: this.outsideClick,\n        outsideEsc: this.outsideEsc,\n        triggers: this.triggers,\n        show:\n        /**\n        * @return {?}\n        */\n        () => this.show()\n      });\n\n      this.setConfig();\n    }\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n\n\n    ngOnChanges(changes) {\n      if (!this._datepickerRef || !this._datepickerRef.instance) {\n        return;\n      }\n\n      if (changes.minDate) {\n        this._datepickerRef.instance.minDate = this.minDate;\n      }\n\n      if (changes.maxDate) {\n        this._datepickerRef.instance.maxDate = this.maxDate;\n      }\n\n      if (changes.datesDisabled) {\n        this._datepickerRef.instance.datesDisabled = this.datesDisabled;\n      }\n\n      if (changes.isDisabled) {\n        this._datepickerRef.instance.isDisabled = this.isDisabled;\n      }\n\n      if (changes.dateCustomClasses) {\n        this._datepickerRef.instance.dateCustomClasses = this.dateCustomClasses;\n      }\n    }\n    /**\n     * Opens an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     * @return {?}\n     */\n\n\n    show() {\n      if (this._datepicker.isShown) {\n        return;\n      }\n\n      this.setConfig();\n      this._datepickerRef = this._datepicker.provide({\n        provide: BsDatepickerConfig,\n        useValue: this._config\n      }).attach(BsDaterangepickerContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        placement: this.placement\n      }); // if date changes from external source (model -> view)\n\n      this._subs.push(this.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this._datepickerRef.instance.value = value;\n      })); // if date changes from picker (view -> model)\n\n\n      this._subs.push(this._datepickerRef.instance.valueChange.pipe(filter(\n      /**\n      * @param {?} range\n      * @return {?}\n      */\n      range => range && range[0] && !!range[1])).subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        this.bsValue = value;\n        this.hide();\n      }));\n    }\n    /**\n     * Set config for daterangepicker\n     * @return {?}\n     */\n\n\n    setConfig() {\n      this._config = Object.assign({}, this._config, this.bsConfig, {\n        value: this._bsValue,\n        isDisabled: this.isDisabled,\n        minDate: this.minDate || this.bsConfig && this.bsConfig.minDate,\n        maxDate: this.maxDate || this.bsConfig && this.bsConfig.maxDate,\n        dateCustomClasses: this.dateCustomClasses || this.bsConfig && this.bsConfig.dateCustomClasses,\n        datesDisabled: this.datesDisabled || this.bsConfig && this.bsConfig.datesDisabled\n      });\n    }\n    /**\n     * Closes an element’s datepicker. This is considered a “manual” triggering of\n     * the datepicker.\n     * @return {?}\n     */\n\n\n    hide() {\n      if (this.isOpen) {\n        this._datepicker.hide();\n      }\n\n      for (const sub of this._subs) {\n        sub.unsubscribe();\n      }\n    }\n    /**\n     * Toggles an element’s datepicker. This is considered a “manual” triggering\n     * of the datepicker.\n     * @return {?}\n     */\n\n\n    toggle() {\n      if (this.isOpen) {\n        return this.hide();\n      }\n\n      this.show();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnDestroy() {\n      this._datepicker.dispose();\n    }\n\n  }\n\n  BsDaterangepickerDirective.ɵfac = function BsDaterangepickerDirective_Factory(t) {\n    return new (t || BsDaterangepickerDirective)(ɵngcc0.ɵɵdirectiveInject(BsDaterangepickerConfig), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ViewContainerRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc3.ComponentLoaderFactory));\n  };\n\n  BsDaterangepickerDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDaterangepickerDirective,\n    selectors: [[\"\", \"bsDaterangepicker\", \"\"]],\n    inputs: {\n      placement: \"placement\",\n      triggers: \"triggers\",\n      outsideClick: \"outsideClick\",\n      container: \"container\",\n      outsideEsc: \"outsideEsc\",\n      isOpen: \"isOpen\",\n      bsValue: \"bsValue\",\n      bsConfig: \"bsConfig\",\n      isDisabled: \"isDisabled\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      dateCustomClasses: \"dateCustomClasses\",\n      datesDisabled: \"datesDisabled\"\n    },\n    outputs: {\n      bsValueChange: \"bsValueChange\",\n      onShown: \"onShown\",\n      onHidden: \"onHidden\"\n    },\n    exportAs: [\"bsDaterangepicker\"],\n    features: [ɵngcc0.ɵɵNgOnChangesFeature]\n  });\n  /** @nocollapse */\n\n  return BsDaterangepickerDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst BS_DATERANGEPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(\n  /**\n  * @return {?}\n  */\n  () => BsDaterangepickerInputDirective),\n  multi: true\n};\n/** @type {?} */\n\nconst BS_DATERANGEPICKER_VALIDATOR = {\n  provide: NG_VALIDATORS,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(\n  /**\n  * @return {?}\n  */\n  () => BsDaterangepickerInputDirective),\n  multi: true\n};\nlet BsDaterangepickerInputDirective = /*#__PURE__*/(() => {\n  class BsDaterangepickerInputDirective {\n    /**\n     * @param {?} _picker\n     * @param {?} _localeService\n     * @param {?} _renderer\n     * @param {?} _elRef\n     * @param {?} changeDetection\n     */\n    constructor(_picker, _localeService, _renderer, _elRef, changeDetection) {\n      this._picker = _picker;\n      this._localeService = _localeService;\n      this._renderer = _renderer;\n      this._elRef = _elRef;\n      this.changeDetection = changeDetection;\n      this._onChange = Function.prototype;\n      this._onTouched = Function.prototype;\n      /* tslint:disable-next-line: no-unused-variable */\n\n      this._validatorChange = Function.prototype; // update input value on datepicker value update\n\n      this._picker.bsValueChange.subscribe(\n      /**\n      * @param {?} value\n      * @return {?}\n      */\n      value => {\n        /** @type {?} */\n        let preValue = value;\n\n        if (value) {\n          /** @type {?} */\n          const _localeKey = this._localeService.currentLocale;\n          /** @type {?} */\n\n          const _locale = getLocale(_localeKey);\n\n          if (!_locale) {\n            throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n          }\n\n          preValue = value.map(\n          /**\n          * @param {?} v\n          * @return {?}\n          */\n          v => _locale.preinput(v));\n        }\n\n        this._setInputValue(preValue);\n\n        if (this._value !== preValue) {\n          this._value = preValue;\n\n          this._onChange(preValue);\n\n          this._onTouched();\n        }\n\n        this.changeDetection.markForCheck();\n      }); // update input value on locale change\n\n\n      this._localeService.localeChange.subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        this._setInputValue(this._value);\n      });\n    }\n    /**\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    _setInputValue(date) {\n      /** @type {?} */\n      let range = '';\n\n      if (date) {\n        /** @type {?} */\n        const start = !date[0] ? '' : formatDate(date[0], this._picker._config.rangeInputFormat, this._localeService.currentLocale);\n        /** @type {?} */\n\n        const end = !date[1] ? '' : formatDate(date[1], this._picker._config.rangeInputFormat, this._localeService.currentLocale);\n        range = start && end ? start + this._picker._config.rangeSeparator + end : '';\n      }\n\n      this._renderer.setProperty(this._elRef.nativeElement, 'value', range);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    onChange(event) {\n      /* tslint:disable-next-line: no-any*/\n      this.writeValue(event.target.value);\n\n      this._onChange(this._value);\n\n      this._onTouched();\n    }\n    /**\n     * @param {?} c\n     * @return {?}\n     */\n\n\n    validate(c) {\n      /** @type {?} */\n      const _value = c.value;\n      /** @type {?} */\n\n      const errors = [];\n\n      if (_value === null || _value === undefined || !isArray(_value)) {\n        return null;\n      } // @ts-ignore\n\n\n      _value.sort(\n      /**\n      * @param {?} a\n      * @param {?} b\n      * @return {?}\n      */\n      (a, b) => a - b);\n      /** @type {?} */\n\n\n      const _isFirstDateValid = isDateValid(_value[0]);\n      /** @type {?} */\n\n\n      const _isSecondDateValid = isDateValid(_value[1]);\n\n      if (!_isFirstDateValid) {\n        return {\n          bsDate: {\n            invalid: _value[0]\n          }\n        };\n      }\n\n      if (!_isSecondDateValid) {\n        return {\n          bsDate: {\n            invalid: _value[1]\n          }\n        };\n      }\n\n      if (this._picker && this._picker.minDate && isBefore(_value[0], this._picker.minDate, 'date')) {\n        _value[0] = this._picker.minDate;\n        errors.push({\n          bsDate: {\n            minDate: this._picker.minDate\n          }\n        });\n      }\n\n      if (this._picker && this._picker.maxDate && isAfter(_value[1], this._picker.maxDate, 'date')) {\n        _value[1] = this._picker.maxDate;\n        errors.push({\n          bsDate: {\n            maxDate: this._picker.maxDate\n          }\n        });\n      }\n\n      if (errors.length > 0) {\n        this.writeValue(_value);\n        return errors;\n      }\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnValidatorChange(fn) {\n      this._validatorChange = fn;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    writeValue(value) {\n      if (!value) {\n        this._value = null;\n      } else {\n        /** @type {?} */\n        const _localeKey = this._localeService.currentLocale;\n        /** @type {?} */\n\n        const _locale = getLocale(_localeKey);\n\n        if (!_locale) {\n          throw new Error(`Locale \"${_localeKey}\" is not defined, please add it with \"defineLocale(...)\"`);\n        }\n        /** @type {?} */\n\n\n        let _input = [];\n\n        if (typeof value === 'string') {\n          _input = value.split(this._picker._config.rangeSeparator);\n        }\n\n        if (Array.isArray(value)) {\n          _input = value;\n        }\n\n        this._value = _input.map(\n        /**\n        * @param {?} _val\n        * @return {?}\n        */\n        _val => {\n          if (this._picker._config.useUtc) {\n            return utcAsLocal(parseDate(_val, this._picker._config.dateInputFormat, this._localeService.currentLocale));\n          }\n\n          return parseDate(_val, this._picker._config.dateInputFormat, this._localeService.currentLocale);\n        }).map(\n        /**\n        * @param {?} date\n        * @return {?}\n        */\n        date => isNaN(date.valueOf()) ? null : date);\n      }\n\n      this._picker.bsValue = this._value;\n    }\n    /**\n     * @param {?} isDisabled\n     * @return {?}\n     */\n\n\n    setDisabledState(isDisabled) {\n      this._picker.isDisabled = isDisabled;\n\n      if (isDisabled) {\n        this._renderer.setAttribute(this._elRef.nativeElement, 'disabled', 'disabled');\n\n        return;\n      }\n\n      this._renderer.removeAttribute(this._elRef.nativeElement, 'disabled');\n    }\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    onBlur() {\n      this._onTouched();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    hide() {\n      this._picker.hide();\n\n      this._renderer.selectRootElement(this._elRef.nativeElement).blur();\n    }\n\n  }\n\n  BsDaterangepickerInputDirective.ɵfac = function BsDaterangepickerInputDirective_Factory(t) {\n    return new (t || BsDaterangepickerInputDirective)(ɵngcc0.ɵɵdirectiveInject(BsDaterangepickerDirective, 1), ɵngcc0.ɵɵdirectiveInject(BsLocaleService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n  };\n\n  BsDaterangepickerInputDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BsDaterangepickerInputDirective,\n    selectors: [[\"input\", \"bsDaterangepicker\", \"\"]],\n    hostBindings: function BsDaterangepickerInputDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"change\", function BsDaterangepickerInputDirective_change_HostBindingHandler($event) {\n          return ctx.onChange($event);\n        })(\"keyup.esc\", function BsDaterangepickerInputDirective_keyup_esc_HostBindingHandler() {\n          return ctx.hide();\n        })(\"blur\", function BsDaterangepickerInputDirective_blur_HostBindingHandler() {\n          return ctx.onBlur();\n        });\n      }\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([BS_DATERANGEPICKER_VALUE_ACCESSOR, BS_DATERANGEPICKER_VALIDATOR])]\n  });\n  /** @nocollapse */\n\n  return BsDaterangepickerInputDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsCalendarLayoutComponent = /*#__PURE__*/(() => {\n  class BsCalendarLayoutComponent {}\n\n  BsCalendarLayoutComponent.ɵfac = function BsCalendarLayoutComponent_Factory(t) {\n    return new (t || BsCalendarLayoutComponent)();\n  };\n\n  BsCalendarLayoutComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsCalendarLayoutComponent,\n    selectors: [[\"bs-calendar-layout\"]],\n    ngContentSelectors: _c1,\n    decls: 6,\n    vars: 2,\n    consts: [[\"title\", \"hey there\", 4, \"ngIf\"], [1, \"bs-datepicker-head\"], [1, \"bs-datepicker-body\"], [4, \"ngIf\"], [\"title\", \"hey there\"]],\n    template: function BsCalendarLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef(_c0);\n        ɵngcc0.ɵɵtemplate(0, BsCalendarLayoutComponent_bs_current_date_0_Template, 1, 0, \"bs-current-date\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"div\", 1);\n        ɵngcc0.ɵɵprojection(2);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(3, \"div\", 2);\n        ɵngcc0.ɵɵprojection(4, 1);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtemplate(5, BsCalendarLayoutComponent_bs_timepicker_5_Template, 1, 0, \"bs-timepicker\", 3);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", false);\n        ɵngcc0.ɵɵadvance(5);\n        ɵngcc0.ɵɵproperty(\"ngIf\", false);\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, BsCurrentDateViewComponent, BsTimepickerViewComponent];\n    },\n    encapsulation: 2\n  });\n  return BsCalendarLayoutComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsCurrentDateViewComponent = /*#__PURE__*/(() => {\n  class BsCurrentDateViewComponent {}\n\n  BsCurrentDateViewComponent.ɵfac = function BsCurrentDateViewComponent_Factory(t) {\n    return new (t || BsCurrentDateViewComponent)();\n  };\n\n  BsCurrentDateViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsCurrentDateViewComponent,\n    selectors: [[\"bs-current-date\"]],\n    inputs: {\n      title: \"title\"\n    },\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"current-timedate\"]],\n    template: function BsCurrentDateViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"span\");\n        ɵngcc0.ɵɵtext(2);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵtextInterpolate(ctx.title);\n      }\n    },\n    encapsulation: 2\n  });\n  return BsCurrentDateViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsCustomDatesViewComponent = /*#__PURE__*/(() => {\n  class BsCustomDatesViewComponent {}\n\n  BsCustomDatesViewComponent.ɵfac = function BsCustomDatesViewComponent_Factory(t) {\n    return new (t || BsCustomDatesViewComponent)();\n  };\n\n  BsCustomDatesViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsCustomDatesViewComponent,\n    selectors: [[\"bs-custom-date-view\"]],\n    inputs: {\n      isCustomRangeShown: \"isCustomRangeShown\",\n      ranges: \"ranges\"\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"bs-datepicker-predefined-btns\"], [\"type\", \"button\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 4, \"ngIf\"], [\"type\", \"button\"]],\n    template: function BsCustomDatesViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵtemplate(1, BsCustomDatesViewComponent_button_1_Template, 2, 1, \"button\", 1);\n        ɵngcc0.ɵɵtemplate(2, BsCustomDatesViewComponent_button_2_Template, 2, 0, \"button\", 2);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.ranges);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.isCustomRangeShown);\n      }\n    },\n    directives: [ɵngcc2.NgForOf, ɵngcc2.NgIf],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return BsCustomDatesViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDatepickerDayDecoratorComponent = /*#__PURE__*/(() => {\n  class BsDatepickerDayDecoratorComponent {\n    /**\n     * @param {?} _config\n     * @param {?} _elRef\n     * @param {?} _renderer\n     */\n    constructor(_config, _elRef, _renderer) {\n      this._config = _config;\n      this._elRef = _elRef;\n      this._renderer = _renderer;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      if (this.day.isToday && this._config && this._config.customTodayClass) {\n        this._renderer.addClass(this._elRef.nativeElement, this._config.customTodayClass);\n      }\n\n      if (typeof this.day.customClasses === 'string') {\n        this.day.customClasses.split(' ').filter(\n        /**\n        * @param {?} className\n        * @return {?}\n        */\n        className => className).forEach(\n        /**\n        * @param {?} className\n        * @return {?}\n        */\n        className => {\n          this._renderer.addClass(this._elRef.nativeElement, className);\n        });\n      }\n    }\n\n  }\n\n  BsDatepickerDayDecoratorComponent.ɵfac = function BsDatepickerDayDecoratorComponent_Factory(t) {\n    return new (t || BsDatepickerDayDecoratorComponent)(ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  BsDatepickerDayDecoratorComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDatepickerDayDecoratorComponent,\n    selectors: [[\"\", \"bsDatepickerDayDecorator\", \"\"]],\n    hostVars: 16,\n    hostBindings: function BsDatepickerDayDecoratorComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"disabled\", ctx.day.isDisabled)(\"is-highlighted\", ctx.day.isHovered)(\"is-other-month\", ctx.day.isOtherMonth)(\"is-active-other-month\", ctx.day.isOtherMonthHovered)(\"in-range\", ctx.day.isInRange)(\"select-start\", ctx.day.isSelectionStart)(\"select-end\", ctx.day.isSelectionEnd)(\"selected\", ctx.day.isSelected);\n      }\n    },\n    inputs: {\n      day: \"day\"\n    },\n    attrs: _c2,\n    decls: 1,\n    vars: 1,\n    template: function BsDatepickerDayDecoratorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtext(0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵtextInterpolate(ctx.day.label);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  /** @nocollapse */\n\n  return BsDatepickerDayDecoratorComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @enum {number} */\n\n\nconst BsNavigationDirection = {\n  UP: 0,\n  DOWN: 1\n};\nBsNavigationDirection[BsNavigationDirection.UP] = 'UP';\nBsNavigationDirection[BsNavigationDirection.DOWN] = 'DOWN';\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nlet BsDatepickerNavigationViewComponent = /*#__PURE__*/(() => {\n  class BsDatepickerNavigationViewComponent {\n    constructor() {\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n    }\n    /**\n     * @param {?} down\n     * @return {?}\n     */\n\n\n    navTo(down) {\n      this.onNavigate.emit(down ? BsNavigationDirection.DOWN : BsNavigationDirection.UP);\n    }\n    /**\n     * @param {?} viewMode\n     * @return {?}\n     */\n\n\n    view(viewMode) {\n      this.onViewMode.emit(viewMode);\n    }\n\n  }\n\n  BsDatepickerNavigationViewComponent.ɵfac = function BsDatepickerNavigationViewComponent_Factory(t) {\n    return new (t || BsDatepickerNavigationViewComponent)();\n  };\n\n  BsDatepickerNavigationViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDatepickerNavigationViewComponent,\n    selectors: [[\"bs-datepicker-navigation-view\"]],\n    inputs: {\n      calendar: \"calendar\"\n    },\n    outputs: {\n      onNavigate: \"onNavigate\",\n      onViewMode: \"onViewMode\"\n    },\n    decls: 13,\n    vars: 8,\n    consts: [[\"type\", \"button\", 1, \"previous\", 3, \"disabled\", \"click\"], [\"class\", \"current\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"current\", 3, \"click\"], [\"type\", \"button\", 1, \"next\", 3, \"disabled\", \"click\"]],\n    template: function BsDatepickerNavigationViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"button\", 0);\n        ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_0_listener() {\n          return ctx.navTo(true);\n        });\n        ɵngcc0.ɵɵelementStart(1, \"span\");\n        ɵngcc0.ɵɵtext(2, \"\\u2039\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtext(3, \" \\u200B \");\n        ɵngcc0.ɵɵtemplate(4, BsDatepickerNavigationViewComponent_button_4_Template, 3, 1, \"button\", 1);\n        ɵngcc0.ɵɵtext(5, \" \\u200B \");\n        ɵngcc0.ɵɵelementStart(6, \"button\", 2);\n        ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_6_listener() {\n          return ctx.view(\"year\");\n        });\n        ɵngcc0.ɵɵelementStart(7, \"span\");\n        ɵngcc0.ɵɵtext(8);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtext(9, \" \\u200B \");\n        ɵngcc0.ɵɵelementStart(10, \"button\", 3);\n        ɵngcc0.ɵɵlistener(\"click\", function BsDatepickerNavigationViewComponent_Template_button_click_10_listener() {\n          return ctx.navTo(false);\n        });\n        ɵngcc0.ɵɵelementStart(11, \"span\");\n        ɵngcc0.ɵɵtext(12, \"\\u203A\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵstyleProp(\"visibility\", ctx.calendar.hideLeftArrow ? \"hidden\" : \"visible\");\n        ɵngcc0.ɵɵproperty(\"disabled\", ctx.calendar.disableLeftArrow);\n        ɵngcc0.ɵɵadvance(4);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.calendar.monthTitle);\n        ɵngcc0.ɵɵadvance(4);\n        ɵngcc0.ɵɵtextInterpolate(ctx.calendar.yearTitle);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵstyleProp(\"visibility\", ctx.calendar.hideRightArrow ? \"hidden\" : \"visible\");\n        ɵngcc0.ɵɵproperty(\"disabled\", ctx.calendar.disableRightArrow);\n      }\n    },\n    directives: [ɵngcc2.NgIf],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return BsDatepickerNavigationViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsDaysCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsDaysCalendarViewComponent {\n    /**\n     * @param {?} _config\n     */\n    constructor(_config) {\n      this._config = _config;\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n      this.onHoverWeek = new EventEmitter();\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    navigateTo(event) {\n      /** @type {?} */\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          month: step\n        }\n      });\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    selectDay(event) {\n      this.onSelect.emit(event);\n    }\n    /**\n     * @param {?} week\n     * @return {?}\n     */\n\n\n    selectWeek(week) {\n      if (!this._config.selectWeek) {\n        return;\n      }\n\n      if (week.days && week.days[0] && !week.days[0].isDisabled && this._config.selectFromOtherMonth) {\n        this.onSelect.emit(week.days[0]);\n        return;\n      }\n\n      if (week.days.length === 0) {\n        return;\n      }\n      /** @type {?} */\n\n\n      const selectedDay = week.days.find(\n      /**\n      * @param {?} day\n      * @return {?}\n      */\n      day => {\n        return this._config.selectFromOtherMonth ? !day.isDisabled : !day.isOtherMonth && !day.isDisabled;\n      });\n      this.onSelect.emit(selectedDay);\n    }\n    /**\n     * @param {?} cell\n     * @param {?} isHovered\n     * @return {?}\n     */\n\n\n    weekHoverHandler(cell, isHovered) {\n      if (!this._config.selectWeek) {\n        return;\n      }\n      /** @type {?} */\n\n\n      const hasActiveDays = cell.days.find(\n      /**\n      * @param {?} day\n      * @return {?}\n      */\n      day => {\n        return this._config.selectFromOtherMonth ? !day.isDisabled : !day.isOtherMonth && !day.isDisabled;\n      });\n\n      if (hasActiveDays) {\n        cell.isHovered = isHovered;\n        this.isWeekHovered = isHovered;\n        this.onHoverWeek.emit(cell);\n      }\n    }\n    /**\n     * @param {?} cell\n     * @param {?} isHovered\n     * @return {?}\n     */\n\n\n    hoverDay(cell, isHovered) {\n      if (this._config.selectFromOtherMonth && cell.isOtherMonth) {\n        cell.isOtherMonthHovered = isHovered;\n      }\n\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n\n  }\n\n  BsDaysCalendarViewComponent.ɵfac = function BsDaysCalendarViewComponent_Factory(t) {\n    return new (t || BsDaysCalendarViewComponent)(ɵngcc0.ɵɵdirectiveInject(BsDatepickerConfig));\n  };\n\n  BsDaysCalendarViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsDaysCalendarViewComponent,\n    selectors: [[\"bs-days-calendar-view\"]],\n    inputs: {\n      calendar: \"calendar\",\n      options: \"options\"\n    },\n    outputs: {\n      onNavigate: \"onNavigate\",\n      onViewMode: \"onViewMode\",\n      onSelect: \"onSelect\",\n      onHover: \"onHover\",\n      onHoverWeek: \"onHoverWeek\"\n    },\n    decls: 9,\n    vars: 4,\n    consts: [[3, \"calendar\", \"onNavigate\", \"onViewMode\"], [\"role\", \"grid\", 1, \"days\", \"weeks\"], [4, \"ngIf\"], [\"aria-label\", \"weekday\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngFor\", \"ngForOf\"], [\"aria-label\", \"weekday\"], [\"class\", \"week\", 3, \"active-week\", 4, \"ngIf\"], [\"role\", \"gridcell\", 4, \"ngFor\", \"ngForOf\"], [1, \"week\"], [3, \"click\", \"mouseenter\", \"mouseleave\"], [\"role\", \"gridcell\"], [\"bsDatepickerDayDecorator\", \"\", 3, \"day\", \"click\", \"mouseenter\", \"mouseleave\"]],\n    template: function BsDaysCalendarViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"bs-calendar-layout\");\n        ɵngcc0.ɵɵelementStart(1, \"bs-datepicker-navigation-view\", 0);\n        ɵngcc0.ɵɵlistener(\"onNavigate\", function BsDaysCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n          return ctx.navigateTo($event);\n        })(\"onViewMode\", function BsDaysCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n          return ctx.changeViewMode($event);\n        });\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(2, \"table\", 1);\n        ɵngcc0.ɵɵelementStart(3, \"thead\");\n        ɵngcc0.ɵɵelementStart(4, \"tr\");\n        ɵngcc0.ɵɵtemplate(5, BsDaysCalendarViewComponent_th_5_Template, 1, 0, \"th\", 2);\n        ɵngcc0.ɵɵtemplate(6, BsDaysCalendarViewComponent_th_6_Template, 2, 1, \"th\", 3);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(7, \"tbody\");\n        ɵngcc0.ɵɵtemplate(8, BsDaysCalendarViewComponent_tr_8_Template, 3, 2, \"tr\", 4);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"calendar\", ctx.calendar);\n        ɵngcc0.ɵɵadvance(4);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.showWeekNumbers);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.calendar.weekdays);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.calendar.weeks);\n      }\n    },\n    directives: [BsCalendarLayoutComponent, BsDatepickerNavigationViewComponent, ɵngcc2.NgIf, ɵngcc2.NgForOf, BsDatepickerDayDecoratorComponent],\n    encapsulation: 2\n  });\n  /** @nocollapse */\n\n  return BsDaysCalendarViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsMonthCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsMonthCalendarViewComponent {\n    constructor() {\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    navigateTo(event) {\n      /** @type {?} */\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          year: step\n        }\n      });\n    }\n    /**\n     * @param {?} month\n     * @return {?}\n     */\n\n\n    viewMonth(month) {\n      this.onSelect.emit(month);\n    }\n    /**\n     * @param {?} cell\n     * @param {?} isHovered\n     * @return {?}\n     */\n\n\n    hoverMonth(cell, isHovered) {\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n\n  }\n\n  BsMonthCalendarViewComponent.ɵfac = function BsMonthCalendarViewComponent_Factory(t) {\n    return new (t || BsMonthCalendarViewComponent)();\n  };\n\n  BsMonthCalendarViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsMonthCalendarViewComponent,\n    selectors: [[\"bs-month-calendar-view\"]],\n    inputs: {\n      calendar: \"calendar\"\n    },\n    outputs: {\n      onNavigate: \"onNavigate\",\n      onViewMode: \"onViewMode\",\n      onSelect: \"onSelect\",\n      onHover: \"onHover\"\n    },\n    decls: 5,\n    vars: 2,\n    consts: [[3, \"calendar\", \"onNavigate\", \"onViewMode\"], [\"role\", \"grid\", 1, \"months\"], [4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"disabled\", \"is-highlighted\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"click\", \"mouseenter\", \"mouseleave\"]],\n    template: function BsMonthCalendarViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"bs-calendar-layout\");\n        ɵngcc0.ɵɵelementStart(1, \"bs-datepicker-navigation-view\", 0);\n        ɵngcc0.ɵɵlistener(\"onNavigate\", function BsMonthCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n          return ctx.navigateTo($event);\n        })(\"onViewMode\", function BsMonthCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n          return ctx.changeViewMode($event);\n        });\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(2, \"table\", 1);\n        ɵngcc0.ɵɵelementStart(3, \"tbody\");\n        ɵngcc0.ɵɵtemplate(4, BsMonthCalendarViewComponent_tr_4_Template, 2, 1, \"tr\", 2);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"calendar\", ctx.calendar);\n        ɵngcc0.ɵɵadvance(3);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.calendar.months);\n      }\n    },\n    directives: [BsCalendarLayoutComponent, BsDatepickerNavigationViewComponent, ɵngcc2.NgForOf],\n    encapsulation: 2\n  });\n  return BsMonthCalendarViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsTimepickerViewComponent = /*#__PURE__*/(() => {\n  class BsTimepickerViewComponent {\n    constructor() {\n      this.ampm = 'ok';\n      this.hours = 0;\n      this.minutes = 0;\n    }\n\n  }\n\n  BsTimepickerViewComponent.ɵfac = function BsTimepickerViewComponent_Factory(t) {\n    return new (t || BsTimepickerViewComponent)();\n  };\n\n  BsTimepickerViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsTimepickerViewComponent,\n    selectors: [[\"bs-timepicker\"]],\n    decls: 16,\n    vars: 3,\n    consts: [[1, \"bs-timepicker-container\"], [1, \"bs-timepicker-controls\"], [\"type\", \"button\", 1, \"bs-decrease\"], [\"type\", \"text\", \"placeholder\", \"00\", 3, \"value\"], [\"type\", \"button\", 1, \"bs-increase\"], [\"type\", \"button\", 1, \"switch-time-format\"], [\"src\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAKCAYAAABi8KSDAAABSElEQVQYV3XQPUvDUBQG4HNuagtVqc6KgouCv6GIuIntYBLB9hcIQpLStCAIV7DYmpTcRWcXqZio3Vwc/UCc/QEqfgyKGbr0I7nS1EiHeqYzPO/h5SD0jaxUZjmSLCB+OFb+UFINFwASAEAdpu9gaGXVyAHHFQBkHpKHc6a9dzECvADyY9sqlAMsK9W0jzxDXqeytr3mhQckxSji27TJJ5/rPmIpwJJq3HrtduriYOurv1a4i1p5HnhkG9OFymi0ReoO05cGwb+ayv4dysVygjeFmsP05f8wpZQ8fsdvfmuY9zjWSNqUtgYFVnOVReILYoBFzdQI5/GGFzNHhGbeZnopDGU29sZbscgldmC99w35VOATTycIMMcBXIfpSVGzZhA6C8hh00conln6VQ9TGgV32OEAKQC4DrBq7CJwd0ggR7Vq/rPrfgB+C3sGypY5DAAAAABJRU5ErkJggg==\", \"alt\", \"\"]],\n    template: function BsTimepickerViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"div\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"div\", 1);\n        ɵngcc0.ɵɵelementStart(2, \"button\", 2);\n        ɵngcc0.ɵɵtext(3, \"-\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelement(4, \"input\", 3);\n        ɵngcc0.ɵɵelementStart(5, \"button\", 4);\n        ɵngcc0.ɵɵtext(6, \"+\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(7, \"div\", 1);\n        ɵngcc0.ɵɵelementStart(8, \"button\", 2);\n        ɵngcc0.ɵɵtext(9, \"-\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelement(10, \"input\", 3);\n        ɵngcc0.ɵɵelementStart(11, \"button\", 4);\n        ɵngcc0.ɵɵtext(12, \"+\");\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(13, \"button\", 5);\n        ɵngcc0.ɵɵtext(14);\n        ɵngcc0.ɵɵelement(15, \"img\", 6);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(4);\n        ɵngcc0.ɵɵproperty(\"value\", ctx.hours);\n        ɵngcc0.ɵɵadvance(6);\n        ɵngcc0.ɵɵproperty(\"value\", ctx.minutes);\n        ɵngcc0.ɵɵadvance(4);\n        ɵngcc0.ɵɵtextInterpolate1(\"\", ctx.ampm, \" \");\n      }\n    },\n    encapsulation: 2\n  });\n  return BsTimepickerViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet BsYearsCalendarViewComponent = /*#__PURE__*/(() => {\n  class BsYearsCalendarViewComponent {\n    constructor() {\n      this.onNavigate = new EventEmitter();\n      this.onViewMode = new EventEmitter();\n      this.onSelect = new EventEmitter();\n      this.onHover = new EventEmitter();\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    navigateTo(event) {\n      /** @type {?} */\n      const step = BsNavigationDirection.DOWN === event ? -1 : 1;\n      this.onNavigate.emit({\n        step: {\n          year: step * yearsPerCalendar\n        }\n      });\n    }\n    /**\n     * @param {?} year\n     * @return {?}\n     */\n\n\n    viewYear(year) {\n      this.onSelect.emit(year);\n    }\n    /**\n     * @param {?} cell\n     * @param {?} isHovered\n     * @return {?}\n     */\n\n\n    hoverYear(cell, isHovered) {\n      this.onHover.emit({\n        cell,\n        isHovered\n      });\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    changeViewMode(event) {\n      this.onViewMode.emit(event);\n    }\n\n  }\n\n  BsYearsCalendarViewComponent.ɵfac = function BsYearsCalendarViewComponent_Factory(t) {\n    return new (t || BsYearsCalendarViewComponent)();\n  };\n\n  BsYearsCalendarViewComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: BsYearsCalendarViewComponent,\n    selectors: [[\"bs-years-calendar-view\"]],\n    inputs: {\n      calendar: \"calendar\"\n    },\n    outputs: {\n      onNavigate: \"onNavigate\",\n      onViewMode: \"onViewMode\",\n      onSelect: \"onSelect\",\n      onHover: \"onHover\"\n    },\n    decls: 5,\n    vars: 2,\n    consts: [[3, \"calendar\", \"onNavigate\", \"onViewMode\"], [\"role\", \"grid\", 1, \"years\"], [4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"disabled\", \"is-highlighted\", \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 3, \"click\", \"mouseenter\", \"mouseleave\"]],\n    template: function BsYearsCalendarViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"bs-calendar-layout\");\n        ɵngcc0.ɵɵelementStart(1, \"bs-datepicker-navigation-view\", 0);\n        ɵngcc0.ɵɵlistener(\"onNavigate\", function BsYearsCalendarViewComponent_Template_bs_datepicker_navigation_view_onNavigate_1_listener($event) {\n          return ctx.navigateTo($event);\n        })(\"onViewMode\", function BsYearsCalendarViewComponent_Template_bs_datepicker_navigation_view_onViewMode_1_listener($event) {\n          return ctx.changeViewMode($event);\n        });\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementStart(2, \"table\", 1);\n        ɵngcc0.ɵɵelementStart(3, \"tbody\");\n        ɵngcc0.ɵɵtemplate(4, BsYearsCalendarViewComponent_tr_4_Template, 2, 1, \"tr\", 2);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"calendar\", ctx.calendar);\n        ɵngcc0.ɵɵadvance(3);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.calendar.years);\n      }\n    },\n    directives: [BsCalendarLayoutComponent, BsDatepickerNavigationViewComponent, ɵngcc2.NgForOf],\n    encapsulation: 2\n  });\n  return BsYearsCalendarViewComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst _exports = [BsDatepickerContainerComponent, BsDatepickerDirective, BsDatepickerInlineContainerComponent, BsDatepickerInlineDirective, BsDatepickerInputDirective, BsDaterangepickerContainerComponent, BsDaterangepickerDirective, BsDaterangepickerInlineContainerComponent, BsDaterangepickerInlineDirective, BsDaterangepickerInputDirective];\nlet BsDatepickerModule = /*#__PURE__*/(() => {\n  class BsDatepickerModule {\n    /**\n     * @return {?}\n     */\n    static forRoot() {\n      return {\n        ngModule: BsDatepickerModule,\n        providers: [ComponentLoaderFactory, PositioningService, BsDatepickerStore, BsDatepickerActions, BsDatepickerConfig, BsDaterangepickerConfig, BsDatepickerInlineConfig, BsDaterangepickerInlineConfig, BsDatepickerEffects, BsLocaleService]\n      };\n    }\n\n  }\n\n  BsDatepickerModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: BsDatepickerModule\n  });\n  BsDatepickerModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function BsDatepickerModule_Factory(t) {\n      return new (t || BsDatepickerModule)();\n    },\n    imports: [[CommonModule]]\n  });\n  return BsDatepickerModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(BsDatepickerModule, {\n    declarations: function () {\n      return [BsCalendarLayoutComponent, BsCurrentDateViewComponent, BsCustomDatesViewComponent, BsDatepickerDayDecoratorComponent, BsDatepickerNavigationViewComponent, BsDaysCalendarViewComponent, BsMonthCalendarViewComponent, BsTimepickerViewComponent, BsYearsCalendarViewComponent, BsDatepickerContainerComponent, BsDatepickerDirective, BsDatepickerInlineContainerComponent, BsDatepickerInlineDirective, BsDatepickerInputDirective, BsDaterangepickerContainerComponent, BsDaterangepickerDirective, BsDaterangepickerInlineContainerComponent, BsDaterangepickerInlineDirective, BsDaterangepickerInputDirective];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [BsDatepickerContainerComponent, BsDatepickerDirective, BsDatepickerInlineContainerComponent, BsDatepickerInlineDirective, BsDatepickerInputDirective, BsDaterangepickerContainerComponent, BsDaterangepickerDirective, BsDaterangepickerInlineContainerComponent, BsDaterangepickerInlineDirective, BsDaterangepickerInputDirective];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nclass DateFormatter {\n  /**\n   * @param {?} date\n   * @param {?} format\n   * @param {?} locale\n   * @return {?}\n   */\n  format(date, format, locale) {\n    return formatDate(date, format, locale);\n  }\n\n}\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet DatePickerInnerComponent = /*#__PURE__*/(() => {\n  class DatePickerInnerComponent {\n    constructor() {\n      this.selectionDone = new EventEmitter(undefined);\n      this.update = new EventEmitter(false);\n      this.activeDateChange = new EventEmitter(undefined);\n      /* tslint:disable-next-line: no-any*/\n\n      this.stepDay = {};\n      /* tslint:disable-next-line: no-any*/\n\n      this.stepMonth = {};\n      /* tslint:disable-next-line: no-any*/\n\n      this.stepYear = {};\n      this.modes = ['day', 'month', 'year'];\n      this.dateFormatter = new DateFormatter();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get activeDate() {\n      return this._activeDate;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set activeDate(value) {\n      this._activeDate = value;\n    } // todo: add formatter value to Date object\n\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      // todo: use date for unique value\n      this.uniqueId = `datepicker--${Math.floor(Math.random() * 10000)}`;\n\n      if (this.initDate) {\n        this.activeDate = this.initDate;\n        this.selectedDate = new Date(this.activeDate.valueOf());\n        this.update.emit(this.activeDate);\n      } else if (this.activeDate === undefined) {\n        this.activeDate = new Date();\n      }\n    } // this.refreshView should be called here to reflect the changes on the fly\n    // tslint:disable-next-line:no-unused-variable\n\n    /**\n     * @param {?} changes\n     * @return {?}\n     */\n\n\n    ngOnChanges(changes) {\n      this.refreshView();\n      this.checkIfActiveDateGotUpdated(changes.activeDate);\n    } // Check if activeDate has been update and then emit the activeDateChange with the new date\n\n    /* tslint:disable-next-line: no-any */\n\n    /**\n     * @param {?} activeDate\n     * @return {?}\n     */\n\n\n    checkIfActiveDateGotUpdated(activeDate) {\n      if (activeDate && !activeDate.firstChange) {\n        /** @type {?} */\n        const previousValue = activeDate.previousValue;\n\n        if (previousValue && previousValue instanceof Date && previousValue.getTime() !== activeDate.currentValue.getTime()) {\n          this.activeDateChange.emit(this.activeDate);\n        }\n      }\n    }\n    /**\n     * @param {?} handler\n     * @param {?} type\n     * @return {?}\n     */\n\n\n    setCompareHandler(handler, type) {\n      if (type === 'day') {\n        this.compareHandlerDay = handler;\n      }\n\n      if (type === 'month') {\n        this.compareHandlerMonth = handler;\n      }\n\n      if (type === 'year') {\n        this.compareHandlerYear = handler;\n      }\n    }\n    /**\n     * @param {?} date1\n     * @param {?} date2\n     * @return {?}\n     */\n\n\n    compare(date1, date2) {\n      if (date1 === undefined || date2 === undefined) {\n        return undefined;\n      }\n\n      if (this.datepickerMode === 'day' && this.compareHandlerDay) {\n        return this.compareHandlerDay(date1, date2);\n      }\n\n      if (this.datepickerMode === 'month' && this.compareHandlerMonth) {\n        return this.compareHandlerMonth(date1, date2);\n      }\n\n      if (this.datepickerMode === 'year' && this.compareHandlerYear) {\n        return this.compareHandlerYear(date1, date2);\n      }\n\n      return void 0;\n    }\n    /**\n     * @param {?} handler\n     * @param {?} type\n     * @return {?}\n     */\n\n\n    setRefreshViewHandler(handler, type) {\n      if (type === 'day') {\n        this.refreshViewHandlerDay = handler;\n      }\n\n      if (type === 'month') {\n        this.refreshViewHandlerMonth = handler;\n      }\n\n      if (type === 'year') {\n        this.refreshViewHandlerYear = handler;\n      }\n    }\n    /**\n     * @return {?}\n     */\n\n\n    refreshView() {\n      if (this.datepickerMode === 'day' && this.refreshViewHandlerDay) {\n        this.refreshViewHandlerDay();\n      }\n\n      if (this.datepickerMode === 'month' && this.refreshViewHandlerMonth) {\n        this.refreshViewHandlerMonth();\n      }\n\n      if (this.datepickerMode === 'year' && this.refreshViewHandlerYear) {\n        this.refreshViewHandlerYear();\n      }\n    }\n    /**\n     * @param {?} date\n     * @param {?} format\n     * @return {?}\n     */\n\n\n    dateFilter(date, format) {\n      return this.dateFormatter.format(date, format, this.locale);\n    }\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} dateObject\n     * @return {?}\n     */\n\n\n    isActive(dateObject) {\n      if (this.compare(dateObject.date, this.activeDate) === 0) {\n        this.activeDateId = dateObject.uid;\n        return true;\n      }\n\n      return false;\n    }\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} date\n     * @param {?} format\n     * @return {?}\n     */\n\n\n    createDateObject(date, format) {\n      /* tslint:disable-next-line: no-any*/\n\n      /** @type {?} */\n      const dateObject = {};\n      dateObject.date = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n      dateObject.date = this.fixTimeZone(dateObject.date);\n      dateObject.label = this.dateFilter(date, format);\n      dateObject.selected = this.compare(date, this.selectedDate) === 0;\n      dateObject.disabled = this.isDisabled(date);\n      dateObject.current = this.compare(date, new Date()) === 0;\n      dateObject.customClass = this.getCustomClassForDate(dateObject.date);\n      return dateObject;\n    }\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} arr\n     * @param {?} size\n     * @return {?}\n     */\n\n\n    split(arr, size) {\n      /* tslint:disable-next-line: no-any*/\n\n      /** @type {?} */\n      const arrays = [];\n\n      while (arr.length > 0) {\n        arrays.push(arr.splice(0, size));\n      }\n\n      return arrays;\n    } // Fix a hard-reproducible bug with timezones\n    // The bug depends on OS, browser, current timezone and current date\n    // i.e.\n    // var date = new Date(2014, 0, 1);\n    // console.log(date.getFullYear(), date.getMonth(), date.getDate(),\n    // date.getHours()); can result in \"2013 11 31 23\" because of the bug.\n\n    /**\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    fixTimeZone(date) {\n      /** @type {?} */\n      const hours = date.getHours();\n      return new Date(date.getFullYear(), date.getMonth(), date.getDate(), hours === 23 ? hours + 2 : 0);\n    }\n    /**\n     * @param {?} date\n     * @param {?=} isManual\n     * @return {?}\n     */\n\n\n    select(date, isManual = true) {\n      if (this.datepickerMode === this.minMode) {\n        if (!this.activeDate) {\n          this.activeDate = new Date(0, 0, 0, 0, 0, 0, 0);\n        }\n\n        this.activeDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n        this.activeDate = this.fixTimeZone(this.activeDate);\n\n        if (isManual) {\n          this.selectionDone.emit(this.activeDate);\n        }\n      } else {\n        this.activeDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n        this.activeDate = this.fixTimeZone(this.activeDate);\n\n        if (isManual) {\n          this.datepickerMode = this.modes[this.modes.indexOf(this.datepickerMode) - 1];\n        }\n      }\n\n      this.selectedDate = new Date(this.activeDate.valueOf());\n      this.update.emit(this.activeDate);\n      this.refreshView();\n    }\n    /**\n     * @param {?} direction\n     * @return {?}\n     */\n\n\n    move(direction) {\n      /* tslint:disable-next-line: no-any*/\n\n      /** @type {?} */\n      let expectedStep;\n\n      if (this.datepickerMode === 'day') {\n        expectedStep = this.stepDay;\n      }\n\n      if (this.datepickerMode === 'month') {\n        expectedStep = this.stepMonth;\n      }\n\n      if (this.datepickerMode === 'year') {\n        expectedStep = this.stepYear;\n      }\n\n      if (expectedStep) {\n        /** @type {?} */\n        const year = this.activeDate.getFullYear() + direction * (expectedStep.years || 0);\n        /** @type {?} */\n\n        const month = this.activeDate.getMonth() + direction * (expectedStep.months || 0);\n        this.activeDate = new Date(year, month, 1);\n        this.refreshView();\n        this.activeDateChange.emit(this.activeDate);\n      }\n    }\n    /**\n     * @param {?} _direction\n     * @return {?}\n     */\n\n\n    toggleMode(_direction) {\n      /** @type {?} */\n      const direction = _direction || 1;\n\n      if (this.datepickerMode === this.maxMode && direction === 1 || this.datepickerMode === this.minMode && direction === -1) {\n        return;\n      }\n\n      this.datepickerMode = this.modes[this.modes.indexOf(this.datepickerMode) + direction];\n      this.refreshView();\n    }\n    /**\n     * @protected\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    getCustomClassForDate(date) {\n      if (!this.customClass) {\n        return '';\n      } // todo: build a hash of custom classes, it will work faster\n\n      /** @type {?} */\n\n\n      const customClassObject = this.customClass.find(\n      /**\n      * @param {?} customClass\n      * @return {?}\n      */\n      customClass => {\n        return customClass.date.valueOf() === date.valueOf() && customClass.mode === this.datepickerMode;\n      }, this);\n      return customClassObject === undefined ? '' : customClassObject.clazz;\n    }\n    /**\n     * @protected\n     * @param {?} date1Disabled\n     * @param {?} date2\n     * @return {?}\n     */\n\n\n    compareDateDisabled(date1Disabled, date2) {\n      if (date1Disabled === undefined || date2 === undefined) {\n        return undefined;\n      }\n\n      if (date1Disabled.mode === 'day' && this.compareHandlerDay) {\n        return this.compareHandlerDay(date1Disabled.date, date2);\n      }\n\n      if (date1Disabled.mode === 'month' && this.compareHandlerMonth) {\n        return this.compareHandlerMonth(date1Disabled.date, date2);\n      }\n\n      if (date1Disabled.mode === 'year' && this.compareHandlerYear) {\n        return this.compareHandlerYear(date1Disabled.date, date2);\n      }\n\n      return undefined;\n    }\n    /**\n     * @protected\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    isDisabled(date) {\n      /** @type {?} */\n      let isDateDisabled = false;\n\n      if (this.dateDisabled) {\n        this.dateDisabled.forEach(\n        /**\n        * @param {?} disabledDate\n        * @return {?}\n        */\n        disabledDate => {\n          if (this.compareDateDisabled(disabledDate, date) === 0) {\n            isDateDisabled = true;\n          }\n        });\n      }\n\n      if (this.dayDisabled) {\n        isDateDisabled = isDateDisabled || this.dayDisabled.indexOf(date.getDay()) > -1;\n      }\n\n      return isDateDisabled || this.minDate && this.compare(date, this.minDate) < 0 || this.maxDate && this.compare(date, this.maxDate) > 0;\n    }\n\n  }\n\n  DatePickerInnerComponent.ɵfac = function DatePickerInnerComponent_Factory(t) {\n    return new (t || DatePickerInnerComponent)();\n  };\n\n  DatePickerInnerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: DatePickerInnerComponent,\n    selectors: [[\"datepicker-inner\"]],\n    inputs: {\n      activeDate: \"activeDate\",\n      datepickerMode: \"datepickerMode\",\n      locale: \"locale\",\n      startingDay: \"startingDay\",\n      yearRange: \"yearRange\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      minMode: \"minMode\",\n      maxMode: \"maxMode\",\n      showWeeks: \"showWeeks\",\n      formatDay: \"formatDay\",\n      formatMonth: \"formatMonth\",\n      formatYear: \"formatYear\",\n      formatDayHeader: \"formatDayHeader\",\n      formatDayTitle: \"formatDayTitle\",\n      formatMonthTitle: \"formatMonthTitle\",\n      onlyCurrentMonth: \"onlyCurrentMonth\",\n      shortcutPropagation: \"shortcutPropagation\",\n      customClass: \"customClass\",\n      monthColLimit: \"monthColLimit\",\n      yearColLimit: \"yearColLimit\",\n      dateDisabled: \"dateDisabled\",\n      dayDisabled: \"dayDisabled\",\n      initDate: \"initDate\"\n    },\n    outputs: {\n      selectionDone: \"selectionDone\",\n      update: \"update\",\n      activeDateChange: \"activeDateChange\"\n    },\n    features: [ɵngcc0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"well well-sm bg-faded p-a card\", \"role\", \"application\", 4, \"ngIf\"], [\"role\", \"application\", 1, \"well\", \"well-sm\", \"bg-faded\", \"p-a\", \"card\"]],\n    template: function DatePickerInnerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵtemplate(0, DatePickerInnerComponent_div_0_Template, 2, 0, \"div\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.datepickerMode);\n      }\n    },\n    directives: [ɵngcc2.NgIf],\n    encapsulation: 2\n  });\n  return DatePickerInnerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet DatepickerConfig = /*#__PURE__*/(() => {\n  class DatepickerConfig {\n    constructor() {\n      this.locale = 'en';\n      this.datepickerMode = 'day';\n      this.startingDay = 0;\n      this.yearRange = 20;\n      this.minMode = 'day';\n      this.maxMode = 'year';\n      this.showWeeks = true;\n      this.formatDay = 'DD';\n      this.formatMonth = 'MMMM';\n      this.formatYear = 'YYYY';\n      this.formatDayHeader = 'dd';\n      this.formatDayTitle = 'MMMM YYYY';\n      this.formatMonthTitle = 'YYYY';\n      this.onlyCurrentMonth = false;\n      this.monthColLimit = 3;\n      this.yearColLimit = 5;\n      this.shortcutPropagation = false;\n    }\n\n  }\n\n  DatepickerConfig.ɵfac = function DatepickerConfig_Factory(t) {\n    return new (t || DatepickerConfig)();\n  };\n\n  DatepickerConfig.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: DatepickerConfig,\n    factory: DatepickerConfig.ɵfac\n  });\n  return DatepickerConfig;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/** @type {?} */\n\n\nconst DATEPICKER_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n\n  /* tslint:disable-next-line: no-use-before-declare */\n  useExisting: forwardRef(\n  /**\n  * @return {?}\n  */\n  () => DatePickerComponent),\n  multi: true\n};\n/* tslint:disable:component-selector-name component-selector-type */\n\n/* tslint:enable:component-selector-name component-selector-type */\n\nlet DatePickerComponent = /*#__PURE__*/(() => {\n  class DatePickerComponent {\n    /**\n     * @param {?} config\n     */\n    constructor(config) {\n      /**\n       * sets datepicker mode, supports: `day`, `month`, `year`\n       */\n      this.datepickerMode = 'day';\n      /**\n       * if false week numbers will be hidden\n       */\n\n      this.showWeeks = true;\n      this.selectionDone = new EventEmitter(undefined);\n      /**\n       * callback to invoke when the activeDate is changed.\n       */\n\n      this.activeDateChange = new EventEmitter(undefined);\n      /* tslint:disable-next-line: no-any*/\n\n      this.onChange = Function.prototype;\n      /* tslint:disable-next-line: no-any*/\n\n      this.onTouched = Function.prototype;\n      this._now = new Date();\n      this.config = config;\n      this.configureOptions();\n    }\n    /**\n     * currently active date\n     * @return {?}\n     */\n\n\n    get activeDate() {\n      return this._activeDate || this._now;\n    }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    set activeDate(value) {\n      this._activeDate = value;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    configureOptions() {\n      Object.assign(this, this.config);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    onUpdate(event) {\n      this.activeDate = event;\n      this.onChange(event);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    onSelectionDone(event) {\n      this.selectionDone.emit(event);\n    }\n    /**\n     * @param {?} event\n     * @return {?}\n     */\n\n\n    onActiveDateChange(event) {\n      this.activeDateChange.emit(event);\n    } // todo: support null value\n\n    /* tslint:disable-next-line: no-any*/\n\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n\n\n    writeValue(value) {\n      if (this._datePicker.compare(value, this._activeDate) === 0) {\n        return;\n      }\n\n      if (value && value instanceof Date) {\n        this.activeDate = value;\n\n        this._datePicker.select(value, false);\n\n        return;\n      }\n\n      this.activeDate = value ? new Date(value) : void 0;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    /**\n     * @param {?} fn\n     * @return {?}\n     */\n\n\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n\n  }\n\n  DatePickerComponent.ɵfac = function DatePickerComponent_Factory(t) {\n    return new (t || DatePickerComponent)(ɵngcc0.ɵɵdirectiveInject(DatepickerConfig));\n  };\n\n  DatePickerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: DatePickerComponent,\n    selectors: [[\"datepicker\"]],\n    viewQuery: function DatePickerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵviewQuery(DatePickerInnerComponent, 3);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        ɵngcc0.ɵɵqueryRefresh(_t = ɵngcc0.ɵɵloadQuery()) && (ctx._datePicker = _t.first);\n      }\n    },\n    inputs: {\n      datepickerMode: \"datepickerMode\",\n      showWeeks: \"showWeeks\",\n      activeDate: \"activeDate\",\n      initDate: \"initDate\",\n      minDate: \"minDate\",\n      maxDate: \"maxDate\",\n      minMode: \"minMode\",\n      maxMode: \"maxMode\",\n      formatDay: \"formatDay\",\n      formatMonth: \"formatMonth\",\n      formatYear: \"formatYear\",\n      formatDayHeader: \"formatDayHeader\",\n      formatDayTitle: \"formatDayTitle\",\n      formatMonthTitle: \"formatMonthTitle\",\n      startingDay: \"startingDay\",\n      yearRange: \"yearRange\",\n      onlyCurrentMonth: \"onlyCurrentMonth\",\n      shortcutPropagation: \"shortcutPropagation\",\n      monthColLimit: \"monthColLimit\",\n      yearColLimit: \"yearColLimit\",\n      customClass: \"customClass\",\n      dateDisabled: \"dateDisabled\",\n      dayDisabled: \"dayDisabled\"\n    },\n    outputs: {\n      selectionDone: \"selectionDone\",\n      activeDateChange: \"activeDateChange\"\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([DATEPICKER_CONTROL_VALUE_ACCESSOR])],\n    decls: 4,\n    vars: 24,\n    consts: [[3, \"activeDate\", \"locale\", \"datepickerMode\", \"initDate\", \"minDate\", \"maxDate\", \"minMode\", \"maxMode\", \"showWeeks\", \"formatDay\", \"formatMonth\", \"formatYear\", \"formatDayHeader\", \"formatDayTitle\", \"formatMonthTitle\", \"startingDay\", \"yearRange\", \"customClass\", \"dateDisabled\", \"dayDisabled\", \"onlyCurrentMonth\", \"shortcutPropagation\", \"monthColLimit\", \"yearColLimit\", \"update\", \"selectionDone\", \"activeDateChange\"], [\"tabindex\", \"0\"]],\n    template: function DatePickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"datepicker-inner\", 0);\n        ɵngcc0.ɵɵlistener(\"update\", function DatePickerComponent_Template_datepicker_inner_update_0_listener($event) {\n          return ctx.onUpdate($event);\n        })(\"selectionDone\", function DatePickerComponent_Template_datepicker_inner_selectionDone_0_listener($event) {\n          return ctx.onSelectionDone($event);\n        })(\"activeDateChange\", function DatePickerComponent_Template_datepicker_inner_activeDateChange_0_listener($event) {\n          return ctx.onActiveDateChange($event);\n        });\n        ɵngcc0.ɵɵelement(1, \"daypicker\", 1);\n        ɵngcc0.ɵɵelement(2, \"monthpicker\", 1);\n        ɵngcc0.ɵɵelement(3, \"yearpicker\", 1);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"activeDate\", ctx.activeDate)(\"locale\", ctx.config.locale)(\"datepickerMode\", ctx.datepickerMode)(\"initDate\", ctx.initDate)(\"minDate\", ctx.minDate)(\"maxDate\", ctx.maxDate)(\"minMode\", ctx.minMode)(\"maxMode\", ctx.maxMode)(\"showWeeks\", ctx.showWeeks)(\"formatDay\", ctx.formatDay)(\"formatMonth\", ctx.formatMonth)(\"formatYear\", ctx.formatYear)(\"formatDayHeader\", ctx.formatDayHeader)(\"formatDayTitle\", ctx.formatDayTitle)(\"formatMonthTitle\", ctx.formatMonthTitle)(\"startingDay\", ctx.startingDay)(\"yearRange\", ctx.yearRange)(\"customClass\", ctx.customClass)(\"dateDisabled\", ctx.dateDisabled)(\"dayDisabled\", ctx.dayDisabled)(\"onlyCurrentMonth\", ctx.onlyCurrentMonth)(\"shortcutPropagation\", ctx.shortcutPropagation)(\"monthColLimit\", ctx.monthColLimit)(\"yearColLimit\", ctx.yearColLimit);\n      }\n    },\n    directives: function () {\n      return [DatePickerInnerComponent, DayPickerComponent, MonthPickerComponent, YearPickerComponent];\n    },\n    encapsulation: 2\n  });\n  /** @nocollapse */\n\n  return DatePickerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet DayPickerComponent = /*#__PURE__*/(() => {\n  class DayPickerComponent {\n    /**\n     * @param {?} datePicker\n     */\n    constructor(datePicker) {\n      this.labels = [];\n      this.rows = [];\n      this.weekNumbers = [];\n      this.datePicker = datePicker;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get isBs4() {\n      return !isBs3();\n    }\n    /*protected getDaysInMonth(year:number, month:number) {\n       return ((month === 1) && (year % 4 === 0) &&\n       ((year % 100 !== 0) || (year % 400 === 0))) ? 29 : DAYS_IN_MONTH[month];\n       }*/\n\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      /** @type {?} */\n      const self = this;\n      this.datePicker.stepDay = {\n        months: 1\n      };\n      this.datePicker.setRefreshViewHandler(\n      /**\n      * @return {?}\n      */\n      function () {\n        /** @type {?} */\n        const year = this.activeDate.getFullYear();\n        /** @type {?} */\n\n        const month = this.activeDate.getMonth();\n        /** @type {?} */\n\n        const firstDayOfMonth = new Date(year, month, 1);\n        /** @type {?} */\n\n        const difference = this.startingDay - firstDayOfMonth.getDay();\n        /** @type {?} */\n\n        const numDisplayedFromPreviousMonth = difference > 0 ? 7 - difference : -difference;\n        /** @type {?} */\n\n        const firstDate = new Date(firstDayOfMonth.getTime());\n\n        if (numDisplayedFromPreviousMonth > 0) {\n          firstDate.setDate(-numDisplayedFromPreviousMonth + 1);\n        } // 42 is the number of days on a six-week calendar\n\n        /** @type {?} */\n\n\n        const _days = self.getDates(firstDate, 42);\n        /** @type {?} */\n\n\n        const days = [];\n\n        for (let i = 0; i < 42; i++) {\n          /** @type {?} */\n          const _dateObject = this.createDateObject(_days[i], this.formatDay);\n\n          _dateObject.secondary = _days[i].getMonth() !== month;\n          _dateObject.uid = this.uniqueId + '-' + i;\n          days[i] = _dateObject;\n        }\n\n        self.labels = [];\n\n        for (let j = 0; j < 7; j++) {\n          self.labels[j] = {};\n          self.labels[j].abbr = this.dateFilter(days[j].date, this.formatDayHeader);\n          self.labels[j].full = this.dateFilter(days[j].date, 'EEEE');\n        }\n\n        self.title = this.dateFilter(this.activeDate, this.formatDayTitle);\n        self.rows = this.split(days, 7);\n\n        if (this.showWeeks) {\n          self.weekNumbers = [];\n          /** @type {?} */\n\n          const thursdayIndex = (4 + 7 - this.startingDay) % 7;\n          /** @type {?} */\n\n          const numWeeks = self.rows.length;\n\n          for (let curWeek = 0; curWeek < numWeeks; curWeek++) {\n            self.weekNumbers.push(self.getISO8601WeekNumber(self.rows[curWeek][thursdayIndex].date));\n          }\n        }\n      }, 'day');\n      this.datePicker.setCompareHandler(\n      /**\n      * @param {?} date1\n      * @param {?} date2\n      * @return {?}\n      */\n      function (date1, date2) {\n        /** @type {?} */\n        const d1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());\n        /** @type {?} */\n\n        const d2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());\n        return d1.getTime() - d2.getTime();\n      }, 'day');\n      this.datePicker.refreshView();\n    }\n    /**\n     * @protected\n     * @param {?} startDate\n     * @param {?} n\n     * @return {?}\n     */\n\n\n    getDates(startDate, n) {\n      /** @type {?} */\n      const dates = new Array(n);\n      /** @type {?} */\n\n      let current = new Date(startDate.getTime());\n      /** @type {?} */\n\n      let i = 0;\n      /** @type {?} */\n\n      let date;\n\n      while (i < n) {\n        date = new Date(current.getTime());\n        date = this.datePicker.fixTimeZone(date);\n        dates[i++] = date;\n        current = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);\n      }\n\n      return dates;\n    }\n    /**\n     * @protected\n     * @param {?} date\n     * @return {?}\n     */\n\n\n    getISO8601WeekNumber(date) {\n      /** @type {?} */\n      const checkDate = new Date(date.getTime()); // Thursday\n\n      checkDate.setDate(checkDate.getDate() + 4 - (checkDate.getDay() || 7));\n      /** @type {?} */\n\n      const time = checkDate.getTime(); // Compare with Jan 1\n\n      checkDate.setMonth(0);\n      checkDate.setDate(1);\n      return Math.floor(Math.round((time - checkDate.getTime()) / 86400000) / 7) + 1;\n    }\n\n  }\n\n  DayPickerComponent.ɵfac = function DayPickerComponent_Factory(t) {\n    return new (t || DayPickerComponent)(ɵngcc0.ɵɵdirectiveInject(DatePickerInnerComponent));\n  };\n\n  DayPickerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: DayPickerComponent,\n    selectors: [[\"daypicker\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"role\", \"grid\", \"aria-activedescendant\", \"activeDateId\", 4, \"ngIf\"], [\"role\", \"grid\", \"aria-activedescendant\", \"activeDateId\"], [\"type\", \"button\", \"class\", \"btn btn-default btn-secondary btn-sm pull-left float-left\", \"tabindex\", \"-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-secondary\", \"btn-sm\", 2, \"width\", \"100%\", 3, \"id\", \"disabled\", \"ngClass\", \"click\"], [\"type\", \"button\", \"class\", \"btn btn-default btn-secondary btn-sm pull-right float-right\", \"tabindex\", \"-1\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-secondary\", \"btn-sm\", \"pull-left\", \"float-left\", 3, \"click\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-secondary\", \"btn-sm\", \"pull-right\", \"float-right\", 3, \"click\"], [1, \"text-center\"], [\"aria-label\", \"labelz.full\"], [\"class\", \"h6\", \"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center\", \"role\", \"gridcell\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 1, \"text-center\", 3, \"id\"], [\"type\", \"button\", \"style\", \"min-width:100%;\", \"tabindex\", \"-1\", 3, \"class\", \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"tabindex\", \"-1\", 2, \"min-width\", \"100%\", 3, \"ngClass\", \"disabled\", \"click\"], [3, \"ngClass\"]],\n    template: function DayPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, DayPickerComponent_table_0_Template, 18, 15, \"table\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.datePicker.datepickerMode === \"day\");\n      }\n    },\n    directives: [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgForOf],\n    styles: [\"[_nghost-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n      color: #292b2c;\\n      background-color: #fff;\\n      border-color: #ccc;\\n    }\\n    [_nghost-%COMP%]   .btn-info[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n      color: #292b2c !important;\\n    }\"]\n  });\n  /** @nocollapse */\n\n  return DayPickerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet MonthPickerComponent = /*#__PURE__*/(() => {\n  class MonthPickerComponent {\n    /**\n     * @param {?} datePicker\n     */\n    constructor(datePicker) {\n      this.rows = [];\n      this.datePicker = datePicker;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get isBs4() {\n      return !isBs3();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      /** @type {?} */\n      const self = this;\n      this.datePicker.stepMonth = {\n        years: 1\n      };\n      this.datePicker.setRefreshViewHandler(\n      /**\n      * @return {?}\n      */\n      function () {\n        /** @type {?} */\n        const months = new Array(12);\n        /** @type {?} */\n\n        const year = this.activeDate.getFullYear();\n        /** @type {?} */\n\n        let date;\n\n        for (let i = 0; i < 12; i++) {\n          date = new Date(year, i, 1);\n          date = this.fixTimeZone(date);\n          months[i] = this.createDateObject(date, this.formatMonth);\n          months[i].uid = this.uniqueId + '-' + i;\n        }\n\n        self.title = this.dateFilter(this.activeDate, this.formatMonthTitle);\n        self.rows = this.split(months, self.datePicker.monthColLimit);\n      }, 'month');\n      this.datePicker.setCompareHandler(\n      /**\n      * @param {?} date1\n      * @param {?} date2\n      * @return {?}\n      */\n      function (date1, date2) {\n        /** @type {?} */\n        const d1 = new Date(date1.getFullYear(), date1.getMonth());\n        /** @type {?} */\n\n        const d2 = new Date(date2.getFullYear(), date2.getMonth());\n        return d1.getTime() - d2.getTime();\n      }, 'month');\n      this.datePicker.refreshView();\n    }\n\n  }\n\n  MonthPickerComponent.ɵfac = function MonthPickerComponent_Factory(t) {\n    return new (t || MonthPickerComponent)(ɵngcc0.ɵɵdirectiveInject(DatePickerInnerComponent));\n  };\n\n  MonthPickerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: MonthPickerComponent,\n    selectors: [[\"monthpicker\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"role\", \"grid\", 4, \"ngIf\"], [\"role\", \"grid\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", \"pull-left\", \"float-left\", 3, \"click\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", 2, \"width\", \"100%\", 3, \"id\", \"disabled\", \"ngClass\", \"click\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", \"pull-right\", \"float-right\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center\", \"role\", \"gridcell\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 1, \"text-center\", 3, \"ngClass\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", 2, \"min-width\", \"100%\", 3, \"ngClass\", \"disabled\", \"click\"], [3, \"ngClass\"]],\n    template: function MonthPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, MonthPickerComponent_table_0_Template, 15, 8, \"table\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.datePicker.datepickerMode === \"month\");\n      }\n    },\n    directives: [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgForOf],\n    styles: [_c9]\n  });\n  /** @nocollapse */\n\n  return MonthPickerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet YearPickerComponent = /*#__PURE__*/(() => {\n  class YearPickerComponent {\n    /**\n     * @param {?} datePicker\n     */\n    constructor(datePicker) {\n      this.rows = [];\n      this.datePicker = datePicker;\n    }\n    /**\n     * @return {?}\n     */\n\n\n    get isBs4() {\n      return !isBs3();\n    }\n    /**\n     * @return {?}\n     */\n\n\n    ngOnInit() {\n      /** @type {?} */\n      const self = this;\n      this.datePicker.stepYear = {\n        years: this.datePicker.yearRange\n      };\n      this.datePicker.setRefreshViewHandler(\n      /**\n      * @return {?}\n      */\n      function () {\n        /** @type {?} */\n        const years = new Array(this.yearRange);\n        /** @type {?} */\n\n        let date;\n        /** @type {?} */\n\n        const start = self.getStartingYear(this.activeDate.getFullYear());\n\n        for (let i = 0; i < this.yearRange; i++) {\n          date = new Date(start + i, 0, 1);\n          date = this.fixTimeZone(date);\n          years[i] = this.createDateObject(date, this.formatYear);\n          years[i].uid = this.uniqueId + '-' + i;\n        }\n\n        self.title = [years[0].label, years[this.yearRange - 1].label].join(' - ');\n        self.rows = this.split(years, self.datePicker.yearColLimit);\n      }, 'year');\n      this.datePicker.setCompareHandler(\n      /**\n      * @param {?} date1\n      * @param {?} date2\n      * @return {?}\n      */\n      function (date1, date2) {\n        return date1.getFullYear() - date2.getFullYear();\n      }, 'year');\n      this.datePicker.refreshView();\n    }\n    /**\n     * @protected\n     * @param {?} year\n     * @return {?}\n     */\n\n\n    getStartingYear(year) {\n      // todo: parseInt\n      return (year - 1) / this.datePicker.yearRange * this.datePicker.yearRange + 1;\n    }\n\n  }\n\n  YearPickerComponent.ɵfac = function YearPickerComponent_Factory(t) {\n    return new (t || YearPickerComponent)(ɵngcc0.ɵɵdirectiveInject(DatePickerInnerComponent));\n  };\n\n  YearPickerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: YearPickerComponent,\n    selectors: [[\"yearpicker\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"role\", \"grid\", 4, \"ngIf\"], [\"role\", \"grid\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", \"pull-left\", \"float-left\", 3, \"click\"], [\"role\", \"heading\", \"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", 2, \"width\", \"100%\", 3, \"id\", \"disabled\", \"ngClass\", \"click\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", \"btn-sm\", \"pull-right\", \"float-right\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center\", \"role\", \"gridcell\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"gridcell\", 1, \"text-center\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"btn\", \"btn-default\", 2, \"min-width\", \"100%\", 3, \"ngClass\", \"disabled\", \"click\"], [3, \"ngClass\"]],\n    template: function YearPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, YearPickerComponent_table_0_Template, 15, 8, \"table\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.datePicker.datepickerMode === \"year\");\n      }\n    },\n    directives: [ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc2.NgForOf],\n    styles: [_c9]\n  });\n  /** @nocollapse */\n\n  return YearPickerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n\nlet DatepickerModule = /*#__PURE__*/(() => {\n  class DatepickerModule {\n    /**\n     * @return {?}\n     */\n    static forRoot() {\n      return {\n        ngModule: DatepickerModule,\n        providers: [DatepickerConfig]\n      };\n    }\n\n  }\n\n  DatepickerModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: DatepickerModule\n  });\n  DatepickerModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function DatepickerModule_Factory(t) {\n      return new (t || DatepickerModule)();\n    },\n    imports: [[CommonModule, FormsModule]]\n  });\n  return DatepickerModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(DatepickerModule, {\n    declarations: function () {\n      return [DatePickerComponent, DatePickerInnerComponent, DayPickerComponent, MonthPickerComponent, YearPickerComponent];\n    },\n    imports: function () {\n      return [CommonModule, FormsModule];\n    },\n    exports: function () {\n      return [DatePickerComponent, DatePickerInnerComponent, DayPickerComponent, MonthPickerComponent, YearPickerComponent];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nexport { BsDatepickerConfig, BsDatepickerContainerComponent, BsDatepickerDirective, BsDatepickerInlineConfig, BsDatepickerInlineContainerComponent, BsDatepickerInlineDirective, BsDatepickerInputDirective, BsDatepickerModule, BsDaterangepickerConfig, BsDaterangepickerContainerComponent, BsDaterangepickerDirective, BsDaterangepickerInlineConfig, BsDaterangepickerInlineContainerComponent, BsDaterangepickerInlineDirective, BsDaterangepickerInputDirective, BsLocaleService, DateFormatter, DatePickerComponent, DatePickerInnerComponent, DatepickerConfig, DatepickerModule, DayPickerComponent, MonthPickerComponent, YearPickerComponent, DATEPICKER_CONTROL_VALUE_ACCESSOR as ɵa, BsDatepickerAbstractComponent as ɵb, BsDatepickerStore as ɵc, BsDatepickerEffects as ɵd, BsDatepickerActions as ɵe, datepickerAnimation as ɵf, BsCalendarLayoutComponent as ɵg, BsCurrentDateViewComponent as ɵh, BsCustomDatesViewComponent as ɵi, BsDatepickerDayDecoratorComponent as ɵj, BsDatepickerNavigationViewComponent as ɵk, BsDaysCalendarViewComponent as ɵl, BsMonthCalendarViewComponent as ɵm, BsTimepickerViewComponent as ɵn, BsYearsCalendarViewComponent as ɵo }; //# sourceMappingURL=ngx-bootstrap-datepicker.js.map", "map": null, "metadata": {}, "sourceType": "module"}