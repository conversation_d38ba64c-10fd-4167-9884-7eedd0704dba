{"ast": null, "code": "import copyObject from './_copyObject.js';\nimport getSymbolsIn from './_getSymbolsIn.js';\n/**\n * Copies own and inherited symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\n\nfunction copySymbolsIn(source, object) {\n  return copyObject(source, getSymbolsIn(source), object);\n}\n\nexport default copySymbolsIn;", "map": null, "metadata": {}, "sourceType": "module"}