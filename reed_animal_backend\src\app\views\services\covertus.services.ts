import { Injectable } from '@angular/core';

import { Observable, of } from 'rxjs';

import { Api } from '../Api';


@Injectable({
    providedIn: 'root'
})
export class CovertusService extends Api {

    //Get All Covertus List
    GetCovertusList(params: any): Observable<any> {
         return this.http.get(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`, { params });
    }

    //Edit or update Covertus
    UpdateCovertus(data: any): Observable<any> {
         return this.http.post(`${this.config.APIUrl4}/covertus?token=${localStorage.auth_token}`, data);
    }
}