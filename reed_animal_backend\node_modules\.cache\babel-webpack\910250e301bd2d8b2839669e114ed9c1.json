{"ast": null, "code": "import baseGet from './_baseGet.js';\n/**\n * A specialized version of `baseProperty` which supports deep paths.\n *\n * @private\n * @param {Array|string} path The path of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\n\nfunction basePropertyDeep(path) {\n  return function (object) {\n    return baseGet(object, path);\n  };\n}\n\nexport default basePropertyDeep;", "map": null, "metadata": {}, "sourceType": "module"}