{"ast": null, "code": "/**\n * The base implementation of `_.propertyOf` without support for deep paths.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyOf(object) {\n  return function (key) {\n    return object == null ? undefined : object[key];\n  };\n}\n\nexport default basePropertyOf;", "map": null, "metadata": {}, "sourceType": "module"}