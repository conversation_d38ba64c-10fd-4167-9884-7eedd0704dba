{"ast": null, "code": "/** Used to escape characters for inclusion in compiled string literals. */\nvar stringEscapes = {\n  '\\\\': '\\\\',\n  \"'\": \"'\",\n  '\\n': 'n',\n  '\\r': 'r',\n  '\\u2028': 'u2028',\n  '\\u2029': 'u2029'\n};\n/**\n * Used by `_.template` to escape characters for inclusion in compiled string literals.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\n\nfunction escapeStringChar(chr) {\n  return '\\\\' + stringEscapes[chr];\n}\n\nexport default escapeStringChar;", "map": null, "metadata": {}, "sourceType": "module"}