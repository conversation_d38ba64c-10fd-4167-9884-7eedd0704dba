{"ast": null, "code": "import isObject from './isObject.js';\n/** Built-in value references. */\n\nvar objectCreate = Object.create;\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\n\nvar baseCreate = function () {\n  function object() {}\n\n  return function (proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n\n    object.prototype = proto;\n    var result = new object();\n    object.prototype = undefined;\n    return result;\n  };\n}();\n\nexport default baseCreate;", "map": null, "metadata": {}, "sourceType": "module"}