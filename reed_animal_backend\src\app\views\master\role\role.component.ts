import { Component, OnInit, ViewChild, } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Role } from '../../models/role.models';
import { TokenStorageService } from '../../services/token-storage.service';
import { RoleService } from '../../services/role.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';

@Component({
  selector: 'app-role',
  templateUrl: './role.component.html',
  styleUrls: ['./role.component.scss']
})

export class RoleComponent implements OnInit {
  @ViewChild('AddModal') public AddModal: ModalDirective;
  @ViewChild('primaryModal') public primaryModal: ModalDirective;
  @ViewChild('deleteModal') public deleteModal: ModalDirective;

  permissions = [];
  roles = [];
  page = 1;
  count = 0;
  search = '';
  name = '';
  role: Role = {}
  rolefailed = false;
  Id: any
  Add = true;
  Edit = true;
  Delete = true;
  page1 = 1;
  count1 = 0;
  constructor(private roleService: RoleService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit() {
    this.tokens();
  }

  //clear modal window
  clear(): void {
    this.role = {};
    this.AddModal.hide();

  }

  //token verified module
  tokens(): void {
    const key = this.tokenStorage.getToken();
    const Role = this.tokenStorage.getUser();
    // if (key != null) {
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Role") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        }
      })
    this.GetRoleLists();
    // }
    // else {
    // this.router.navigate(['/login']);
    // }
  }


  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};

    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //page handle request
  getrequestparams1(page1: number): any {
    let skip: any = {};

    skip[`skip`] = (page1 - 1) * 6;
    // skip['limit'] = 6  
    return skip;
  }

  //Get All Role List
  GetRoleLists(): void {
    // console.log('search-->', this.name)
    const skip = this.getrequestparams(this.page);
    this.roleService.GetRoleList(skip, this.name)
      .subscribe((res: any) => {
        this.count = res.count - 1;
        var arr = []
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].name !== "Super Admin") {
            arr.push(res.data[i])
          }
        }
        this.roles = arr;
        // console.log(this.roles);
        // console.log(this.count);
      });
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    // console.log(this.page);
    this.GetRoleLists();
  }

  //Edit or update role 
  GetRole(id): void {
    // console.log('id-->', id);
    this.Id = id
    this.page1 = 1;
    this.count1 = 0;
    this.roleService.GetRoleDetail(id)
      .subscribe((res) => {
        this.role = res.data[0];
        // console.log(res.data)
      })
  }

  //Role based search in permission collection
  onChange(id): void {
    // console.log('search-->', this.search)
    const skip = this.getrequestparams1(this.page1);
    this.Permission.GetRoleDetails(id, skip)
      .subscribe((res: any) => {
        this.permissions = res.data;
        this.count1 = res.count
        // console.log(res)
      })
  }

  //Page handle 
  handlePageChange1(event: number) {
    this.page1 = event;
    // console.log(this.page);
    this.onChange(this.Id);
  }

  EditRole(id): void {
    // console.log('id-->', id, this.role.name)
    const data = {
      name: this.role.name
    }
    this.roleService.UpdateRole(id, data)
      .subscribe((res) => {
        // console.log('res-->', res);
        this.role = {};
        this.GetRoleLists();
      })
  }

  //Status ON & OFF
  changeing(active, id) {
    const data = { acc_activation: active };
    // console.log('data-->', data);
    this.roleService.UpdateRole(id, data)
      .subscribe((res: any) => {
        // console.log('res-->', res);
      })
  }

  //Add new Role
  AddRole(): void {
    // console.log('name-->', this.role.name)
    if (this.role.name != undefined && this.role.name != '') {
      const data = {
        name: this.role.name
      }
      this.roleService.NewRole(data)
        .subscribe((res) => {
          this.role = {};
          this.rolefailed = false
          this.AddModal.hide();
          // console.log('new-->', res)
          this.GetRoleLists();
        })
    } this.rolefailed = true

  }

  //Delete Role using id
  Deleterole(): void {
    // console.log('id-->', id)
    this.roleService.DeleteRole(this.Id)
      .subscribe((res) => {
        // console.log('res-->', res)
        this.GetRoleLists();
      })
  }
  GetRolee(id) {
    this.Id = id
  }


  //ON or OFF value in table
  changed(param, value, id, i): void {
    // console.log(param, value, id, i)
    const data1 = param
    const data = {
      [data1]: value
    }
    if (param == 'all') {
      const data = {
        status: value,
        delete: value,
        edit: value,
        add: value
      }
      this.permissions[i].delete = value
      this.permissions[i].edit = value
      this.permissions[i].add = value
      this.Permission.UpdatePermission(id, data)
        .subscribe((res: any) => {
        })
    }
    this.Permission.UpdatePermission(id, data)
      .subscribe((res: any) => {
      })

  }
}