<div class="app-body auth-login-sign">
  <main class="main d-flex align-items-center">
    <div class="container">
      <div class="row">
        <div class="col-md-5 mx-auto">
          <div class="card-group">
            <div class="card p-4">
              <div class="card-body">
                <form>
                  <h1 style="margin-bottom: 20px;">Forgot Password</h1>

                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="icon-envelope-letter icons"></i></span>
                    </div>
                    <input type="email" class="form-control form-control-lg" placeholder="Email" autocomplete="Email"
                      required [(ngModel)]="forgot.email" id="email" [ngModelOptions]="{standalone: true}" (click)="getfocus()">
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <button type="button" class="btn btn-primary px-4" (click)="ForgotPassword()">Submit</button>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12 text-right">
                      <button type="submit" class="btn btn-link px-0" [routerLink]="['/login']"><a href='javascript:;'>Login</a></button>
                    </div>
                  </div>
                </form>
                <div class="alert alert-danger" role="alert" *ngIf="isLoginFailed" style="margin-top: 10px;">
                  {{ data }}
                </div>
              </div>
            </div>
          
          </div>
        </div>
      </div>
    </div>
  </main>
</div>