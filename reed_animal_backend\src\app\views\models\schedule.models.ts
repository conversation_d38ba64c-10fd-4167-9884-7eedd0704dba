export class Appointment {
    user_id: [{
        "login_type": "";
        "social_media_id": "";
        "first_name": "";
        "last_name": "";
        "phone_number": "";
        "active": "";
        "delete": "";
        "pelfies": [];
        "tokens": "";
        "device_token": "";
        "stripe_id": "";
        "_id": "";
        "email": "";
        "createdAt": "";
        "updatedAt": "";
        "__v": 0;
        "password": "";
        "notification_status": "";
    }];
    doctor_id: [{
        "_id": "";
        "name": "";
        "email": "";
        "password": "";
        "role_id": "";
        "location": "";
        "address": "";
        "phone_no": "";
        "status": Boolean;
        "resetPasswordToken": "";
        "createdAt": "";
        "updatedAt": "";
        "__v": 0;
        "tokens": "";
    }];
    pet_id: [{
        "user_id": "";
        "_id": "";
        "pet_mid": "";
        "pet_name": "";
        "age": 12;
        "animal_type": "";
        "color": "";
        "breed": "";
        "dob": "";
        "spay": "";
        "gender": "";
        "current_vet": "";
        "owner_name": "";
        "createdAt": "";
        "updatedAt": "";
        "__v": 0;
        "image_url": "";
    }];
    payment_completed: Boolean;
    video_status: Boolean;
    notifi: <PERSON>olean;
    _id: "";
    kind_appointment: "";
    prefer: "";
    location: "";
    doctor_name = "";
    time: "";
    date: "";
    day: "";
    apt_date_time: "";
    pet_name: "";
    species: "";
    breed_name: "";
    status: "";
    name: "";
    createdAt: "";
    updatedAt: "";
    __v: 0;
    treatment: {
        "weight": "",
        "temp": "",
        "pulse": "",
        "resp": "",
        "vaccinationdata": {
            "DHP": {
                "checked": Boolean,
                "date": ""
            },
            "BORD": {
                "checked": Boolean,
                "date": ""
            },
            "LEPTO": {
                "checked": Boolean,
                "date": ""
            },
            "Rabies": {
                "checked": Boolean,
                "date": ""
            },
            "HWT": {
                "checked": Boolean,
                "date": ""
            },
            "Fecal": {
                "checked": Boolean,
                "date": ""
            },
            "Bloodwork": {
                "checked": Boolean,
                "date": ""
            },
            "Influenza": {
                "checked": Boolean,
                "date": ""
            }
        },
        "placePet": "",
        "activityPet": "",
        "weightchange": "",
        "drinkingHabits": "",
        "Stool": "",
        "UrinaryHabits": "",
        "Appetite": "",
        "diet": "",
        "flea": "",
        "suppliment": "",
        "EDUD": "",
        "CSVD": "",
        "RxRefill": {
            "Value": "",
            "index": null
        },
        "Dentalcare": {
            "Value": "",
            "index": null
        },
        "Nailtrim": {
            "Value": "",
            "index": ''
        },
        "notes": "",
        "RefillNotes": "",
        "DentalNotes": "",
        "NailTrimNotes": "",
        "roomno": "",
        "bcs": "",
        "crt": "",
        "diseaselist": {
            "General": 0,
            "EENT": 0,
            "Oral": 1,
            "Respiritory": 0,
            "Cardiovascular": 0,
            "GI/Abdomen": 0,
            "Musculoskel": 0,
            "Integument": 0,
            "Uro-Genital": 1,
            "Lymphatic": 0,
            "Neurologic": 0,
            "Endocrine": 0
        },
        "commonAsse": "",
        "plan": ""
    }
}