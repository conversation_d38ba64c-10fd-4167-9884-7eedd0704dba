<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        Module
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-12 my-3">
            <button *ngIf="Add" type="button" class="btn btn-primary mr-1" data-toggle="modal" (click)="AddModal.show()">
              Add Module
            </button>
            <div class="form-group table-search">
              <div class="input-group" style="top: 3px;">
                <div class="input-group-prepend">
                  <span class="input-group-text"><i class="fa fa-search" (click)="GetModuleLists()"></i></span>
                </div>
                <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" (input)="GetModuleLists()"
                [(ngModel)]="name">
              </div>
            </div>

          </div>
        </div>

        <table class="table table-striped">
          <thead>
            <tr>
              <th>Module Name</th>
              <th *ngIf="Edit">Status</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of modules| paginate: { id: 'listing_pagination',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };">
              <td>{{user.name}}</td>
              <td *ngIf="Edit"><label class="switch">
                  <input type="checkbox" checked="user.acc_activation" (change)="changed(user.acc_activation,user._id)" [(ngModel)]="user.acc_activation">
                  <span class="slider round"></span>
                </label></td>
              <td>
                <a data-toggle="modal" *ngIf="Edit" (click)="primaryModal.show();GetModule(user._id);" style="cursor: pointer;"><span
                    class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                <a data-toggle="modal" *ngIf="Delete" (click)="deleteModal.show();GetModulee(user._id);" style="cursor: pointer;"><span
                    class="badge badge-danger"><i class="fa fa-trash"></i> Delete</span></a>
              </td>
            </tr>
          </tbody>
        </table>
        <div>
          <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true"
            (pageChange)="handlePageChange($event)">
          </pagination-controls>
        </div>
      </div>

    </div>
  </div>
  <!--/.col-->
</div>

<!-- Add Modal -->
<div bsModal #AddModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" module="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" rodule="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Add Module</h4>
        <!-- <button type="button" class="close" (click)="primaryModal.hide()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="name">Module Name</label>
              <input type="text" id="module-name" placeholder="Enter your Module Name" class="form-control" autocomplete="off" required 
              [(ngModel)]="module.name" [ngModelOptions]="{standalone: true}" (keydown.enter)="AddModule()">
              <div *ngIf="rolefailed" style="font-size: smaller;color: red;">*please enter Module</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="AddModal.hide();clear();">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="AddModule();">Save</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Edit Modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" Module="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" Module="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Edit Module</h4>
        <!-- <button type="button" class="close" (click)="primaryModal.hide()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="name">Module Name</label>
              <input type="text" id="edit-name" placeholder="Enter your Module Name" class="form-control" autocomplete="off" required 
              [(ngModel)]="module.name" [ngModelOptions]="{standalone: true}" (keydown.enter)="EditModule(module.id);primaryModal.hide();">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="EditModule(module.id);primaryModal.hide();">Save</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Delete Modal -->
<div bsModal #deleteModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-danger modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Are you sure ?</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <p>Do you want to delete this Module?</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="deleteModal.hide()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="DeleteModule();deleteModal.hide();">Delete</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->