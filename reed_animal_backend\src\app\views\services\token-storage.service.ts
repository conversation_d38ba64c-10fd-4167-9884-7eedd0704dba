import { Injectable,Compiler } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth-user';
const MODULE_KEY = 'auth-module';

@Injectable({
  providedIn: 'root'
})
export class TokenStorageService {

  constructor(private _compiler: Compiler, private router: Router) { }

  signOut(): void {
    window.localStorage.clear();
    this._compiler.clearCache();
    this.router.navigate(['./login']);
  }

  public saveToken(token: string): void {
    window.localStorage.removeItem(TOKEN_KEY);
    window.localStorage.setItem(TOKEN_KEY, token);
  }

  public getToken(): string {
    return localStorage.getItem(TOKEN_KEY);
  }

  public saveUser(user): void {
    window.localStorage.removeItem(USER_KEY);
    window.localStorage.setItem(USER_KEY, JSON.stringify(user));
  }

  public getUser(): any { 
    return JSON.parse(localStorage.getItem(USER_KEY));
  }
  public saveModule(module): void {
    window.localStorage.removeItem(MODULE_KEY);
    window.localStorage.setItem(MODULE_KEY, JSON.stringify(module));
  }
  public getModule(): any { 
    return JSON.parse(localStorage.getItem(MODULE_KEY));
  }
  
}
