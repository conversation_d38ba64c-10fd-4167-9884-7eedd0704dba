import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { IconSetService } from '@coreui/icons-angular';
import { freeSet } from '@coreui/icons';
import { HTTPStatus } from './views/CommonInterceptor';
import { navItems } from './_nav';
import { INavData } from '@coreui/angular';

@Component({
  // tslint:disable-next-line
  selector: 'body',
  template: '<router-outlet></router-outlet>',
  providers: [IconSetService],
})

export class AppComponent implements OnInit {
  public loader = false;
  public navItems = navItems;
  navItem: INavData[] = [
  ]
  constructor(
    private router: Router,
    public iconSet: IconSetService,
    private httpStatus: HTTPStatus
  ) {
     iconSet.icons = { ...freeSet };
    this.getHttpResponse();
  }

  ngOnInit() {
    this.router.events.subscribe((evt) => {
      if (!(evt instanceof NavigationEnd)) {
        return;
      }
      window.scrollTo(0, 0);
    });

  }
  getHttpResponse() {
    this.httpStatus.getHttpStatus().subscribe((status: boolean) => {
      this.loader = status;
    });
  }
}