.confirm-color {
    background-color: green;
    padding: 7px;
    color: #fff;
    font-weight: 700;
    border-radius: 0.25rem;
    font-size: 75%;
}

#select1 {
    width: 100%;
}

.para-textarea {
    background: #e4e7ea;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    color: rgb(92, 104, 115);
    border: 1px solid #e4e7ea;
    min-height: calc(1.5em + 0.75rem + 2px);
}

.para-textarea:focus,
.para-textarea:focus-visible,
.para-textarea:hover {
    border-color: #568d2c;
    box-shadow: 0 0 0 0.2rem rgb(86 141 44 / 25%);
    outline: none;
    border: 1px solid #568d2c;
}

.normal-case {
    background: #568d2c;
    color: #fff;
}

.name-pastvisit h5 {
    color: #568d2c;
    margin-bottom: 2rem;
}

.name-pastvisit h5 span {
    float: right;
    color: #000;
    font-size: 15px;
}

.details {
    font-weight: bold;
}

.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
}

.image-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    // border: 2px dashed #ccc;
    position: relative;
    // border-radius: 1px solid green;
    border: 1px solid green;
    left: 55%;
    top: -10%;
}

.image-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.image-placeholder span {
    font-size: 2em;
    color: #888;
}

input[type="file"] {
    display: none;
}

.search-container {
    position: relative;
    width: 300px;
    margin: 0 auto;
}

.search-box {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
}

.suggestions-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    position: absolute;
    width: 100%;
    background: white;
    border: 1px solid #ccc;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.suggestions-list li {
    padding: 10px;
    cursor: pointer;
}

.suggestions-list li:hover {
    background: #f0f0f0;
}

input[type="checkbox"].custom-checkbox {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    position: relative;
}

input[type="checkbox"].custom-checkbox:checked {
    background-color: #568d2c;
    border: 2px solid #568d2c;
}

input[type="checkbox"].custom-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.highlight-row {
    background-color: green;
    /* Set your desired color */
}

.focus {
    outline: none;
}

.sticky-datepicker .bs-datepicker {
    position: sticky;
    top: 0;
    z-index: 1050;
    /* Adjust this if you have other elements with higher z-index */
}

.text {
    color: white;
}