<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Customers
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-12 my-3">
                        <button type="button" *ngIf="Add" class="btn btn-primary mr-1" data-toggle="modal" (click)="primaryModal.show()">
                            Add Customer
                        </button>
                        <div class="form-group table-search">
                            <div class="input-group" style="top: 3px;">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-search"></i></span>
                                </div>
                                <input type="text" id="Search" name="Search" placeholder="Search" class="form-control" autocomplete="off" class="form-control" (input)="GetCustomerLists()" [(ngModel)]="name">
                            </div>
                        </div>
                    </div>
                </div>

                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name <i class="fa fa-sort" checked="sort" (click)="Field('first_name')"></i></th>
                            <!-- <th>Last Name</th> -->
                            <th>Email <i class="fa fa-sort" checked="sort" (click)="Field('email')"></i></th>
                            <!-- <th>Phone No <i class="fa fa-sort" checked="sort" (click)="Field('phone_number')"></i></th> -->
                            <th *ngIf="Edit">Status</th>
                            <th *ngIf="Edit" style="text-align:center;">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of customers| paginate: { id: 'listing_pagination',
               itemsPerPage: 10,
               currentPage: page,
               totalItems: count };">
                            <td (click)="SearchbyuserId(user._id)">{{user.first_name}}</td>
                            <!-- <td>{{user.last_name}}</td> -->
                            <td (click)="SearchbyuserId(user._id)">{{user.email}}
                                <img *ngIf="user.login_type=='facebook'" width='15px' src='../../../../assets/img/facebook.png' />
                                <img *ngIf="user.login_type=='google'" width='15px' src='../../../../assets/img/google.png' />
                                <img *ngIf="user.login_type=='apple'" width='15px' src='../../../../assets/img/apple.png' />
                            </td>
                            <!-- <td (click)="SearchbyuserId(user._id)">{{user.phone_number}}</td> -->
                            <td *ngIf="Edit"><label class="switch">
                                    <input type="checkbox" checked="user.active"
                                        (change)="changed(user.active,user._id)" [(ngModel)]="user.active" />
                                    <span class="slider round"></span>
                                </label></td>
                            <td style="text-align:center;">
                                <!-- <a *ngIf="Edit" data-toggle="modal" (click)="EditModal.show();GetCustomer(user._id);"
                  style="cursor: pointer;"><span class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a> -->
                                <a data-toggle="modal" *ngIf="Delete" (click)="removeModal.show();GetCustomer(user._id);" style="cursor: pointer;"><span
                                        class="badge badge-danger"><i class="fa fa-trash"></i>
                                        Delete</span></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>

<!-- Add Modal -->
<div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Add Customer</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <form class="form" [formGroup]="AddForm" autocomplete=off>

                            <div class="form-group">
                                <label for="firstName">Name</label>
                                <input type="text" class="form-control" placeholder="Enter Name" formControlName="firstName" [ngClass]="{ 'is-invalid': submitted && f.firstName.errors }" />
                                <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
                                    <div *ngIf="f.firstName.errors.required">Name is required</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control" placeholder="e.g. <EMAIL>" formControlName="email" [ngClass]="{ 'is-invalid': submitted && f.email.errors }" />
                                <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                                    <div *ngIf="f.email.errors.required">Email is required</div>
                                    <div *ngIf="f.email.errors.email || f.email.errors.pattern">Email must be a valid email address</div>
                                </div>
                            </div>

                            <!-- <div class="form-group">
                <label for="phone_no">Phone Number</label>
                <input type="text" class="form-control" placeholder="Enter Phone number" formControlName="phone_no"
                  [ngClass]="{ 'is-invalid': submitted && f.phone_no.errors }" autocomplete=off>
                <div *ngIf="submitted && f.phone_no.errors" class="invalid-feedback">
                  <div *ngIf="f.phone_no.errors.required">Phone number is required</div>
                  <div *ngIf="f.phone_no.errors.minlength">Phone number must be at least 10 digits</div>
                </div>
              </div> -->

                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" class="form-control" placeholder="e.g. Abcdef@123" formControlName="password" [ngClass]="{ 'is-invalid': submitted && f.password.errors }" autocomplete="off" />
                                <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                                    <div *ngIf="f.password.errors.required">Password is required</div>
                                    <div *ngIf="f.password.errors.minlength">Password must be at least 8 characters
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
                <button class="btn btn-primary" type="submit" (click)="AddCustomer();">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Edit Modal -->
<div bsModal #EditModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Customer</h4>
                <!-- <button type="button" class="close" (click)="primaryModal.hide()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button> -->
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="role-name1" placeholder="Enter Name" class="form-control" autocomplete="off" required [(ngModel)]="user.first_name" [ngModelOptions]="{standalone: true}" (keydown.enter)="EditCustomer(user._id);" />
                        </div>
                        <div class="form-group">
                            <label for="name">Email</label>
                            <input type="email" id="email-name21" placeholder="Enter Email" class="form-control" autocomplete="off" required [(ngModel)]="user.email" [ngModelOptions]="{standalone: true}" (keydown.enter)="EditCustomer(user._id)" readonly />
                        </div>
                        <!-- <div class="form-group">
              <label for="name">Phone Number</label>
              <input type="email" id="email-name1" placeholder="Enter Phone Number" class="form-control"
                autocomplete="off" required [(ngModel)]="user.phone_number" [ngModelOptions]="{standalone: true}"
                (keydown.enter)="EditCustomer()" />
            </div> -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="EditModal.hide();clear();">Cancel</button>
                <button type="button" class="btn btn-primary" (click)="EditCustomer(user._id);">Save</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<!-- Delete Modal -->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Customer?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="Deletecustomer(user._id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<div bsModal #okayModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <h4 class="modal-title">Customer Created Successfully</h4>
            <p>please, Check the mail and Activate Account</p>
            <button type="button" class="btn btn-secondary" (click)="okayModal.hide();clear();">ok</button>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>