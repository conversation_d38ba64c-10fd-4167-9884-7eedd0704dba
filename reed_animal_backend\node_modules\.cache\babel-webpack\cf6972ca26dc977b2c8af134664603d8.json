{"ast": null, "code": "import baseToNumber from './_baseToNumber.js';\nimport baseToString from './_baseToString.js';\n/**\n * Creates a function that performs a mathematical operation on two values.\n *\n * @private\n * @param {Function} operator The function to perform the operation.\n * @param {number} [defaultValue] The value used for `undefined` arguments.\n * @returns {Function} Returns the new mathematical operation function.\n */\n\nfunction createMathOperation(operator, defaultValue) {\n  return function (value, other) {\n    var result;\n\n    if (value === undefined && other === undefined) {\n      return defaultValue;\n    }\n\n    if (value !== undefined) {\n      result = value;\n    }\n\n    if (other !== undefined) {\n      if (result === undefined) {\n        return other;\n      }\n\n      if (typeof value == 'string' || typeof other == 'string') {\n        value = baseToString(value);\n        other = baseToString(other);\n      } else {\n        value = baseToNumber(value);\n        other = baseToNumber(other);\n      }\n\n      result = operator(value, other);\n    }\n\n    return result;\n  };\n}\n\nexport default createMathOperation;", "map": null, "metadata": {}, "sourceType": "module"}