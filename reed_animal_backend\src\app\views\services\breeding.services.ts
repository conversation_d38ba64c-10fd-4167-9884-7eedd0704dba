import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class BreedingService extends Api {

    //Add New Breeding 
    NewBreeding(data): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/Breeding?token=${localStorage.auth_token}`, data);
    }

    //Get All Breeding Type 
    GetBreedingsList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Breeding?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Get Particular Breeding by using Breeding id 
    GetBreedingDetail(id): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`);
    }

    //Update or Edit Breeding details
    UpdateBreeding(id, data): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`, data);
    }

    //Delete Breeding by using id
    DeleteBreeding(id): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/Breeding/${id}?token=${localStorage.auth_token}`);
    }

    //Get All Animal Type
    GetTypesList(): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`);
    }
}