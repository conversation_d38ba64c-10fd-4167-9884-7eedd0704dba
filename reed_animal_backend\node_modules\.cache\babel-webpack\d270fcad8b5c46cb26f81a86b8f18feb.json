{"ast": null, "code": "/**\n * The base implementation of `_.sum` and `_.sumBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the sum.\n */\nfunction baseSum(array, iteratee) {\n  var result,\n      index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var current = iteratee(array[index]);\n\n    if (current !== undefined) {\n      result = result === undefined ? current : result + current;\n    }\n  }\n\n  return result;\n}\n\nexport default baseSum;", "map": null, "metadata": {}, "sourceType": "module"}