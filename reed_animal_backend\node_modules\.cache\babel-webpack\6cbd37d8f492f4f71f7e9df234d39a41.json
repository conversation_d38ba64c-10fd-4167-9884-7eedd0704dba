{"ast": null, "code": "import isIndex from './_isIndex.js';\n/**\n * The base implementation of `_.nth` which doesn't coerce arguments.\n *\n * @private\n * @param {Array} array The array to query.\n * @param {number} n The index of the element to return.\n * @returns {*} Returns the nth element of `array`.\n */\n\nfunction baseNth(array, n) {\n  var length = array.length;\n\n  if (!length) {\n    return;\n  }\n\n  n += n < 0 ? length : 0;\n  return isIndex(n, length) ? array[n] : undefined;\n}\n\nexport default baseNth;", "map": null, "metadata": {}, "sourceType": "module"}