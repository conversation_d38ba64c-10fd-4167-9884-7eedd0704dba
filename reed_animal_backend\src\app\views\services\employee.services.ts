import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class Employeeservice extends Api {

    //Get All employee
    GetEmployeeList(params: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/employee`, { params });
    }

    GetViewLog(id:any): Observable<any>{
        return this.http.get(`${this.config.APIUrl5}/${id}`);
    }

    //Get All Module 
    GetRoleList(): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/role_active?search=&token=${localStorage.auth_token}`);
    }

    //Get All Module 
    NewEmployee(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl}/employee?token=${localStorage.auth_token}`, data);
    }

    //Get Employee details by using Employee id
    GetEmployeeDetail(id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`);
    }

    //Edit or Update Employee Details
    EditEmployeeDetail(id: any, params: any): Observable<any> {
        return this.http.put(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`, params);
    }

    //Delete Employee by using id
    DeleteEmployee(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/employee/${id}?token=${localStorage.auth_token}`);
    }
}