{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n/** Used as the size to enable large array optimizations. */\n\nvar LARGE_ARRAY_SIZE = 200;\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\n\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      isCommon = true,\n      length = array.length,\n      result = [],\n      valuesLength = values.length;\n\n  if (!length) {\n    return result;\n  }\n\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  } else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n\n  outer: while (++index < length) {\n    var value = array[index],\n        computed = iteratee == null ? value : iteratee(value);\n    value = comparator || value !== 0 ? value : 0;\n\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n\n      result.push(value);\n    } else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n\n  return result;\n}\n\nexport default baseDifference;", "map": null, "metadata": {}, "sourceType": "module"}