import { Component, OnInit } from '@angular/core';
import { Loginservice } from '../../views/services/login.service'
import { ActivatedRoute, Router } from '@angular/router';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';


@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {

  token = '';
  TokenFailed = false;
  TokenPass = false;
  password = {
    firstName: '',
    password: ''
  }
  errormessage = '';
  required = false;
  isLoginFailed = false;
  response = [];
  validForm: FormGroup;
  isFormReady = false;
  submitted = false;
  show: boolean = false;
  resultName: any;
  failed = false;
  success = false;
  constructor(private formBuilder: FormBuilder, private userservice: Loginservice, private route: ActivatedRoute, private router: Router) { }

  ngOnInit(): void {
    this.route.queryParams
      .subscribe((params: any) => {
        const data={
          token:params['token']
        }
        // console.log('data-->',data)
        this.userservice.PasswordToken(data)
          .subscribe((res: any) => {
            if (res.code===100) {
              this.TokenFailed = true
            } else {
              this.TokenPass = true;
              this.token = params['token']
              this.valid()
            }
            // console.log(res)
          })
      });
      this.valid()

  }

  valid(): void {
    this.validForm = this.formBuilder.group({
      firstName: ['', [Validators.required,  Validators.minLength(8)]],
      password: ['', [Validators.required, Validators.minLength(8)]],
    });
  }

//Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]'),
  getfocus(): void {
    this.failed = false;
  }

  get f() {
    return this.validForm.controls;
  }

  onSubmit() {
    this.submitted = true;
    if (this.validForm.value.firstName != '' && this.validForm.value.password != '' && this.validForm.value.firstName.length>=8 && this.validForm.value.password.length>=8) {
      if (this.validForm.value.firstName === this.validForm.value.password) {
        this.isFormReady = true;
        const inputRequest = {
          newPassword: this.validForm.value.firstName,
          confirmPassword: this.validForm.value.password,
          token: this.token
        }
        // console.log('result-->', inputRequest);
        this.userservice.ChangePassword(inputRequest).subscribe((result: any) => {
          // console.log('result-->', result);
          if (result.code === 200) {
            this.success = true;
            this.TokenPass = false;
          }else{
            this.errormessage = result.data
          }
        });
      } else {
        this.failed = true;
      }
    }
  }
}
