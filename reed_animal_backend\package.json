{"name": "<PERSON><PERSON>-Admin-Panel", "version": "1.0.0", "description": "CoreUI Free Angular 11 Admin Template", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "http://holeczek.pl", "github": "https://github.com/mrholek", "twitter": "https://twitter.com/luka<PERSON><PERSON>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/xidedix"}], "homepage": "https://coreui.io/angular", "copyright": "Copyright 2021 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"ng": "ng", "postinstall": "ngcc --properties es2015 browser module main --first-only", "start": "ng serve", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular-persian/material-date-picker": "^1.8.1", "@angular/animations": "^11.1.0", "@angular/cdk": "^11.1.0", "@angular/common": "^11.1.0", "@angular/compiler": "^11.1.0", "@angular/core": "^11.1.0", "@angular/forms": "^11.1.0", "@angular/localize": "^11.1.0", "@angular/material-moment-adapter": "^17.3.3", "@angular/platform-browser": "^11.1.0", "@angular/platform-browser-dynamic": "^11.1.0", "@angular/router": "^11.1.0", "@coreui/angular": "~2.11.1", "@coreui/coreui": "^2.1.16", "@coreui/coreui-plugin-chartjs-custom-tooltips": "^1.3.1", "@coreui/icons": "^2.0.0-rc.0", "@coreui/icons-angular": "1.0.0-alpha.3", "@danielmoncada/angular-datetime-picker-moment-adapter": "^4.0.0", "bja-ngx-pagination": "^0.2.2", "bootstrap": "^4.6.0", "chart.js": "^2.9.4", "classlist.js": "^1.1.20150312", "core-js": "^3.8.3", "flag-icon-css": "^3.5.0", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "moment": "^2.29.1", "ng2-charts": "^2.4.2", "ngx-bootstrap": "5.5.0", "ngx-moment": "^5.0.0", "ngx-pagination": "^5.0.0", "ngx-perfect-scrollbar": "^10.1.0", "ngx-toastr": "^14.0.0", "rxjs": "^6.6.3", "simple-line-icons": "^2.5.5", "ts-helpers": "^1.1.2", "tslib": "^2.0.0", "web-animations-js": "^2.3.2", "zone.js": "^0.11.3"}, "devDependencies": {"@angular-devkit/build-angular": "^12.2.5", "@angular/cli": "^11.2.0", "@angular/compiler-cli": "^11.1.0", "@angular/language-service": "^11.1.0", "@types/jasmine": "^3.6.3", "@types/jasminewd2": "^2.0.8", "@types/node": "^14.18.46", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "^8.10.2", "tslint": "~6.1.0", "typescript": "~4.0.5"}, "engines": {"node": ">= 10.13", "npm": ">= 6"}}