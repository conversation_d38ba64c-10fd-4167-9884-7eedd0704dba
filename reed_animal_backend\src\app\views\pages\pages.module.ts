import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalModule } from 'ngx-bootstrap/modal';
import { PagesRoutingModule } from './pages-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CustomersComponent } from './customers/customers.component';
import { AppointmentsComponent } from './appointments/appointments.component';
import { ShoppingComponent } from './shopping/shopping.component';
import { OrdersComponent } from './orders/orders.component';
import { ReportComponent } from './report/report.component';
import { PetDetailComponent } from './pet-detail/pet-detail.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';

import { TabsModule } from 'ngx-bootstrap/tabs';
import { ResourcesComponent } from './resources/resources.component';
import { ScheduleComponent } from './schedule/schedule.component';
import { BsDatepickerModule, BsDatepickerConfig } from 'ngx-bootstrap/datepicker';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { DatePipe } from '@angular/common';
import { BreedingComponent } from '../master/breeding/breeding.component';
import { AnimalTypeComponent } from '../master/animal-type/animal-type.component';
import { ShopSettingComponent } from './shop-setting/shop-setting.component';
import { ProductsComponent } from './products/products.component';
import { PelfiesComponent } from './pelfies/pelfies.component';
import { OrderDetailsComponent } from './order-details/order-details.component';
import { BannersComponent } from './banners/banners.component';
import { AddBannersComponent } from './add-banners/add-banners.component';

@NgModule({
  declarations: [CustomersComponent, ResourcesComponent, AppointmentsComponent, ShoppingComponent, OrdersComponent, ReportComponent, PetDetailComponent, ScheduleComponent, ShopSettingComponent, ProductsComponent, PelfiesComponent, OrderDetailsComponent, BannersComponent, AddBannersComponent],
  imports: [
    CommonModule, BsDatepickerModule.forRoot(), TimepickerModule.forRoot(), PopoverModule.forRoot(),
    PagesRoutingModule, ModalModule, TabsModule, NgxPaginationModule, FormsModule, ReactiveFormsModule, BsDropdownModule,
  ],
  providers: [BsDatepickerConfig, DatePipe, AnimalTypeComponent, BreedingComponent],
})
export class PagesModule { }
