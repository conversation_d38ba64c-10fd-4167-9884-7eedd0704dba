<div class="row" (keydown)="handleKeyboardEvent($event)">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Order <strong>#{{user_details.order_id}}</strong>
            </div>
            <div class="card-body">
                <div class="card-body">
                    <!-- Customer Info -->
                    <div class="row">
                        <div class="form-group col-sm-4">
                            <label for="petname">Customer Name</label>
                            <input type="text" id="petname" value="{{user_details.user_name}}" class="form-control" readonly>
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="email">Email Address</label>
                            <input type="text" id="email-address" value="{{user_details.user_id?user_details.user_id.email:''}}" class="form-control" readonly>
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="Created">Order Date</label>
                            <input type="text" placeholder="Created Date" class="form-control" value="{{user_details.createdAt|date:'dd-MMM-YYYY'}}" readonly>
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="petname">Approval</label>
                            <select class="form-select  form-control" [disabled]="approval" placeholder="" aria-label=".form-select-lg example" required placeholder="Category" (change)="approvel()" [(ngModel)]='user_details.approved'>
                              
                                <option disabled value="0">--select--</option>
                                <option value="1">Approved</option>                      
                                <option [disabled]="startup"  value="2">Declined</option>
                        
                            </select>



                            <div *ngIf="messagebox">
                                <textarea type="text" [(ngModel)]="user_details.reason" (blur)="out()" maxlength="130" placeholder="Tell the customer" style="height:100px; width:100%; padding: 10px;
                                   
                                border-radius: 5px;
                                outline: none;
                                font-size: 16px;
                                box-sizing: border-box;margin-top: 5px;"></textarea>
                                <p style="color: red;" *ngIf="textvalue">Please enter decline note</p>
                                <div *ngIf="declinsubBtn" class="modal-footer" style="justify-content: center;border-top: 0px solid;">

                                    <button type="button" [disabled]="DeclinsubmitBtn" class="btn btn-primary" style="margin-top: -15px; background-color: rgb(61, 106, 27) !important; border: none;" (click)="textUpdate()">SUBMIT</button>
                                </div>
                            </div>


                        </div>



                        <!-- Ordered Status -->

                        <div class="form-group col-sm-4">
                            <label for="petname">Order Status</label>
                            <select class="form-select  form-control" (change)="order_status()" [disabled]="isDropdownDisabled" (change)="Key='status';Value=$event.target.value;" aria-label=".form-select-lg example" [(ngModel)]='user_details.status'>
                                <option value=0 disabled>In progress</option>
                                <option value=1 [disabled]="complete" >Completed</option>
                                <option value=2 [disabled]="readdTopickup">Ready For Pickup</option>
                                <option *ngIf="canclebtn"  value=3>Cancel</option>
                            </select>
                            <div _ngcontent-awq-c208 class="owl-dt-container-inner ng-tns-c208-0 ng-trigger ng-trigger-fadeInPicker" ng-reflect-enabled="true" style="/* opacity: 1; */background: #fff;box-shadow: 0 5px 5px -3px rgb(0 0 0 / 20%), 0 8px 10px 1px rgb(0 0 0 / 14%), 0 3px 14px 2px rgb(0 0 0 / 12%);"></div>
                        </div>
                        <div class="form-group col-sm-4">
                            <label for="Location">Delivered Date</label>
                            <!-- <input type="date" [disabled]="dropDownStatus" id="Location" value="{{user_details.delivery_date==null?'':user_details.delivery_date|date:'dd MMM yyyy'}}" class="form-control"> -->

                            <input type="text" [(ngModel)]="user_details.delivery_date" placeholder="DD-MM-YYYY" [disabled]="dropDownStatusdate" [minDate]="todayDate" class="form-control" bsDatepicker [bsConfig]="{ isAnimated: true, dateInputFormat: 'DD-MMM-YYYY',showWeekNumbers:false}"
                                (bsValueChange)="delivery()">

                        </div>



                        <div class="form-group col-sm-4">
                            <label for="Location">Pickup Location</label>
                            <!-- <select class="form-select  form-control" [disabled]="dropDownStatus" (change)="Key='location';Value=$event.target.value;" aria-label=".form-select-lg example" [(ngModel)]='user_details.location'>
                                <option value='null' disabled>--Select location--</option>
                                <option value=1>Saratoga</option>
                                <option value=2>Campbell</option>
                            </select> -->



                            <select [(ngModel)]='user_details.location' class="form-select  form-control" aria-label=".form-select-lg example" (change)="select_location()" [disabled]="dropDownStatus">                                
                                <option  *ngFor="let location of this.final_location" [value]="location._id"  >{{location.name}}</option>
                            </select>



                        </div>
                    </div>
                    <!-- Payment Info -->
                    <div class="row" *ngIf="user_details.payment_details!=('null'||null)">
                        <div class="col-sm-12">
                            <a href="javascript:(0)" (click)="window(user_details.payment_details.charges.data[0].receipt_url);">Payment
                                Details</a>
                        </div>
                    </div>
                    <!-- Product Info -->
                    <div class="row">
                        <div class="col-md-12" style="margin:auto; margin-top:25px; margin-bottom:50px;">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Name</th>
                                        <th>Refill Qty</th>
                                        <th>Refill Status</th>
                                        <th>Cost</th>
                                        <th>Discount</th>
                                        <th>Tax</th>
                                        <th>Qty</th>
                                        <th>Total</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of user_details.product;let i=index;">
                                        <td>
                                            <a href="#/pages/products?search={{item.product_id._id}}"><img [src]="item.product_id.poster_image" width="45"></a>
                                        </td>
                                        <td>{{item.product_id.title}}</td>
                                        <td>
                                            <input type="number" [(ngModel)]="item.refill_quantity" class="form-control form-control-sm" placeholder="Qty" min="0" style="width: 80px;">
                                        </td>
                                        <td>
                                            <select class="form-select form-control" [(ngModel)]="item.approval" (change)="onRefillStatusChange(i, $event)">
                                                <option value="">--select--</option>
                                                <option value="approved">Approved</option>
                                                <option value="declined">Declined</option>
                                            </select>
                                            
                                            <!-- Refill Declined Textarea and Submit Button -->
                                            <div *ngIf="item.approval == 'declined'" class="mt-2">
                                                <textarea [(ngModel)]="item.refill_decline_reason" class="form-control form-control-sm" rows="3" placeholder="Tell the customer" style="height:100px; width:100%; padding: 10px; border-radius: 5px; outline: none; font-size: 16px; box-sizing: border-box;"></textarea>
                                                <div class="mt-2" style="text-align: center;">
                                                    <button type="button" class="btn btn-sm btn-primary" style="background-color: rgb(61, 106, 27) !important; border: none;" (click)="submitRefillDecline(i)">SUBMIT</button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>$ {{item._id.price.toFixed(2)}}</td>
                                        <td>$ {{item.discount_amount.toFixed(2)}}</td>
                                        <td>$ {{item.tax_amount.toFixed(2)}}</td>
                                        <td>{{item.quantity}}</td>
                                        <td>$ {{item.sub_total.toFixed(2)}}</td>
                                        <td>
                                            <div *ngIf="!item.showNotesInput">
                                                <button *ngIf="!item.notes" type="button" class="btn btn-sm btn-outline-primary" (click)="showNotesInput(i)">Add Notes</button>
                                                <button *ngIf="item.notes" type="button" class="btn btn-sm btn-outline-info" (click)="showNotesInput(i)">View Notes</button>
                                            </div>
                                            <div *ngIf="item.showNotesInput" class="notes-input-container">
                                                <textarea [(ngModel)]="item.notes" class="form-control form-control-sm" rows="3" placeholder="Enter notes..."></textarea>
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-success me-1" (click)="saveNotes(i)">Save</button>
                                                    <button type="button" class="btn btn-sm btn-secondary" (click)="cancelNotes(i)">Cancel</button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th>Total</th>
                                        <th>$ {{user_details.total_amount}}</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <button type="button" [disabled]="submit" *ngIf="submitBtn" class="btn btn-primary" style="position: absolute;left: 45%; background-color: rgb(61, 106, 27) !important; border: none;" (click)="onsubmit()">SUBMIT</button>
                </div>
            </div>
        </div>
    </div>

</div>



<!-- Delete Modal -->
<div bsModal #UpdateModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Update Order?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to change the {{this.Key}} of this order ?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="UpdateModal.hide();GetOrder();Key='';Value='';">Cancel</button>
                <button type="button" class="btn btn-primary" (click)="Update();">Update</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->



<div bsModal #Declined="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary modal-sm" role="document">
        <div class="modal-content" style="height: 200px;
        width: 600px;
        margin-left: -120px;">
            <div class="modal-header">
                <h4 class="modal-title">Message</h4>
            </div>
            <!-- <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to change the {{this.Key}} of this order ?</p>
                    </div>
                </div>
            </div> -->
            <textarea type="text" [(ngModel)]="textareaValue" maxlength="225" placeholder="Type something" style="height:150px; padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            outline: none;
            font-size: 16px;
            box-sizing: border-box;margin-top: 5px;"></textarea>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="Declined.hide();">Cancel</button>
                <button type="button" class="btn btn-primary" (click)="textUpdate()">Updated</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>