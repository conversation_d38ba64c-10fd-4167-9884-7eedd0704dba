import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { TokenStorageService } from '../../services/token-storage.service';
import { PermissionService } from '../../services/permission.service';
import { Employeeservice } from '../../services/employee.services';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductService } from '../../services/product.services'

@Component({
  selector: 'app-shop-setting',
  templateUrl: './shop-setting.component.html',
  styleUrls: ['./shop-setting.component.scss']
})
export class ShopSettingComponent implements OnInit {
  @ViewChild('primaryModalCategory') public primaryModalCategory: ModalDirective;
  @ViewChild('removeModalCategory') public removeModalCategory: ModalDirective;
  @ViewChild('EditModalCategory') public EditModalCategory: ModalDirective;

  @ViewChild('primaryModalBrand') public primaryModalBrand: ModalDirective;
  @ViewChild('removeModalBrand') public removeModalBrand: ModalDirective;
  @ViewChild('EditModalBrand') public EditModalBrand: ModalDirective;

  @ViewChild('primaryModalVariant') public primaryModalVariant: ModalDirective;
  @ViewChild('removeModalVariant') public removeModalVariant: ModalDirective;
  @ViewChild('EditModalVariant') public EditModalVariant: ModalDirective;


  Add = true;
  Edit = true;
  Delete = true;

  loginForm: FormGroup;
  isFormReady = false;
  submitted = false;
  Category = [];
  page = 1;
  count = 0;
  id = ''

  Brand: FormGroup;
  isFormReadyB = false;
  submittedB = false;
  brand = [];
  pageB = 1;
  countB = 0;

  variant: FormGroup;
  items: FormArray;
  isFormReadyV = false;
  submittedV = false;
  Variant = [];
  pageV = 1;
  countV = 0;
  name ='';
  finalproduct: any;

  constructor(private productService: ProductService, private route: ActivatedRoute, private router: Router, private tokenStorage: TokenStorageService, private formBuilder: FormBuilder, private Permission: PermissionService, private EmployeeService: Employeeservice) { }

  ngOnInit(): void {
    this.tokens();
    this.SignForm();
    this. ListProduct()
  }

  //token verified type
  tokens(): void {
    const Role = this.tokenStorage.getUser();
    this.Permission.GetModule(Role.role_id._id)
      .subscribe((res: any) => {
        // console.log(res)
        for (var i = 0; i < res.data.length; i++) {
          if (res.data[i].module_name == "Shop Setting") {
            this.Add = res.data[i].add
            this.Edit = res.data[i].edit
            this.Delete = res.data[i].delete
            // console.log(this.Add, this.Edit, this.Delete)
          }
        }
      })
    this.EmployeeService.GetEmployeeDetail(Role._id)
      .subscribe((res) => {
        // console.log(res.data[0].status)
        if (res.data.status == false) {
          this.tokenStorage.signOut()
        } else {
          this.ListCategory();
          this.ListBrand();
          this.ListVariant();
        }
      })
  }

  //clear modal window
  clear(): void {
    this.loginForm.reset();
    this.isFormReady = false;
    this.submitted = false;
    this.Brand.reset();
    this.isFormReadyB = false;
    this.submittedB = false;
    this.variant.reset();
    this.isFormReadyV = false;
    this.submittedV = false;
  }

  SignForm() {

    //Category
    this.loginForm = this.formBuilder.group({
      name: ['', [Validators.required]],
    });

    //Brand
    this.Brand = this.formBuilder.group({
      name: ['', [Validators.required]],
    });

    //variant
    this.variant = this.formBuilder.group({
      name: ['', [Validators.required]],
      items: this.formBuilder.array([this.createItem()])
    });
  }

  createItem(): FormGroup {
    return this.formBuilder.group({
      value: ['', [Validators.required]],
      sort: ['', [Validators.required]],
    });
  }

  addItem(): void {
    this.items = this.variant.get('items') as FormArray;
    this.items.push(this.createItem());
  }

  removeItem(i: number) {
    this.items.removeAt(i);
  }

  get f() {
    return this.loginForm.controls;
  }
  get b() {
    return this.Brand.controls;
  }
  get v() {
    return this.variant.controls;
  }

  /**Category */
  //Add new Category
  AddCategory(): void {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {
      const data = {
        name: this.loginForm.value.name
      }
      this.productService.AddCategory(data)
        .subscribe((res) => {
          // console.log(res)
          this.primaryModalCategory.hide();
          this.clear();
          this.ListCategory();
        })
    }
  }

  //page handle request
  getrequestparams(page: number): any {
    let skip: any = {};
    skip[`skip`] = (page - 1) * 10;
    return skip;
  }

  //List all Category
  ListCategory() {
    const skip = this.getrequestparams(this.page);
    this.productService.GetCategory(skip)
      .subscribe((res: any) => {
        this.Category = res.data;
        this.count = res.count;
      })
  }

  //Page handle 
  handlePageChange(event: number) {
    this.page = event;
    this.ListCategory();
  }

  //Get category by using category index
  GetCategoryBy(index, param) {
    if (param == 'Edit') {
      this.f.name.setValue(this.Category[index].name, {
        onlySelf: true
      })
      this.id = this.Category[index]._id;
      this.EditModalCategory.show();
    } else {
      this.id = this.Category[index]._id;
      this.removeModalCategory.show();
    }
  }

  //Update category
  UpdateCategory(Id) {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return
    }
    else {
      const data = {
        name: this.loginForm.value.name
      }
      this.productService.UpdateCategory(Id, data)
        .subscribe((res) => {
          this.EditModalCategory.hide();
          this.clear();
          this.ListCategory();
        })
    }
  }

  //Delete Category
  DeleteCategory(Id) {
    this.productService.DeleteCategory(Id)
      .subscribe((res) => {
        // console.log(res)
        this.removeModalCategory.hide();
        this.clear();
        this.ListCategory();
      })
  }

  /**Brand  */

  //Add new brand
  AddBrand(): void {
    this.submitted = true;
    if (this.Brand.invalid) {
      return
    }
    else {
      const data = {
        name: this.Brand.value.name
      }
      this.productService.AddBrand(data)
        .subscribe((res) => {
          this.primaryModalBrand.hide();
          this.clear();
          this.ListBrand();
        })
    }
  }

  //page handle request
  getrequestparamsB(pageB: number): any {
    let skip: any = {};
    skip[`skip`] = (pageB - 1) * 10;
    return skip;
  }

  //List all Brand
  ListBrand() {
    const skip = this.getrequestparamsB(this.pageB);
    this.productService.GetBrand(skip)
      .subscribe((res: any) => {
        this.brand = res.data;

      
        this.countB = res.count;
      })
  }

  //Page handle 
  handlePageChangeB(event: number) {
    this.pageB = event;
    this.ListBrand();
  }

  //Get Brand by using Brand index
  GetBrandBy(index, param) {
    if (param == 'Edit') {
      this.b.name.setValue(this.brand[index].name, {
        onlySelf: true
      })
      this.id = this.brand[index]._id;
      this.EditModalBrand.show();
    } else {
      this.id = this.brand[index]._id;
      this.removeModalBrand.show();
    }
  }

  //Update brand
  UpdateBrand(Id) {
    this.submitted = true;
    if (this.Brand.invalid) {
      return
    }
    else {
      const data = {
        name: this.Brand.value.name
      }
      this.productService.UpdateBrand(Id, data)
        .subscribe((res) => {
          // console.log(res)
          this.EditModalBrand.hide();
          this.clear();
          this.ListBrand();
        })
    }
  }

  //Delete Brand
  DeleteBrand(Id) {
    this.productService.DeleteBrand(Id)
      .subscribe((res) => {
        // console.log(res)
        this.removeModalBrand.hide();
        this.clear();
        this.ListBrand();
      })
  }

  /**Variant */
  AddVariant() {
    this.submittedV = true;
    if (this.variant.invalid) {
      return
    }
    else {
      var date = this.variant.value.items.sort(function(a,b) { return a.sort.valueOf() - b.sort.valueOf();});
      const data = {
        name: this.variant.value.name,
        items: date,
      }
      this.productService.AddVariant(data)
        .subscribe((res) => {
          this.primaryModalVariant.hide();
          this.clear();
          this.ListVariant();
        })
    }
  }

  //page handle request
  getrequestparamsV(pageV: number): any {
    let skip: any = {};
    skip[`skip`] = (pageV - 1) * 10;
    return skip;
  }

  //list all variant
  ListVariant() {
    const skip = this.getrequestparamsV(this.pageV);
    this.productService.GetVariant(skip)
      .subscribe((res: any) => {
        // console.log(res)
        this.Variant = res.data;
        this.countV = res.count;
      })
  }

  //Page handle 
  handlePageChangeV(event: number) {
    this.pageV = event;
    this.ListVariant();
  }

  //Get variant by using index
  GetVariantBy(index, param) {
    if (param == 'Edit') {
      this.v.name.setValue(this.Variant[index].name, {
        onlySelf: true
      })
      this.items = this.variant.get('items') as FormArray;
      this.items.clear();
      // console.log(this.items)
      // var tagsArray = [];
      this.Variant[index].items.forEach(product => this.items.push(this.formBuilder.group({ value: [product.value, [Validators.required]], sort: [product.sort, [Validators.required]] })));
      // this.variant.setControl('items', this.formBuilder.array(tagsArray || []));
      // this.items.removeAt(0);
      this.id = this.Variant[index]._id;
      this.EditModalVariant.show();
    } else {
      this.id = this.Variant[index]._id;
      this.removeModalVariant.show();
    }
  }

  //Update variant
  UpdateVariant(Id) {
    this.submittedV = true;
    if (this.variant.invalid) {
      return
    }
    else {
      var date = this.variant.value.items.sort(function(a,b) { return a.sort.valueOf() - b.sort.valueOf();});
      // console.log(date)
      const data = {
        name: this.variant.value.name,
        items: date
      }
      this.productService.UpdateVariant(Id, data)
        .subscribe((res) => {
          console.log("value of variante",res)
          this.EditModalVariant.hide();
          this.clear();
          this.ListVariant();
        })
    }
  }


  ListProduct() {
    console.log("@@@",this.name)
    const skip = this.getrequestparams(this.page);
    this.productService.  GetProduct(this.name,skip)
      .subscribe((res: any) => {
        this.finalproduct = res.data;
        console.log("final product list",this.finalproduct)
      })
  }

  //Delete Variant
  DeleteVariant(Id) {
    this.productService.DeleteVariant(Id)
      .subscribe((res) => {
        // console.log(res)
        this.removeModalVariant.hide();
        this.clear();
        this.ListVariant();
      })
  }

}
