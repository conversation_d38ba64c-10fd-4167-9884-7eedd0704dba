{"ast": null, "code": "/**\n * The base implementation of `_.conformsTo` which accepts `props` to check.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n */\nfunction baseConformsTo(object, source, props) {\n  var length = props.length;\n\n  if (object == null) {\n    return !length;\n  }\n\n  object = Object(object);\n\n  while (length--) {\n    var key = props[length],\n        predicate = source[key],\n        value = object[key];\n\n    if (value === undefined && !(key in object) || !predicate(value)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport default baseConformsTo;", "map": null, "metadata": {}, "sourceType": "module"}