## [CoreUI for Angular](./README.md) version `changelog`

###### `v2.11.1`

- chore: update to `Angular 11.1`

###### dependencies update
- update `@angular/animations` to `^11.1.0`
- update `@angular/cdk` to `^11.1.0`
- update `@angular/common` to `^11.1.0`
- update `@angular/compiler` to `^11.1.0`
- update `@angular/core` to `^11.1.0`
- update `@angular/forms` to `^11.1.0`
- update `@angular/localize` to `^11.1.0`
- update `@angular/platform-browser` to `^11.1.0`
- update `@angular/platform-browser-dynamic` to `^11.1.0`
- update `@angular/router` to `^11.1.0`
- update `@coreui/angular` to `~2.11.1`
- update `bootstrap` to `^4.6.0`
- update `core-js` to `^3.8.3`
- update `@angular-devkit/build-angular` to `^0.1101.1`
- update `@angular/cli` to `^11.1.1`
- update `@angular/compiler-cli` to `^11.1.0`
- update `@angular/language-service` to `^11.1.0`
- update `@types/jasmine` to `^3.6.3`
- update `@types/node` to `^14.14.22`

###### `v2.11.0`

- chore: update to `Angular 11` and `TypeScript 4`
  - [https://update.angular.io/](https://update.angular.io/?v=10.2-11.0)
  - [https://v11.angular.io/guide/updating-to-version-11](https://v11.angular.io/guide/updating-to-version-11)
  - [TypeScript: Documentation - TypeScript 4](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-0.html)
  - remove deprecated support for IE 9, 10, and IE mobile

###### dependencies update
- update `@angular/animations` to `^11.0.9`
- update `@angular/cdk` to `^11.0.4`
- update `@angular/common` to `^11.0.9`
- update `@angular/compiler` to `^11.0.9`
- update `@angular/core` to `^11.0.9`
- update `@angular/forms` to `^11.0.9`
- update `@angular/localize` to `^11.0.9`
- update `@angular/platform-browser` to `^11.0.9`
- update `@angular/platform-browser-dynamic` to `^11.0.9`
- update `@angular/router` to `^11.0.9`
- update `@coreui/angular` to `~2.11.0`
- update `@angular-devkit/build-angular` to `^0.1100.7`
- update `@angular/cli` to `^11.0.7`
- update `@angular/compiler-cli` to `^11.0.9`
- update `@angular/language-service` to `^11.0.9`
- update `@types/jasmine` to `~3.6.0`
- update `codelyzer` to `^6.0.0`
- update `karma-coverage` to `~2.0.3`
- update `typescript` to `~4.0.5`

###### `v2.10.0`

- chore: update to `Angular 10` and `TypeScript 3.9`
  - [https://update.angular.io/](https://update.angular.io/?v=9.1-10.2)
  - [https://v10.angular.io/guide/updating-to-version-10](https://v10.angular.io/guide/updating-to-version-10)
  - [TypeScript: Documentation - TypeScript 3.9](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-9.html)


- test: deprecate `async()` in favour of `waitForAsync()`

###### dependencies update
- update `@angular/animations` to `^10.2.4`
- update `@angular/cdk` to `^10.2.7`
- update `@angular/common` to `^10.2.4`
- update `@angular/compiler` to `^10.2.4`
- update `@angular/core` to `^10.2.4`
- update `@angular/forms` to `^10.2.4`
- update `@angular/localize` to `^10.2.4`
- update `@angular/platform-browser` to `^10.2.4`
- update `@angular/platform-browser-dynamic` to `^10.2.4`
- update `@angular/router` to `^10.2.4`
- update `@coreui/angular` to `~2.10.0`
- update `classlist.js` to `^1.1.20150312`
- update `ng2-charts` to `^2.4.2`
- update `ngx-bootstrap` to `^6.2.0`
- update `ngx-perfect-scrollbar` to `^10.1.0`
- update `tslib` to `^2.0.0`
- update `@angular-devkit/build-angular` to `^0.1002.1`
- update `@angular/cli` to `^10.2.1`
- update `@angular/compiler-cli` to `^10.2.4`
- update `@angular/language-service` to `^10.2.4`
- update `jasmine-core` to `~3.6.0`
- update `jasmine-spec-reporter` to `~5.0.0`
- update `karma` to `~5.2.0`
- update `karma-chrome-launcher` to `~3.1.0`
- update `karma-coverage-istanbul-reporter` to `~3.0.2`
- update `karma-jasmine` to `~4.0.0`
- update `karma-jasmine-html-reporter` to `^1.5.0`
- update `protractor` to `~7.0.0`
- update `tslint` to `~6.1.0`
- update `typescript` to `^3.9.7`


###### `v2.9.6`
- feat(icons): update to @coreui/icons v2 and @coreui/icons-angular

###### dependencies update
- update `@angular/animations` to `^9.1.13`
- update `@angular/cdk` to `^9.2.4`
- update `@angular/common` to `^9.1.13`
- update `@angular/compiler` to `^9.1.13`
- update `@angular/core` to `^9.1.13`
- update `@angular/forms` to `^9.1.13`
- update `@angular/platform-browser` to `^9.1.13`
- update `@angular/platform-browser-dynamic` to `^9.1.13`
- update `@angular/router` to `^9.1.13`
- update `@coreui/angular`: `~2.9.6`
- update `@coreui/icons` to `^2.0.0-rc.0`
- update `@coreui/icons-angular` to `1.0.0-alpha.3`
- update `bootstrap` to `^4.5.3`
- update `chart.js` to `^2.9.4`
- update `core-js` to `^3.8.2`
- update `ng2-charts` to `~2.3.3`
- update `ngx-bootstrap` to `^5.6.2`
- update `rxjs` to `^6.6.3`
- update `simple-line-icons` to `^2.5.5`
- update `tslib` to `^1.14.1`
- update `@angular-devkit/build-angular` to `^0.901.13`
- update `@angular/cli` to `^9.1.13`
- update `@angular/compiler-cli` to `^9.1.13`
- update `@angular/language-service` to `^9.1.13`
- update `@types/jasmine` to `^3.6.2`
- update `@types/node` to `^14.14.20`
- update `karma` to `^5.2.3`

###### `v2.9.5`
- fix(simple-line-icons): Can't resolve simple-line-icons.css - fixes #196

###### dependencies update
- update `simple-line-icons`  to `^2.5.2`
- update `@angular-devkit/build-angular`  to `^0.901.12`
- update `@angular/cli`  to `^9.1.12`
- update `@types/jasmine`  to `^3.5.12`
- update `jasmine-core`  to `^3.6.0`
- update `jasmine-spec-reporter`  to `^5.0.2`
- update `karma`  to `^5.1.1`
- update `karma-coverage-istanbul-reporter`  to `^3.0.3`
- update `tslint`  to `^6.1.3`

###### `v2.9.4`
- refactor(polyfills): update core-js polyfills imports

###### dependencies update
- update `@angular/animations` to `^9.1.12`
- update `@angular/common` to `^9.1.12`
- update `@angular/compiler` to `^9.1.12`
- update `@angular/core` to `^9.1.12`
- update `@angular/forms` to `^9.1.12`
- update `@angular/platform-browser` to `^9.1.12`
- update `@angular/platform-browser-dynamic` to `^9.1.12`
- update `@angular/router` to `^9.1.12`
- update `@coreui/angular` to `~2.9.4`
- update `bootstrap` to `^4.5.0`
- update `core-js` to `^3.6.5`
- update `flag-icon-css` to `^3.5.0`
- update `moment` to `^2.27.0`
- update `mutationobserver-shim` to `^0.3.7`
- update `ng2-charts` to `^2.3.2`
- update `rxjs` to `^6.6.0`
- update `tslib` to `^1.13.0`
- update `@angular-devkit/build-angular` to `^0.901.11`
- update `@angular/cli` to `^9.1.11`
- update `@angular/compiler-cli` to `^9.1.12`
- update `@angular/language-service` to `^9.1.12`
- update `@types/jasmine` to `^3.5.11`
- update `@types/node` to `^13.13.14`
- update `karma` to `^5.1.0`
- update `karma-jasmine` to `^3.3.1`
- update `karma-jasmine-html-reporter` to `^1.5.4`
- update `protractor` to `^7.0.0`
- update `ts-node` to `^8.10.2`

###### `v2.9.2`
- fix(navbars): cannot find module `ngx-bootstrap`

###### dependencies update
- update `@angular/animations` to `^9.1.1`
- update `@angular/common` to `^9.1.1`
- update `@angular/compiler` to `^9.1.1`
- update `@angular/core` to `^9.1.1`
- update `@angular/forms` to `^9.1.1`
- update `@angular/platform-browser` to `^9.1.1`
- update `@angular/platform-browser-dynamic` to `^9.1.1`
- update `@angular/router` to `^9.1.1`
- update `@coreui/angular` to `^2.9.2`
- update `bootstrap` to `^4.4.1`
- update `chart.js` to `^2.9.3`
- update `mutationobserver-shim` to `^0.3.5`
- update `ngx-bootstrap` to `^5.6.1`
- update `ngx-perfect-scrollbar` to `^9.0.0`
- update `rxjs` to `^6.5.5`
- update `tslib` to `^1.11.1`
- update `zone.js` to `~0.10.3`
- update `@angular-devkit/build-angular` to `~0.901.1`
- update `@angular/cli` to `^9.1.1`
- update `@angular/compiler-cli` to `^9.1.1`
- update `@angular/language-service` to `^9.1.1`
- update `@types/jasmine` to `^3.5.10`
- update `@types/node` to `^13.11.1`
- update `codelyzer` to `^5.2.2`
- update `jasmine-spec-reporter` to `^5.0.1`
- update `karma` to `^5.0.1`
- update `karma-coverage-istanbul-reporter` to `^2.1.1`
- update `karma-jasmine` to `^3.1.1`
- update `karma-jasmine-html-reporter` to `^1.5.3`
- update `protractor` to `^5.4.3`
- update `ts-node` to `^8.8.2`
- update `tslint` to `^6.1.1`

###### `v2.9.0`
- chore: update to `Angular 9.0.0`
    - [https://update.angular.io/](https://update.angular.io/#8.0:9.0)
    - [https://angular.io/guide/updating-to-version-9](https://angular.io/guide/updating-to-version-9)
    - [https://blog.angular.io/version-9-of-angular-now-available-project-ivy-has-arrived](https://blog.angular.io/version-9-of-angular-now-available-project-ivy-has-arrived-23c97b63cfa3)

- chore: update to `TypeScript 3.7`
    - [https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-7.html](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-7.html)
    - [https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-6.html](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-6.html)

###### dependencies update
- update `@angular/animations` to `^9.0.0`
- update `@angular/common` to `^9.0.0`
- update `@angular/compiler` to `^9.0.0`
- update `@angular/core` to `^9.0.0`
- update `@angular/forms` to `^9.0.0`
- update `@angular/platform-browser` to `^9.0.0`
- update `@angular/platform-browser-dynamic` to `^9.0.0`
- update `@angular/router` to `^9.0.0`
- update `@coreui/angular` to `^2.9.0`
- update `flag-icon-css` to `^3.4.6`
- update `@angular-devkit/build-angular` to `^0.900.1`
- update `@angular/cli` to `^9.0.1`
- update `@angular/compiler-cli` to `^9.0.0`
- update `@angular/language-service` to `^9.0.0`
- update `@types/jasmine` to `^3.5.3`
- update `karma-jasmine-html-reporter` to `^1.5.2`
- update `typescript` to `~3.7.5`

###### `v2.7.5`
- fix(carousels): move to picsum.photos

###### dependencies update
- update `@coreui/angular` to `^2.7.5`
- update `@angular/animations` to `^9.0.0-rc.12`
- update `@angular/common` to `^9.0.0-rc.12`
- update `@angular/compiler` to `^9.0.0-rc.12`
- update `@angular/core` to `^9.0.0-rc.12`
- update `@angular/forms` to `^9.0.0-rc.12`
- update `@angular/platform-browser` to `^9.0.0-rc.12`
- update `@angular/platform-browser-dynamic` to `^9.0.0-rc.12`
- update `@angular/router` to `^9.0.0-rc.12`
- update `@angular-devkit/build-angular` to `^0.900.0-rc.12`
- update `@angular/cli` to `^9.0.0-rc.12`
- update `@angular/compiler-cli` to `^9.0.0-rc.12`
- update `@angular/language-service` to `^9.0.0-rc.12` 

###### `v2.7.2` 
- feat: add missing Navbars example - thanks @EliasDerHai

###### dependencies update
- update `@angular/animations` to `^9.0.0-rc.7`
- update `@angular/common` to `^9.0.0-rc.7`
- update `@angular/compiler` to `^9.0.0-rc.7`
- update `@angular/core` to `^9.0.0-rc.7`
- update `@angular/forms` to `^9.0.0-rc.7`
- update `@angular/platform-browser` to `^9.0.0-rc.7`
- update `@angular/platform-browser-dynamic` to `^9.0.0-rc.7`
- update `@angular/router` to `^9.0.0-rc.7`
- update `@coreui/angular` to `^2.7.2`
- update `core-js` to `^2.6.11`
- update `@angular-devkit/build-angular` to `^0.900.0-rc.7`
- update `@angular/cli` to `^9.0.0-rc.7`
- update `@angular/compiler-cli` to `^9.0.0-rc.7`
- update `@angular/language-service` to `^9.0.0-rc.7`
- update `codelyzer` to `^5.2.1`

###### `v2.7.0` for Angular 9
- chore: upgrade to Angular 9 - see also: 
    - `https://next.angular.io/guide/updating-to-version-9`
    - `https://update.angular.io/#8.0:9.0`

###### dependencies update
- update `@coreui/angular` to `^2.7.0`
- update `@angular/animations` to `^9.0.0-rc.0`
- update `@angular/common` to `^9.0.0-rc.0`
- update `@angular/compiler` to `^9.0.0-rc.0`
- update `@angular/core` to `^9.0.0-rc.0`
- update `@angular/forms` to `^9.0.0-rc.0`
- update `@angular/platform-browser` to `^9.0.0-rc.0`
- update `@angular/platform-browser-dynamic` to `^9.0.0-rc.0`
- update `@angular/router` to `^9.0.0-rc.0`
- update `zone.js` to `~0.10.2`
- update `@angular-devkit/build-angular` to `~0.900.0-rc.0`
- update `@angular/cli` to `^9.0.0-rc.0`
- update `@angular/compiler-cli` to `^9.0.0-rc.0`
- update `@angular/language-service` to `^9.0.0-rc.0`
- update `@types/jasmine` to `^3.4.5`
- update `@types/node` to `^12.11.1`
- update `codelyzer` to `^5.1.2`
- update `typescript` to `~3.6.4`
- update `chart.js` to `^2.9.2`

###### `v2.5.3`
- fix(polyfills): add optional settings for IE10 issues
- refactor(_nav.ts): INavData moved from _nav.ts to import from @coreui/angular
- refactor(default-layout): drop MutationObserver, add minimizedChange event
- fix(dashboard): `scales.[x/y]Axes.barPercentage` is deprecated. Please use `dataset.barPercentage` instead
- fix(widgets): `scales.[x/y]Axes.barPercentage` is deprecated. Please use `dataset.barPercentage` instead
- test: add e2e test bed with some tests 

###### dependencies update
- update `@angular/animations` to `^8.2.10`
- update `@angular/common` to `^8.2.12`
- update `@angular/compiler` to `^8.2.12`
- update `@angular/core` to `^8.2.12`
- update `@angular/forms` to `^8.2.12`
- update `@angular/platform-browser` to `^8.2.12`
- update `@angular/platform-browser-dynamic` to `^8.2.12`
- update `@angular/router` to `^8.2.12`
- update `@coreui/angular` to `^2.6.3`
- update `@coreui/coreui` to `^2.1.16`
- update `chart.js` to `^2.9.1`
- update `core-js` to `^2.6.10`
- update `flag-icon-css` to `^3.4.5`
- update `ngx-bootstrap` to `^5.2.0`
- update `rxjs` to `^6.5.3`
- update `web-animations-js` to `^2.3.2`
- update `zone.js` to `^0.10.2`
- update `@angular-devkit/build-angular` to `^0.803.15`
- update `@angular/cli` to `^8.3.15`
- update `@angular/compiler-cli` to `^8.2.12`
- update `@angular/language-service` to `^8.2.12`
- update `@types/jasmine` to `^3.4.4`
- update `@types/jasminewd2` to `^2.0.8`
- update `@types/node` to `^12.7.12`
- update `codelyzer` to `^5.2.0`
- update `jasmine-core` to `^3.5.0`
- update `karma` to `^4.4.1`
- update `karma-chrome-launcher` to `^3.1.0`
- update `karma-coverage-istanbul-reporter` to `^2.1.0`    
- update `ts-node` to `^8.4.1`
- update `tslint` to `^5.20.0`

###### `v2.5.2`
- fix(cards): card with header actions
- fix(carousels): images and intervals cleanup

###### dependencies update
- update `@angular/animations` to `^8.0.2`
- update `@angular/common` to `^8.0.2`
- update `@angular/compiler` to `^8.0.2`
- update `@angular/core` to `^8.0.2`
- update `@angular/forms` to `^8.0.2`
- update `@angular/platform-browser` to `^8.0.2`
- update `@angular/platform-browser-dynamic` to `^8.0.2`
- update `@angular/router` to `^8.0.2`
- update `@coreui/angular` to `^2.5.2`
- update `@coreui/coreui` to `^2.1.12`
- update `@angular-devkit/build-angular` to `^0.800.3`
- update `@angular/cli` to `^8.0.3`
- update `@angular/compiler-cli` to `^8.0.2`
- update `@angular/language-service` to `^8.0.2`
- update `@types/node` to `^12.0.8`
- update `ts-node` to `^8.3.0`
    
###### `v2.5.1`
###### dependencies update
- update `@coreui/angular` to `^2.5.1`
- update `ngx-bootstrap` to `^5.0.0` 
- update `ngx-perfect-scrollbar` to `^8.0.0`
- update `tslib` to `^1.10.0`
- update `@angular-devkit/build-angular` to `^0.800.2`
- update `@angular/cli` to `^8.0.2`
- update `@types/node` to `^12.0.7`

###### `v2.5.0`
- refactor(app.routing): lazy loading via dynamic import()
- refactor(modals): `ViewChild` second parameter 
- refactor(tsconfig): `module: esnext` and `target: es5` 
- chore: update `ng2-charts@2.3.0` imports
- chore: update `ngx-bootstrap@4.2.0` 
- fix(collapse): `No provider for AnimationBuilder` add import `BrowserAnimationsModule` to app.module
- fix(polyfill): import `web-animations-js`
- chore(tslint): remove depracated/undefined rules  
- chore(tsconfig): set "target" to "es5" / IE11 compatible

###### breaking changes
- `Angular v8.0.0` (see https://update.angular.io/)
- `@ViewChild` second parameter required 
- `BrowserAnimationsModule` required
- routing: lazy loading via dynamic import()

###### dependencies update
- update: Angular to `v8.0.0`
- update: `@angular/animations` to `^8.0.0`
- update: `@angular/common` to `^8.0.0`
- update: `@angular/compiler` to `^8.0.0`
- update: `@angular/core` to `^8.0.0`
- update: `@angular/forms` to `^8.0.0`
- update: `@angular/http` to `^8.0.0-beta.10`
- update: `@angular/platform-browser` to `^8.0.0`
- update: `@angular/platform-browser-dynamic` to `^8.0.0`
- update: `@angular/router` to `^8.0.0`
- update: `@coreui/angular` to `^2.5.0`
- update: `@coreui/coreui` to `^2.1.11`
- update: `@coreui/coreui-plugin-chartjs-custom-tooltips` to `^1.3.1`
- update: `core-js` to `^2.6.9`
- update: `ng2-charts` to `^2.3.0`
- update: `ngx-boottsrap` to `^4.2.0`
- update: `rxjs` to `^6.5.2`
- update: `tsickle` to `^0.35.0`
- update: `tslib` to `^1.9.3`
- update: `zone.js` to `^0.9.1`
- update: `@angular-devkit/build-angular` to `^0.800.1`
- update: `@angular/cli` to `^8.0.1`
- update: `@angular/compiler-cli` to `^8.0.0`
- update: `@angular/language-service` to `^8.0.0`
- update: `@types/node` to `^12.0.4`
- update: `codelyzer` to `^5.1.0`
- update: `jasmine-core` to `^3.4.0`
- update: `karma` to `^4.1.0`
- update: `karma-jasmine-html-reporter` to `^1.4.2`
- update: `ts-node` to `^8.2.0`
- update: `tslint` to `^5.17.0`
- update: `typescript` to `~3.4.5`

###### `v2.4.5`
- refactor(modals): add `@ViewChild` 
- update: `@coreui/angular` to `^2.4.5`
- update: `@coreui/coreui` to `^2.1.8`
- update: `@angular/animations` to `^7.2.10`
- update: `@angular/common` to `^7.2.10`
- update: `@angular/compiler` to `^7.2.10`
- update: `@angular/core` to `^7.2.10`
- update: `@angular/forms` to `^7.2.10`
- update: `@angular/http` to `^7.2.10`
- update: `@angular/platform-browser` to `^7.2.10`
- update: `@angular/platform-browser-dynamic` to `^7.2.10`
- update: `@angular/router` to `^7.2.10`
- update: `chart.js` to `^2.8.0`
- update: `flag-icon-css` to `^3.3.0`
- update: `@angular-devkit/build-angular` to `^0.13.6`
- update: `@angular/cli` to `^7.3.6`
- update: `@angular/compiler-cli` to `^7.2.10`
- update: `@angular/language-service` to `^7.2.10`
- update: `@types/jasmine` to `^3.3.12`
- update: `@types/node` to `^11.11.4`
- update: `karma` to `^4.0.1`
- update: `ts-node` to `^8.0.3`
- update: `tslint` to `^5.14.0`

###### `v2.4.3`
- update: `@coreui/angular` to `^2.4.3`
- update: `@coreui/coreui` to `^2.1.7`
- update: `@angular/animations` to `^7.2.6`
- update: `@angular/common` to `^7.2.6`
- update: `@angular/compiler` to `^7.2.6`
- update: `@angular/core` to `^7.2.6`
- update: `@angular/forms` to `^7.2.6`
- update: `@angular/http` to `^7.2.6`
- update: `@angular/platform-browser` to `^7.2.6`
- update: `@angular/platform-browser-dynamic` to `^7.2.6`
- update: `@angular/router` to `^7.2.6`
- update: `@angular-devkit/build-angular` to `^0.13.3`
- update: `@angular/cli` to `^7.3.3`
- update: `@angular/compiler-cli` to `^7.2.6`
- update: `@angular/language-service` to `^7.2.6`
- update: `ngx-perfect-scrollbar` to `^7.2.1`

###### `v2.4.0`
- fix(sidebar): navItems reassignment bug #126, #42
- refactor(default-layout): use `cui-breadcrumb` instead of deprecated `app-breadcrumb` (migrate when ready)
- fix(polyfills.ts): ie issues add `core-js/es7/array` and `core-js/es7/object`
- update: `@angular/animations` to `^7.2.5`
- update: `@angular/common` to `^7.2.5`
- update: `@angular/compiler` to `^7.2.5`
- update: `@angular/core` to `^7.2.5`
- update: `@angular/forms` to `^7.2.5`
- update: `@angular/http` to `^7.2.5`
- update: `@angular/platform-browser` to `^7.2.5`
- update: `@angular/platform-browser-dynamic` to `^7.2.5`
- update: `@angular/router` to `^7.2.5`
- update: `@coreui/angular` to `^2.4.1`
- update: `bootstrap` to `^4.3.1`
- update: `core-js` to `^2.6.5`
- update: `moment` to `^2.24.0`
- update: `ngx-bootstrap` to `^3.2.0`
- update: `rxjs` to `^6.4.0`
- update: `tsickle` to `^0.34.3`
- update: `zone.js` to `^0.8.29`
- update: `@angular-devkit/build-angular` to `^0.13.2`
- update: `@angular/cli` to `^7.3.2`
- update: `@angular/compiler-cli` to `^7.2.5`
- update: `@angular/language-service` to `^7.2.5`
- update: `@types/jasmine` to `^3.3.9`
- update: `@types/node` to `^10.12.26`
- update: `karma` to `^4.0.0`
- update: `karma-coverage-istanbul-reporter` to `^2.0.5`
    
###### `v2.2.4`
- refactor(colors): use 'DOCUMENT' of '@angular/common'
- refactor(default-layout): use 'DOCUMENT' of '@angular/common' #133 - thanks @damingerdai 
- refactor(main): add compiler option `preserveWhitespaces` #128 - thanks @ctaleck 
- refactor(routing): added URL 404 default #125 - thanks @slam24
- refactor(dropdowns): remove temp css fix
- refactor(_nav.ts): NavData interface add semicolons
- update: `@angular/animations` to `^7.2.1`
- update: `@angular/common` to `^7.2.1`
- update: `@angular/compiler` to `^7.2.1`
- update: `@angular/core` to `^7.2.1`
- update: `@angular/forms` to `^7.2.1`
- update: `@angular/http` to `^7.2.1`
- update: `@angular/platform-browser` to `^7.2.1`
- update: `@angular/platform-browser-dynamic` to `^7.2.1`
- update: `@angular/router` to `^7.2.1`
- update: `@coreui/angular` to `^2.2.4`
- update: `mutationobserver-shim` to `^0.3.3`
- update: `zone.js` to `^0.8.28`
- update: `@angular-devkit/build-angular` to `^0.12.2`
- update: `@angular/cli` to `^7.2.2`
- update: `@angular/compiler-cli` to `^7.2.1`
- update: `@angular/language-service` to `^7.2.1`
- update: `@types/jasmine` to `^3.3.7`
- update: `typescript` to `~3.2.4`

###### `v2.2.3`
- refactor(_nav.ts): add NavData interface
- update: `bootstrap` to `^4.2.1`
- update: `@coreui/angular` to `^2.2.3`
- update: `@coreui/coreui` to `^2.1.6`
- update: `@angular/animations` to `^7.2.0`
- update: `@angular/common` to `^7.2.0`
- update: `@angular/compiler` to `^7.2.0`
- update: `@angular/core` to `^7.2.0`
- update: `@angular/forms` to `^7.2.0`
- update: `@angular/http` to `^7.2.0`
- update: `@angular/platform-browser` to `^7.2.0`
- update: `@angular/platform-browser-dynamic` to `^7.2.0`
- update: `@angular/router` to `^7.2.0`
- update: `core-js` to `^2.6.2`
- update: `moment` to `^2.23.0`
- update: `ngx-bootstrap` to `^3.1.4`
- update: `ngx-perfect-scrollbar` to `^7.2.0`
- update: `zone.js` to `^0.8.27`
- update: `@angular-devkit/build-angular` to `^0.12.1`
- update: `@angular/cli` to `^7.2.1`
- update: `@angular/compiler-cli` to `^7.2.0`
- update: `@angular/language-service` to `^7.2.0`
- update: `@types/jasmine` to `^3.3.5`
- update: `@types/node` to `^10.12.18`
- update: `karma` to `^3.1.4`
- update: `protractor` to `^5.4.2`
- update: `tslint` to `^5.12.1`

###### `v2.2.1`
- fix(routes): add default routes
- fix(colors): class order for `bg-blue`
- refactor(dafault-layout): observer add `attributeFilter` prop & `disconnect()` 
- update: `@coreui/angular` to `2.2.1`
- update: `core-js` to `2.6.0`
- update: `@types/node` to `10.12.12`
- update: `@angular/cli` to `7.1.1`
- update: `@angular-devkit/build-angular` to `0.11.1`
- update: `@types/jasmine` to `3.3.1`

###### `v2.2.0`
- chore: update Angular to `^7.1.0` see: https://update.angular.io/
- update: `@angular/animations` to `^7.1.1`
- update: `@angular/common` to `^7.1.1`
- update: `@angular/compiler` to `^7.1.1`
- update: `@angular/core` to `^7.1.1`
- update: `@angular/forms` to `^7.1.1`
- update: `@angular/http` to `^7.1.1`
- update: `@angular/platform-browser` to `^7.1.1`
- update: `@angular/platform-browser-dynamic` to `^7.1.1`
- update: `@angular/router` to `^7.1.1`
- update: `@coreui/angular` to `^2.2.0`
- update: `ngx-perfect-scrollbar` to `^7.1.0`
- update: `tslib` to `^1.9.0`
- update: `@angular-devkit/build-angular` to `~0.11.0`
- update: `@angular/cli` to `^7.1.0`
- update: `@angular/compiler-cli` to `^7.1.1`
- update: `@angular/language-service` to `^7.1.1`
- update: `@types/jasmine` to `^3.3.0`
- update: `karma-jasmine` to `^2.0.1`
- update: `typescript` to `3.1.x`

###### `v2.1.0`
- feat: sidebar nav-link  `attributes` - optional JS object with valid JS API naming:
  - valid attributes: `rel`, `target`, `hidden`, `disabled`, etc...
  - item example (`_nav.ts`):
```
...
{
  name: 'Try CoreUI PRO',
  url: 'https://coreui.io/pro/react/',
  icon: 'cui-layers icons',
  variant: 'danger',
  attributes: { target: '_blank', rel: "noopener" },
},
...
```
- update: `@coreui/angular` to `2.1.0`
- update: `@coreui/coreui` to `^2.1.3`
- update: `ngx-bootstrap` to `^3.1.2`
- update: `@angular/cli` to `^6.2.8` 
- update: `@types/jasmine` to `^2.8.12`
- update: `@types/jasminewd2` to `^2.0.6`
- update: `@types/node` to `^10.12.11`
- update: `jasmine-core` to `^3.3.0`
- update: `karma` to `^3.1.3`
- update: `karma-jasmine-html-reporter` to `^1.4.0`


###### `v2.0.1`
- refactor(modals): buttons spacing
- refactor(brand-buttons): buttons spacing
- update: `@coreui/coreui` to `2.0.20`
- update: `@angular/*` to `6.1.10`
- update: `@angular/cli` to `6.2.6`
- update: `chart.js` to `2.7.3`
- update: `flag-icon-css` to `3.2.1`
- update: `ngx-perfect-scrollbar` to `6.3.1`
- update: `rxjs` to `6.3.3`
- update: `rxjs-compat` to `6.3.3`
- update: `tsickle` to `0.33.0`
- update: `@types/jasmine` to `2.8.9`
- update: `@types/jasminewd2` to `2.0.5`
- update: `@types/node` to `10.12.0`
- update: `codelyzer` to `4.5.0`
- update: `karma-coverage-istanbul-reporter` to `2.0.4`
- update: `protractor` to `5.4.1`

###### `v2.0.0`
- fix(dropdowns): dropup misplaced temp fix
- chore: update `@coreui/icons` to `0.3.0`
- refactor(coreui-icons): move to `@coreui/icons v0.3.0`
- update: `@angular/*` to `6.1.6`
- update: `angular-devkit/build-angular` to `0.7.5`
- update: `@angular/cli` to `6.1.5`
- update: `rxjs` to `6.3.0`
- update: `rxjs-compat` to `6.3.0`
- update: `@types/node` to `10.9.4`
- update: `jasmine-core` to `3.2.1`
- update: `karma-coverage-istanbul-reporter` to `2.0.2`
- update: `karma-jasmine-html-reporter` to `1.3.1`

###### `v2.0.0-rc.4`
- fix(forms): validation classes example closes #95  
- fix(forms): card-header-actions example
- fix(forms): autocomplete
- fix(login): form, autocomplete
- fix(register): form, autocomplete
- update: `@types/node` to `10.7.0`
- update: `codelyzer` to `4.4.4`
- update: `karma-jasmine-html-reporter` to `1.3.0`
- update: `typescript` to `2.9.2`

###### `v2.0.0-rc.3`
- fix(carousels): move to `loremflickr` image placeholders
- refactor: code cleanup
- tests: fix minimal testing
- update: `@angular/*` to `6.1.2`
- update: `@angular/cli` to `6.1.3`
- update: `@angular-devkit/build-angular` to `0.7.3`
- update: `ngx-perfect-scrollbar` to `6.3.0`
- update: `rxjs` to `6.2.2`
- update: `rxjs-compat` to `6.2.2`
- update: `@types/node` to `10.5.8`
- update: `codelyzer` to `4.4.3`
- update: `jasmine-core` to `3.2.0`
- update: `karma` to `3.0.0`
- update: `protractor` to `5.4.0`
- update: `ts-node` to `7.0.1`
- update: `tslint` to `5.11.0`

###### `v2.0.0-rc.2`
- update: @angular/animations to 6.0.9
- update: @angular/common to 6.0.9
- update: @angular/compiler to 6.0.9
- update: @angular/core to 6.0.9
- update: @angular/forms to 6.0.9
- update: @angular/http to 6.0.9
- update: @angular/platform-browser to 6.0.9
- update: @angular/platform-browser-dynamic to 6.0.9
- update: @angular/router to 6.0.9
- update: @coreui/coreui to 2.0.4
- update: @coreui/icons to 0.3.0
- update: bootstrap to 4.1.2
- update: codelyzer to 4.4.2
- update: karma-jasmine-html-reporter to 1.2.0

###### `v2.0.0-rc.1`
- chore: dependencies update

###### `v1.0.10`
- update: ngx-bootstrap to `2.0.2`
- update: dependencies

###### `v1.0.9`
- update: bootstrap to `v4.0.0`
- update: dependencies

###### `v1.0.8`
- update: bootstrap to `4.0.0-beta.3`
- update: ngx-bootstrap to `2.0.0-rc.0`
- fix(forms): duplicate `select` ids, toggleCollapse
- fix(dashboard): btnRadio
- refactor: `input-group-addon` to new `4.0.0-beta.3` classes
- feature: some Bootstrap4 components added
- feat: mobile sidebar link click closes the sidebar
- update: dependencies
