{"ast": null, "code": "import castPath from './_castPath.js';\nimport to<PERSON>ey from './_toKey.js';\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\n\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n\n  return index && index == length ? object : undefined;\n}\n\nexport default baseGet;", "map": null, "metadata": {}, "sourceType": "module"}