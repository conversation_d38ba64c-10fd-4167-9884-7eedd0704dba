{"ast": null, "code": "import isStrictComparable from './_isStrictComparable.js';\nimport keys from './keys.js';\n/**\n * Gets the property names, values, and compare flags of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the match data of `object`.\n */\n\nfunction getMatchData(object) {\n  var result = keys(object),\n      length = result.length;\n\n  while (length--) {\n    var key = result[length],\n        value = object[key];\n    result[length] = [key, value, isStrictComparable(value)];\n  }\n\n  return result;\n}\n\nexport default getMatchData;", "map": null, "metadata": {}, "sourceType": "module"}