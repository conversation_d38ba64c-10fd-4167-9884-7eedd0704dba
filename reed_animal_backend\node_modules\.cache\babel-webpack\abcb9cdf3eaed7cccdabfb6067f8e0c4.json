{"ast": null, "code": "import memoizeCapped from './_memoizeCapped.js';\n/** Used to match property names within property paths. */\n\nvar rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n/** Used to match backslashes in property paths. */\n\nvar reEscapeChar = /\\\\(\\\\)?/g;\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\n\nvar stringToPath = memoizeCapped(function (string) {\n  var result = [];\n\n  if (string.charCodeAt(0) === 46\n  /* . */\n  ) {\n    result.push('');\n  }\n\n  string.replace(rePropName, function (match, number, quote, subString) {\n    result.push(quote ? subString.replace(reEscapeChar, '$1') : number || match);\n  });\n  return result;\n});\nexport default stringToPath;", "map": null, "metadata": {}, "sourceType": "module"}