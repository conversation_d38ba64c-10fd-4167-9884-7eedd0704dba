{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Pseudo [x-pseudo]\n//! author : <PERSON> : https://github.com/andrewhood125\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var xPseudo = moment.defineLocale('x-pseudo', {\n    months: 'J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér'.split('_'),\n    monthsShort: 'J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý'.split('_'),\n    weekdaysShort: 'S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát'.split('_'),\n    weekdaysMin: 'S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[T~ódá~ý át] LT',\n      nextDay: '[T~ómó~rró~w át] LT',\n      nextWeek: 'dddd [át] LT',\n      lastDay: '[Ý~ést~érdá~ý át] LT',\n      lastWeek: '[L~ást] dddd [át] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'í~ñ %s',\n      past: '%s á~gó',\n      s: 'á ~féw ~sécó~ñds',\n      ss: '%d s~écóñ~ds',\n      m: 'á ~míñ~úté',\n      mm: '%d m~íñú~tés',\n      h: 'á~ñ hó~úr',\n      hh: '%d h~óúrs',\n      d: 'á ~dáý',\n      dd: '%d d~áýs',\n      M: 'á ~móñ~th',\n      MM: '%d m~óñt~hs',\n      y: 'á ~ýéár',\n      yy: '%d ý~éárs'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\n    ordinal: function (number) {\n      var b = number % 10,\n          output = ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return xPseudo;\n});", "map": null, "metadata": {}, "sourceType": "script"}