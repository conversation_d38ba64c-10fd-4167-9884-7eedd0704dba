<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <!-- <div class="card-header">
        Pet Detail
      </div> -->
      <div class="card-body">
        <div class="card-body">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12">
              <h5 class="mb-3">Customer information</h5>

              <div class="row">

                <div class="form-group col-sm-6">
                  <label for="Name">Name</label>
                  <input type="text" id="Name" [(ngModel)]="user.first_name" class="form-control"
                  [ngModelOptions]="{standalone: true}" readonly>
                </div>

                <div class="form-group col-sm-6">
                  <label for="Email">Email</label>
                  <input type="email" id="Email" [(ngModel)]="user.email" class="form-control"
                  [ngModelOptions]="{standalone: true}" readonly>
                </div>

              </div>
              <div class="row">

                <!-- <div class="form-group col-sm-6">
                  <label for="Phone">Phone</label>
                  <input type="text" id="Phone" [(ngModel)]="user.phone_number" class="form-control"
                  [ngModelOptions]="{standalone: true}" readonly>
                </div> -->

                <div class="form-group col-sm-6">
                  <label for="registered-date">Registered Date</label>
                  <input type="text" id="registered-date" datetime="yyyy-MM-dd" [(ngModel)]="date" class="form-control"
                  [ngModelOptions]="{standalone: true}" readonly>
                </div>

              </div>
            </div>
          </div>
          <hr>
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 my-3">

              <form *ngIf="valid">
                <h5 class="mb-3">Pet information</h5>
                <div style="margin-bottom: 30px; text-align: left;">
                  <select id="select1" name="select1" class="form-control" (change)="onChange($event.target.value);">
                    <option *ngFor="let role of Pets" [value]="role._id">{{role.animal_type}} - {{role.pet_name}}
                    </option>
                  </select>
                  <!-- <button type="button" class="btn btn-primary mr-1" data-toggle="modal" (click)="primaryModal.show()">
                Add Pet
                </button> -->
                </div>

                <div class="row">
                  <div class="col-sm-3" style="margin:auto;">
                    <div class="pet-imageprofile">
                      <img [src]="customers.image_url" style="width: 100%">
                    </div>
                    <!-- <div class="imge-edit-delete">
                    <a data-toggle="modal" style="cursor: pointer;"><span
                        class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                    <a data-toggle="modal" (click)="removeModal.show();" style="cursor: pointer;"><span
                        class="badge badge-danger"><i class="fa fa-trash"></i> Delete</span></a>
                  </div> -->
                  </div>
                </div>
                <div class="row">

                  <div class="form-group col-sm-6">
                    <label for="petname">Pet Name</label>
                    <input type="text" id="petname" [(ngModel)]="customers.pet_name"
                      [ngModelOptions]="{standalone: true}" class="form-control" readonly>
                  </div>

                  <div class="form-group col-sm-6">
                    <label for="Animaltype">Species</label>
                    <input type="text" id="Animaltype" [(ngModel)]="customers.animal_type"
                      [ngModelOptions]="{standalone: true}" class="form-control" readonly>
                  </div>

                </div>

                <div class="row">

                  <div class="form-group col-sm-6">
                    <label for="Breed">Breed</label>
                    <input type="text" id="Breed" [(ngModel)]="customers.breed" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>

                  <div class="form-group col-sm-6">
                    <label for="Coat-color">Coat Color</label>
                    <input type="text" id="Coat-color" [(ngModel)]="customers.color" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>

                </div>
                <div class="row">

                  <div class="form-group col-sm-6">
                    <label for="Gender">Gender</label>
                    <input type="text" id="Gender" [(ngModel)]="customers.gender" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>
                  <div class="form-group col-sm-6">
                    <label for="Spayed">Spayed</label>
                    <input type="text" id="Spayed" [(ngModel)]="customers.spay" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>

                </div>
                <div class="row">

                  <div class="form-group col-sm-6">
                    <label for="Birth">Date of Birth</label>
                    <input type="text" id="Birth" [(ngModel)]="customers.dob" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>
                  <div class="form-group col-sm-6">
                    <label for="Age">Pet Medical Id</label>
                    <input type="text" id="age" [(ngModel)]="customers.pet_mid" class="form-control"
                      [ngModelOptions]="{standalone: true}" readonly>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <div>
            <hr>
            <div class="row">
              <div class="col-md-12" style="margin:auto; margin-bottom:50px;">
                <!-- Nav tabs -->
                <tabset>
                  <tab>
                    <ng-template tabHeading>Upcoming</ng-template>
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Time</th>
                          <th>Pet Name</th>
                          <th>Appointment Type</th>
                          <th>Doctor</th>
                          <th>Location</th>
                          <th>Reason</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let user of upcomings">
                          <td>{{user.date|date:'dd MMM yyyy'}}</td>
                          <td>{{user.time}}</td>
                          <td>{{user.pet_name}}</td>
                          <td>{{user.prefer}}</td>
                          <td>{{user.doctor_name}}</td>
                          <td>{{user.location}}</td>
                          <td>{{user.kind_appointment}}</td>
                        </tr>
                      </tbody>
                    </table>

                  </tab>
                  <tab>
                    <ng-template tabHeading>Past Visits</ng-template>
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Time</th>
                          <th>Pet Name</th>
                          <th>Appointment Type</th>
                          <th>Doctor</th>
                          <th>Location</th>
                          <th>Reason</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let user of pastvisits">
                          <td>{{user.date|date:'dd MMM yyyy'}}</td>
                          <td>{{user.time}}</td>
                          <td>{{user.pet_name}}</td>
                          <td>{{user.prefer}}</td>
                          <td>{{user.doctor_name}}</td>
                          <td>{{user.location}}</td>
                          <td>{{user.kind_appointment}}</td>
                        </tr>
                      </tbody>
                    </table>
                  </tab>
                </tabset>
              </div>
              <!--/.col-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--/.col-->
</div>

<!-- Add Modal -->
<!-- <div bsModal #primaryModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Add Pet</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">

            <form class="form" [formGroup]="AddForm" autocomplete=off>

              <div class="form-group">
                <label for="pet_name">Pet Name</label>
                <input type="text" class="form-control" placeholder="Enter Pet Name" formControlName="pet_name"
                  [ngClass]="{ 'is-invalid': submitted && f.pet_name.errors }" />
                <div *ngIf="submitted && f.pet_name.errors" class="invalid-feedback">
                  <div *ngIf="f.pet_name.errors.required">Name is required</div>
                </div>
              </div>

              <div class="form-group">
                <label for="color">color</label>
                <input type="text" class="form-control" placeholder="Enter color" formControlName="color"
                  [ngClass]="{ 'is-invalid': submitted && f.color.errors }" />
                <div *ngIf="submitted && f.color.errors" class="invalid-feedback">
                  <div *ngIf="f.color.errors.required">Color is required</div>
                </div>
              </div>

              <div class="form-group">
                <label for="dob">Date of Birth</label>
                <input type="text" class="form-control" [minDate]="minDate" [maxDate]="maxDate" #dp="bsDatepicker"
                  bsDatepicker [(bsValue)]="myDateValue">
                <div *ngIf="dobfailed" style="font-size: smaller;color: red;">*please select Date of Birth</div>
              </div>

              <div class="form-group">
                <label for="Species">Species</label>
                <select class="form-control" (change)="Species($event)">
                  <option value=''>--Select--</option>
                  <option *ngFor="let role of types" [ngValue]="role.name">{{role.name}}</option>
                </select>
                <div *ngIf="speciefailed" style="font-size: smaller;color: red;">*please select Species</div>
              </div>

              <div class="form-group">
                <label for="Breed">Breed</label>
                <select class="form-control" (change)="Breed($event)">
                  <option value=''>--Select--</option>
                  <option *ngFor="let role of breedings" [ngValue]="role.name">{{role.name}}</option>
                </select>
                <div *ngIf="breedfailed" style="font-size: smaller;color: red;">*please select Breed</div>
              </div>

              <div class="form-group">
                <label for="Gender">Gender</label>
                <select class="form-control" (change)="Gender($event)">
                  <option value=''>--Select--</option>
                  <option value='Male'>Male</option>
                  <option value='Female'>Female</option>
                </select>
                <div *ngIf="genderfailed" style="font-size: smaller;color: red;">*please select Gender</div>
              </div>

              <div class="form-group">
                <label for="spay">Spay</label>
                <select class="form-control" (change)="Spay($event)">
                  <option value=''>--Select--</option>
                  <option value='Yes'>Yes</option>
                  <option value='No'>No</option>
                </select>
                <div *ngIf="spayfailed" style="font-size: smaller;color: red;">*please select Spay</div>
              </div>

              <div class="form-group">
                <label for="PetPicture">Pet Picture</label>
                <input type="file" #file (change)="onChanged($event)">
                <div *ngIf="picfailed" style="font-size: smaller;color: red;">*please select Picture</div>
              </div>

            </form>
          </div>
        </div>
        <div *ngIf="picfailed" style="font-size: small;color: red;">*please select Above field</div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="primaryModal.hide();clear();">Cancel</button>
        <button class="btn btn-primary" type="submit" (click)="AddCustomer();">Save</button>
      </div>
    </div>
  </div>
</div> -->

<!-- Edit Modal -->
<!-- <div bsModal #EditModal="bs-modal" id="myModal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-primary" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Edit Customer</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group">
              <label for="name">Name</label>
              <input type="text" id="role-name1" placeholder="Enter Name" class="form-control" autocomplete="off"
                required [(ngModel)]="user.first_name" [ngModelOptions]="{standalone: true}"
                (keydown.enter)="EditCustomer();" />
            </div>
            <div class="form-group">
              <label for="name">color</label>
              <input type="color" id="color-name21" placeholder="Enter color" class="form-control" autocomplete="off"
                required [(ngModel)]="user.color" [ngModelOptions]="{standalone: true}" (keydown.enter)="EditCustomer()"
                readonly />
            </div>
            <div class="form-group">
              <label for="name">Phone Number</label>
              <input type="color" id="color-name1" placeholder="Enter Phone Number" class="form-control"
                autocomplete="off" required [(ngModel)]="user.phone_number" [ngModelOptions]="{standalone: true}"
                (keydown.enter)="EditCustomer()" />
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="EditModal.hide();clear();">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="EditCustomer(user._id);">Save</button>
      </div>
    </div>
  </div>
</div> -->

<!-- Delete Modal -->
<!-- <div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-danger modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Are you sure ?</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <p>Do you want to delete this Pet?</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="DeletePet(pet_id)">Delete</button>
      </div>
    </div>
  </div>
</div> -->