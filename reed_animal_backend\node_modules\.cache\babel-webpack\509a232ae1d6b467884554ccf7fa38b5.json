{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport stubArray from './stubArray.js';\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Built-in value references. */\n\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\n\nvar getSymbols = !nativeGetSymbols ? stubArray : function (object) {\n  if (object == null) {\n    return [];\n  }\n\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function (symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\nexport default getSymbols;", "map": null, "metadata": {}, "sourceType": "module"}