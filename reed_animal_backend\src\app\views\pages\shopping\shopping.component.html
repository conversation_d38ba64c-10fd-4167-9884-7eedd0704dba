<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                Products
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 my-3">
                        <button type="button" routerLink="/pages/products" class="btn btn-primary mr-1">
              Add product
            </button>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12 form-group table-search" style="width:50%;">
                            <label style="visibility: hidden;margin: 0;"> &nbsp;</label>
                            <div class="input-group" style="top: 3px;">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" (click)="page=1; ListProduct()"><i class="fa fa-search"></i></span>
                                </div>
                                <input type="text" id="Search" name="Search" placeholder="Search" autocomplete="off" class="form-control" (input)="page=1; ListProduct()" [(ngModel)]="name" />
                            </div>
                        </div>
                    </div>
 
                </div>
 
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Product Name</th>
                            <th>SKU</th>
                            <th>Product Image</th>
                            <th>Category</th>
                            <th>Brand</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let user of Products| paginate: { id: 'listing_pagination',
            itemsPerPage: 10,
            currentPage: page,
            totalItems: count };let i = index;">
                            <!-- <p>{{user|json}}</p> -->
                            <td>{{user.title}}</td>
                            <td>{{user.sku}}</td>
                            <td><img [src]="user.poster_image" style="width: 35px;"></td>
                            <td>{{user.category}}</td>
                            <td>{{user.brand}}</td>
 
                            <td><label class="switch">                          
                <input type="checkbox" (change)="changed($event,user._id);event($event); "  
                  [checked]="user.status">
                  <span class="slider round"></span>
                </label></td>
                            <td>
                                <a data-toggle="modal" (click)="EditProduct(user._id,user);" style="cursor: pointer; margin-right: 10px;"><span
                    class="badge badge-success"><i class="fa fa-edit"></i> Edit</span></a>
                                <a style="cursor: pointer; margin-right: 10px;" (click)="openNotes(user)"><span class="badge badge-info"><i class="fa fa-sticky-note"></i> Notes</span></a>
                                <a style="cursor: pointer;" (click)="GetProductById(i,'Delete')"><span class="badge badge-danger"><i class="fa fa-trash"></i>
                    Delete</span></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="width:100%;">
                    <pagination-controls id="listing_pagination" style="text-align:right;" maxSize="5" directionLinks="true" (pageChange)="handlePageChange($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!--/.col-->
</div>
 
<!-- Delete Modal-->
<div bsModal #removeModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-danger modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Are you sure ?</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <p>Do you want to delete this Product?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="removeModal.hide()">Cancel</button>
                <button type="button" class="btn btn-danger" (click)="DeleteProduct(id)">Delete</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->
 
<!-- Notes Modal-->
<div bsModal #notesModal="bs-modal" class="modal fade" tabindex="-1" role="dialog" [config]="{'backdrop':'static', 'keyboard': false}" aria-labelledby="notesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Notes for {{currentProductName}}</h4>
                <button type="button" class="close" (click)="notesModal.hide()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label for="notes">Notes:</label>
                            <textarea class="form-control" id="notes" rows="6" [(ngModel)]="currentNote" placeholder="Enter your notes here..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="notesModal.hide()">Cancel</button>
                <button type="button" class="btn btn-primary" (click)="saveNote()">Save Notes</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->