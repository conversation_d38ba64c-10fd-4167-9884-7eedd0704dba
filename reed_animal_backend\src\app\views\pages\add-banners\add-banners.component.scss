.styled-checkbox {
    position: absolute;
    opacity: 0;
}

.styled-checkbox + label {
    position: relative;
    cursor: pointer;
    padding: 0;
}

.styled-checkbox + label:before {
    content: "";
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #eee;
}

.styled-checkbox:hover + label:before {
    background: #568d2c;
}

.styled-checkbox:focus + label:before {
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
}

.styled-checkbox:checked + label:before {
    background: #568d2c;
}

.styled-checkbox:disabled + label {
    color: #b8b8b8;
    cursor: auto;
}

.styled-checkbox:disabled + label:before {
    box-shadow: none;
    background: #ddd;
}

.styled-checkbox:checked + label:after {
    content: "";
    position: absolute;
    left: 5px;
    top: 10px;
    background: white;
    width: 2px;
    height: 2px;
    box-shadow: 2px 0 0 white, 4px 0 0 white, 4px -2px 0 white, 4px -4px 0 white, 4px -6px 0 white, 4px -8px 0 white;
    transform: rotate(45deg);
}

[type=file] {
    height: 0;
    overflow: hidden;
    width: 0;
}

[type=file] + label {
    background: #f15d22;
    border: none;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-family: "Rubik", sans-serif;
    font-size: inherit;
    font-weight: 500;
    margin-bottom: 1rem;
    outline: none;
    padding: 1rem 50px;
    position: relative;
    transition: all 0.3s;
    vertical-align: middle;
}

[type=file] + label:hover {
    background-color: #d3460d;
}

[type=file] + label.btn-2 {
    background-color: #568d2c;
    border-radius: 50px;
    overflow: hidden;
    width: 150px;
    margin-left: 43%;
}

[type=file] + label.btn-2::before {
    color: #fff;
    content: "\f093";
    font: normal normal normal 14px/1 FontAwesome;
    font-size: 100%;
    height: 100%;
    right: 130%;
    line-height: 3.3;
    position: absolute;
    top: 0px;
    transition: all 0.3s;
}

[type=file] + label.btn-2:hover {
    background-color: #497f42;
}

[type=file] + label.btn-2:hover::before {
    right: 75%;
}

.img-wrap {
    position: relative;
    display: inline-block;
    // border: 1px red solid;
    font-size: 0;
}

.img-wrap .close {
    position: absolute;
    top: 18px;
    right: 2px;
    z-index: 100;
    background-color: #FFF;
    padding: 5px 2px 2px;
    color: #000;
    font-weight: bold;
    cursor: pointer;
    opacity: .2;
    text-align: center;
    font-size: 22px;
    line-height: 10px;
    border-radius: 50%;
}

.img-wrap:hover .close {
    opacity: 1;
}

.errorfileval {
    visibility: hidden;
    padding: 0;
}

.form-control.errorfileval.is-invalid {
    border: none;
    overflow: hidden;
    padding: 0;
    background: none;
}

.image_view {
    height: 150px;
    width: 300px;
    text-align: center;
    // background-color: #497f42;
    position: relative;
    left: 36%;
    top: 5%;
    border: 1px black dotted;
}