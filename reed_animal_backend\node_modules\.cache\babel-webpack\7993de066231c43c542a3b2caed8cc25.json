{"ast": null, "code": "import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseUniq from './_baseUniq.js';\n/**\n * The base implementation of methods like `_.xor`, without support for\n * iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of values.\n */\n\nfunction baseXor(arrays, iteratee, comparator) {\n  var length = arrays.length;\n\n  if (length < 2) {\n    return length ? baseUniq(arrays[0]) : [];\n  }\n\n  var index = -1,\n      result = Array(length);\n\n  while (++index < length) {\n    var array = arrays[index],\n        othIndex = -1;\n\n    while (++othIndex < length) {\n      if (othIndex != index) {\n        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);\n      }\n    }\n  }\n\n  return baseUniq(baseFlatten(result, 1), iteratee, comparator);\n}\n\nexport default baseXor;", "map": null, "metadata": {}, "sourceType": "module"}