{"ast": null, "code": "import basePropertyOf from './_basePropertyOf.js';\n/** Used to map characters to HTML entities. */\n\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\n\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\nexport default escapeHtmlChar;", "map": null, "metadata": {}, "sourceType": "module"}