{"ast": null, "code": "import { Injectable, Inject, Renderer2, Directive, Input, HostListener, ElementRef, NgModule, Component, HostBinding, ɵɵdefineInjectable, ɵɵinject, EventEmitter, Output, Pipe } from '@angular/core';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { NavigationEnd, Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/router';\nimport * as ɵngcc2 from '@angular/common';\nconst _c0 = [\"*\"];\n\nfunction AppBreadcrumbComponent_ng_template_0_li_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"a\", 4);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ɵngcc0.ɵɵnextContext(2).$implicit;\n    ɵngcc0.ɵɵproperty(\"routerLink\", breadcrumb_r1.url);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(breadcrumb_r1.label.title);\n  }\n}\n\nfunction AppBreadcrumbComponent_ng_template_0_li_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 4);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ɵngcc0.ɵɵnextContext(2).$implicit;\n    ɵngcc0.ɵɵproperty(\"routerLink\", breadcrumb_r1.url);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(breadcrumb_r1.label.title);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    active: a0\n  };\n};\n\nfunction AppBreadcrumbComponent_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"li\", 2);\n    ɵngcc0.ɵɵtemplate(1, AppBreadcrumbComponent_ng_template_0_li_0_a_1_Template, 2, 2, \"a\", 3);\n    ɵngcc0.ɵɵtemplate(2, AppBreadcrumbComponent_ng_template_0_li_0_span_2_Template, 2, 2, \"span\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const last_r2 = ɵngcc0.ɵɵnextContext().last;\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction1(3, _c1, last_r2));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !last_r2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", last_r2);\n  }\n}\n\nfunction AppBreadcrumbComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, AppBreadcrumbComponent_ng_template_0_li_0_Template, 3, 5, \"li\", 1);\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ctx.$implicit;\n    const last_r2 = ctx.last;\n    ɵngcc0.ɵɵproperty(\"ngIf\", breadcrumb_r1.label.title && (breadcrumb_r1.url.slice(-1) == \"/\" || last_r2));\n  }\n}\n\nfunction CuiBreadcrumbComponent_ng_template_1_li_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"a\", 5);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ɵngcc0.ɵɵnextContext(2).$implicit;\n    ɵngcc0.ɵɵproperty(\"routerLink\", breadcrumb_r1.url);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(breadcrumb_r1.label.title);\n  }\n}\n\nfunction CuiBreadcrumbComponent_ng_template_1_li_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 5);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ɵngcc0.ɵɵnextContext(2).$implicit;\n    ɵngcc0.ɵɵproperty(\"routerLink\", breadcrumb_r1.url);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate(breadcrumb_r1.label.title);\n  }\n}\n\nfunction CuiBreadcrumbComponent_ng_template_1_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"li\", 3);\n    ɵngcc0.ɵɵtemplate(1, CuiBreadcrumbComponent_ng_template_1_li_0_a_1_Template, 2, 2, \"a\", 4);\n    ɵngcc0.ɵɵtemplate(2, CuiBreadcrumbComponent_ng_template_1_li_0_span_2_Template, 2, 2, \"span\", 4);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const last_r2 = ɵngcc0.ɵɵnextContext().last;\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpureFunction1(3, _c1, last_r2));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", !last_r2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", last_r2);\n  }\n}\n\nfunction CuiBreadcrumbComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, CuiBreadcrumbComponent_ng_template_1_li_0_Template, 3, 5, \"li\", 2);\n  }\n\n  if (rf & 2) {\n    const breadcrumb_r1 = ctx.$implicit;\n    const last_r2 = ctx.last;\n    ɵngcc0.ɵɵproperty(\"ngIf\", breadcrumb_r1.label.title && (breadcrumb_r1.url.slice(-1) == \"/\" || last_r2));\n  }\n}\n\nfunction AppHeaderComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 2);\n    ɵngcc0.ɵɵelement(1, \"span\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMapInterpolate1(\"navbar-toggler \", ctx_r0.sidebarTogglerMobileClass, \"\");\n  }\n}\n\nfunction AppHeaderComponent_ng_template_2_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"img\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"appHtmlAttr\", ctx_r6.navbarBrand)(\"ngClass\", \"navbar-brand\");\n  }\n}\n\nfunction AppHeaderComponent_ng_template_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"img\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"appHtmlAttr\", ctx_r7.navbarBrandFull)(\"ngClass\", \"navbar-brand-full\");\n  }\n}\n\nfunction AppHeaderComponent_ng_template_2_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"img\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"appHtmlAttr\", ctx_r8.navbarBrandMinimized)(\"ngClass\", \"navbar-brand-minimized\");\n  }\n}\n\nfunction AppHeaderComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵtemplate(0, AppHeaderComponent_ng_template_2_img_0_Template, 1, 2, \"img\", 4);\n    ɵngcc0.ɵɵtemplate(1, AppHeaderComponent_ng_template_2_img_1_Template, 1, 2, \"img\", 4);\n    ɵngcc0.ɵɵtemplate(2, AppHeaderComponent_ng_template_2_img_2_Template, 1, 2, \"img\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.navbarBrand);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.navbarBrandFull);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.navbarBrandMinimized);\n  }\n}\n\nfunction AppHeaderComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 6);\n    ɵngcc0.ɵɵelement(1, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.navbarBrandText.text, ɵngcc0.ɵɵsanitizeHtml);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.navbarBrandText.icon, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction AppHeaderComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 8);\n    ɵngcc0.ɵɵelement(1, \"span\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMapInterpolate1(\"navbar-toggler \", ctx_r3.sidebarTogglerClass, \"\");\n    ɵngcc0.ɵɵproperty(\"appSidebarToggler\", ctx_r3.sidebarToggler);\n  }\n}\n\nfunction AppHeaderComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 9);\n    ɵngcc0.ɵɵelement(1, \"span\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMapInterpolate1(\"navbar-toggler \", ctx_r4.asideTogglerClass, \"\");\n    ɵngcc0.ɵɵproperty(\"appAsideMenuToggler\", ctx_r4.asideMenuToggler);\n  }\n}\n\nfunction AppHeaderComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"button\", 10);\n    ɵngcc0.ɵɵelement(1, \"span\", 3);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMapInterpolate1(\"navbar-toggler \", ctx_r5.asideTogglerMobileClass, \"\");\n  }\n}\n\nfunction AppSidebarNavDropdownComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 3);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavIcon\");\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx_r0.item));\n  }\n}\n\nfunction AppSidebarNavDropdownComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 3);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavBadge\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 2, ctx_r1.item));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r1.item.badge.text);\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_dropdown_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"app-sidebar-nav-dropdown\", 7);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavItemClass\");\n  }\n\n  if (rf & 2) {\n    const item_r1 = ɵngcc0.ɵɵnextContext().$implicit;\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassProp(\"open\", ctx_r2.helper.isActive(ctx_r2.router, item_r1));\n    ɵngcc0.ɵɵproperty(\"item\", item_r1)(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 4, item_r1));\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_divider_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"app-sidebar-nav-divider\", 8);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavItemClass\");\n  }\n\n  if (rf & 2) {\n    const item_r1 = ɵngcc0.ɵɵnextContext().$implicit;\n    ɵngcc0.ɵɵproperty(\"item\", item_r1)(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 3, item_r1))(\"appHtmlAttr\", item_r1.attributes);\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_title_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"app-sidebar-nav-title\", 8);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavItemClass\");\n  }\n\n  if (rf & 2) {\n    const item_r1 = ɵngcc0.ɵɵnextContext().$implicit;\n    ɵngcc0.ɵɵproperty(\"item\", item_r1)(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 3, item_r1))(\"appHtmlAttr\", item_r1.attributes);\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_label_5_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"app-sidebar-nav-label\", 9);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavItemClass\");\n  }\n\n  if (rf & 2) {\n    const item_r1 = ɵngcc0.ɵɵnextContext().$implicit;\n    ɵngcc0.ɵɵproperty(\"item\", item_r1)(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 2, item_r1));\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_link_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"app-sidebar-nav-link\", 10);\n    ɵngcc0.ɵɵlistener(\"linkClick\", function AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_link_7_Template_app_sidebar_nav_link_linkClick_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r13);\n      const ctx_r12 = ɵngcc0.ɵɵnextContext(2);\n      return ctx_r12.hideMobile();\n    });\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavItemClass\");\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = ɵngcc0.ɵɵnextContext().$implicit;\n    ɵngcc0.ɵɵproperty(\"item\", item_r1)(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 2, item_r1));\n  }\n}\n\nfunction AppSidebarNavItemsComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵelementContainerStart(1, 1);\n    ɵngcc0.ɵɵtemplate(2, AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_dropdown_2_Template, 2, 6, \"app-sidebar-nav-dropdown\", 2);\n    ɵngcc0.ɵɵtemplate(3, AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_divider_3_Template, 2, 5, \"app-sidebar-nav-divider\", 3);\n    ɵngcc0.ɵɵtemplate(4, AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_title_4_Template, 2, 5, \"app-sidebar-nav-title\", 3);\n    ɵngcc0.ɵɵtemplate(5, AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_label_5_Template, 2, 4, \"app-sidebar-nav-label\", 4);\n    ɵngcc0.ɵɵtemplate(6, AppSidebarNavItemsComponent_ng_container_0_ng_container_6_Template, 1, 0, \"ng-container\", 5);\n    ɵngcc0.ɵɵtemplate(7, AppSidebarNavItemsComponent_ng_container_0_app_sidebar_nav_link_7_Template, 2, 4, \"app-sidebar-nav-link\", 6);\n    ɵngcc0.ɵɵelementContainerEnd();\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitch\", ctx_r0.helper.itemType(item_r1));\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"dropdown\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"divider\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"title\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"label\");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"empty\");\n  }\n}\n\nfunction AppSidebarNavLinkContentComponent_ng_container_0_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 2);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavIcon\");\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx_r1.item));\n  }\n}\n\nfunction AppSidebarNavLinkContentComponent_ng_container_0_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 2);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavBadge\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 2, ctx_r2.item));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r2.item.badge.text);\n  }\n}\n\nfunction AppSidebarNavLinkContentComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtemplate(1, AppSidebarNavLinkContentComponent_ng_container_0_i_1_Template, 2, 3, \"i\", 1);\n    ɵngcc0.ɵɵelementContainerStart(2);\n    ɵngcc0.ɵɵtext(3);\n    ɵngcc0.ɵɵelementContainerEnd();\n    ɵngcc0.ɵɵtemplate(4, AppSidebarNavLinkContentComponent_ng_container_0_span_4_Template, 3, 4, \"span\", 1);\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.helper.hasIcon(ctx_r0.item));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r0.item.name);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r0.helper.hasBadge(ctx_r0.item));\n  }\n}\n\nfunction AppSidebarNavLinkComponent_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"a\", 4);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavLink\");\n    ɵngcc0.ɵɵelement(2, \"app-sidebar-nav-link-content\", 5);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 3, ctx_r0.item))(\"appHtmlAttr\", ctx_r0.item.attributes);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"item\", ctx_r0.item);\n  }\n}\n\nfunction AppSidebarNavLinkComponent_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"a\", 6);\n    ɵngcc0.ɵɵlistener(\"click\", function AppSidebarNavLinkComponent_a_2_Template_a_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r4);\n      const ctx_r3 = ɵngcc0.ɵɵnextContext();\n      return ctx_r3.linkClicked();\n    });\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavLink\");\n    ɵngcc0.ɵɵelement(2, \"app-sidebar-nav-link-content\", 5);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 4, ctx_r1.item))(\"href\", ctx_r1.href, ɵngcc0.ɵɵsanitizeUrl)(\"appHtmlAttr\", ctx_r1.item.attributes);\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"item\", ctx_r1.item);\n  }\n}\n\nconst _c2 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction AppSidebarNavLinkComponent_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"a\", 7);\n    ɵngcc0.ɵɵlistener(\"click\", function AppSidebarNavLinkComponent_a_3_Template_a_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ctx_r5.linkClicked();\n    });\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavLink\");\n    ɵngcc0.ɵɵelement(2, \"app-sidebar-nav-link-content\", 5);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassProp(\"active\", ctx_r2.linkActive && !(ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActiveOptions));\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 16, ctx_r2.item))(\"appHtmlAttr\", ctx_r2.item.attributes)(\"target\", ctx_r2.item.attributes == null ? null : ctx_r2.item.attributes.target)(\"queryParams\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParams)(\"fragment\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.fragment)(\"queryParamsHandling\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.queryParamsHandling)(\"preserveFragment\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.preserveFragment)(\"skipLocationChange\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.skipLocationChange)(\"replaceUrl\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.replaceUrl)(\"state\", ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.state)(\"routerLink\", ctx_r2.item.url)(\"routerLinkActive\", (ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActive) || \"active\")(\"routerLinkActiveOptions\", (ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActiveOptions == null ? null : ctx_r2.item.linkProps.routerLinkActiveOptions.exact) ? ctx_r2.item.linkProps == null ? null : ctx_r2.item.linkProps.routerLinkActiveOptions : ɵngcc0.ɵɵpureFunction0(18, _c2));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵproperty(\"item\", ctx_r2.item);\n  }\n}\n\nfunction AppSidebarNavLabelComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"i\", 2);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ctx_r0.getLabelIconClass());\n  }\n}\n\nfunction AppSidebarNavLabelComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"span\", 2);\n    ɵngcc0.ɵɵpipe(1, \"appSidebarNavBadge\");\n    ɵngcc0.ɵɵtext(2);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵproperty(\"ngClass\", ɵngcc0.ɵɵpipeBind1(1, 2, ctx_r1.item));\n    ɵngcc0.ɵɵadvance(2);\n    ɵngcc0.ɵɵtextInterpolate(ctx_r1.item.badge.text);\n  }\n}\n\nconst sidebarCssClasses = ['sidebar-show', 'sidebar-sm-show', 'sidebar-md-show', 'sidebar-lg-show', 'sidebar-xl-show'];\nconst asideMenuCssClasses = ['aside-menu-show', 'aside-menu-sm-show', 'aside-menu-md-show', 'aside-menu-lg-show', 'aside-menu-xl-show'];\n\nconst RemoveClasses = NewClassNames => {\n  const MatchClasses = NewClassNames.map(Class => document.body.classList.contains(Class));\n  return MatchClasses.indexOf(true) !== -1;\n};\n\nconst ɵ0 = RemoveClasses;\n\nconst ToggleClasses = (Toggle, ClassNames) => {\n  const Level = ClassNames.indexOf(Toggle);\n  const NewClassNames = ClassNames.slice(0, Level + 1);\n\n  if (RemoveClasses(NewClassNames)) {\n    NewClassNames.map(Class => document.body.classList.remove(Class));\n  } else {\n    document.body.classList.add(Toggle);\n  }\n};\n\nlet ClassToggler = /*#__PURE__*/(() => {\n  class ClassToggler {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n    }\n\n    removeClasses(NewClassNames) {\n      const MatchClasses = NewClassNames.map(Class => this.document.body.classList.contains(Class));\n      return MatchClasses.indexOf(true) !== -1;\n    }\n\n    toggleClasses(Toggle, ClassNames) {\n      const Level = ClassNames.indexOf(Toggle);\n      const NewClassNames = ClassNames.slice(0, Level + 1);\n\n      if (this.removeClasses(NewClassNames)) {\n        NewClassNames.map(Class => this.renderer.removeClass(this.document.body, Class));\n      } else {\n        this.renderer.addClass(this.document.body, Toggle);\n      }\n    }\n\n  }\n\n  ClassToggler.ɵfac = function ClassToggler_Factory(t) {\n    return new (t || ClassToggler)(ɵngcc0.ɵɵinject(DOCUMENT), ɵngcc0.ɵɵinject(ɵngcc0.Renderer2));\n  };\n\n  ClassToggler.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: ClassToggler,\n    factory: ClassToggler.ɵfac\n  });\n  return ClassToggler;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\r\n * Allows the sidebar to be toggled via click.\r\n */\n\n\nlet SidebarToggleDirective = /*#__PURE__*/(() => {\n  class SidebarToggleDirective {\n    constructor(classToggler) {\n      this.classToggler = classToggler;\n    }\n\n    ngOnInit() {\n      this.bp = this.breakpoint;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const cssClass = this.bp ? `sidebar-${this.bp}-show` : sidebarCssClasses[0];\n      this.classToggler.toggleClasses(cssClass, sidebarCssClasses);\n    }\n\n  }\n\n  SidebarToggleDirective.ɵfac = function SidebarToggleDirective_Factory(t) {\n    return new (t || SidebarToggleDirective)(ɵngcc0.ɵɵdirectiveInject(ClassToggler));\n  };\n\n  SidebarToggleDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: SidebarToggleDirective,\n    selectors: [[\"\", \"appSidebarToggler\", \"\"]],\n    hostBindings: function SidebarToggleDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function SidebarToggleDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    },\n    inputs: {\n      breakpoint: [\"appSidebarToggler\", \"breakpoint\"]\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([ClassToggler])]\n  });\n  return SidebarToggleDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet SidebarMinimizeDirective = /*#__PURE__*/(() => {\n  class SidebarMinimizeDirective {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const body = this.document.body;\n      body.classList.contains('sidebar-minimized') ? this.renderer.removeClass(body, 'sidebar-minimized') : this.renderer.addClass(body, 'sidebar-minimized');\n    }\n\n  }\n\n  SidebarMinimizeDirective.ɵfac = function SidebarMinimizeDirective_Factory(t) {\n    return new (t || SidebarMinimizeDirective)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  SidebarMinimizeDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: SidebarMinimizeDirective,\n    selectors: [[\"\", \"appSidebarMinimizer\", \"\"]],\n    hostBindings: function SidebarMinimizeDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function SidebarMinimizeDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    }\n  });\n  return SidebarMinimizeDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet MobileSidebarToggleDirective = /*#__PURE__*/(() => {\n  class MobileSidebarToggleDirective {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const body = this.document.body;\n      body.classList.contains('sidebar-show') ? this.renderer.removeClass(body, 'sidebar-show') : this.renderer.addClass(body, 'sidebar-show');\n    }\n\n  }\n\n  MobileSidebarToggleDirective.ɵfac = function MobileSidebarToggleDirective_Factory(t) {\n    return new (t || MobileSidebarToggleDirective)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  MobileSidebarToggleDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: MobileSidebarToggleDirective,\n    selectors: [[\"\", \"appMobileSidebarToggler\", \"\"]],\n    hostBindings: function MobileSidebarToggleDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function MobileSidebarToggleDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    }\n  });\n  return MobileSidebarToggleDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\r\n * Allows the off-canvas sidebar to be closed via click.\r\n */\n\n\nlet SidebarOffCanvasCloseDirective = /*#__PURE__*/(() => {\n  class SidebarOffCanvasCloseDirective {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const body = this.document.body;\n\n      if (body.classList.contains('sidebar-off-canvas')) {\n        body.classList.contains('sidebar-show') ? this.renderer.removeClass(body, 'sidebar-show') : this.renderer.addClass(body, 'sidebar-show');\n      }\n    }\n\n  }\n\n  SidebarOffCanvasCloseDirective.ɵfac = function SidebarOffCanvasCloseDirective_Factory(t) {\n    return new (t || SidebarOffCanvasCloseDirective)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  SidebarOffCanvasCloseDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: SidebarOffCanvasCloseDirective,\n    selectors: [[\"\", \"appSidebarClose\", \"\"]],\n    hostBindings: function SidebarOffCanvasCloseDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function SidebarOffCanvasCloseDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    }\n  });\n  return SidebarOffCanvasCloseDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet BrandMinimizeDirective = /*#__PURE__*/(() => {\n  class BrandMinimizeDirective {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const body = this.document.body;\n      body.classList.contains('brand-minimized') ? this.renderer.removeClass(body, 'brand-minimized') : this.renderer.addClass(body, 'brand-minimized');\n    }\n\n  }\n\n  BrandMinimizeDirective.ɵfac = function BrandMinimizeDirective_Factory(t) {\n    return new (t || BrandMinimizeDirective)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  BrandMinimizeDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: BrandMinimizeDirective,\n    selectors: [[\"\", \"appBrandMinimizer\", \"\"]],\n    hostBindings: function BrandMinimizeDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function BrandMinimizeDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    }\n  });\n  return BrandMinimizeDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\r\n * Allows the aside to be toggled via click.\r\n */\n\n\nlet AsideToggleDirective = /*#__PURE__*/(() => {\n  class AsideToggleDirective {\n    constructor(classToggler) {\n      this.classToggler = classToggler;\n    }\n\n    ngOnInit() {\n      this.bp = this.breakpoint;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      const cssClass = this.bp ? `aside-menu-${this.bp}-show` : asideMenuCssClasses[0];\n      this.classToggler.toggleClasses(cssClass, asideMenuCssClasses);\n    }\n\n  }\n\n  AsideToggleDirective.ɵfac = function AsideToggleDirective_Factory(t) {\n    return new (t || AsideToggleDirective)(ɵngcc0.ɵɵdirectiveInject(ClassToggler));\n  };\n\n  AsideToggleDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: AsideToggleDirective,\n    selectors: [[\"\", \"appAsideMenuToggler\", \"\"]],\n    hostBindings: function AsideToggleDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function AsideToggleDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    },\n    inputs: {\n      breakpoint: [\"appAsideMenuToggler\", \"breakpoint\"]\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([ClassToggler])]\n  });\n  return AsideToggleDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet HtmlAttributesDirective = /*#__PURE__*/(() => {\n  class HtmlAttributesDirective {\n    constructor(renderer, el) {\n      this.renderer = renderer;\n      this.el = el;\n    }\n\n    ngOnInit() {\n      const attribs = this.appHtmlAttr;\n\n      for (const attr in attribs) {\n        if (attr === 'style' && typeof attribs[attr] === 'object') {\n          this.setStyle(attribs[attr]);\n        } else if (attr === 'class') {\n          this.addClass(attribs[attr]);\n        } else {\n          this.setAttrib(attr, attribs[attr]);\n        }\n      }\n    }\n\n    setStyle(styles) {\n      for (const style in styles) {\n        this.renderer.setStyle(this.el.nativeElement, style, styles[style]);\n      }\n    }\n\n    addClass(classes) {\n      const classArray = Array.isArray(classes) ? classes : classes.split(' ');\n      classArray.filter(element => element.length > 0).forEach(element => {\n        this.renderer.addClass(this.el.nativeElement, element);\n      });\n    }\n\n    setAttrib(key, value) {\n      value !== null ? this.renderer.setAttribute(this.el.nativeElement, key, value) : this.renderer.removeAttribute(this.el.nativeElement, key);\n    }\n\n  }\n\n  HtmlAttributesDirective.ɵfac = function HtmlAttributesDirective_Factory(t) {\n    return new (t || HtmlAttributesDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  HtmlAttributesDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: HtmlAttributesDirective,\n    selectors: [[\"\", \"appHtmlAttr\", \"\"]],\n    inputs: {\n      appHtmlAttr: \"appHtmlAttr\"\n    }\n  });\n  return HtmlAttributesDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet LayoutModule = /*#__PURE__*/(() => {\n  class LayoutModule {}\n\n  LayoutModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: LayoutModule\n  });\n  LayoutModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function LayoutModule_Factory(t) {\n      return new (t || LayoutModule)();\n    },\n    providers: [ClassToggler],\n    imports: [[CommonModule]]\n  });\n  return LayoutModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(LayoutModule, {\n    declarations: function () {\n      return [AsideToggleDirective, BrandMinimizeDirective, MobileSidebarToggleDirective, SidebarToggleDirective, SidebarMinimizeDirective, SidebarOffCanvasCloseDirective, HtmlAttributesDirective];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [AsideToggleDirective, BrandMinimizeDirective, MobileSidebarToggleDirective, SidebarToggleDirective, SidebarMinimizeDirective, SidebarOffCanvasCloseDirective, HtmlAttributesDirective];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nfunction Replace(el) {\n  const nativeElement = el.nativeElement;\n  const parentElement = nativeElement.parentElement; // move all children out of the element\n\n  while (nativeElement.firstChild) {\n    parentElement.insertBefore(nativeElement.firstChild, nativeElement);\n  } // remove the empty element(the host)\n\n\n  parentElement.removeChild(nativeElement);\n}\n\nlet AppAsideComponent = /*#__PURE__*/(() => {\n  class AppAsideComponent {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n      this.fixedClass = 'aside-menu-fixed';\n      this.asideMenuClass = true;\n    }\n\n    ngOnInit() {\n      this.isFixed(this.fixed);\n      this.isOffCanvas(this.offCanvas);\n      this.displayBreakpoint(this.display);\n    }\n\n    ngOnDestroy() {\n      this.renderer.removeClass(this.document.body, this.fixedClass);\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, this.fixedClass);\n      }\n    }\n\n    isOffCanvas(offCanvas = this.offCanvas) {\n      if (offCanvas) {\n        this.renderer.addClass(this.document.body, 'aside-menu-off-canvas');\n      }\n    }\n\n    displayBreakpoint(display = this.display) {\n      if (display !== false) {\n        const cssClass = this.display ? `aside-menu-${this.display}-show` : asideMenuCssClasses[0];\n        this.renderer.addClass(this.document.body, cssClass);\n      }\n    }\n\n  }\n\n  AppAsideComponent.ɵfac = function AppAsideComponent_Factory(t) {\n    return new (t || AppAsideComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  AppAsideComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppAsideComponent,\n    selectors: [[\"app-aside\"], [\"cui-aside\"]],\n    hostVars: 2,\n    hostBindings: function AppAsideComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"aside-menu\", ctx.asideMenuClass);\n      }\n    },\n    inputs: {\n      display: \"display\",\n      fixed: \"fixed\",\n      offCanvas: \"offCanvas\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppAsideComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppAsideComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppAsideModule = /*#__PURE__*/(() => {\n  class AppAsideModule {}\n\n  AppAsideModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: AppAsideModule\n  });\n  AppAsideModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function AppAsideModule_Factory(t) {\n      return new (t || AppAsideModule)();\n    },\n    imports: [[CommonModule, LayoutModule], LayoutModule]\n  });\n  return AppAsideModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(AppAsideModule, {\n    declarations: function () {\n      return [AppAsideComponent];\n    },\n    imports: function () {\n      return [CommonModule, LayoutModule];\n    },\n    exports: function () {\n      return [AppAsideComponent, LayoutModule];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppBreadcrumbService = /*#__PURE__*/(() => {\n  class AppBreadcrumbService {\n    constructor(router, route) {\n      this.router = router;\n      this.route = route;\n      this.breadcrumbSubject = new BehaviorSubject(new Array());\n      this.breadcrumbs = this.breadcrumbSubject.asObservable();\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        const breadcrumbs = [];\n        let currentRoute = this.route.root;\n        let url = '';\n\n        do {\n          const childrenRoutes = currentRoute.children;\n          currentRoute = null; // tslint:disable-next-line:no-shadowed-variable\n\n          childrenRoutes.forEach(route => {\n            if (route.outlet === 'primary') {\n              const routeSnapshot = route.snapshot;\n              url += '/' + routeSnapshot.url.map(segment => segment.path).join('/');\n              breadcrumbs.push({\n                label: route.snapshot.data,\n                url\n              });\n              currentRoute = route;\n            }\n          });\n        } while (currentRoute);\n\n        this.breadcrumbSubject.next(Object.assign([], breadcrumbs));\n        return breadcrumbs;\n      });\n    }\n\n  }\n\n  AppBreadcrumbService.ɵfac = function AppBreadcrumbService_Factory(t) {\n    return new (t || AppBreadcrumbService)(ɵngcc0.ɵɵinject(ɵngcc1.Router), ɵngcc0.ɵɵinject(ɵngcc1.ActivatedRoute));\n  };\n\n  AppBreadcrumbService.ɵprov = ɵɵdefineInjectable({\n    factory: function AppBreadcrumbService_Factory() {\n      return new AppBreadcrumbService(ɵɵinject(Router), ɵɵinject(ActivatedRoute));\n    },\n    token: AppBreadcrumbService,\n    providedIn: \"root\"\n  });\n  return AppBreadcrumbService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppBreadcrumbComponent = /*#__PURE__*/(() => {\n  class AppBreadcrumbComponent {\n    constructor(document, renderer, service, el) {\n      this.document = document;\n      this.renderer = renderer;\n      this.service = service;\n      this.el = el;\n      this.fixedClass = 'breadcrumb-fixed';\n    }\n\n    ngOnInit() {\n      Replace(this.el);\n      this.isFixed(this.fixed);\n      this.breadcrumbs = this.service.breadcrumbs;\n    }\n\n    ngOnDestroy() {\n      this.renderer.removeClass(this.document.body, this.fixedClass);\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, this.fixedClass);\n      }\n    }\n\n  }\n\n  AppBreadcrumbComponent.ɵfac = function AppBreadcrumbComponent_Factory(t) {\n    return new (t || AppBreadcrumbComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(AppBreadcrumbService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  AppBreadcrumbComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppBreadcrumbComponent,\n    selectors: [[\"app-breadcrumb\"]],\n    inputs: {\n      fixed: \"fixed\"\n    },\n    decls: 2,\n    vars: 3,\n    consts: [[\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"breadcrumb-item\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"breadcrumb-item\", 3, \"ngClass\"], [3, \"routerLink\", 4, \"ngIf\"], [3, \"routerLink\"]],\n    template: function AppBreadcrumbComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, AppBreadcrumbComponent_ng_template_0_Template, 1, 1, \"ng-template\", 0);\n        ɵngcc0.ɵɵpipe(1, \"async\");\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(1, 1, ctx.breadcrumbs));\n      }\n    },\n    directives: [ɵngcc2.NgForOf, ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc1.RouterLinkWithHref, ɵngcc1.RouterLink],\n    pipes: [ɵngcc2.AsyncPipe],\n    encapsulation: 2\n  });\n  return AppBreadcrumbComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet CuiBreadcrumbComponent = /*#__PURE__*/(() => {\n  class CuiBreadcrumbComponent {\n    constructor(document, renderer, service) {\n      this.document = document;\n      this.renderer = renderer;\n      this.service = service;\n      this.fixedClass = 'breadcrumb-fixed';\n    }\n\n    ngOnInit() {\n      this.isFixed(this.fixed);\n      this.breadcrumbs = this.service.breadcrumbs;\n    }\n\n    ngOnDestroy() {\n      this.renderer.removeClass(this.document.body, this.fixedClass);\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, this.fixedClass);\n      }\n    }\n\n  }\n\n  CuiBreadcrumbComponent.ɵfac = function CuiBreadcrumbComponent_Factory(t) {\n    return new (t || CuiBreadcrumbComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(AppBreadcrumbService));\n  };\n\n  CuiBreadcrumbComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: CuiBreadcrumbComponent,\n    selectors: [[\"cui-breadcrumb\"]],\n    inputs: {\n      fixed: \"fixed\"\n    },\n    ngContentSelectors: _c0,\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"breadcrumb\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"breadcrumb-item\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"breadcrumb-item\", 3, \"ngClass\"], [3, \"routerLink\", 4, \"ngIf\"], [3, \"routerLink\"]],\n    template: function CuiBreadcrumbComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵelementStart(0, \"ol\", 0);\n        ɵngcc0.ɵɵtemplate(1, CuiBreadcrumbComponent_ng_template_1_Template, 1, 1, \"ng-template\", 1);\n        ɵngcc0.ɵɵpipe(2, \"async\");\n        ɵngcc0.ɵɵprojection(3);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ɵngcc0.ɵɵpipeBind1(2, 1, ctx.breadcrumbs));\n      }\n    },\n    directives: [ɵngcc2.NgForOf, ɵngcc2.NgIf, ɵngcc2.NgClass, ɵngcc1.RouterLinkWithHref, ɵngcc1.RouterLink],\n    pipes: [ɵngcc2.AsyncPipe],\n    encapsulation: 2\n  });\n  return CuiBreadcrumbComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // @dynamic\n\n\nlet AppBreadcrumbModule = /*#__PURE__*/(() => {\n  class AppBreadcrumbModule {\n    static forRoot(config) {\n      return {\n        ngModule: AppBreadcrumbModule,\n        providers: [AppBreadcrumbService]\n      };\n    }\n\n  }\n\n  AppBreadcrumbModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: AppBreadcrumbModule\n  });\n  AppBreadcrumbModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function AppBreadcrumbModule_Factory(t) {\n      return new (t || AppBreadcrumbModule)();\n    },\n    imports: [[CommonModule, RouterModule]]\n  });\n  return AppBreadcrumbModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(AppBreadcrumbModule, {\n    declarations: function () {\n      return [AppBreadcrumbComponent, CuiBreadcrumbComponent];\n    },\n    imports: function () {\n      return [CommonModule, RouterModule];\n    },\n    exports: function () {\n      return [AppBreadcrumbComponent, CuiBreadcrumbComponent];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppFooterComponent = /*#__PURE__*/(() => {\n  class AppFooterComponent {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n      this.fixedClass = 'footer-fixed';\n      this.appFooterClass = true;\n    }\n\n    ngOnInit() {\n      this.isFixed(this.fixed);\n    }\n\n    ngOnDestroy() {\n      this.renderer.removeClass(this.document.body, this.fixedClass);\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, this.fixedClass);\n      }\n    }\n\n  }\n\n  AppFooterComponent.ɵfac = function AppFooterComponent_Factory(t) {\n    return new (t || AppFooterComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  AppFooterComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppFooterComponent,\n    selectors: [[\"app-footer\"], [\"cui-footer\"]],\n    hostVars: 2,\n    hostBindings: function AppFooterComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"app-footer\", ctx.appFooterClass);\n      }\n    },\n    inputs: {\n      fixed: \"fixed\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppFooterComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppFooterModule = /*#__PURE__*/(() => {\n  class AppFooterModule {}\n\n  AppFooterModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: AppFooterModule\n  });\n  AppFooterModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function AppFooterModule_Factory(t) {\n      return new (t || AppFooterModule)();\n    },\n    imports: [[CommonModule]]\n  });\n  return AppFooterModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(AppFooterModule, {\n    declarations: function () {\n      return [AppFooterComponent];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [AppFooterComponent];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppHeaderComponent = /*#__PURE__*/(() => {\n  class AppHeaderComponent {\n    constructor(document, renderer) {\n      this.document = document;\n      this.renderer = renderer;\n      this.navbarBrandText = {\n        icon: '🅲',\n        text: '🅲 CoreUI'\n      };\n      this.navbarBrandRouterLink = '';\n      this.fixedClass = 'header-fixed';\n      this.appHeaderClass = true;\n      this.navbarClass = true;\n      this.breakpoints = ['xl', 'lg', 'md', 'sm', 'xs'];\n      this.sidebarTogglerClass = 'd-none d-md-block';\n      this.sidebarTogglerMobileClass = 'd-lg-none';\n      this.asideTogglerClass = 'd-none d-md-block';\n      this.asideTogglerMobileClass = 'd-lg-none';\n    }\n\n    ngOnInit() {\n      this.isFixed(this.fixed);\n      this.navbarBrandImg = Boolean(this.navbarBrand || this.navbarBrandFull || this.navbarBrandMinimized);\n      this.navbarBrandRouterLink = this.navbarBrandRouterLink[0] ? this.navbarBrandRouterLink : this.navbarBrandHref;\n      this.sidebarTogglerClass = this.setToggerBreakpointClass(this.sidebarToggler);\n      this.sidebarTogglerMobileClass = this.setToggerMobileBreakpointClass(this.sidebarToggler);\n      this.asideTogglerClass = this.setToggerBreakpointClass(this.asideMenuToggler);\n      this.asideTogglerMobileClass = this.setToggerMobileBreakpointClass(this.asideMenuToggler);\n    }\n\n    ngOnDestroy() {\n      this.renderer.removeClass(this.document.body, this.fixedClass);\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, this.fixedClass);\n      }\n    }\n\n    setToggerBreakpointClass(breakpoint = 'md') {\n      let togglerClass = 'd-none d-md-block';\n\n      if (this.breakpoints.includes(breakpoint)) {\n        const breakpointIndex = this.breakpoints.indexOf(breakpoint);\n        togglerClass = `d-none d-${breakpoint}-block`;\n      }\n\n      return togglerClass;\n    }\n\n    setToggerMobileBreakpointClass(breakpoint = 'lg') {\n      let togglerClass = 'd-lg-none';\n\n      if (this.breakpoints.includes(breakpoint)) {\n        togglerClass = `d-${breakpoint}-none`;\n      }\n\n      return togglerClass;\n    }\n\n  }\n\n  AppHeaderComponent.ɵfac = function AppHeaderComponent_Factory(t) {\n    return new (t || AppHeaderComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  AppHeaderComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppHeaderComponent,\n    selectors: [[\"app-header\"], [\"cui-header\"]],\n    hostVars: 4,\n    hostBindings: function AppHeaderComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"app-header\", ctx.appHeaderClass)(\"navbar\", ctx.navbarClass);\n      }\n    },\n    inputs: {\n      navbarBrandText: \"navbarBrandText\",\n      navbarBrandRouterLink: \"navbarBrandRouterLink\",\n      fixed: \"fixed\",\n      navbarBrand: \"navbarBrand\",\n      navbarBrandFull: \"navbarBrandFull\",\n      navbarBrandMinimized: \"navbarBrandMinimized\",\n      navbarBrandHref: \"navbarBrandHref\",\n      sidebarToggler: \"sidebarToggler\",\n      mobileSidebarToggler: \"mobileSidebarToggler\",\n      asideMenuToggler: \"asideMenuToggler\",\n      mobileAsideMenuToggler: \"mobileAsideMenuToggler\"\n    },\n    ngContentSelectors: _c0,\n    decls: 8,\n    vars: 7,\n    consts: [[3, \"ngIf\"], [1, \"navbar-brand\", 3, \"routerLink\"], [\"type\", \"button\", \"appSidebarToggler\", \"\"], [1, \"navbar-toggler-icon\"], [3, \"appHtmlAttr\", \"ngClass\", 4, \"ngIf\"], [3, \"appHtmlAttr\", \"ngClass\"], [1, \"navbar-brand-full\", 3, \"innerHTML\"], [1, \"navbar-brand-minimized\", 3, \"innerHTML\"], [\"type\", \"button\", 3, \"appSidebarToggler\"], [\"type\", \"button\", 3, \"appAsideMenuToggler\"], [\"type\", \"button\", \"appAsideMenuToggler\", \"\"]],\n    template: function AppHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵtemplate(0, AppHeaderComponent_ng_template_0_Template, 2, 3, \"ng-template\", 0);\n        ɵngcc0.ɵɵelementStart(1, \"a\", 1);\n        ɵngcc0.ɵɵtemplate(2, AppHeaderComponent_ng_template_2_Template, 3, 3, \"ng-template\", 0);\n        ɵngcc0.ɵɵtemplate(3, AppHeaderComponent_ng_template_3_Template, 2, 2, \"ng-template\", 0);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵtemplate(4, AppHeaderComponent_ng_template_4_Template, 2, 4, \"ng-template\", 0);\n        ɵngcc0.ɵɵprojection(5);\n        ɵngcc0.ɵɵtemplate(6, AppHeaderComponent_ng_template_6_Template, 2, 4, \"ng-template\", 0);\n        ɵngcc0.ɵɵtemplate(7, AppHeaderComponent_ng_template_7_Template, 2, 3, \"ng-template\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.mobileSidebarToggler != false);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"routerLink\", ctx.navbarBrandRouterLink);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.navbarBrandImg);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", !ctx.navbarBrandImg);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.sidebarToggler != false);\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.asideMenuToggler != false);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.mobileAsideMenuToggler != false);\n      }\n    },\n    directives: [ɵngcc2.NgIf, ɵngcc1.RouterLinkWithHref, SidebarToggleDirective, HtmlAttributesDirective, ɵngcc2.NgClass, AsideToggleDirective],\n    encapsulation: 2\n  });\n  return AppHeaderComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppHeaderModule = /*#__PURE__*/(() => {\n  class AppHeaderModule {}\n\n  AppHeaderModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: AppHeaderModule\n  });\n  AppHeaderModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function AppHeaderModule_Factory(t) {\n      return new (t || AppHeaderModule)();\n    },\n    imports: [[CommonModule, RouterModule, LayoutModule], LayoutModule]\n  });\n  return AppHeaderModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(AppHeaderModule, {\n    declarations: function () {\n      return [AppHeaderComponent];\n    },\n    imports: function () {\n      return [CommonModule, RouterModule, LayoutModule];\n    },\n    exports: function () {\n      return [AppHeaderComponent, LayoutModule];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarService = /*#__PURE__*/(() => {\n  class AppSidebarService {\n    constructor() {\n      this.events = new BehaviorSubject({});\n      this.events$ = this.events.asObservable();\n    }\n\n    toggle(action) {\n      this.events.next(action);\n    }\n\n  }\n\n  AppSidebarService.ɵfac = function AppSidebarService_Factory(t) {\n    return new (t || AppSidebarService)();\n  };\n\n  AppSidebarService.ɵprov = ɵɵdefineInjectable({\n    factory: function AppSidebarService_Factory() {\n      return new AppSidebarService();\n    },\n    token: AppSidebarService,\n    providedIn: \"root\"\n  });\n  return AppSidebarService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarComponent = /*#__PURE__*/(() => {\n  class AppSidebarComponent {\n    constructor(document, renderer, sidebarService) {\n      this.document = document;\n      this.renderer = renderer;\n      this.sidebarService = sidebarService;\n      this._minimized = false;\n      /**\r\n       * Emits whenever the minimized state of the sidebar changes.\r\n       * Primarily used to facilitate two-way binding.\r\n       */\n\n      this.minimizedChange = new EventEmitter();\n      this.sidebarClass = true;\n    }\n\n    get minimized() {\n      return this._minimized;\n    }\n\n    set minimized(value) {\n      // only update / emit events when the value changes\n      if (this._minimized !== value) {\n        this._minimized = value;\n\n        this._updateMinimized(value);\n\n        this.minimizedChange.emit(value);\n        this.sidebarService.toggle({\n          minimize: value\n        });\n      }\n    }\n\n    ngOnInit() {\n      this.displayBreakpoint(this.display);\n      this.isCompact(this.compact);\n      this.isFixed(this.fixed);\n      this.isOffCanvas(this.offCanvas);\n      this.sidebarService.toggle({\n        minimize: this.minimized\n      });\n      this.subscriptionEvents = this.sidebarService.events$.subscribe(action => {\n        if (action.minimize !== undefined) {\n          action.minimize === 'toggle' ? this.toggleMinimized() : this.minimized = !!action.minimize;\n        }\n      });\n    }\n\n    ngOnDestroy() {\n      this.subscriptionEvents.unsubscribe();\n      this.minimizedChange.complete();\n      this.renderer.removeClass(this.document.body, 'sidebar-fixed');\n\n      this._updateMinimized(false);\n    }\n\n    isCompact(compact = this.compact) {\n      if (compact) {\n        this.renderer.addClass(this.document.body, 'sidebar-compact');\n      }\n    }\n\n    isFixed(fixed = this.fixed) {\n      if (fixed) {\n        this.renderer.addClass(this.document.body, 'sidebar-fixed');\n      }\n    }\n\n    toggleMinimized() {\n      this.minimized = !this._minimized;\n    }\n\n    isOffCanvas(offCanvas = this.offCanvas) {\n      if (offCanvas) {\n        this.renderer.addClass(this.document.body, 'sidebar-off-canvas');\n      }\n    }\n\n    displayBreakpoint(display = this.display) {\n      if (display !== false) {\n        const cssClass = display ? `sidebar-${display}-show` : sidebarCssClasses[0];\n        this.renderer.addClass(this.document.body, cssClass);\n      }\n    }\n\n    _updateMinimized(minimized) {\n      const body = this.document.body;\n\n      if (minimized) {\n        this.renderer.addClass(body, 'sidebar-minimized');\n        this.renderer.addClass(body, 'brand-minimized');\n      } else {\n        this.renderer.removeClass(body, 'sidebar-minimized');\n        this.renderer.removeClass(body, 'brand-minimized');\n      }\n    }\n\n  }\n\n  AppSidebarComponent.ɵfac = function AppSidebarComponent_Factory(t) {\n    return new (t || AppSidebarComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(AppSidebarService));\n  };\n\n  AppSidebarComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarComponent,\n    selectors: [[\"app-sidebar\"], [\"cui-sidebar\"]],\n    hostVars: 2,\n    hostBindings: function AppSidebarComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"sidebar\", ctx.sidebarClass);\n      }\n    },\n    inputs: {\n      minimized: \"minimized\",\n      compact: \"compact\",\n      display: \"display\",\n      fixed: \"fixed\",\n      offCanvas: \"offCanvas\"\n    },\n    outputs: {\n      minimizedChange: \"minimizedChange\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppSidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppSidebarComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavComponent {\n    constructor(router) {\n      this.router = router;\n      this.navItems = [];\n      this.sidebarNavClass = true;\n      this.role = 'nav';\n      this.navItemsArray = [];\n    }\n\n    ngOnChanges(changes) {\n      this.navItemsArray = Array.isArray(this.navItems) ? this.navItems.slice() : [];\n    }\n\n  }\n\n  AppSidebarNavComponent.ɵfac = function AppSidebarNavComponent_Factory(t) {\n    return new (t || AppSidebarNavComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Router));\n  };\n\n  AppSidebarNavComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavComponent,\n    selectors: [[\"app-sidebar-nav\"], [\"cui-sidebar-nav\"]],\n    hostVars: 3,\n    hostBindings: function AppSidebarNavComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"role\", ctx.role);\n        ɵngcc0.ɵɵclassProp(\"sidebar-nav\", ctx.sidebarNavClass);\n      }\n    },\n    inputs: {\n      navItems: \"navItems\",\n      role: \"role\"\n    },\n    features: [ɵngcc0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"nav\", 3, \"items\"]],\n    template: function AppSidebarNavComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelement(0, \"app-sidebar-nav-items\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"items\", ctx.navItemsArray);\n      }\n    },\n    directives: function () {\n      return [AppSidebarNavItemsComponent];\n    },\n    encapsulation: 2\n  });\n  return AppSidebarNavComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarMinimizerComponent = /*#__PURE__*/(() => {\n  class AppSidebarMinimizerComponent {\n    constructor(sidebarService) {\n      this.sidebarService = sidebarService;\n      this.role = 'button';\n      this.sidebarMinimizerClass = true;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      this.sidebarService.toggle({\n        minimize: 'toggle'\n      });\n    }\n\n  }\n\n  AppSidebarMinimizerComponent.ɵfac = function AppSidebarMinimizerComponent_Factory(t) {\n    return new (t || AppSidebarMinimizerComponent)(ɵngcc0.ɵɵdirectiveInject(AppSidebarService));\n  };\n\n  AppSidebarMinimizerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarMinimizerComponent,\n    selectors: [[\"app-sidebar-minimizer\"], [\"cui-sidebar-minimizer\"]],\n    hostVars: 3,\n    hostBindings: function AppSidebarMinimizerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function AppSidebarMinimizerComponent_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵattribute(\"role\", ctx.role);\n        ɵngcc0.ɵɵclassProp(\"sidebar-minimizer\", ctx.sidebarMinimizerClass);\n      }\n    },\n    inputs: {\n      role: \"role\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function AppSidebarMinimizerComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n  return AppSidebarMinimizerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet SidebarNavService = /*#__PURE__*/(() => {\n  class SidebarNavService {}\n\n  SidebarNavService.ɵfac = function SidebarNavService_Factory(t) {\n    return new (t || SidebarNavService)();\n  };\n\n  SidebarNavService.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: SidebarNavService,\n    factory: SidebarNavService.ɵfac\n  });\n  return SidebarNavService;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet SidebarNavHelper = /*#__PURE__*/(() => {\n  class SidebarNavHelper {\n    constructor() {\n      this.hasBadge = item => Boolean(item.badge);\n\n      this.hasIcon = item => Boolean(item.icon);\n    }\n\n    itemType(item) {\n      if (item.divider) {\n        return 'divider';\n      } else if (item.title) {\n        return 'title';\n      } else if (item.children) {\n        return 'dropdown';\n      } else if (item.label) {\n        return 'label';\n      } else if (!Object.keys(item).length) {\n        return 'empty';\n      } else {\n        return 'link';\n      }\n    }\n\n    isActive(router, item) {\n      return router.isActive(item.url, false);\n    }\n\n    getIconClass(item) {\n      const classes = {\n        'nav-icon': true\n      };\n\n      if (this.hasIcon(item)) {\n        const icon = item.icon;\n        classes[icon] = this.hasIcon(item);\n      }\n\n      return classes;\n    }\n\n  }\n\n  SidebarNavHelper.ɵfac = function SidebarNavHelper_Factory(t) {\n    return new (t || SidebarNavHelper)();\n  };\n\n  SidebarNavHelper.ɵprov = ɵngcc0.ɵɵdefineInjectable({\n    token: SidebarNavHelper,\n    factory: SidebarNavHelper.ɵfac\n  });\n  return SidebarNavHelper;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarFooterComponent = /*#__PURE__*/(() => {\n  class AppSidebarFooterComponent {\n    constructor() {\n      this.sidebarFooterClass = true;\n    }\n\n  }\n\n  AppSidebarFooterComponent.ɵfac = function AppSidebarFooterComponent_Factory(t) {\n    return new (t || AppSidebarFooterComponent)();\n  };\n\n  AppSidebarFooterComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarFooterComponent,\n    selectors: [[\"app-sidebar-footer\"], [\"cui-sidebar-footer\"]],\n    hostVars: 2,\n    hostBindings: function AppSidebarFooterComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"sidebar-footer\", ctx.sidebarFooterClass);\n      }\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppSidebarFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppSidebarFooterComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarFormComponent = /*#__PURE__*/(() => {\n  class AppSidebarFormComponent {\n    constructor() {\n      this.sidebarFormClass = true;\n    }\n\n  }\n\n  AppSidebarFormComponent.ɵfac = function AppSidebarFormComponent_Factory(t) {\n    return new (t || AppSidebarFormComponent)();\n  };\n\n  AppSidebarFormComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarFormComponent,\n    selectors: [[\"app-sidebar-form\"], [\"cui-sidebar-form\"]],\n    hostVars: 2,\n    hostBindings: function AppSidebarFormComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"sidebar-form\", ctx.sidebarFormClass);\n      }\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppSidebarFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppSidebarFormComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarHeaderComponent = /*#__PURE__*/(() => {\n  class AppSidebarHeaderComponent {\n    constructor() {\n      this.sidebarHeaderClass = true;\n    }\n\n  }\n\n  AppSidebarHeaderComponent.ɵfac = function AppSidebarHeaderComponent_Factory(t) {\n    return new (t || AppSidebarHeaderComponent)();\n  };\n\n  AppSidebarHeaderComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarHeaderComponent,\n    selectors: [[\"app-sidebar-header\"], [\"cui-sidebar-header\"]],\n    hostVars: 2,\n    hostBindings: function AppSidebarHeaderComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        ɵngcc0.ɵɵclassProp(\"sidebar-header\", ctx.sidebarHeaderClass);\n      }\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function AppSidebarHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵprojectionDef();\n        ɵngcc0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return AppSidebarHeaderComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet NavDropdownDirective = /*#__PURE__*/(() => {\n  class NavDropdownDirective {\n    constructor(el) {\n      this.el = el;\n    }\n\n    toggle() {\n      this.el.nativeElement.classList.toggle('open');\n    }\n\n  }\n\n  NavDropdownDirective.ɵfac = function NavDropdownDirective_Factory(t) {\n    return new (t || NavDropdownDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n  };\n\n  NavDropdownDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: NavDropdownDirective,\n    selectors: [[\"\", \"appNavDropdown\", \"\"]]\n  });\n  return NavDropdownDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\r\n * Allows the dropdown to be toggled via click.\r\n */\n\n\nlet NavDropdownToggleDirective = /*#__PURE__*/(() => {\n  class NavDropdownToggleDirective {\n    constructor(dropdown) {\n      this.dropdown = dropdown;\n    }\n\n    toggleOpen($event) {\n      $event.preventDefault();\n      this.dropdown.toggle();\n    }\n\n  }\n\n  NavDropdownToggleDirective.ɵfac = function NavDropdownToggleDirective_Factory(t) {\n    return new (t || NavDropdownToggleDirective)(ɵngcc0.ɵɵdirectiveInject(NavDropdownDirective));\n  };\n\n  NavDropdownToggleDirective.ɵdir = ɵngcc0.ɵɵdefineDirective({\n    type: NavDropdownToggleDirective,\n    selectors: [[\"\", \"appNavDropdownToggle\", \"\"]],\n    hostBindings: function NavDropdownToggleDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function NavDropdownToggleDirective_click_HostBindingHandler($event) {\n          return ctx.toggleOpen($event);\n        });\n      }\n    }\n  });\n  return NavDropdownToggleDirective;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavDividerComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavDividerComponent {\n    constructor() {}\n\n    ngOnInit() {}\n\n  }\n\n  AppSidebarNavDividerComponent.ɵfac = function AppSidebarNavDividerComponent_Factory(t) {\n    return new (t || AppSidebarNavDividerComponent)();\n  };\n\n  AppSidebarNavDividerComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavDividerComponent,\n    selectors: [[\"app-sidebar-nav-divider\"], [\"cui-sidebar-nav-divider\"]],\n    inputs: {\n      item: \"item\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function AppSidebarNavDividerComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n  return AppSidebarNavDividerComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavDropdownComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavDropdownComponent {\n    constructor(helper) {\n      this.helper = helper;\n    }\n\n  }\n\n  AppSidebarNavDropdownComponent.ɵfac = function AppSidebarNavDropdownComponent_Factory(t) {\n    return new (t || AppSidebarNavDropdownComponent)(ɵngcc0.ɵɵdirectiveInject(SidebarNavHelper));\n  };\n\n  AppSidebarNavDropdownComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavDropdownComponent,\n    selectors: [[\"app-sidebar-nav-dropdown\"], [\"cui-sidebar-nav-dropdown\"]],\n    inputs: {\n      item: \"item\"\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([SidebarNavHelper])],\n    decls: 6,\n    vars: 5,\n    consts: [[\"appNavDropdownToggle\", \"\", 1, \"nav-link\", \"nav-dropdown-toggle\", 3, \"appHtmlAttr\"], [3, \"ngClass\", 4, \"ngIf\"], [1, \"nav-dropdown-items\", 3, \"items\"], [3, \"ngClass\"]],\n    template: function AppSidebarNavDropdownComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"a\", 0);\n        ɵngcc0.ɵɵtemplate(1, AppSidebarNavDropdownComponent_i_1_Template, 2, 3, \"i\", 1);\n        ɵngcc0.ɵɵelementContainerStart(2);\n        ɵngcc0.ɵɵtext(3);\n        ɵngcc0.ɵɵelementContainerEnd();\n        ɵngcc0.ɵɵtemplate(4, AppSidebarNavDropdownComponent_span_4_Template, 3, 4, \"span\", 1);\n        ɵngcc0.ɵɵelementEnd();\n        ɵngcc0.ɵɵelement(5, \"app-sidebar-nav-items\", 2);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"appHtmlAttr\", ctx.item.attributes);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.helper.hasIcon(ctx.item));\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵtextInterpolate(ctx.item.name);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.helper.hasBadge(ctx.item));\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"items\", ctx.item.children);\n      }\n    },\n    directives: function () {\n      return [NavDropdownToggleDirective, HtmlAttributesDirective, ɵngcc2.NgIf, AppSidebarNavItemsComponent, ɵngcc2.NgClass];\n    },\n    pipes: function () {\n      return [AppSidebarNavIconPipe, AppSidebarNavBadgePipe];\n    },\n    styles: [\".nav-dropdown-toggle[_ngcontent-%COMP%] { cursor: pointer; }\", \".nav-dropdown-items[_ngcontent-%COMP%] { display: block; }\"]\n  });\n  return AppSidebarNavDropdownComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavItemsComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavItemsComponent {\n    constructor(document, renderer, router, helper) {\n      this.document = document;\n      this.renderer = renderer;\n      this.router = router;\n      this.helper = helper;\n    }\n\n    set items(items) {\n      this._items = [...items];\n    }\n\n    get items() {\n      return this._items;\n    }\n\n    hideMobile() {\n      if (this.document.body.classList.contains('sidebar-show')) {\n        this.renderer.removeClass(this.document.body, 'sidebar-show');\n      }\n    }\n\n  }\n\n  AppSidebarNavItemsComponent.ɵfac = function AppSidebarNavItemsComponent_Factory(t) {\n    return new (t || AppSidebarNavItemsComponent)(ɵngcc0.ɵɵdirectiveInject(DOCUMENT), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Router), ɵngcc0.ɵɵdirectiveInject(SidebarNavHelper));\n  };\n\n  AppSidebarNavItemsComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavItemsComponent,\n    selectors: [[\"app-sidebar-nav-items\"], [\"cui-sidebar-nav-items\"]],\n    inputs: {\n      items: \"items\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngFor\", \"ngForOf\"], [3, \"ngSwitch\"], [\"appNavDropdown\", \"\", \"routerLinkActive\", \"open\", 3, \"item\", \"open\", \"ngClass\", 4, \"ngSwitchCase\"], [3, \"item\", \"ngClass\", \"appHtmlAttr\", 4, \"ngSwitchCase\"], [\"class\", \"nav-item\", 3, \"item\", \"ngClass\", 4, \"ngSwitchCase\"], [4, \"ngSwitchCase\"], [\"class\", \"nav-item\", 3, \"item\", \"ngClass\", \"linkClick\", 4, \"ngSwitchDefault\"], [\"appNavDropdown\", \"\", \"routerLinkActive\", \"open\", 3, \"item\", \"ngClass\"], [3, \"item\", \"ngClass\", \"appHtmlAttr\"], [1, \"nav-item\", 3, \"item\", \"ngClass\"], [1, \"nav-item\", 3, \"item\", \"ngClass\", \"linkClick\"]],\n    template: function AppSidebarNavItemsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, AppSidebarNavItemsComponent_ng_container_0_Template, 8, 6, \"ng-container\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgForOf, ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgSwitchDefault, AppSidebarNavDropdownComponent, NavDropdownDirective, ɵngcc1.RouterLinkActive, ɵngcc2.NgClass, AppSidebarNavDividerComponent, HtmlAttributesDirective, AppSidebarNavTitleComponent, AppSidebarNavLabelComponent, AppSidebarNavLinkComponent];\n    },\n    pipes: function () {\n      return [AppSidebarNavItemClassPipe];\n    },\n    encapsulation: 2\n  });\n  return AppSidebarNavItemsComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavLinkContentComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavLinkContentComponent {\n    constructor(helper) {\n      this.helper = helper;\n    }\n\n    ngOnInit() {}\n\n    ngOnDestroy() {}\n\n  }\n\n  AppSidebarNavLinkContentComponent.ɵfac = function AppSidebarNavLinkContentComponent_Factory(t) {\n    return new (t || AppSidebarNavLinkContentComponent)(ɵngcc0.ɵɵdirectiveInject(SidebarNavHelper));\n  };\n\n  AppSidebarNavLinkContentComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavLinkContentComponent,\n    selectors: [[\"app-sidebar-nav-link-content\"], [\"cui-sidebar-nav-link-content\"]],\n    inputs: {\n      item: \"item\"\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([SidebarNavHelper])],\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function AppSidebarNavLinkContentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, AppSidebarNavLinkContentComponent_ng_container_0_Template, 5, 3, \"ng-container\", 0);\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", true);\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgIf, ɵngcc2.NgClass];\n    },\n    pipes: function () {\n      return [AppSidebarNavIconPipe, AppSidebarNavBadgePipe];\n    },\n    encapsulation: 2\n  });\n  return AppSidebarNavLinkContentComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavLinkComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavLinkComponent {\n    constructor(router) {\n      this.router = router;\n      this.linkClick = new EventEmitter();\n      this.navigationEndObservable = router.events.pipe(filter(event => {\n        return event instanceof NavigationEnd;\n      }));\n    }\n\n    set item(item) {\n      this._Item = JSON.parse(JSON.stringify(item));\n    }\n\n    get item() {\n      return this._Item;\n    }\n\n    ngOnInit() {\n      this.url = typeof this.item.url === 'string' ? this.item.url : this.router.serializeUrl(this.router.createUrlTree(this.item.url));\n      this.linkType = this.getLinkType();\n      this.href = this.isDisabled() ? '' : this.item.href || this.url;\n      this.linkActive = this.router.url.split(/[?#(;]/)[0] === this.href.split(/[?#(;]/)[0];\n      this.navSubscription = this.navigationEndObservable.subscribe(event => {\n        const itemUrlArray = this.href.split(/[?#(;]/)[0].split('/');\n        const urlArray = event.urlAfterRedirects.split(/[?#(;]/)[0].split('/');\n        this.linkActive = itemUrlArray.every((value, index) => value === urlArray[index]);\n      });\n    }\n\n    ngOnDestroy() {\n      this.navSubscription.unsubscribe();\n    }\n\n    getLinkType() {\n      return this.isDisabled() ? 'disabled' : this.isExternalLink() ? 'external' : 'link';\n    }\n\n    isDisabled() {\n      return this.item.attributes && this.item.attributes.disabled ? true : null;\n    }\n\n    isExternalLink() {\n      return !!this.item.href || this.url.substring(0, 4) === 'http';\n    }\n\n    linkClicked() {\n      this.linkClick.emit();\n    }\n\n  }\n\n  AppSidebarNavLinkComponent.ɵfac = function AppSidebarNavLinkComponent_Factory(t) {\n    return new (t || AppSidebarNavLinkComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc1.Router));\n  };\n\n  AppSidebarNavLinkComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavLinkComponent,\n    selectors: [[\"app-sidebar-nav-link\"], [\"cui-sidebar-nav-link\"]],\n    inputs: {\n      item: \"item\"\n    },\n    outputs: {\n      linkClick: \"linkClick\"\n    },\n    features: [ɵngcc0.ɵɵProvidersFeature([SidebarNavHelper])],\n    decls: 4,\n    vars: 3,\n    consts: [[3, \"ngSwitch\"], [3, \"ngClass\", \"appHtmlAttr\", 4, \"ngSwitchCase\"], [3, \"ngClass\", \"href\", \"appHtmlAttr\", \"click\", 4, \"ngSwitchCase\"], [3, \"ngClass\", \"appHtmlAttr\", \"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLink\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"active\", \"click\", 4, \"ngSwitchDefault\"], [3, \"ngClass\", \"appHtmlAttr\"], [3, \"item\"], [3, \"ngClass\", \"href\", \"appHtmlAttr\", \"click\"], [3, \"ngClass\", \"appHtmlAttr\", \"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"routerLink\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"click\"]],\n    template: function AppSidebarNavLinkComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementContainerStart(0, 0);\n        ɵngcc0.ɵɵtemplate(1, AppSidebarNavLinkComponent_a_1_Template, 3, 5, \"a\", 1);\n        ɵngcc0.ɵɵtemplate(2, AppSidebarNavLinkComponent_a_2_Template, 3, 6, \"a\", 2);\n        ɵngcc0.ɵɵtemplate(3, AppSidebarNavLinkComponent_a_3_Template, 3, 19, \"a\", 3);\n        ɵngcc0.ɵɵelementContainerEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngSwitch\", ctx.linkType);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"disabled\");\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngSwitchCase\", \"external\");\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgSwitch, ɵngcc2.NgSwitchCase, ɵngcc2.NgSwitchDefault, ɵngcc2.NgClass, HtmlAttributesDirective, AppSidebarNavLinkContentComponent, ɵngcc1.RouterLinkWithHref, ɵngcc1.RouterLinkActive];\n    },\n    pipes: function () {\n      return [AppSidebarNavLinkPipe];\n    },\n    encapsulation: 2\n  });\n  return AppSidebarNavLinkComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavTitleComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavTitleComponent {\n    constructor(el, renderer) {\n      this.el = el;\n      this.renderer = renderer;\n    }\n\n    ngOnInit() {\n      const nativeElement = this.el.nativeElement;\n      const name = this.renderer.createText(this.item.name);\n\n      if (this.item.class) {\n        const classes = this.item.class;\n        this.renderer.addClass(nativeElement, classes);\n      }\n\n      if (this.item.wrapper) {\n        const wrapper = this.renderer.createElement(this.item.wrapper.element);\n        this.addAttribs(this.item.wrapper.attributes, wrapper);\n        this.renderer.appendChild(wrapper, name);\n        this.renderer.appendChild(nativeElement, wrapper);\n      } else {\n        this.renderer.appendChild(nativeElement, name);\n      }\n    }\n\n    addAttribs(attribs, element) {\n      if (attribs) {\n        for (const attr in attribs) {\n          if (attr === 'style' && typeof attribs[attr] === 'object') {\n            this.setStyle(attribs[attr], element);\n          } else if (attr === 'class') {\n            this.addClass(attribs[attr], element);\n          } else {\n            this.setAttrib(attr, attribs[attr], element);\n          }\n        }\n      }\n    }\n\n    setStyle(styles, el) {\n      for (const style in styles) {\n        this.renderer.setStyle(el, style, styles[style]);\n      }\n    }\n\n    addClass(classes, el) {\n      const classArray = Array.isArray(classes) ? classes : classes.split(' ');\n      classArray.filter(element => element.length > 0).forEach(element => {\n        this.renderer.addClass(el, element);\n      });\n    }\n\n    setAttrib(key, value, el) {\n      this.renderer.setAttribute(el, key, value);\n    }\n\n  }\n\n  AppSidebarNavTitleComponent.ɵfac = function AppSidebarNavTitleComponent_Factory(t) {\n    return new (t || AppSidebarNavTitleComponent)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.Renderer2));\n  };\n\n  AppSidebarNavTitleComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavTitleComponent,\n    selectors: [[\"app-sidebar-nav-title\"], [\"cui-sidebar-nav-title\"]],\n    inputs: {\n      item: \"item\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function AppSidebarNavTitleComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n  return AppSidebarNavTitleComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavLabelComponent = /*#__PURE__*/(() => {\n  class AppSidebarNavLabelComponent {\n    constructor(helper) {\n      this.helper = helper;\n      this.classes = {\n        'nav-label': true,\n        active: true\n      };\n      this.iconClasses = {};\n    }\n\n    ngOnInit() {\n      this.iconClasses = this.helper.getIconClass(this.item);\n    }\n\n    getItemClass() {\n      const itemClass = this.item.class;\n      this.classes[itemClass] = !!itemClass;\n      return this.classes;\n    }\n\n    getLabelIconClass() {\n      const variant = `text-${this.item.label.variant}`;\n      this.iconClasses[variant] = !!this.item.label.variant;\n      const labelClass = this.item.label.class;\n      this.iconClasses[labelClass] = !!labelClass;\n      return this.iconClasses;\n    }\n\n  }\n\n  AppSidebarNavLabelComponent.ɵfac = function AppSidebarNavLabelComponent_Factory(t) {\n    return new (t || AppSidebarNavLabelComponent)(ɵngcc0.ɵɵdirectiveInject(SidebarNavHelper));\n  };\n\n  AppSidebarNavLabelComponent.ɵcmp = ɵngcc0.ɵɵdefineComponent({\n    type: AppSidebarNavLabelComponent,\n    selectors: [[\"app-sidebar-nav-label\"], [\"cui-sidebar-nav-label\"]],\n    inputs: {\n      item: \"item\"\n    },\n    decls: 5,\n    vars: 6,\n    consts: [[3, \"ngClass\", \"href\", \"appHtmlAttr\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function AppSidebarNavLabelComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        ɵngcc0.ɵɵelementStart(0, \"a\", 0);\n        ɵngcc0.ɵɵtemplate(1, AppSidebarNavLabelComponent_i_1_Template, 1, 1, \"i\", 1);\n        ɵngcc0.ɵɵelementContainerStart(2);\n        ɵngcc0.ɵɵtext(3);\n        ɵngcc0.ɵɵelementContainerEnd();\n        ɵngcc0.ɵɵtemplate(4, AppSidebarNavLabelComponent_span_4_Template, 3, 4, \"span\", 1);\n        ɵngcc0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        ɵngcc0.ɵɵpropertyInterpolate(\"href\", ctx.item.url, ɵngcc0.ɵɵsanitizeUrl);\n        ɵngcc0.ɵɵproperty(\"ngClass\", ctx.getItemClass())(\"appHtmlAttr\", ctx.item.attributes);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.helper.hasIcon(ctx.item));\n        ɵngcc0.ɵɵadvance(2);\n        ɵngcc0.ɵɵtextInterpolate(ctx.item.name);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.helper.hasBadge(ctx.item));\n      }\n    },\n    directives: function () {\n      return [ɵngcc2.NgClass, HtmlAttributesDirective, ɵngcc2.NgIf];\n    },\n    pipes: function () {\n      return [AppSidebarNavBadgePipe];\n    },\n    encapsulation: 2\n  });\n  return AppSidebarNavLabelComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavIconPipe = /*#__PURE__*/(() => {\n  class AppSidebarNavIconPipe {\n    transform(item, args) {\n      const classes = {\n        'nav-icon': true\n      };\n      const icon = item.icon;\n      classes[icon] = !!item.icon;\n      return classes;\n    }\n\n  }\n\n  AppSidebarNavIconPipe.ɵfac = function AppSidebarNavIconPipe_Factory(t) {\n    return new (t || AppSidebarNavIconPipe)();\n  };\n\n  AppSidebarNavIconPipe.ɵpipe = ɵngcc0.ɵɵdefinePipe({\n    name: \"appSidebarNavIcon\",\n    type: AppSidebarNavIconPipe,\n    pure: true\n  });\n  return AppSidebarNavIconPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavBadgePipe = /*#__PURE__*/(() => {\n  class AppSidebarNavBadgePipe {\n    transform(item, args) {\n      const classes = {\n        badge: true\n      };\n      const variant = `badge-${item.badge.variant}`;\n      classes[variant] = !!item.badge.variant;\n      classes[item.badge.class] = !!item.badge.class;\n      return classes;\n    }\n\n  }\n\n  AppSidebarNavBadgePipe.ɵfac = function AppSidebarNavBadgePipe_Factory(t) {\n    return new (t || AppSidebarNavBadgePipe)();\n  };\n\n  AppSidebarNavBadgePipe.ɵpipe = ɵngcc0.ɵɵdefinePipe({\n    name: \"appSidebarNavBadge\",\n    type: AppSidebarNavBadgePipe,\n    pure: true\n  });\n  return AppSidebarNavBadgePipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavLinkPipe = /*#__PURE__*/(() => {\n  class AppSidebarNavLinkPipe {\n    transform(item) {\n      const classes = {\n        'nav-link': true\n      };\n      const disabled = item.attributes && item.attributes.disabled;\n      classes['disabled'] = disabled;\n      classes['btn-link'] = disabled;\n      classes[`nav-link-${item.variant}`] = !!item.variant;\n      return classes;\n    }\n\n  }\n\n  AppSidebarNavLinkPipe.ɵfac = function AppSidebarNavLinkPipe_Factory(t) {\n    return new (t || AppSidebarNavLinkPipe)();\n  };\n\n  AppSidebarNavLinkPipe.ɵpipe = ɵngcc0.ɵɵdefinePipe({\n    name: \"appSidebarNavLink\",\n    type: AppSidebarNavLinkPipe,\n    pure: true\n  });\n  return AppSidebarNavLinkPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarNavItemClassPipe = /*#__PURE__*/(() => {\n  class AppSidebarNavItemClassPipe {\n    constructor(helper) {\n      this.helper = helper;\n    }\n\n    transform(item, ...args) {\n      const itemType = this.helper.itemType(item);\n      let itemClass;\n\n      if (['divider', 'title'].includes(itemType)) {\n        itemClass = `nav-${itemType}`;\n      } else if (itemType === 'dropdown') {\n        itemClass = 'nav-item nav-dropdown';\n      } else {\n        itemClass = 'nav-item';\n      }\n\n      return item.class ? `${itemClass} ${item.class}` : itemClass;\n    }\n\n  }\n\n  AppSidebarNavItemClassPipe.ɵfac = function AppSidebarNavItemClassPipe_Factory(t) {\n    return new (t || AppSidebarNavItemClassPipe)(ɵngcc0.ɵɵdirectiveInject(SidebarNavHelper));\n  };\n\n  AppSidebarNavItemClassPipe.ɵpipe = ɵngcc0.ɵɵdefinePipe({\n    name: \"appSidebarNavItemClass\",\n    type: AppSidebarNavItemClassPipe,\n    pure: true\n  });\n  return AppSidebarNavItemClassPipe;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet AppSidebarModule = /*#__PURE__*/(() => {\n  class AppSidebarModule {}\n\n  AppSidebarModule.ɵmod = ɵngcc0.ɵɵdefineNgModule({\n    type: AppSidebarModule\n  });\n  AppSidebarModule.ɵinj = ɵngcc0.ɵɵdefineInjector({\n    factory: function AppSidebarModule_Factory(t) {\n      return new (t || AppSidebarModule)();\n    },\n    providers: [SidebarNavHelper, AppSidebarService],\n    imports: [[CommonModule, RouterModule, LayoutModule], LayoutModule]\n  });\n  return AppSidebarModule;\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(AppSidebarModule, {\n    declarations: function () {\n      return [AppSidebarFooterComponent, AppSidebarFormComponent, AppSidebarHeaderComponent, AppSidebarMinimizerComponent, AppSidebarMinimizerComponent, AppSidebarComponent, AppSidebarNavItemsComponent, AppSidebarNavComponent, AppSidebarNavDividerComponent, AppSidebarNavDropdownComponent, AppSidebarNavLinkComponent, AppSidebarNavLinkContentComponent, AppSidebarNavTitleComponent, NavDropdownDirective, NavDropdownToggleDirective, AppSidebarNavLabelComponent, AppSidebarNavIconPipe, AppSidebarNavBadgePipe, AppSidebarNavLinkPipe, AppSidebarNavItemClassPipe];\n    },\n    imports: function () {\n      return [CommonModule, RouterModule, LayoutModule];\n    },\n    exports: function () {\n      return [AppSidebarFooterComponent, AppSidebarFormComponent, AppSidebarHeaderComponent, AppSidebarMinimizerComponent, AppSidebarComponent, AppSidebarNavItemsComponent, AppSidebarNavComponent, AppSidebarNavDividerComponent, AppSidebarNavDropdownComponent, AppSidebarNavLabelComponent, AppSidebarNavLinkComponent, AppSidebarNavLinkContentComponent, AppSidebarNavTitleComponent, NavDropdownDirective, NavDropdownToggleDirective, LayoutModule];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/*\r\n * Public API Surface of @coreui/angular\r\n */\n// export * from './lib/shared/index';\n// export * from './lib/coreui.module';\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { AppAsideComponent, AppAsideModule, AppBreadcrumbComponent, AppBreadcrumbModule, AppFooterComponent, AppFooterModule, AppHeaderComponent, AppHeaderModule, AppSidebarComponent, AppSidebarMinimizerComponent, AppSidebarModule, AppSidebarNavComponent, CuiBreadcrumbComponent, SidebarNavHelper, LayoutModule as ɵa, SidebarToggleDirective as ɵb, AppSidebarNavItemClassPipe as ɵba, SidebarMinimizeDirective as ɵc, MobileSidebarToggleDirective as ɵd, SidebarOffCanvasCloseDirective as ɵe, BrandMinimizeDirective as ɵf, AsideToggleDirective as ɵg, HtmlAttributesDirective as ɵh, ClassToggler as ɵi, AppBreadcrumbService as ɵj, AppSidebarService as ɵk, AppSidebarFooterComponent as ɵl, AppSidebarFormComponent as ɵm, AppSidebarHeaderComponent as ɵn, AppSidebarNavItemsComponent as ɵo, AppSidebarNavDividerComponent as ɵp, AppSidebarNavDropdownComponent as ɵq, AppSidebarNavLabelComponent as ɵr, AppSidebarNavLinkContentComponent as ɵs, AppSidebarNavLinkComponent as ɵt, AppSidebarNavTitleComponent as ɵu, NavDropdownDirective as ɵv, NavDropdownToggleDirective as ɵw, AppSidebarNavIconPipe as ɵx, AppSidebarNavBadgePipe as ɵy, AppSidebarNavLinkPipe as ɵz }; //# sourceMappingURL=coreui-angular.js.map", "map": null, "metadata": {}, "sourceType": "module"}