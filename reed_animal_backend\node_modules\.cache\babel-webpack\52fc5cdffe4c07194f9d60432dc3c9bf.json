{"ast": null, "code": "import baseForRight from './_baseForRight.js';\nimport keys from './keys.js';\n/**\n * The base implementation of `_.forOwnRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\n\nfunction baseForOwnRight(object, iteratee) {\n  return object && baseForRight(object, iteratee, keys);\n}\n\nexport default baseForOwnRight;", "map": null, "metadata": {}, "sourceType": "module"}