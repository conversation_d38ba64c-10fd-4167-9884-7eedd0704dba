{"ast": null, "code": "import baseRest from './_baseRest.js';\n/**\n * A `baseRest` alias which can be replaced with `identity` by module\n * replacement plugins.\n *\n * @private\n * @type {Function}\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\n\nvar castRest = baseRest;\nexport default castRest;", "map": null, "metadata": {}, "sourceType": "module"}