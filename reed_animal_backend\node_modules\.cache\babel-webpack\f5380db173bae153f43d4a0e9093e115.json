{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n/**\n * Creates a function that produces an instance of `Ctor` regardless of\n * whether it was invoked as part of a `new` expression or by `call` or `apply`.\n *\n * @private\n * @param {Function} Ctor The constructor to wrap.\n * @returns {Function} Returns the new wrapped function.\n */\n\nfunction createCtor(Ctor) {\n  return function () {\n    // Use a `switch` statement to work with class constructors. See\n    // http://ecma-international.org/ecma-262/7.0/#sec-ecmascript-function-objects-call-thisargument-argumentslist\n    // for more details.\n    var args = arguments;\n\n    switch (args.length) {\n      case 0:\n        return new Ctor();\n\n      case 1:\n        return new Ctor(args[0]);\n\n      case 2:\n        return new Ctor(args[0], args[1]);\n\n      case 3:\n        return new Ctor(args[0], args[1], args[2]);\n\n      case 4:\n        return new Ctor(args[0], args[1], args[2], args[3]);\n\n      case 5:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4]);\n\n      case 6:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5]);\n\n      case 7:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);\n    }\n\n    var thisBinding = baseCreate(Ctor.prototype),\n        result = Ctor.apply(thisBinding, args); // Mimic the constructor's `return` behavior.\n    // See https://es5.github.io/#x13.2.2 for more details.\n\n    return isObject(result) ? result : thisBinding;\n  };\n}\n\nexport default createCtor;", "map": null, "metadata": {}, "sourceType": "module"}