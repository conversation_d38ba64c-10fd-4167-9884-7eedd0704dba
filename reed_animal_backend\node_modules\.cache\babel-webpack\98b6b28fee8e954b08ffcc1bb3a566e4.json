{"ast": null, "code": "import isArrayLikeObject from './isArrayLikeObject.js';\n/**\n * Casts `value` to an empty array if it's not an array like object.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array|Object} Returns the cast array-like object.\n */\n\nfunction castArrayLikeObject(value) {\n  return isArrayLikeObject(value) ? value : [];\n}\n\nexport default castArrayLikeObject;", "map": null, "metadata": {}, "sourceType": "module"}