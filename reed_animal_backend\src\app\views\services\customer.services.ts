import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Api } from '../Api';

@Injectable({
    providedIn: 'root'
})
export class CustomerService extends Api {

    //Get All Animal Type
    GetCustomerList(params: any, data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/user?search=${data}&token=${localStorage.auth_token}`, { params });
    }

    //Edit or update user
    UpdateUser(id: any, data: any): Observable<any> {
        // console.log('daat->',data)
        return this.http.put(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`, data);
    }

    //Get Pets Details by using user_id
    FindById(id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/pet/${id}?token=${localStorage.auth_token}`);
    }

    //Get particular pet details by using pet id
    GetPetDetails(id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/petDetails/${id}?token=${localStorage.auth_token}`);
    }

    //Get upcoming appointments by using user id
    GetUpcomingAppoint(id: any, data: any): Observable<any> {
        // console.log('res',id)
        return this.http.get(`${this.config.APIUrl}/pet/upcomingAppointment/${id}?token=${localStorage.auth_token}&apt_date_time=${data}`);
    }

    //get Past visits by using user id
    GetPastVisit(id: any, data: any): Observable<any> {
        // console.log('res',id)
        return this.http.get(`${this.config.APIUrl}/pet/PastVisit/${id}?token=${localStorage.auth_token}&apt_date_time=${data}`);
    }

    //get Past visits by using user id
    AddCustomer(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl3}/signUp/`, data);
    }

    //Get User Details by using user_id
    FindByUserId(id: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`);
    }

    //Delete User by using user_id
    DeleteCustomer(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl}/user/${id}?token=${localStorage.auth_token}`);
    }

    //Get All Breeding Type 
    GetBreedingsList(data: any): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/Breeding?search=${data}&token=${localStorage.auth_token}`);
    }

    //Get All Animal Type
    GetTypesList(): Observable<any> {
        return this.http.get(`${this.config.APIUrl}/animal?search=&token=${localStorage.auth_token}`);
    }

    //Picutre upload
    uploadFile(data: File): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('file', data);
        return this.http.post(`${this.config.APIUrl3}/v1/pet/petimage?token=${localStorage.auth_token}`, formData, {
            reportProgress: true,
            responseType: 'json'
        });
    }

    //Add pet 
    AddPet(data: any): Observable<any> {
        return this.http.post(`${this.config.APIUrl3}/v1/pet?token=${localStorage.auth_token}`, data);
    }

    //Delete Pet by using user_id
    Deletepet(id: any): Observable<any> {
        return this.http.delete(`${this.config.APIUrl3}/v1/pet/${id}?token=${localStorage.auth_token}`);
    }
}