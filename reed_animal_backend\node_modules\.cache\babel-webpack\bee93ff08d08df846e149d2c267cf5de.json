{"ast": null, "code": "import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n/** `Object#toString` result references. */\n\nvar mapTag = '[object Map]';\n/**\n * The base implementation of `_.isMap` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n */\n\nfunction baseIsMap(value) {\n  return isObjectLike(value) && getTag(value) == mapTag;\n}\n\nexport default baseIsMap;", "map": null, "metadata": {}, "sourceType": "module"}