{"ast": null, "code": "import composeArgs from './_composeArgs.js';\nimport composeArgsRight from './_composeArgsRight.js';\nimport replaceHolders from './_replaceHolders.js';\n/** Used as the internal argument placeholder. */\n\nvar PLACEHOLDER = '__lodash_placeholder__';\n/** Used to compose bitmasks for function metadata. */\n\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_BOUND_FLAG = 4,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_ARY_FLAG = 128,\n    WRAP_REARG_FLAG = 256;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMin = Math.min;\n/**\n * Merges the function metadata of `source` into `data`.\n *\n * Merging metadata reduces the number of wrappers used to invoke a function.\n * This is possible because methods like `_.bind`, `_.curry`, and `_.partial`\n * may be applied regardless of execution order. Methods like `_.ary` and\n * `_.rearg` modify function arguments, making the order in which they are\n * executed important, preventing the merging of metadata. However, we make\n * an exception for a safe combined case where curried functions have `_.ary`\n * and or `_.rearg` applied.\n *\n * @private\n * @param {Array} data The destination metadata.\n * @param {Array} source The source metadata.\n * @returns {Array} Returns `data`.\n */\n\nfunction mergeData(data, source) {\n  var bitmask = data[1],\n      srcBitmask = source[1],\n      newBitmask = bitmask | srcBitmask,\n      isCommon = newBitmask < (WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG | WRAP_ARY_FLAG);\n  var isCombo = srcBitmask == WRAP_ARY_FLAG && bitmask == WRAP_CURRY_FLAG || srcBitmask == WRAP_ARY_FLAG && bitmask == WRAP_REARG_FLAG && data[7].length <= source[8] || srcBitmask == (WRAP_ARY_FLAG | WRAP_REARG_FLAG) && source[7].length <= source[8] && bitmask == WRAP_CURRY_FLAG; // Exit early if metadata can't be merged.\n\n  if (!(isCommon || isCombo)) {\n    return data;\n  } // Use source `thisArg` if available.\n\n\n  if (srcBitmask & WRAP_BIND_FLAG) {\n    data[2] = source[2]; // Set when currying a bound function.\n\n    newBitmask |= bitmask & WRAP_BIND_FLAG ? 0 : WRAP_CURRY_BOUND_FLAG;\n  } // Compose partial arguments.\n\n\n  var value = source[3];\n\n  if (value) {\n    var partials = data[3];\n    data[3] = partials ? composeArgs(partials, value, source[4]) : value;\n    data[4] = partials ? replaceHolders(data[3], PLACEHOLDER) : source[4];\n  } // Compose partial right arguments.\n\n\n  value = source[5];\n\n  if (value) {\n    partials = data[5];\n    data[5] = partials ? composeArgsRight(partials, value, source[6]) : value;\n    data[6] = partials ? replaceHolders(data[5], PLACEHOLDER) : source[6];\n  } // Use source `argPos` if available.\n\n\n  value = source[7];\n\n  if (value) {\n    data[7] = value;\n  } // Use source `ary` if it's smaller.\n\n\n  if (srcBitmask & WRAP_ARY_FLAG) {\n    data[8] = data[8] == null ? source[8] : nativeMin(data[8], source[8]);\n  } // Use source `arity` if one is not provided.\n\n\n  if (data[9] == null) {\n    data[9] = source[9];\n  } // Use source `func` and merge bitmasks.\n\n\n  data[0] = source[0];\n  data[1] = newBitmask;\n  return data;\n}\n\nexport default mergeData;", "map": null, "metadata": {}, "sourceType": "module"}