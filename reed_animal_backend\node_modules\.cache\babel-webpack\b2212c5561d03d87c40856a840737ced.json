{"ast": null, "code": "import asciiSize from './_asciiSize.js';\nimport hasUnicode from './_hasUnicode.js';\nimport unicodeSize from './_unicodeSize.js';\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\n\nfunction stringSize(string) {\n  return hasUnicode(string) ? unicodeSize(string) : asciiSize(string);\n}\n\nexport default stringSize;", "map": null, "metadata": {}, "sourceType": "module"}