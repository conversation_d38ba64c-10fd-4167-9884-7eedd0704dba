{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport cacheHas from './_cacheHas.js';\nimport createSet from './_createSet.js';\nimport setToArray from './_setToArray.js';\n/** Used as the size to enable large array optimizations. */\n\nvar LARGE_ARRAY_SIZE = 200;\n/**\n * The base implementation of `_.uniqBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\n\nfunction baseUniq(array, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      length = array.length,\n      isCommon = true,\n      result = [],\n      seen = result;\n\n  if (comparator) {\n    isCommon = false;\n    includes = arrayIncludesWith;\n  } else if (length >= LARGE_ARRAY_SIZE) {\n    var set = iteratee ? null : createSet(array);\n\n    if (set) {\n      return setToArray(set);\n    }\n\n    isCommon = false;\n    includes = cacheHas;\n    seen = new SetCache();\n  } else {\n    seen = iteratee ? [] : result;\n  }\n\n  outer: while (++index < length) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n    value = comparator || value !== 0 ? value : 0;\n\n    if (isCommon && computed === computed) {\n      var seenIndex = seen.length;\n\n      while (seenIndex--) {\n        if (seen[seenIndex] === computed) {\n          continue outer;\n        }\n      }\n\n      if (iteratee) {\n        seen.push(computed);\n      }\n\n      result.push(value);\n    } else if (!includes(seen, computed, comparator)) {\n      if (seen !== result) {\n        seen.push(computed);\n      }\n\n      result.push(value);\n    }\n  }\n\n  return result;\n}\n\nexport default baseUniq;", "map": null, "metadata": {}, "sourceType": "module"}