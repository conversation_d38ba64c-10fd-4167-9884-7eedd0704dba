// Here you can add other styles

.auth-login-sign .card.bg-primary {
    border-color: #568d2c;
}

.auth-login-sign .bg-primary {
    background-color: #000 !important;
}
.auth-login-sign .btn {
    border-radius: 5px;
    border: 1px solid #568d2c;
    padding: 0.5rem 1.75rem;
}
.auth-login-sign .btn-primary:not(:disabled):not(.disabled):active, .auth-login-sign .btn-primary:not(:disabled):not(.disabled).active,.auth-login-sign .show > .btn-primary.dropdown-toggle,
.auth-login-sign .btn-primary:hover,.auth-login-sign .btn-primary:focus,.auth-login-sign .btn-success:not(:disabled):not(.disabled):active,.auth-login-sign .btn-success:not(:disabled):not(.disabled).active,
.auth-login-sign .show > .btn-success.dropdown-toggle,.auth-login-sign .btn-success:hover {
    color: #fff;
    background-color: #568d2c;
    border-color: #568d2c;
}
.auth-login-sign .btn-primary,.auth-login-sign .btn-success {
    color: #fff;
    background-color: #568d2c;
    border-color: #568d2c;
    padding: 0.5rem 1.75rem;
    line-height: 2;
    width:100%;
}
.auth-login-sign .btn-link { border-color: transparent;
    color: #568d2c;
}
.auth-login-sign .btn-link:hover {
    color: #568d2c;
    text-decoration: none;
}
.auth-login-sign .btn-primary:not(:disabled):not(.disabled):active:focus, 
.auth-login-sign .btn-primary:not(:disabled):not(.disabled).active:focus, 
.auth-login-sign .show > .btn-primary.dropdown-toggle:focus,
.auth-login-sign .btn-success:not(:disabled):not(.disabled):active:focus, 
.auth-login-sign .btn-success:not(:disabled):not(.disabled).active:focus, 
.auth-login-sign .show > .btn-success.dropdown-toggle:focus {
    box-shadow: none;
}
.auth-login-sign .btn-primary:focus, .auth-login-sign .btn-primary.focus,
.auth-login-sign .btn-success:focus,.auth-login-sign .btn-success.focus {
    box-shadow: none;
}
.auth-login-sign .form-control:focus {
    box-shadow: none;
    border-color: #568d2c;
}
.auth-login-sign .input-group:focus .input-group-text,
.auth-login-sign .input-group:hover .input-group-text {
    color:#568d2c;
}
.auth-login-sign .input-group-text i,
.auth-login-sign .input-group-text {font-weight:bold}
a,a:focus { color:#568d2c;}
a:hover {
    color: #568d2c;
    text-decoration: none;
}
.sidebar .nav-link.active .nav-icon {
    color: #568d2c;
    font-weight: 500;
}
.sidebar .nav-link:hover,.sidebar .nav-link.active:hover .nav-icon {
    color: #fff;
    background: #568d2c;
}
.sidebar .nav-link.active {
    border-bottom:2px solid #568d2c;
}
.sidebar .nav-link .nav-icon {
    color: #fff;
    font-weight: 500;  
}
.page-link,.page-link:hover {
    color:#568d2c;
    font-weight: 500;
}
.page-item.active .page-link {
    background-color: #568d2c;
    border-color: #568d2c;
}
.page-link:focus {
    box-shadow: 0 0 0 0.2rem rgb(86 141 44 / 25%);
}
.pagination {
float:right;
}
.btn-primary  {
    color: #fff;
    background-color: #568d2c;
    border-color: #568d2c;
    padding: 0.1rem 0.75rem;
    line-height: 2;
}
.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, 
.show > .btn-primary.dropdown-toggle,.btn-primary:hover {
    color: #fff;
    background-color: #568d2c;
    border-color: #568d2c;
}
.btn-primary:focus, .btn-primary.focus,.btn-primary:not(:disabled):not(.disabled):active:focus, 
.btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgb(86 141 44 / 25%);
}
.form-control:focus {
    border-color: #568d2c;
    box-shadow: 0 0 0 0.2rem rgb(86 141 44 / 25%);
}
.table-search {
float:right;
width:30%;
display:inline-block;
margin: auto;
}
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;

  }
  
  .switch input { 
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    left: 4px;
    bottom: 5px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  input:checked + .slider {
    background-color: #568d2c;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px #568d2c;
  }
  
  input:checked + .slider:before {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
  }
  
  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }
  .table th, .table td {
   vertical-align: middle;  
  }
  .badge {padding:7px; }
  .modal-primary .modal-header {
    background-color: #568d2c;
    padding:0.35rem 0.35rem;
}
.modal-primary .modal-content {
    border-color: #568d2c;
}
.btn-secondary,.btn-danger {
    padding: 0.1rem .75rem;
    line-height: 2;
}
.modal-dialog.modal-primary .modal-footer button,.modal-dialog.modal-danger .modal-footer button {
    width: 35%;
    margin: auto;
}
.modal-danger p {
    font-size: 18px;
    text-align: center;
    margin-bottom: 0;
}
.edit-slider input:checked + .slider {
background-color: #39b2d5;
}
.view-slider input:checked + .slider {
    background-color: #3a9d5d;
 }
 .delete-slider input:checked + .slider {
    background-color: #f63c3a;
 }
 .action-slider input:checked + .slider {
    background-color: #ff5722;
 }
#select1{
    width: 25%;
    display: inline-block;
}
app-forgot-password,app-reset-password {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}
label { font-weight: 500; }

.spinner-top {
    background: #000000ba;
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: 10000;
}
.spinner-border {
    position: absolute;
    top: 35%;
    width: 100px;
    height: 100px;
    // z-index: 10000000;
}
.spin-load {
    position: absolute;
    top: 35%;
    width: 100px;
    height: 100px;
}
body { position: relative; }
