import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { LocationStrategy, HashLocationStrategy } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';


import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';
import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';
import { ReactiveFormsModule } from '@angular/forms';

import { IconModule, IconSetModule, IconSetService } from '@coreui/icons-angular';
import { HTTPStatus, RequestInterceptor } from '../../src/app/views/CommonInterceptor';

const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true
};

import { AppComponent } from './app.component';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';

// Import containers
import { DefaultLayoutComponent } from './containers';

import { P404Component } from './views/error/404.component';
import { P500Component } from './views/error/500.component';
import { LoginComponent } from './views/login/login.component';
import { RegisterComponent } from './views/register/register.component';
import { ForgotPasswordComponent } from './views/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './views/reset-password/reset-password.component';

const APP_CONTAINERS = [
  DefaultLayoutComponent
];

import {
  AppAsideModule,
  AppBreadcrumbModule,
  AppHeaderModule,
  AppFooterModule,
  AppSidebarModule,
} from '@coreui/angular';

// Import routing module
import { AppRoutingModule } from './app.routing';
import { MomentModule } from 'ngx-moment';
// Import 3rd party components
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { ChartsModule } from 'ng2-charts';
import { Configuration } from '../configuration';
import { ModalModule } from 'ngx-bootstrap/modal';

//Service
import { TokenStorageService } from '../app/views/services/token-storage.service';
import { Loginservice } from '../app/views/services/login.service';
import { RoleService } from '../app/views/services/role.service';
import { ModuleService } from '../app/views/services/module.service';
import { AnimalTypeService } from '../app/views/services/animal_type.services';
import { treatmentService } from '../app/views/services/treatment.services';
import { LocationService } from '../app/views/services/location.sevices';
import { BreedingService } from '../app/views/services/breeding.services';
import { CustomerService } from '../app/views/services/customer.services';
import { DoctorService } from '../app/views/services/doctor.services';
import { ResourcesService } from '../app/views/services/resources.services';
import { AppForgotPasswordComponent } from './views/app-forgot-password/app-forgot-password.component';
import { AppointmentService } from '../app/views/services/appointments.services';
import { ProductService } from '../app/views/services/product.services';
import { PelfieService } from '../app/views/services/pelfies.services';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { AuthGuardService } from '../app/authguard';
import { OrderService } from '../app/views/services/order.service';
import { CovertusService } from '../app/views/services/covertus.services';
import { ToastrModule } from 'ngx-toastr';
import { CommonModule } from '@angular/common';


@NgModule({
  imports: [

    BsDatepickerModule.forRoot(),
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    AppAsideModule,
    AppBreadcrumbModule.forRoot(),
    AppFooterModule,
    AppHeaderModule,
    AppSidebarModule,
    PerfectScrollbarModule,
    BsDropdownModule.forRoot(),
    TabsModule.forRoot(),
    ChartsModule,
    CommonModule,
    ToastrModule.forRoot(), // ToastrModule added
    IconModule,
    IconSetModule.forRoot(),
    HttpClientModule,
    FormsModule,
    NgxPaginationModule,
    ReactiveFormsModule, MomentModule, ModalModule
  ],
  declarations: [
    AppComponent,
    ...APP_CONTAINERS,
    P404Component,
    P500Component,
    LoginComponent,
    RegisterComponent,
    ForgotPasswordComponent,
    ResetPasswordComponent,
    AppForgotPasswordComponent,

  ],
  providers: [
    {
      provide: LocationStrategy,
      useClass: HashLocationStrategy
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RequestInterceptor,
      multi: true
    }, HTTPStatus,
    IconSetService, Loginservice, ResourcesService, AnimalTypeService, AppointmentService, CustomerService, BreedingService, LocationService, treatmentService, TokenStorageService, Configuration, RoleService, ModuleService, DefaultLayoutComponent, DoctorService, AuthGuardService, ProductService, PelfieService, OrderService, CovertusService
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
