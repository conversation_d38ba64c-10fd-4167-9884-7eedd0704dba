{"ast": null, "code": "import isPlainObject from './isPlainObject.js';\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\n\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\n\nexport default customOmitClone;", "map": null, "metadata": {}, "sourceType": "module"}